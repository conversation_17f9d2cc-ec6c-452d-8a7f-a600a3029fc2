{"permissions": {"allow": ["Bash(python -m pytest tests/ --verbose --tb=short)", "<PERSON><PERSON>(python:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(pip install:*)", "Bash(ping:*)", "Bash(copy interface_gui_placeholder.py interface_07_sendConclusion_gui.py)", "Bash(cp:*)", "<PERSON><PERSON>(dir:*)", "Bash(mkdir -p logs)", "Bash(pkill -f gui_main.py)", "<PERSON><PERSON>(taskkill:*)", "Bash(cd \"D:\\python\\福能AI对接\")", "Bash(python -c \"import gui_main; print(''语法检查通过'')\")", "Bash(python test_interface_methods.py)", "Bash(python test_interfaces_12_15.py)", "<PERSON><PERSON>(curl:*)", "Bash(grep:*)"], "deny": []}}