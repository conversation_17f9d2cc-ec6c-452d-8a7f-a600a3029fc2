# 环境变量配置文件示例
# 复制此文件为 .env 并修改相应的配置值

# ================================
# 天健云API配置
# ================================
TIANJIAN_BASE_URL=http://**************:9300
TIANJIAN_API_KEY=3CNVizIjUq87IrczWqQB8SxjvPmVMTKM
TIANJIAN_MIC_CODE=MIC1.001E
TIANJIAN_MISC_ID=MISC1.00001A
TIANJIAN_TIMEOUT=30

# ================================
# 接口数据库配置（用于接口脚本）
# ================================
INTERFACE_DB_HOST=************
INTERFACE_DB_PORT=1433
INTERFACE_DB_NAME=Examdb
INTERFACE_DB_USER=znzj
INTERFACE_DB_PASSWORD=2025znzj/888
INTERFACE_DB_DRIVER=ODBC Driver 17 for SQL Server

# ================================
# 主数据库配置（用于Web API）
# ================================
DB_HOST=************
DB_PORT=1433
DB_NAME=Examdb
DB_USER=znzj
DB_PASSWORD=2025znzj/888
DB_DRIVER=ODBC Driver 17 for SQL Server

# ================================
# Flask应用配置
# ================================
FLASK_ENV=development
FLASK_DEBUG=true
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
SECRET_KEY=your-secret-key-here

# ================================
# 日志配置
# ================================
LOG_LEVEL=INFO
LOG_FILE=dx_api.log
LOG_MAX_BYTES=10485760
LOG_BACKUP_COUNT=5

# ================================
# 安全配置
# ================================
SIGNATURE_SECRET=your-signature-secret
RATE_LIMIT_ENABLED=true
RATE_LIMIT_DEFAULT=100/hour

# ================================
# CORS配置
# ================================
CORS_ORIGINS=*
