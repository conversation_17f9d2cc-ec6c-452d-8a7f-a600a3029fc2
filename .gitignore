# PyInstaller打包文件
dist/
build/
*.spec

# Python缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 虚拟环境
venv/
env/
ENV/

# IDE文件
.vscode/
.idea/
*.swp
*.swo

# 日志文件
*.log
logs/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 临时文件
*.tmp
*.temp
*.bak

# 系统文件
.DS_Store
Thumbs.db

# 压缩文件
*.zip
*.rar
*.7z

# 可执行文件
*.exe
*.msi

# 配置文件（包含敏感信息）
.env
config.ini
secrets.json

# 测试覆盖率报告
htmlcov/
.coverage
.pytest_cache/

# 备份目录
backup_*/ 