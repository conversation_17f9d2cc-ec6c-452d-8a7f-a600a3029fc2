# ⚠️ 重要说明

**此独立接收端服务已弃用，请使用GUI内置的07号接口服务。**

- **新的服务位置**: GUI程序内置的 `TianjianInterfaceService`
- **启动方式**: 运行 `python gui_main.py`，服务会自动启动
- **服务端口**: 仍然是 5007
- **接收端点**: 仍然是 `/dx/inter/receiveConclusion`
- **优势**: 统一管理、GUI日志集成、更好的调试功能

---

# 原文档内容（仅供参考）

# 天健云07号接口接收端实现说明

## 功能概述

07号接口接收端用于接收天健云回传的总检信息，并将这些信息写入到对应的体检数据库中。这是一个HTTP服务，天健云可以通过POST请求向该服务发送总检结论数据。

## 实现的功能

### 1. 接收端服务 (`interface_07_receiveConclusion.py`)

**主要功能**：
- 接收天健云回传的总检信息
- 根据医院编码自动路由到对应的数据库
- 将总检结论写入T_Diag_result和T_Check_Result_Illness表
- 更新T_Register_Main表的总检状态
- 支持新增字段处理：mappingId、deptId、abnormalLevel、childrenCode等
- 提供字段映射和数据转换功能

**核心类**：
- `TianjianInterface07Receiver`: 接收端处理类

**主要方法**：
- `validate_signature()`: 验证请求签名（可选）
- `process_conclusion_data()`: 处理总检数据
- `_update_diagnosis_info()`: 更新数据库信息
- `_insert_conclusion_record()`: 插入结论记录

### 2. 数据库操作

**涉及的数据表**：

1. **T_Register_Main** - 体检登记主表
   - 更新`cStatus`字段（总检状态）
   - 更新`cOperCode`、`cOperName`（总检医生）
   - 更新`dOperdate`（总检时间）

2. **T_Diag_result** - 总检结论表
   - `cClientCode`: 体检号
   - `cDiag`: 总检结论
   - `cDiagDesc`: 总检结论描述
   - `cDoctCode`: 总检医生编码
   - `cDoctName`: 总检医生姓名
   - `dDoctOperdate`: 总检时间
   - `cOperCode`: 初审医生编码
   - `cOpername`: 初审医生姓名
   - `dOperDate`: 初审时间
   - `cShopCode`: 机构编码

3. **T_Check_Result_Illness** - 结论词表
   - `cClientCode`: 体检号
   - `cDeptcode`: 科室代码（支持从deptId字段映射）
   - `cMainName`: 项目名称
   - `cIllnessCode`: 结论词代码
   - `cIllnessName`: 结论词名称
   - `cIllExplain`: 医学解释
   - `cReason`: 检查结果汇总
   - `cAdvice`: 建议
   - `cGrade`: 重要性等级（支持从abnormalLevel映射：1:A->1, 2:B->2, 3:C->3, 9:OTHER->3）
   - `nPrintIndex`: 显示序号（使用displaySequnce字段）

### 4. 新增字段处理说明

**新增字段映射关系**：
- `mappingId`: 健管系统结论词字典id - 记录到日志，用于系统间数据追踪
- `childrenCode`: 子结论词编码集合 - 记录到日志，建议后续扩展子结论词关联表
- `deptId`: 科室id - 映射到T_Check_Result_Illness表的cDeptcode字段
- `abnormalLevel`: 重要异常等级 - 映射到cGrade字段
  - 1:A级 → 等级1（重要）
  - 2:B级 → 等级2（次要）
  - 3:C级 → 等级3（其他）
  - 9:OTHER → 等级3（其他）

### 3. 接口规范

**接收端点**: `POST /dx/inter/receiveConclusion`

**请求头**:
```
Content-Type: application/json
sign: MD5签名（可选）
timestamp: 时间戳（可选）
nonce: 随机数（可选）
mic-code: 医院编码
misc-id: 系统ID
```

**请求体格式**:
```json
{
  "hospital": {
    "code": "医院编码",
    "name": "院区名称"
  },
  "peNo": "体检号",
  "firstCheckFinishTime": "初检完成时间yyyy-MM-dd HH:mm:ss",
  "firstCheckFinishDoctor": {
    "code": "初审医生系统编号",
    "name": "初审医生姓名",
    "synonyms": null,
    "zero": null
  },
  "mainCheckFinishTime": "总审完成时间yyyy-MM-dd HH:mm:ss",
  "mainCheckFinishDoctor": {
    "code": "总审医生系统编号",
    "name": "总审医生姓名",
    "synonyms": null,
    "zero": null
  },
  "currentNodeType": "当前操作节点用于流程控制，1登记,2分科3主检4总检",
  "conclusionList": [
    {
      "mappingId": "健管系统结论词字典id",
      "conclusionName": "结论词",
      "conclusionCode": "结论词编号",
      "parentCode": "父类编码,合并结论词存在",
      "suggest": "解释建议",
      "explain": "医学解释",
      "checkResult": "检查结果汇总描述",
      "level": "1-重要；2-次要；3-其他",
      "displaySequnce": "显示序号",
      "childrenCode": "子结论词编码集合",
      "deptId": "科室id",
      "abnormalLevel": "重要异常等级1:A 2:B 3:C 9:OTHER"
    }
  ]
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "总检信息更新成功",
  "code": 0,
  "data": {
    "peNo": "体检号",
    "updated_records": "更新记录数",
    "conclusion_count": "结论数量"
  }
}
```

## 使用方法

### 1. 启动接收端服务

**方法一：直接启动**
```bash
python interface_07_receiveConclusion.py
```

**方法二：使用启动脚本**
```bash
python start_interface_07_receiver.py
```

服务将在端口5007上启动，监听所有网络接口。

### 2. 健康检查

访问健康检查端点确认服务状态：
```bash
curl http://localhost:5007/health
```

### 3. 测试接收功能

使用测试脚本验证功能：
```bash
python test_interface_07_receiver.py
```

## 配置要求

### 1. 多机构配置

需要在`multi_org_config.py`中配置各医院的信息：
- 医院编码（天健云MIC代码）
- 数据库连接信息
- API密钥（用于签名验证）

### 2. 数据库权限

接收端需要对以下表有读写权限：
- T_Register_Main
- T_Diag_result
- T_Check_Result_Illness

## 错误处理

**错误码说明**：
- `1000`: 请求数据为空
- `1001`: 体检号不能为空
- `1002`: 未找到医院编码对应的机构配置
- `1003`: 数据库连接未配置
- `1004`: 数据库连接失败
- `1005`: 数据更新失败
- `1999`: 服务器内部错误

## 安全考虑

1. **签名验证**：支持MD5签名验证（可选启用）
2. **数据验证**：对输入数据进行严格验证
3. **错误处理**：完善的异常处理和错误日志
4. **事务处理**：数据库操作支持事务回滚

## 部署建议

1. **生产环境**：使用WSGI服务器（如Gunicorn）部署
2. **负载均衡**：可以部署多个实例进行负载均衡
3. **监控**：建议添加日志监控和性能监控
4. **备份**：定期备份数据库，确保数据安全

## 测试验证

提供了完整的测试脚本：
- 健康检查测试
- 正常数据接收测试
- 异常数据处理测试
- 数据库写入验证

## 新增字段详细说明

### 1. mappingId（健管系统结论词字典id）
- **用途**: 用于健管系统与体检系统之间的数据映射和追踪
- **处理方式**: 记录到系统日志中，便于数据同步和问题排查
- **建议**: 后续可考虑在数据库中增加专门字段存储此映射关系

### 2. childrenCode（子结论词编码集合）
- **用途**: 存储与主结论词相关的子结论词编码
- **数据类型**: 数组或集合类型
- **处理方式**: 当前记录到日志中
- **建议**: 如需完整支持，建议创建子结论词关联表

### 3. deptId（科室id）
- **用途**: 指定结论词所属的科室
- **映射关系**: 映射到T_Check_Result_Illness表的cDeptcode字段
- **处理逻辑**:
  - 如果提供deptId，优先使用作为科室代码
  - 如果未提供，则使用parentCode作为科室代码
  - 字段长度限制为6个字符

### 4. abnormalLevel（重要异常等级）
- **用途**: 标识结论的异常严重程度
- **取值范围**:
  - 1: A级（最重要）
  - 2: B级（重要）
  - 3: C级（一般）
  - 9: OTHER（其他）
- **映射关系**: 映射到T_Check_Result_Illness表的cGrade字段
- **映射规则**:
  ```
  1 (A级) → "1" (重要)
  2 (B级) → "2" (次要)
  3 (C级) → "3" (其他)
  9 (OTHER) → "3" (其他)
  ```

### 5. displaySequnce（显示序号）
- **用途**: 控制结论词在报告中的显示顺序
- **映射关系**: 映射到T_Check_Result_Illness表的nPrintIndex字段
- **默认值**: 如果未提供，使用结论在列表中的序号

## 版本更新说明

### v2.0 (当前版本)
- 新增支持mappingId、childrenCode、deptId、abnormalLevel字段
- 增强科室代码映射逻辑
- 优化异常等级处理
- 完善日志记录和错误处理

### v1.0 (原始版本)
- 基础的总检信息接收功能
- 支持基本的结论词处理

## 注意事项

1. 确保数据库连接配置正确
2. 体检号必须在数据库中存在
3. 时间格式必须为`yyyy-MM-dd HH:mm:ss`
4. 结论数据会覆盖原有的总检结论
5. 服务启动后会占用5007端口
6. 新增字段为可选字段，向后兼容旧版本数据格式
7. 建议在生产环境中监控新增字段的处理日志
