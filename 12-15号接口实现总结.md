# 天健云12-15号接口实现总结

## 概述

本文档总结了天健云12-15号接口的完整实现，这些接口都是**可选功能**，主要用于主检系统的辅助管理功能。

## 接口列表

### 12号接口 - 主检锁定与解锁 (lockPeInfo)
**功能**：主检系统锁定和解锁报告的主检任务状态，防止多个医生同时操作同一体检单。

**端点**：`POST /dx/inter/lockPeInfo`

**请求格式**：
```json
{
  "operator": "操作人主键id",
  "peInfoList": [
    {
      "accountId": "DOCTOR001",      // 医生账号ID
      "currentNodeType": 3,          // 当前节点类型（3=主检）
      "force": false,                // 是否强制操作
      "operationType": 1,            // 操作类型（1=锁定，2=解锁）
      "peNo": "PE202501010001"       // 体检号
    }
  ]
}
```

**实现文件**：`interface_12_lockPeInfo.py`
**核心方法**：`lock_pe_info(request_data)`

---

### 13号接口 - 体检状态更新 (updatePeStatus)
**功能**：更新体检报告的流程状态，支持登记、检查、总检、总审等状态变更。

**端点**：`POST /dx/inter/updatePeStatus`

**请求格式**：
```json
{
  "nodeType": "3",                   // 节点类型 (1登记 2检查 3总检 4总审)
  "timestamp": "2025-01-12 10:30:00", // 变更时间
  "doUser": {                        // 操作人信息
    "code": "DOCTOR001",
    "name": "张医生"
  },
  "peNo": "PE202501010001"           // 体检号
}
```

**实现文件**：`interface_13_updatePeStatus.py`
**核心方法**：`update_pe_status_service(request_data)`

---

### 14号接口 - 重要异常标注 (markAbnormal)
**功能**：主检系统将发现的重要异常通知给体检系统，支持紧急、严重、重要等级别标注。

**端点**：`POST /dx/inter/markAbnormal`

**请求格式**：
```json
{
  "abnormalList": [
    {
      "peNo": "PE202501010001",      // 体检号  
      "abnormalType": "URGENT",      // 异常类型（URGENT/SERIOUS/IMPORTANT/NORMAL）
      "abnormalContent": "血压异常偏高", // 异常内容
      "deptCode": "DEPT001",         // 科室编码
      "doctorCode": "DOCTOR001",     // 医生编码
      "remark": "需要立即复查"        // 备注
    }
  ]
}
```

**实现文件**：`interface_14_markAbnormal.py`
**核心方法**：`mark_abnormal_service(request_data)`

---

### 15号接口 - 分科退回 (returnDept)
**功能**：主检系统将自动发现或人工发现的分科异常问题，返回给体检系统重新处理。

**端点**：`POST /dx/inter/returnDept`

**请求格式**：
```json
{
  "returnList": [
    {
      "peNo": "PE202501010001",      // 体检号
      "deptCode": "DEPT001",         // 科室编码
      "returnReason": "INCOMPLETE",  // 退回原因（INCOMPLETE/ERROR/RECHECK/OTHER）
      "returnDoctorCode": "DOCTOR001", // 退回医生编码
      "returnRemark": "检查项目不完整" // 退回备注
    }
  ]
}
```

**实现文件**：`interface_15_returnDept.py`
**核心方法**：`return_dept_service(request_data)`

## 技术架构

### 1. 统一服务架构
所有12-15号接口都集成在 `gui_main.py` 的 `TianjianInterfaceService` 类中，通过5007端口提供统一服务。

### 2. 实现模式
每个接口都遵循统一的实现模式：
- **接口类**：如 `TianjianInterface12`
- **服务入口方法**：如 `lock_pe_info(request_data)` - GUI服务调用
- **业务逻辑方法**：如 `lock_or_unlock_pe_info()` - 具体业务实现
- **请求发送方法**：`send_request()` - HTTP请求处理

### 3. 参数验证
每个接口都包含完整的参数验证：
- 必要字段检查
- 数据类型验证
- 业务规则验证
- 错误信息返回

### 4. 错误处理
统一的错误处理机制：
```json
{
  "code": -1,
  "msg": "错误描述",
  "data": null
}
```

## 部署方式

### 1. 启动服务
```bash
python gui_main.py
```
服务自动启动在5007端口，包含所有21个接口。

### 2. 健康检查
```bash
curl http://localhost:5007/health
```

### 3. 接口调用示例
```bash
# 12号接口
curl -X POST http://localhost:5007/dx/inter/lockPeInfo \
  -H "Content-Type: application/json" \
  -d '{"operator":"ADMIN001","peInfoList":[...]}'

# 13号接口  
curl -X POST http://localhost:5007/dx/inter/updatePeStatus \
  -H "Content-Type: application/json" \
  -d '{"nodeType":"3","peNo":"PE001","doUser":{...}}'
```

## 测试方式

### 1. 单独测试
```bash
python interface_12_lockPeInfo.py     # 测试12号接口
python interface_13_updatePeStatus.py # 测试13号接口  
python interface_14_markAbnormal.py   # 测试14号接口
python interface_15_returnDept.py     # 测试15号接口
```

### 2. 综合测试
```bash
python test_interfaces_12_15.py       # 测试所有12-15号接口
```

### 3. GUI测试
在GUI界面的"管理功能接口 (07-15)"标签页中，可以直接测试这些接口。

## 数据库支持

### 主要访问表
- `T_Register_Main` - 体检登记主表
- `Code_Operator_dict` - 操作员字典表
- `Code_Dept_dict` - 科室字典表
- `T_Check_result_Main` - 检查结果主表

### SQL查询示例
```sql
-- 查询体检信息
SELECT trm.cRegNo as peNo, trm.cName as patientName, trm.cStatus as status
FROM T_Register_Main trm 
WHERE trm.cRegNo = ?

-- 查询操作员信息
SELECT cod.cOperCode, cod.cOperName 
FROM Code_Operator_dict cod
WHERE cod.cOperCode = ?
```

## 业务场景

### 1. 主检锁定场景
- 医生A开始主检体检单PE001
- 系统调用12号接口锁定该体检单
- 医生B尝试主检同一体检单时被拒绝
- 医生A完成主检后调用解锁接口

### 2. 状态更新场景  
- 体检单完成分科检查
- 系统调用13号接口更新状态为"总检"
- 主检医生可以开始总检工作

### 3. 异常标注场景
- 主检医生发现紧急异常
- 调用14号接口标注异常级别为URGENT
- 体检系统收到通知并处理

### 4. 分科退回场景
- 主检发现某科室检查数据有误
- 调用15号接口退回该科室重新检查
- 科室收到退回通知并重新处理

## 注意事项

1. **可选性**：这些接口都是可选功能，根据实际需求选择使用
2. **权限控制**：需要配合实际的权限管理系统
3. **并发处理**：锁定机制需要考虑并发场景
4. **数据一致性**：状态更新需要保证数据一致性
5. **异常处理**：异常标注需要及时通知相关人员

## 扩展性

该架构支持轻松扩展：
- 新增接口只需添加路由和实现类
- 统一的错误处理和日志记录
- 可配置的API参数和超时设置
- 支持测试模式和生产模式切换

---

**实现完成时间**：2025-01-24  
**实现状态**：✅ 完成  
**测试状态**：✅ 通过  
**部署状态**：✅ 已集成到GUI服务