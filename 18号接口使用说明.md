# 18号接口 - 查询医生信息 (getDoctorInfo)

## 接口说明

18号接口用于查询医生的基本信息，包括姓名、身份证号、手机号、性别、所属科室等。

## 功能特点

- 支持查询所有医生信息
- 支持根据医生ID查询特定医生
- 支持根据医院编码筛选医生
- 支持从本地数据库获取医生信息
- 提供测试模式验证接口功能

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | String | 否 | 医生主键，如果不传则查询所有的数据 |
| hospitalCode | String | 否 | 医院编码，适用于多院区的情况 |

## 响应格式

```json
{
  "code": 0,
  "msg": "查询成功",
  "data": [
    {
      "name": "张医生",
      "icCode": "110101198001011234",
      "phoneNo": "***********",
      "sex": {
        "code": "1",
        "name": "男性"
      },
      "accountCode": "DOCTOR001",
      "accountId": "DOC001",
      "dept": {
        "code": "DEPT001",
        "name": "内科"
      },
      "memo": "主治医师",
      "createTime": "2025-07-24 10:30:00"
    }
  ]
}
```

## 数据库表结构

### Code_Operator_dict (操作员字典表)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| cCode | varchar | 操作员编码(医生编码) |
| cName | varchar | 操作员姓名 |
| cCardNo | varchar | 身份证号 |
| cMobil | varchar | 手机号 |
| cDepartmentCode | varchar | 科室编码 |
| cDoctorTag | varchar | 是否医生标记(1为医生) |
| cStopTag | varchar | 停用标记(0为启用) |
| cMemo | varchar | 备注信息 |

### Code_Dept_dict (科室字典表)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| cCode | varchar | 科室编码 |
| cName | varchar | 科室名称 |
| cStopTag | varchar | 停用标记(0为启用) |

## 使用示例

### Python代码示例

```python
from interface_18_getDoctorInfo import TianjianInterface18
from config import Config

# 获取API配置
api_config = Config.get_tianjian_api_config()

# 创建接口实例
interface = TianjianInterface18(api_config)

# 查询所有医生信息
result = interface.query_doctor_info()

# 查询特定医生信息
result = interface.query_doctor_info(doctor_id="DOCTOR001")

# 从数据库获取医生信息
doctors = interface.get_doctor_info_from_db(limit=10)
```

### GUI界面操作

1. 运行 `interface_18_gui.py` 启动图形界面
2. 在"医生ID"字段中输入医生编码查询特定医生
3. 在"医院编码"字段中输入医院编码进行筛选
4. 点击"查询医生信息"按钮获取数据
5. 点击"从数据库查询"按钮直接从本地数据库获取数据
6. 点击"测试模式查询"按钮使用模拟数据验证接口

## 注意事项

1. 实际API调用可能会因为网络或服务器问题而失败
2. 测试模式下返回的是模拟数据，不反映真实医生信息
3. 数据库查询需要确保数据库连接正常
4. 医生信息仅包含标记为医生(cDoctorTag='1')且未停用(cStopTag='0')的记录