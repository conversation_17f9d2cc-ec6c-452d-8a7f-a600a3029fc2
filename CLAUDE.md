# CLAUDE.md

本文件为Claude Code (claude.ai/code)在此代码库中工作时提供指导。

## 项目概述

这是一个**医疗数据同步系统**，用于将嘉仁体检中心的体检数据同步到天健云平台。这是一个生产就绪的Python应用程序，使用MD5签名认证处理安全的医疗数据传输。

## 核心开发命令

### 主应用程序命令（传统接口）
```bash
# 检查系统状态
python main.py status

# 显示配置信息
python main.py config

# 同步前预览数据
python main.py preview

# 同步体检数据（最近N天）
python main.py sync-exam --days 7

# 同步字典数据
python main.py sync-dict

# 测试API连接
python main.py test-api
```

### 新天健云接口命令（01-06号）
```bash
# 01号接口 - 单次体检基本信息传输
python main.py tianjian-01 --days 7 --limit 10 --test-mode

# 02号接口 - 申请项目字典数据传输  
python main.py tianjian-02 --limit 50 --test-mode --batch-size 20

# 03号接口 - 体检科室结果传输
python main.py tianjian-03 --days 7 --limit 10 --test-mode --batch-size 5

# 04号接口 - 医生信息传输
python main.py tianjian-04 --test-mode --batch-size 10

# 05号接口 - 科室信息传输
python main.py tianjian-05 --test-mode --batch-size 10

# 06号接口 - 字典信息传输
python main.py tianjian-06 --test-mode --batch-size 10

# 测试所有天健云接口
python main.py tianjian-all --test-only
python main.py tianjian-all --send-actual
```

### 新健康同步模块命令
```bash
# 测试系统连接
python health_sync/cli.py test

# 显示系统配置
python health_sync/cli.py config

# 申请项目操作
python health_sync/cli.py apply-item summary
python health_sync/cli.py apply-item list --limit 10
python health_sync/cli.py apply-item sync --batch-size 50
python health_sync/cli.py apply-item sync --dry-run --show-sample
python health_sync/cli.py apply-item sync --debug
```

### 测试命令
```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_db_connection.py
pytest tests/test_api_connection.py

# 代码格式化和检查
black .
flake8 .

# 带覆盖率的测试
pytest --cov=health_sync

# 数据库连接测试
python tests/test_db_connection.py

# API连接测试
python tests/test_api_connection.py
python tests/real_api_test.py

# 接口特定测试
python tests/test_apply_item_interface.py

# 数据同步测试
python tests/test_sync_data.py

# 配置测试
python tests/test_config.py
```

### 安装和设置
```bash
# 安装依赖
pip install -r requirements.txt
pip install -r health_sync/requirements.txt

# 数据库架构检查
python check_database_schema.py

# 配置向导
python setup_wizard.py
python health_sync/setup_wizard.py
```

### 代码检查和测试
项目使用：
- **pytest** 作为测试框架
- **black** 用于代码格式化  
- **flake8** 用于代码检查
- **pytest-cov** 用于覆盖率报告

## 架构概述

### 双重架构系统
本项目采用了双重架构系统：

1. **传统系统** (`main.py`, `sync_service.py`等)
   - 原始实现
   - 直接脚本执行
   - 简单命令行界面

2. **新健康同步模块** (`health_sync/`)
   - 现代模块化架构
   - 具有服务层、API客户端和配置管理的适当包结构
   - 具有丰富功能的增强CLI

### 核心组件

#### API层 (`health_sync/api/`)
- **client.py**: 天健云API客户端，支持全面的接口调用
- **schemas.py**: 使用Pydantic进行验证的数据模型
- **signer.py**: 用于安全API认证的MD5签名生成

#### 服务层 (`health_sync/services/`)
- **apply_item_service.py**: 申请项目同步的业务逻辑
- 处理数据检索、格式化和API传输

#### 数据库层 (`health_sync/db/`)
- **models.py**: SQLAlchemy ORM模型
- **session.py**: 数据库连接管理
- **crud.py**: 数据库操作

#### 配置 (`health_sync/config/`)
- **settings.py**: 使用Pydantic的集中配置
- **defaults.yaml**: 默认配置值
- 基于YAML的配置，支持环境变量

### 天健云API集成

系统实现了多个天健云API端点：

1. **接口01**: 体检基本信息传输 (`/dx/inter/sendPeInfo`)
2. **接口02**: 申请项目字典数据传输 (`/dx/inter/syncApplyItem`)
3. **接口06**: 字典信息传输 (`/dx/inter/syncDict`)

认证使用MD5签名：`MD5(api_key + timestamp)`

必需的请求头：
- `sign`: MD5哈希
- `timestamp`: YYYYmmDDHHMMSS格式
- `mic-code`: 机构代码
- `misc-id`: 系统ID

### 数据库架构

主要表：
- **T_Register_Main**: 体检记录
- **Code_Item_Main**: 申请项目定义
- **code_Item_Price**: 项目价格和科室信息
- **Code_Dept_dict**: 科室字典
- **Code_Operator_dict**: 操作员字典

数据库：SQL Server 2008 R2+ with ODBC Driver 17

### 配置管理

关键配置文件：
- **config.yaml**: 主YAML配置
- **env_template.txt**: 环境变量模板

配置包括：
- API端点和认证
- 数据库连接（主要+PACS）
- 同步参数和调度
- 日志和监控设置

## 开发指南

### 代码结构模式

在此代码库中工作时，请遵循这些既定模式：

1. **接口实现模式** - 每个天健云接口都遵循一致的结构：
   ```python
   # 从统一配置系统获取配置
   from config import Config
   
   # 使用database_service.py进行数据库操作
   from database_service import DatabaseService
   
   # 使用适当认证头的API调用
   headers = {
       'sign': md5_signature,
       'timestamp': formatted_timestamp,
       'mic-code': Config.get_tianjian_api_config()['mic_code']
   }
   ```

2. **错误处理模式** - 所有接口使用一致的错误处理：
   ```python
   try:
       # 数据库/API操作
   except Exception as e:
       print(f"❌ 错误: {str(e)}")
       # 记录到适当的日志文件
   ```

3. **数据映射模式** - 数据库到API字段映射遵循每个接口文件中记录的架构

### 添加新功能

1. 使用**health_sync**模块进行新开发
2. 遵循服务层模式
3. 使用Pydantic架构添加适当的数据验证
4. 包含全面的错误处理和日志记录
5. 为新功能编写测试

### 数据库操作

- 使用`health_sync/db/models.py`中的SQLAlchemy ORM模型
- 数据库会话由`health_sync/db/session.py`管理
- 遵循数据访问的仓库模式

### API集成

- 扩展`health_sync/api/client.py`以支持新端点
- 在`health_sync/api/schemas.py`中定义请求/响应架构
- 使用签名工具进行认证

### 测试策略

- 使用pytest进行单元测试
- 使用实际数据库进行集成测试（测试环境）
- 使用天健云测试端点进行API测试
- 使用`debug_apply_item_interface.py`进行详细的API调试

### 调试和故障排除

用于API调试，请使用：
```bash
python tests/debug_apply_item_interface.py  # 显示完整的HTTP请求/响应
python health_sync/cli.py apply-item sync --debug --dry-run
```

**重要说明**：天健云服务运行正常。如果遇到502 Bad Gateway错误，这通常表示请求消息格式不正确，而不是服务器问题。常见原因：
- 缺少必填字段
- 数据类型不正确
- 无效的JSON结构
- 错误的认证头
- 不当的MD5签名格式

### 常用故障排除命令

```bash
# 测试数据库连接
python config_manager.py  # 选择选项2

# 测试API连接  
python config_manager.py  # 选择选项3

# 检查系统状态
python main.py status

# 验证配置
python config_manager.py  # 选择选项5

# 使用详细日志测试特定接口
python interface_01_sendPeInfo.py --test-mode --verbose-message --limit 1
```

这将显示完整的请求/响应负载，用于排除API集成问题。