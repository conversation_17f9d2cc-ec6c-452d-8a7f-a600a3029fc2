# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个**医疗数据同步系统**，用于将嘉仁体检中心的体检数据同步到天健云平台。这是一个生产就绪的Python应用程序，使用MD5签名认证处理安全的医疗数据传输。该系统实现了完整的21个天健云接口，支持体检数据、字典信息、查询管理等全业务流程的数据同步。

## 核心开发命令

### GUI应用程序（推荐）
```bash
# 运行主GUI应用（包含07-21号接口统一服务）
python gui_main.py

# 打包GUI应用为可执行文件
# 使用64位Python环境
C:\Users\<USER>\AppData\Local\Programs\Python\Python38\python.exe -m PyInstaller gui_main.spec

# 快速修复和重新打包
# 双击运行
rebuild_with_fix.bat
fix_and_rebuild.bat
```

### 天健云接口命令（01-21号完整实现）
```bash
# 01号接口 - 单次体检基本信息传输
python interface_01_sendPeInfo.py --test-mode --days 7 --limit 10

# 02号接口 - 申请项目字典数据传输  
python interface_02_syncApplyItem.py --test-mode --batch-size 20

# 03号接口 - 体检科室结果传输
python interface_03_deptInfo.py --test-mode --days 7 --limit 10

# 04-06号接口 - 字典同步（医生、科室、字典）
python interface_04_syncUser.py --test-mode
python interface_05_syncDept.py --test-mode
python interface_06_syncDict.py --test-mode

# 07号接口 - 总检结论上传
python interface_07_sendConclusion.py --test-mode

# 08号接口 - 字典查询
python interface_08_getDict.py --hospital-code 09

# 09号接口 - 重传科室信息
python interface_09_retransmitDeptInfo.py --test-mode

# 10号接口 - 批量获取体检信息
python interface_10_batchGetPeInfo.py --test-mode

# 11号接口 - 申请项目字典查询
python interface_11_getApplyItemDict_standard.py --hospital-code 09

# 16-21号接口 - 高级查询和管理接口
# 这些接口通过GUI的统一Flask服务提供（端口5000）
```

### 健康同步模块命令
```bash
# 测试系统连接
python health_sync/cli.py test

# 显示系统配置
python health_sync/cli.py config

# 申请项目操作
python health_sync/cli.py apply-item summary
python health_sync/cli.py apply-item list --limit 10
python health_sync/cli.py apply-item sync --batch-size 50
python health_sync/cli.py apply-item sync --dry-run --show-sample
python health_sync/cli.py apply-item sync --debug
```

### 测试和验证命令
```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_db_connection.py
pytest tests/test_api_connection.py

# 测试单个接口
python tests/test_interfaces_11_21.py

# 数据库连接测试
python tests/test_db_connection.py

# 配置和环境测试
python check_environment.py
python fix_database_config.py
```

### 故障排除和修复命令
```bash
# 修复PyInstaller打包问题
python fix_pyinstaller.py

# 检查和修复数据库配置
python fix_database_config.py

# 修复Flask Content-Type问题
python fix_flask_content_type.py

# 检查Python环境
python check_environment.py
python find_python.py

# 检查依赖
python check_dependencies.py
python install_dependencies.py
```

### 安装和设置
```bash
# 安装依赖
pip install -r requirements.txt
pip install -r health_sync/requirements.txt

# 主要依赖包
pip install PySide6 pyodbc requests flask PyInstaller

# 数据库架构检查
python check_database_schema.py

# 配置向导
python setup_wizard.py
python health_sync/setup_wizard.py
```

## 架构概述

### 三层架构系统
本项目采用了三层架构系统：

1. **传统CLI系统** (`main.py`, `sync_service.py`等)
   - 命令行接口
   - 直接脚本执行
   - 适合自动化和批处理

2. **现代GUI系统** (`gui_main.py`)
   - PySide6现代化界面
   - 集成07-21号接口的Flask服务
   - 统一端口5000对外提供服务
   - 实时日志和状态监控

3. **健康同步模块** (`health_sync/`)
   - 模块化架构
   - SQLAlchemy ORM
   - Pydantic数据验证
   - 增强CLI

### 数据库架构

#### 双数据库系统
系统使用双数据库架构：

1. **本地业务数据库** (默认: `************/Examdb`)
   - 体检业务数据
   - 科室字典数据  
   - 申请项目数据

2. **中心库** (`***********/Examdb`)
   - 机构配置数据
   - 同步日志数据
   - 系统配置数据

#### 核心数据表
- **T_Register_Main**: 体检记录
- **Code_Item_Main**: 申请项目定义
- **Code_Dept_dict**: 科室字典
- **Code_Operator_dict**: 操作员字典
- **T_TianJian_Sync_Log**: 天健云同步日志
- **T_Organization_Config**: 机构配置

### 天健云API集成

系统实现了完整的21个天健云API端点：

#### 数据同步接口(01-06号)
1. **接口01**: 体检基本信息传输 (`/dx/inter/sendPeInfo`)
2. **接口02**: 申请项目字典传输 (`/dx/inter/syncApplyItem`)
3. **接口03**: 科室结果传输 (`/dx/inter/deptInfo`)
4. **接口04**: 医生信息传输 (`/dx/inter/syncUser`)
5. **接口05**: 科室信息传输 (`/dx/inter/syncDept`)
6. **接口06**: 字典信息传输 (`/dx/inter/syncDict`)

#### 服务接口(07-21号) - 通过GUI Flask服务提供
7. **接口07**: 总检结论上传 (`/dx/inter/sendConclusion`)
8. **接口08**: 字典查询 (`/dx/inter/getDict`)
9. **接口09**: 重传科室信息 (`/dx/inter/retransmitDeptInfo`)
10. **接口10**: 批量获取体检信息 (`/dx/inter/batchGetPeInfo`)
11. **接口11**: 申请项目字典查询 (`/dx/inter/getApplyItemDict`)
12. **接口12**: 锁定体检信息 (`/dx/inter/lockPeInfo`)
13. **接口13**: 更新体检状态 (`/dx/inter/updatePeStatus`)
14. **接口14**: 重要异常标注 (`/dx/inter/markAbnormal`)
15. **接口15**: 分科退回 (`/dx/inter/returnDept`)
16. **接口16**: 查询图片 (`/dx/inter/getImages`)
17. **接口17**: 删除重要异常 (`/dx/inter/deleteAbnormal`)
18. **接口18**: 查询医生信息 (`/dx/inter/getDoctorInfo`)
19. **接口19**: 查询科室信息 (`/dx/inter/getDeptInfo`)
20. **接口20**: 查询个人开单 (`/dx/inter/getPersonalOrders`)
21. **接口21**: 查询异常通知 (`/dx/inter/getAbnormalNotice`)

#### API认证
认证使用MD5签名：`MD5(api_key + timestamp)`

必需的请求头：
- `sign`: MD5哈希
- `timestamp`: YYYYmmDDHHMMSS格式
- `mic-code`: 机构代码
- `misc-id`: 系统ID

### 配置管理

#### 关键配置文件
- **config.py**: 统一配置类，支持环境变量
- **config.yaml**: 主YAML配置
- **.env**: 环境变量配置
- **health_sync/config/defaults.yaml**: 健康同步模块配置

#### 配置层次结构
1. 环境变量 (最高优先级)
2. .env文件
3. config.yaml
4. 代码默认值 (最低优先级)

## 开发指南

### 接口实现模式

每个天健云接口都遵循一致的结构：

```python
# 从统一配置系统获取配置
from config import Config

# 使用database_service.py进行数据库操作
from database_service import get_database_service, get_center_database_service

# MD5签名认证
headers = {
    'sign': md5_signature,
    'timestamp': formatted_timestamp, 
    'mic-code': Config.get_tianjian_api_config()['mic_code'],
    'misc-id': Config.get_tianjian_api_config()['misc_id']
}
```

### 数据库连接模式

```python
# 本地业务数据库
from database_service import get_database_service
db_service = get_database_service()

# 中心库（配置和日志）
from database_service import get_center_database_service  
center_db = get_center_database_service()

# 多机构动态连接
from multi_org_config import get_org_config_by_shop_code
org_config = get_org_config_by_shop_code(shop_code)
```

### Flask服务集成模式

GUI应用集成了Flask服务，统一提供07-21号接口：

```python
# 在gui_main.py中的Flask路由
@self.app.route('/dx/inter/getImages', methods=['POST'])
def get_images():
    # 兼容多种Content-Type
    data = None
    if request.is_json:
        data = request.get_json()
    elif request.data:
        data = json.loads(request.data.decode('utf-8'))
    # 处理业务逻辑...
```

### 错误处理模式

所有接口使用一致的错误处理：

```python
try:
    # 数据库/API操作
    result = interface.process_data(data)
    return jsonify(result)
except Exception as e:
    error_msg = f"接口异常: {str(e)}"
    self.signal_emitter.log_signal.emit("错误", error_msg)
    return jsonify({'code': -1, 'msg': f'操作失败: {str(e)}', 'data': []})
```

### PyInstaller打包模式

使用64位Python环境打包GUI应用：

```bash
# 检查Python环境
python check_environment.py

# 安装依赖到64位Python
C:\Users\<USER>\AppData\Local\Programs\Python\Python38\python.exe -m pip install pyinstaller PySide6 flask pyodbc requests

# 使用spec文件打包
C:\Users\<USER>\AppData\Local\Programs\Python\Python38\python.exe -m PyInstaller gui_main.spec
```

### 多机构支持模式

系统支持多机构部署：

```python
# 机构配置查询
from multi_org_config import get_org_config_by_shop_code
org_config = get_org_config_by_shop_code('09')

# 中心库机构管理
from center_organization_service import CenterOrganizationService
org_service = CenterOrganizationService()
organizations = org_service.get_all_organizations()
```

### 调试和故障排除

#### API调试
```bash
# 显示完整HTTP请求/响应
python tests/debug_apply_item_interface.py
python interface_01_sendPeInfo.py --test-mode --verbose-message --limit 1
```

#### 数据库连接问题
```bash
# 测试数据库连接
python fix_database_config.py

# 检查配置
python config_manager.py
```

#### PyInstaller打包问题
```bash
# 检查模块依赖
python check_dependencies.py

# 修复打包环境
python fix_pyinstaller.py

# 修复Flask Content-Type问题  
python fix_flask_content_type.py
```

### 重要技术说明

1. **数据库架构**: 使用双数据库系统，本地业务数据和中心配置数据分离
2. **Flask服务集成**: GUI应用内嵌Flask服务，统一5000端口对外提供07-21号接口
3. **Content-Type兼容**: Flask路由支持多种Content-Type，解决客户端请求格式问题
4. **64位打包要求**: PyInstaller需要使用64位Python环境进行打包
5. **MD5签名认证**: 所有天健云API调用都需要MD5签名认证
6. **多机构支持**: 支持根据shopcode动态切换不同机构的数据库连接

### 常见问题解决

- **502 Bad Gateway**: 通常是请求格式问题，检查JSON结构和必填字段
- **415 Unsupported Media Type**: Content-Type问题，已在GUI Flask路由中修复
- **数据库连接失败**: 检查数据库名称（默认应为'Examdb'而非'examdb_center'）
- **PyInstaller闪退**: 检查模块依赖和Python环境（需要64位）
- **接口认证失败**: 检查MD5签名算法和时间戳格式