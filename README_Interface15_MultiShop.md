# 天健云15号接口（分科退回）- 多门店数据库支持

## 📋 功能概述

天健云15号接口现已支持多门店数据库架构，可以根据天健云报文中的 `shopCode`（门店编码）自动连接到对应门店的数据库进行数据写入。

## 🔧 技术实现

### 核心功能
1. **多门店数据库支持**: 根据 `shopCode` 自动选择对应门店的数据库连接
2. **单条/批量处理**: 支持单条记录和批量记录的分科退回
3. **权限容错处理**: 数据库权限不足时记录到日志并返回成功状态
4. **完整日志记录**: 详细记录所有处理过程和结果

### 报文格式

#### 单条分科退回
```json
{
  "peNo": "5000003",           // 体检号
  "markDoctor": "DOC001",      // 标记医生
  "errorItem": "ITEM001",      // 错误项目
  "returnDept": {              // 退回科室
    "code": "DEPT001",
    "name": "内科"
  },
  "receiveDoctor": {           // 接收医生
    "code": "DOC002",
    "name": "李医生"
  },
  "remark": "检查结果需要重新确认",  // 退回原因
  "currentNodeType": 2,        // 当前节点类型
  "shopCode": "09"             // 🔑 关键：门店编码
}
```

#### 批量分科退回
```json
{
  "returnList": [
    {
      "peNo": "5000003",
      "markDoctor": "DOC001",
      // ... 其他字段
      "shopCode": "09"
    },
    {
      "peNo": "5000004", 
      "markDoctor": "DOC003",
      // ... 其他字段
      "shopCode": "09"
    }
  ]
}
```

## 🚀 使用方法

### 1. 接口地址
```
POST http://localhost:5007/dx/inter/returnDept
Content-Type: application/json
```

### 2. 核心配置

确保系统中已配置门店编码与数据库的映射关系：

```python
# 多机构配置示例
{
  "09": {
    "org_code": "09", 
    "org_name": "福州嘉仁体检中心",
    "db_host": "***********",
    "db_port": 1433,
    "db_name": "Examdb",
    "db_user": "tj",
    "db_password": "***"
  }
  // 其他门店配置...
}
```

### 3. 数据库表结构

分科退回数据会写入门店对应数据库的以下表：

```sql
-- 主表：分科退回记录
CREATE TABLE T_Check_Result_Return (
    id nvarchar(50) PRIMARY KEY,
    cShopCode nvarchar(10),      -- 门店编码
    cClientCode nvarchar(10),    -- 体检号  
    cMainCode nvarchar(10),      -- 退回项目代码
    cMainName nvarchar(50),      -- 退回项目名称
    cDoctCode nvarchar(10),      -- 标记医生代码
    cDoctName nvarchar(50),      -- 标记医生姓名
    cAuditDoctCode nvarchar(10), -- 接收医生代码
    cAuditDoctName nvarchar(50), -- 接收医生姓名
    cReturnReason nvarchar(300), -- 退回原因
    cReturnType nvarchar(50),    -- 退回类型
    cReturnStatus nvarchar(20),  -- 退回状态
    cReturnTimes int,            -- 退回次数
    cReturnTime datetime,        -- 退回时间
    cReturnDoctCode nvarchar(50),-- 退回科室代码
    cReturnDoctName nvarchar(50),-- 退回科室名称
    cModifyRemark nvarchar(300), -- 修改备注
    cModifyDoctCode nvarchar(10),-- 修改医生代码
    cModifyDoctName nvarchar(50),-- 修改医生姓名
    cModifyTime datetime,        -- 修改时间
    fModifyUsedHour int          -- 修改耗时
);

-- 附件表：退回相关文件（如需要）
CREATE TABLE T_Check_Result_Return_File (
    id nvarchar(50) PRIMARY KEY,
    cReturnId nvarchar(50),      -- 关联主表ID
    cFileName nvarchar(100),     -- 文件名
    pFile image                  -- 文件内容
);
```

## 📊 响应格式

### 成功响应
```json
{
  "code": 0,
  "msg": "分科退回记录保存成功",
  "data": {
    "returnId": "RT_5000003_20250726201917",
    "peNo": "5000003",
    "shopCode": "09",
    "processTime": "2025-07-26 20:19:17"
  }
}
```

### 权限限制响应
```json
{
  "code": 0,
  "msg": "分科退回记录已接收处理（门店09权限限制，已记录到日志）",
  "data": {
    "returnId": "RT_5000003_20250726201917", 
    "peNo": "5000003",
    "shopCode": "09",
    "processTime": "2025-07-26 20:19:17",
    "note": "由于门店09数据库权限限制，数据已记录到日志"
  }
}
```

### 批量处理响应
```json
{
  "code": 0,
  "msg": "批量处理完成: 成功2条, 失败0条",
  "data": {
    "total": 2,
    "success": 2,
    "error": 0,
    "results": [
      { /* 第一条记录结果 */ },
      { /* 第二条记录结果 */ }
    ]
  }
}
```

## 🔍 处理流程

1. **接收请求**: 接收天健云POST请求到 `/dx/inter/returnDept`
2. **提取门店编码**: 从请求数据中提取 `shopCode`
3. **获取数据库配置**: 根据门店编码查找对应的数据库连接配置
4. **连接门店数据库**: 使用门店特定的数据库连接字符串
5. **数据验证**: 验证必要字段（体检号、退回科室、门店编码等）
6. **数据插入**: 将分科退回记录插入到门店数据库
7. **权限处理**: 如遇权限问题，记录到日志并返回成功状态
8. **响应结果**: 返回处理结果给天健云

## 📝 日志示例

```
[DB] 成功保存分科退回记录到门店09数据库: 体检号=5000003, 记录ID=RT_5000003_20250726201917
     退回科室: 内科 (DEPT001)
     退回原因: 检查结果需要重新确认
     接收医生: 李医生 (DOC002)
```

## ⚠️ 注意事项

1. **门店编码必须**: `shopCode` 字段为必填项，系统会据此选择数据库
2. **配置完整性**: 确保每个门店编码都有对应的数据库配置
3. **权限处理**: 系统会优雅处理数据库权限问题，不会影响天健云的数据传输
4. **表结构一致**: 各门店数据库的表结构应保持一致

## 🔧 测试验证

### 直接调用测试
```bash
python interface_15_returnDept_multi_shop.py
```

### HTTP接口测试  
```bash
python test_multi_shop_interface_15.py
```

## 🎯 部署状态

- ✅ **多门店支持**: 已实现并测试通过
- ✅ **数据库路由**: 根据门店编码自动选择数据库
- ✅ **权限容错**: 优雅处理数据库权限问题
- ✅ **批量处理**: 支持单条和批量分科退回
- ✅ **完整日志**: 详细记录所有处理过程

天健云15号接口现已完全支持多门店数据库架构，可以根据报文中的门店编码自动路由到对应的数据库进行数据存储！