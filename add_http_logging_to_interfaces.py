#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量为04-06号接口添加HTTP报文日志功能
"""

import os
import re
from typing import List, Tuple


def add_http_logger_import(file_path: str, interface_num: str) -> bool:
    """为接口文件添加HTTP日志记录器导入"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经导入
        if 'from http_message_logger import get_http_logger' in content:
            print(f"  ✓ {file_path} 已经导入HTTP日志记录器")
            return True
        
        # 查找导入部分的结束位置
        import_pattern = r'(from [^\n]+ import [^\n]+\n)(?=\n|class|def|\w)'
        matches = list(re.finditer(import_pattern, content))
        
        if matches:
            # 在最后一个import语句后添加
            last_import = matches[-1]
            insert_pos = last_import.end()
            new_content = (
                content[:insert_pos] + 
                'from http_message_logger import get_http_logger\n' +
                content[insert_pos:]
            )
        else:
            # 如果没有找到import语句，在文件开头添加
            new_content = 'from http_message_logger import get_http_logger\n' + content
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"  ✓ {file_path} 添加HTTP日志记录器导入成功")
        return True
        
    except Exception as e:
        print(f"  ✗ {file_path} 添加导入失败: {e}")
        return False


def add_http_logger_init(file_path: str, interface_num: str) -> bool:
    """为接口类的__init__方法添加HTTP日志记录器初始化"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经初始化
        if f'self.http_logger = get_http_logger("{interface_num}")' in content:
            print(f"  ✓ {file_path} 已经初始化HTTP日志记录器")
            return True
        
        # 查找__init__方法中合适的位置添加初始化代码
        # 通常在设置api_config之后，创建数据库服务之前
        patterns = [
            r'(self\.api_config = \{[^}]+\}\s*\n)',
            r'(self\.api_config = api_config\s*\n)',
            r'(\s+# 创建数据库服务)',
            r'(\s+self\.db_service = )',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content, re.MULTILINE | re.DOTALL)
            if match:
                insert_pos = match.end()
                new_content = (
                    content[:insert_pos] + 
                    f'\n        # 创建HTTP报文日志记录器\n' +
                    f'        self.http_logger = get_http_logger("{interface_num}")\n' +
                    content[insert_pos:]
                )
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print(f"  ✓ {file_path} 添加HTTP日志记录器初始化成功")
                return True
        
        print(f"  ⚠ {file_path} 未找到合适的位置添加初始化代码")
        return False
        
    except Exception as e:
        print(f"  ✗ {file_path} 添加初始化失败: {e}")
        return False


def add_http_logging_to_send_request(file_path: str, interface_num: str) -> bool:
    """为_send_request方法添加HTTP日志记录功能"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经添加了日志记录
        if 'self.http_logger.log_request' in content:
            print(f"  ✓ {file_path} 已经添加HTTP请求日志记录")
            return True
        
        # 查找并替换请求部分
        request_pattern = r'(# 打印完整的HTTP请求报文到控制台\s*\n\s*print\("=" \* 80\)\s*\n\s*print\(f"【\d+号接口】HTTP请求报文"\)\s*\n\s*print\("=" \* 80\)\s*\n\s*print\(f"请求URL: \{url\}"\)\s*\n\s*print\(f"请求方法: POST"\)\s*\n\s*print\("请求头:"\)\s*\n\s*for key, value in headers\.items\(\):\s*\n\s*print\(f"  \{key\}: \{value\}"\)\s*\n\s*print\("请求体:"\)\s*\n\s*print\(json\.dumps\(request_data, ensure_ascii=False, indent=2\)\)\s*\n\s*print\("=" \* 80\))'
        
        match = re.search(request_pattern, content, re.MULTILINE | re.DOTALL)
        if match:
            # 添加请求ID生成和日志记录
            replacement = match.group(1) + f'''
        
        # 生成请求ID用于日志关联
        request_id = headers.get('x-request-id', str(uuid.uuid4()))
        
        # 记录HTTP请求报文到日志文件
        self.http_logger.log_request(
            url=url,
            method="POST",
            headers=headers,
            request_data=request_data,
            request_id=request_id
        )'''
            
            new_content = content.replace(match.group(1), replacement)
            
            # 查找并替换响应部分
            response_pattern = r'(# 打印完整的HTTP响应报文到控制台\s*\n\s*print\(f"【\d+号接口】HTTP响应报文"\)\s*\n\s*print\("=" \* 80\)\s*\n\s*print\(f"响应状态: HTTP \{response\.status_code\}"\)\s*\n\s*print\("响应头:"\)\s*\n\s*for key, value in response\.headers\.items\(\):\s*\n\s*print\(f"  \{key\}: \{value\}"\)\s*\n\s*print\("响应体:"\)\s*\n\s*# 尝试解析响应内容\s*\n\s*try:\s*\n\s*response_json = response\.json\(\)\s*\n\s*print\(json\.dumps\(response_json, ensure_ascii=False, indent=2\)\)\s*\n\s*except:\s*\n\s*print\(response\.text\)\s*\n\s*print\("=" \* 80\))'
            
            response_match = re.search(response_pattern, new_content, re.MULTILINE | re.DOTALL)
            if response_match:
                response_replacement = response_match.group(1).replace(
                    'try:\n                response_json = response.json()\n                print(json.dumps(response_json, ensure_ascii=False, indent=2))\n            except:\n                print(response.text)',
                    '''response_data = None
            try:
                response_data = response.json()
                print(json.dumps(response_data, ensure_ascii=False, indent=2))
            except:
                response_data = response.text
                print(response_data)'''
                ) + '''
            
            # 记录HTTP响应报文到日志文件
            self.http_logger.log_response(
                status_code=response.status_code,
                headers=dict(response.headers),
                response_data=response_data,
                request_id=request_id
            )'''
                
                new_content = new_content.replace(response_match.group(1), response_replacement)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"  ✓ {file_path} 添加HTTP日志记录成功")
            return True
        
        print(f"  ⚠ {file_path} 未找到合适的位置添加日志记录")
        return False
        
    except Exception as e:
        print(f"  ✗ {file_path} 添加日志记录失败: {e}")
        return False


def process_interface_file(file_path: str, interface_num: str) -> bool:
    """处理单个接口文件"""
    print(f"\n处理 {file_path} ({interface_num}号接口):")
    
    success = True
    success &= add_http_logger_import(file_path, interface_num)
    success &= add_http_logger_init(file_path, interface_num)
    success &= add_http_logging_to_send_request(file_path, interface_num)
    
    return success


def main():
    """主函数"""
    print("开始为04-06号接口添加HTTP报文日志功能...")
    
    interfaces = [
        ("interface_04_syncUser.py", "04"),
        ("interface_05_syncDept.py", "05"),
        ("interface_06_syncDict.py", "06"),
    ]
    
    success_count = 0
    for file_path, interface_num in interfaces:
        if os.path.exists(file_path):
            if process_interface_file(file_path, interface_num):
                success_count += 1
        else:
            print(f"  ✗ 文件不存在: {file_path}")
    
    print(f"\n处理完成: {success_count}/{len(interfaces)} 个接口成功添加HTTP日志功能")


if __name__ == '__main__':
    main()
