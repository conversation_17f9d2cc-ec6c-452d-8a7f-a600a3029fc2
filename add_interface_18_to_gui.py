#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
添加18号接口服务端实现 - 查询医生信息接口
"""

# 在gui_main.py中添加以下内容到TianjianInterfaceService类中

def setup_interface_18(self):
    """设置18号接口 - 查询医生信息接口"""
    
    @self.app.route('/data-external-gw/getDoctor', methods=['POST'])
    def get_doctor_info():
        """18号接口 - 查询医生信息接口"""
        try:
            # 获取请求数据
            data = request.get_json()
            if not data:
                self.signal_emitter.log_signal.emit("错误", "18号接口: 请求数据不能为空")
                return jsonify({
                    'code': 1001,
                    'msg': '请求数据不能为空',
                    'data': []
                })

            doctor_id = data.get('id', '')
            hospital_code = data.get('hospitalCode', '')

            self.signal_emitter.log_signal.emit("接口调用", f"18号接口: 收到查询医生信息请求")
            self.signal_emitter.log_signal.emit("接口调用", f"  医生ID: {doctor_id if doctor_id else '全部'}")
            self.signal_emitter.log_signal.emit("接口调用", f"  医院编码: {hospital_code if hospital_code else '默认'}")

            # 获取API配置
            from config import Config
            api_config = Config.get_tianjian_api_config()

            # 创建18号接口实例
            from interface_18_getDoctorInfo import TianjianInterface18
            interface = TianjianInterface18(api_config)

            # 调用接口查询医生信息
            result = interface.query_doctor_info(
                doctor_id=doctor_id if doctor_id else None,
                hospital_code=hospital_code if hospital_code else None
            )

            self.signal_emitter.log_signal.emit("接口调用", f"18号接口: 返回数据数量 {len(result.get('data', []))}")

            return jsonify(result)

        except Exception as e:
            error_msg = f"18号接口异常: {str(e)}"
            self.signal_emitter.log_signal.emit("错误", error_msg)
            return jsonify({
                'code': 1003,
                'msg': f'查询失败: {str(e)}',
                'data': []
            })