{"org_info": {"org_code": "08", "org_name": "仁泰邯郸东区体检中心", "shop_code": "08"}, "db_info": {"server_name": "WIN-H0VF6A9V3UN", "database_name": "ExamDB", "current_datetime": "2025-07-25 15:08:06.833000", "sql_version": "Microsoft SQL Server 2008 R2 (RTM) - 10.50.1600.1 (X64) \n\tApr  2 2010 15:48:46 \n\tCopyright (c) Microsoft Corporation\n\tEnterprise Edition (64-bit) on Windows NT 6.2 <X64> (Build 9200: )\n"}, "field_status": {"AIDiagnosisStatus": true, "cCanDiagDate": true}, "statistics": {"total_records": 939802, "has_cCanDiagDate_count": 400, "today_cCanDiagDate_count": 13, "ai_status_1_count": 0, "today_and_ai_status_1_count": 0}, "sample_data": [{"cClientCode": "0825749564", "cName": "安宇宣", "cStatus": "1", "dOperdate": "2025-07-24 10:46:15", "cCanDiagDate": "2025-07-25 08:54:46.890000", "AIDiagnosisStatus": 0, "ai_status_desc": "待诊断"}, {"cClientCode": "0825749559", "cName": "刘倩倩", "cStatus": "1", "dOperdate": "2025-07-24 10:29:24", "cCanDiagDate": "2025-07-25 08:54:36.717000", "AIDiagnosisStatus": 0, "ai_status_desc": "待诊断"}, {"cClientCode": "0825749505", "cName": "王新伟", "cStatus": "1", "dOperdate": "2025-07-24 08:48:47", "cCanDiagDate": "2025-07-25 08:54:03.580000", "AIDiagnosisStatus": 0, "ai_status_desc": "待诊断"}], "summary": ["机构编码为 08（非09机构）", "所有必要字段都存在", "今天没有待传输的AI诊断记录"], "check_time": "2025-07-25T15:08:24.843601"}