#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将接口文档复制到临时目录并分析
"""

import shutil
import os

def copy_and_analyze():
    try:
        # 源文件路径
        src_path = r"D:\python\福能AI对接\接口文档.html"
        # 目标路径（使用英文路径）
        dst_path = r"D:\temp_api_doc.html"
        
        # 复制文件
        shutil.copy2(src_path, dst_path)
        print(f"文件已复制到: {dst_path}")
        
        # 读取并分析文件
        with open(dst_path, "r", encoding="utf-8") as f:
            # 读取前1000行查找接口信息
            lines = []
            for i, line in enumerate(f):
                if i >= 1000:
                    break
                lines.append(line)
        
        content = ''.join(lines)
        
        # 查找18号接口信息
        import re
        interfaces = re.findall(r'"name"\s*:\s*"([^"]*18[^"]*|[^"]*医生信息[^"]*)"', content)
        if interfaces:
            print("找到相关接口:")
            for interface in interfaces:
                print(f"  - {interface}")
        
        # 查找URL信息
        urls = re.findall(r'"url"\s*:\s*"([^"]*/getDoctor[^"]*)"', content)
        if urls:
            print("找到相关URL:")
            for url in urls:
                print(f"  - {url}")
                
    except Exception as e:
        print(f"处理文件时出错: {e}")
    finally:
        # 清理临时文件
        if os.path.exists(dst_path):
            try:
                os.remove(dst_path)
                print("临时文件已清理")
            except:
                pass

if __name__ == "__main__":
    copy_and_analyze()