#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接分析HTML文件内容，查找接口信息
"""

def analyze_html_content():
    try:
        # 读取HTML文件
        with open(r"D:\python\福能AI对接\接口文档.html", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 查找接口相关的JSON数据
        import re
        
        # 查找apipostData变量
        apipost_match = re.search(r'apipostData\s*=\s*({.*?});', content, re.DOTALL)
        if apipost_match:
            json_data = apipost_match.group(1)
            print("找到了apipostData数据")
            
            # 查找所有接口的URL和方法
            # 先简化JSON数据，只保留关键字段
            interfaces = re.findall(r'\{[^{}]*"type"\s*:\s*"api"[^{}]*"name"\s*:\s*"[^"]*"[^{}]*"url"\s*:\s*"[^"]*"[^{}]*"method"\s*:\s*"[^"]*"[^{}]*\}', json_data)
            
            print(f"找到 {len(interfaces)} 个接口定义")
            
            # 查找18号接口
            for interface in interfaces:
                name_match = re.search(r'"name"\s*:\s*"([^"]*)"', interface)
                if name_match:
                    name = name_match.group(1)
                    if "18" in name or "医生信息" in name:
                        print(f"\n找到接口: {name}")
                        
                        url_match = re.search(r'"url"\s*:\s*"([^"]*)"', interface)
                        method_match = re.search(r'"method"\s*:\s*"([^"]*)"', interface)
                        
                        if url_match:
                            print(f"  URL: {url_match.group(1)}")
                        if method_match:
                            print(f"  方法: {method_match.group(1)}")
        else:
            print("未找到apipostData数据")
            
    except Exception as e:
        print(f"处理文件时出错: {e}")

if __name__ == "__main__":
    analyze_html_content()