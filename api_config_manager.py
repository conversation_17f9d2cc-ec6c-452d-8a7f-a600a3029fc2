#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云API配置管理器
统一管理API地址，消除硬编码
"""

import os
import yaml
from typing import Dict, Optional


class APIConfigManager:
    """天健云API配置管理器"""
    
    # 默认配置 - 唯一的硬编码位置
    DEFAULT_CONFIG = {
        'base_url': 'http://203.83.237.114:9300',  # 生产环境
        'test_url': 'http://127.0.0.1:9300',       # 测试环境
        'backup_url': 'http://backup.tianjian.com:9300'  # 备用环境
    }
    
    def __init__(self):
        self._config = None
        self._load_config()
    
    def _load_config(self):
        """加载配置，按优先级顺序"""
        self._config = self.DEFAULT_CONFIG.copy()
        
        # 1. 从环境变量加载
        env_url = os.environ.get('TIANJIAN_BASE_URL')
        if env_url:
            self._config['base_url'] = env_url
            print(f"[CONFIG] 使用环境变量API地址: {env_url}")
        
        # 2. 从config.yaml加载
        try:
            config_file = 'config.yaml'
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    yaml_config = yaml.safe_load(f)
                    if yaml_config and 'tianjian_api' in yaml_config:
                        api_config = yaml_config['tianjian_api']
                        if 'base_url' in api_config:
                            self._config['base_url'] = api_config['base_url']
                            print(f"[CONFIG] 使用config.yaml API地址: {api_config['base_url']}")
        except Exception as e:
            print(f"[CONFIG] 读取config.yaml失败: {e}")
        
        # 3. 从.env文件加载
        try:
            env_file = '.env'
            if os.path.exists(env_file):
                with open(env_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line.startswith('TIANJIAN_BASE_URL='):
                            url = line.split('=', 1)[1].strip()
                            self._config['base_url'] = url
                            print(f"[CONFIG] 使用.env API地址: {url}")
                            break
        except Exception as e:
            print(f"[CONFIG] 读取.env失败: {e}")
    
    def get_base_url(self, environment: str = 'production') -> str:
        """获取API基础URL"""
        if environment == 'test':
            return self._config.get('test_url', self._config['base_url'])
        elif environment == 'backup':
            return self._config.get('backup_url', self._config['base_url'])
        else:
            return self._config['base_url']
    
    def get_full_url(self, endpoint: str, environment: str = 'production') -> str:
        """获取完整的API URL"""
        base_url = self.get_base_url(environment)
        return f"{base_url.rstrip('/')}/{endpoint.lstrip('/')}"
    
    def get_config(self) -> Dict[str, str]:
        """获取完整配置"""
        return self._config.copy()
    
    def set_base_url(self, url: str):
        """设置API基础URL"""
        self._config['base_url'] = url
        print(f"[CONFIG] 更新API地址: {url}")
    
    def is_test_environment(self) -> bool:
        """判断是否为测试环境"""
        base_url = self._config['base_url']
        return 'localhost' in base_url or '127.0.0.1' in base_url
    
    def validate_url(self, url: str = None) -> bool:
        """验证URL格式"""
        test_url = url or self._config['base_url']
        return (test_url.startswith('http://') or test_url.startswith('https://')) and ':' in test_url


# 全局实例
_api_config_manager = None


def get_api_config_manager() -> APIConfigManager:
    """获取API配置管理器实例（单例模式）"""
    global _api_config_manager
    if _api_config_manager is None:
        _api_config_manager = APIConfigManager()
    return _api_config_manager


def get_tianjian_base_url(environment: str = 'production') -> str:
    """获取天健云API基础URL的便捷函数"""
    return get_api_config_manager().get_base_url(environment)


def get_tianjian_full_url(endpoint: str, environment: str = 'production') -> str:
    """获取天健云完整API URL的便捷函数"""
    return get_api_config_manager().get_full_url(endpoint, environment)


# 兼容性函数
def get_api_base_url() -> str:
    """向后兼容的函数"""
    return get_tianjian_base_url()


if __name__ == '__main__':
    # 测试配置管理器
    print("=== 天健云API配置管理器测试 ===")
    
    manager = get_api_config_manager()
    
    print(f"生产环境URL: {manager.get_base_url('production')}")
    print(f"测试环境URL: {manager.get_base_url('test')}")
    print(f"备用环境URL: {manager.get_base_url('backup')}")
    
    print(f"\n完整API URL示例:")
    print(f"发送体检信息: {manager.get_full_url('/dx/inter/sendPeInfo')}")
    print(f"同步申请项目: {manager.get_full_url('/dx/inter/syncApplyItem')}")
    
    print(f"\n当前配置: {manager.get_config()}")
    print(f"是否测试环境: {manager.is_test_environment()}")
    print(f"URL格式验证: {manager.validate_url()}")