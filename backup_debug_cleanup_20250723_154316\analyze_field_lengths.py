#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析07号接口字段长度限制
帮助识别可能导致字符串截断的字段
"""

def analyze_field_limits():
    """分析各表字段的长度限制"""
    
    print("07号接口数据库字段长度限制分析")
    print("=" * 60)
    
    # T_Diag_result表字段长度限制（根据常见数据库设计推测）
    diag_result_limits = {
        "cClientCode": 10,      # 体检号
        "cDiag": 255,           # 总检结论
        "cDiagDesc": 500,       # 总检结论描述
        "cDoctCode": 9,         # 总检医生编码
        "cDoctName": 12,        # 总检医生姓名
        "cOperCode": 9,         # 初审医生编码
        "cOpername": 12,        # 初审医生姓名
        "cShopCode": 2          # 机构编码
    }
    
    # T_Check_Result_Illness表字段长度限制
    illness_limits = {
        "cClientCode": 10,      # 体检号
        "cDeptcode": 6,         # 科室代码
        "cMainName": 40,        # 申请项目名称
        "cIllnessCode": 6,      # 结论词代码
        "cIllnessName": 100,    # 结论词名称
        "cIllExplain": 500,     # 医学解释（可能是TEXT类型，限制更大）
        "cReason": 200,         # 检查结果汇总
        "cAdvice": 500,         # 建议（可能是TEXT类型，限制更大）
        "cGrade": 1,            # 重要性等级
        "cDoctCode": 9,         # 医生代码
        "cDoctName": 12,        # 医生姓名
        "nPrintIndex": 4        # 显示序号（数字类型）
    }
    
    print("T_Diag_result表字段长度限制:")
    print("-" * 40)
    for field, limit in diag_result_limits.items():
        print(f"  {field:<15} : 最大 {limit:3d} 字符")
    
    print("\nT_Check_Result_Illness表字段长度限制:")
    print("-" * 40)
    for field, limit in illness_limits.items():
        print(f"  {field:<15} : 最大 {limit:3d} 字符")
    
    return diag_result_limits, illness_limits

def check_sample_data(diag_limits, illness_limits):
    """检查示例数据是否超出长度限制"""
    
    print("\n" + "=" * 60)
    print("示例数据长度检查")
    print("=" * 60)
    
    # 模拟可能的数据
    sample_data = {
        "peNo": "5000006",
        "conclusionName": "血压偏高需要注意心血管健康状况并采取相应措施",
        "suggest": "建议低盐饮食，适量运动，定期监测血压变化，必要时就医咨询专业医生意见，同时要保持良好的生活习惯",
        "explain": "收缩压超过正常范围，可能存在高血压风险，需要引起重视并采取相应的预防措施，这种情况需要长期管理",
        "checkResult": "收缩压150mmHg，舒张压95mmHg，超出正常范围，心率85次/分，血压波动较大",
        "conclusionCode": "BP001",
        "parentCode": "CARDIO",
        "deptId": "DEPT01",
        "mainDoctorCode": "DOC002",
        "mainDoctorName": "李主任",
        "firstDoctorCode": "DOC001", 
        "firstDoctorName": "张医生"
    }
    
    # 检查T_Diag_result相关字段
    print("T_Diag_result字段长度检查:")
    print("-" * 40)
    
    diag_checks = [
        ("cClientCode", sample_data["peNo"], diag_limits["cClientCode"]),
        ("cDiag", sample_data["conclusionName"], diag_limits["cDiag"]),
        ("cDiagDesc", sample_data["explain"] + "\n建议：" + sample_data["suggest"], diag_limits["cDiagDesc"]),
        ("cDoctCode", sample_data["mainDoctorCode"], diag_limits["cDoctCode"]),
        ("cDoctName", sample_data["mainDoctorName"], diag_limits["cDoctName"]),
        ("cOperCode", sample_data["firstDoctorCode"], diag_limits["cOperCode"]),
        ("cOpername", sample_data["firstDoctorName"], diag_limits["cOpername"])
    ]
    
    for field, value, limit in diag_checks:
        length = len(str(value))
        status = "✅" if length <= limit else "❌ 超出限制!"
        print(f"  {field:<15} : {length:3d}/{limit:3d} 字符 {status}")
        if length > limit:
            print(f"    内容: {str(value)[:50]}...")
    
    # 检查T_Check_Result_Illness相关字段
    print("\nT_Check_Result_Illness字段长度检查:")
    print("-" * 40)
    
    illness_checks = [
        ("cClientCode", sample_data["peNo"], illness_limits["cClientCode"]),
        ("cDeptcode", sample_data["parentCode"], illness_limits["cDeptcode"]),
        ("cMainName", "总检结论", illness_limits["cMainName"]),
        ("cIllnessCode", sample_data["conclusionCode"], illness_limits["cIllnessCode"]),
        ("cIllnessName", sample_data["conclusionName"], illness_limits["cIllnessName"]),
        ("cIllExplain", sample_data["explain"], illness_limits["cIllExplain"]),
        ("cReason", sample_data["checkResult"], illness_limits["cReason"]),
        ("cAdvice", sample_data["suggest"], illness_limits["cAdvice"]),
        ("cDoctCode", sample_data["mainDoctorCode"], illness_limits["cDoctCode"]),
        ("cDoctName", sample_data["mainDoctorName"], illness_limits["cDoctName"])
    ]
    
    for field, value, limit in illness_checks:
        length = len(str(value))
        status = "✅" if length <= limit else "❌ 超出限制!"
        print(f"  {field:<15} : {length:3d}/{limit:3d} 字符 {status}")
        if length > limit:
            print(f"    内容: {str(value)[:50]}...")

def suggest_fixes():
    """建议修复方案"""
    
    print("\n" + "=" * 60)
    print("字符串截断问题修复建议")
    print("=" * 60)
    
    suggestions = [
        "1. 在代码中增加字段长度检查和截断处理",
        "2. 对超长文本进行智能截断，保留重要信息",
        "3. 在插入前打印详细的字段长度信息",
        "4. 考虑将长文本存储到专门的扩展表中",
        "5. 在数据库设计时适当增加字段长度限制"
    ]
    
    for suggestion in suggestions:
        print(f"  {suggestion}")
    
    print("\n推荐的字段长度限制调整:")
    print("-" * 40)
    
    recommended_limits = {
        "cDiag": "255 → 500 (总检结论)",
        "cDiagDesc": "500 → 1000 (总检结论描述)", 
        "cIllnessName": "100 → 200 (结论词名称)",
        "cIllExplain": "500 → 1000 (医学解释)",
        "cAdvice": "500 → 1000 (建议)",
        "cReason": "200 → 500 (检查结果汇总)"
    }
    
    for field, change in recommended_limits.items():
        print(f"  {field:<15} : {change}")

if __name__ == "__main__":
    # 分析字段限制
    diag_limits, illness_limits = analyze_field_limits()
    
    # 检查示例数据
    check_sample_data(diag_limits, illness_limits)
    
    # 建议修复方案
    suggest_fixes()
    
    print("\n" + "=" * 60)
    print("分析完成！请根据上述信息调整字段长度限制或数据处理逻辑。")
    print("=" * 60)
