#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API服务状态监控和重试机制
"""

import requests
import time
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
import threading
import queue


class APIServiceMonitor:
    """API服务状态监控器"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.status_history: List[Dict[str, Any]] = []
        self.is_monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        
    def check_service_status(self) -> Dict[str, Any]:
        """检查服务状态"""
        status = {
            'timestamp': datetime.now(),
            'service_available': False,
            'response_time_ms': None,
            'status_code': None,
            'error': None,
            'nginx_responding': False,
            'backend_responding': False
        }
        
        try:
            start_time = time.time()
            response = requests.get(
                f"{self.base_url}/", 
                timeout=10,
                headers={'User-Agent': 'TianjianHealthSync-Monitor/1.0'}
            )
            response_time = (time.time() - start_time) * 1000
            
            status.update({
                'response_time_ms': response_time,
                'status_code': response.status_code,
                'nginx_responding': True
            })
            
            # 检查是否是nginx错误页面
            if response.status_code == 502:
                status['error'] = 'Backend service unavailable (502 Bad Gateway)'
                status['backend_responding'] = False
            elif response.status_code == 200:
                status['service_available'] = True
                status['backend_responding'] = True
            else:
                status['error'] = f'HTTP {response.status_code}'
                
        except requests.exceptions.Timeout:
            status['error'] = 'Request timeout'
        except requests.exceptions.ConnectionError:
            status['error'] = 'Connection error'
        except Exception as e:
            status['error'] = f'Unknown error: {str(e)}'
        
        # 保存状态历史
        self.status_history.append(status)
        
        # 只保留最近100条记录
        if len(self.status_history) > 100:
            self.status_history = self.status_history[-100:]
        
        return status
    
    def get_service_health_summary(self) -> Dict[str, Any]:
        """获取服务健康度摘要"""
        if not self.status_history:
            return {'error': 'No status history available'}
        
        recent_checks = self.status_history[-10:]  # 最近10次检查
        
        available_count = sum(1 for check in recent_checks if check['service_available'])
        total_count = len(recent_checks)
        
        avg_response_time = None
        if any(check['response_time_ms'] for check in recent_checks):
            response_times = [check['response_time_ms'] for check in recent_checks if check['response_time_ms']]
            avg_response_time = sum(response_times) / len(response_times)
        
        return {
            'availability_rate': (available_count / total_count) * 100,
            'total_checks': total_count,
            'available_checks': available_count,
            'avg_response_time_ms': avg_response_time,
            'last_check': recent_checks[-1],
            'trend': 'improving' if recent_checks[-1]['service_available'] else 'degrading'
        }
    
    def start_monitoring(self, check_interval: int = 30):
        """开始监控服务状态"""
        if self.is_monitoring:
            print("[WARN] 监控已在运行")
            return
        
        self.is_monitoring = True
        
        def monitor_loop():
            print(f"[START] 开始监控API服务状态 (间隔: {check_interval}秒)")
            
            while self.is_monitoring:
                status = self.check_service_status()
                
                if status['service_available']:
                    print(f"[OK] 服务正常 - 响应时间: {status['response_time_ms']:.0f}ms")
                else:
                    print(f"[FAIL] 服务异常 - {status['error']}")
                
                time.sleep(check_interval)
        
        self.monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        if self.is_monitoring:
            self.is_monitoring = False
            print("[STOP] 已停止监控")


class SmartRetryHandler:
    """智能重试处理器"""
    
    def __init__(self, monitor: APIServiceMonitor):
        self.monitor = monitor
        
    def execute_with_smart_retry(
        self, 
        operation: Callable,
        max_retries: int = 3,
        base_delay: float = 5.0,
        max_delay: float = 60.0,
        service_check_interval: int = 30
    ) -> Any:
        """
        使用智能重试策略执行操作
        
        Args:
            operation: 要执行的操作函数
            max_retries: 最大重试次数
            base_delay: 基础延迟时间(秒)
            max_delay: 最大延迟时间(秒)
            service_check_interval: 服务状态检查间隔(秒)
        
        Returns:
            操作结果
        """
        
        for attempt in range(max_retries + 1):
            try:
                # 检查服务状态
                status = self.monitor.check_service_status()
                
                if not status['service_available']:
                    if attempt == 0:
                        print(f"[WARN] 检测到服务不可用: {status['error']}")
                        print(f"[RETRY] 将等待服务恢复后重试...")
                    
                    if attempt < max_retries:
                        # 等待服务恢复
                        wait_time = min(base_delay * (2 ** attempt), max_delay)
                        print(f"[WAIT] 等待 {wait_time:.1f} 秒后重试 (第 {attempt + 1}/{max_retries + 1} 次)")
                        
                        # 在等待期间定期检查服务状态
                        waited = 0
                        while waited < wait_time:
                            time.sleep(min(service_check_interval, wait_time - waited))
                            waited += min(service_check_interval, wait_time - waited)
                            
                            # 检查服务是否恢复
                            check_status = self.monitor.check_service_status()
                            if check_status['service_available']:
                                print(f"[OK] 检测到服务已恢复，提前结束等待")
                                break
                        
                        continue
                    else:
                        print(f"[FAIL] 达到最大重试次数，服务仍不可用")
                        raise Exception(f"Service unavailable after {max_retries} retries: {status['error']}")
                
                # 服务可用，执行操作
                print(f"[EXEC] 服务可用，执行操作...")
                result = operation()
                print(f"[OK] 操作执行成功")
                return result
                
            except Exception as e:
                if attempt < max_retries:
                    wait_time = min(base_delay * (2 ** attempt), max_delay)
                    print(f"[ERROR] 操作失败: {e}")
                    print(f"[RETRY] {wait_time:.1f} 秒后重试 (第 {attempt + 1}/{max_retries + 1} 次)")
                    time.sleep(wait_time)
                else:
                    print(f"[FAIL] 操作最终失败: {e}")
                    raise


def demonstrate_smart_retry():
    """演示智能重试机制"""
    print("智能重试机制演示")
    print("="*50)
    
    # 创建监控器
    monitor = APIServiceMonitor("http://203.83.237.114:9300")
    retry_handler = SmartRetryHandler(monitor)
    
    # 定义一个模拟的API操作
    def mock_api_operation():
        """模拟API操作"""
        status = monitor.check_service_status()
        if not status['service_available']:
            raise Exception(f"API operation failed: {status['error']}")
        return {"result": "success", "timestamp": datetime.now()}
    
    try:
        # 使用智能重试执行操作
        result = retry_handler.execute_with_smart_retry(
            operation=mock_api_operation,
            max_retries=3,
            base_delay=5.0,
            service_check_interval=10
        )
        
        print(f"\n[SUCCESS] 操作成功完成:")
        print(f"  结果: {result}")
        
    except Exception as e:
        print(f"\n[FINAL_FAILURE] 操作最终失败: {e}")
    
    # 显示服务健康度摘要
    health = monitor.get_service_health_summary()
    print(f"\n[HEALTH] 服务健康度摘要:")
    print(f"  可用率: {health.get('availability_rate', 0):.1f}%")
    print(f"  检查次数: {health.get('total_checks', 0)}")
    print(f"  平均响应时间: {health.get('avg_response_time_ms', 0):.1f}ms")


def main():
    """主函数"""
    print("API服务状态监控和重试机制")
    print("="*50)
    
    # 先进行一次服务状态检查
    monitor = APIServiceMonitor("http://203.83.237.114:9300")
    status = monitor.check_service_status()
    
    print(f"\n[CHECK] 当前服务状态:")
    print(f"  时间: {status['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"  服务可用: {'是' if status['service_available'] else '否'}")
    print(f"  响应时间: {status['response_time_ms']:.1f}ms" if status['response_time_ms'] else "N/A")
    print(f"  状态码: {status['status_code']}")
    print(f"  错误信息: {status['error']}" if status['error'] else "无")
    
    if not status['service_available']:
        print(f"\n[RECOMMENDATION] 建议:")
        print(f"  1. 天健云服务器当前不可用 (502 Bad Gateway)")
        print(f"  2. 这是服务器端问题，不是代码问题")
        print(f"  3. 可以使用智能重试机制等待服务恢复")
        print(f"  4. 联系天健云技术支持了解服务状态")
        
        # 询问是否演示智能重试
        try:
            choice = input(f"\n是否演示智能重试机制? (y/N): ").strip().lower()
            if choice in ['y', 'yes']:
                demonstrate_smart_retry()
        except KeyboardInterrupt:
            print(f"\n[STOP] 用户中断")


if __name__ == "__main__":
    main()