#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API连接超时优化工具
解决天健云API连接超时问题
"""

import time
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import json
from datetime import datetime
from typing import Dict, Any, Optional, Tuple


class OptimizedAPIClient:
    """
    优化的API客户端
    包含重试机制、超时控制、连接池优化
    """
    
    def __init__(self, base_url: str, timeout_config: Dict[str, int] = None):
        """
        初始化优化的API客户端
        
        Args:
            base_url: API基础URL
            timeout_config: 超时配置
        """
        self.base_url = base_url.rstrip('/')
        
        # 默认超时配置
        self.timeout_config = timeout_config or {
            'connect_timeout': 10,  # 连接超时：10秒
            'read_timeout': 60,     # 读取超时：60秒 (增加到60秒)
            'total_timeout': 120    # 总超时：120秒
        }
        
        # 创建优化的requests会话
        self.session = self._create_optimized_session()
    
    def _create_optimized_session(self) -> requests.Session:
        """创建优化的请求会话"""
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=3,                    # 总重试次数
            read=2,                     # 读取错误重试次数
            connect=2,                  # 连接错误重试次数
            backoff_factor=1,           # 重试间隔倍数
            status_forcelist=[408, 429, 500, 502, 503, 504],  # 需要重试的状态码
            allowed_methods=["HEAD", "GET", "PUT", "DELETE", "OPTIONS", "TRACE", "POST"]
        )
        
        # 创建HTTP适配器
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=10,        # 连接池大小
            pool_maxsize=20            # 连接池最大连接数
        )
        
        # 挂载适配器
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 设置默认请求头
        session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'TianjianHealthSync/1.0',
            'Connection': 'keep-alive'
        })
        
        return session
    
    def test_connection(self, test_endpoint: str = "/dx/inter/syncDict") -> Tuple[bool, Dict[str, Any]]:
        """
        测试API连接
        
        Args:
            test_endpoint: 测试端点
            
        Returns:
            Tuple[bool, Dict]: (是否成功, 测试结果详情)
        """
        test_result = {
            'endpoint': test_endpoint,
            'start_time': datetime.now(),
            'end_time': None,
            'duration_ms': 0,
            'success': False,
            'status_code': None,
            'error': None,
            'timeout_used': None,
            'retry_count': 0
        }
        
        try:
            url = f"{self.base_url}{test_endpoint}"
            
            print(f"[TEST] 测试连接: {url}")
            print(f"[CONFIG] 超时配置: 连接={self.timeout_config['connect_timeout']}s, "
                  f"读取={self.timeout_config['read_timeout']}s")
            
            # 使用简单的测试数据
            test_data = [{"testField": "testValue"}]
            
            # 添加认证头
            headers = self._build_test_headers()
            
            start_time = time.time()
            
            # 发起请求
            response = self.session.post(
                url,
                json=test_data,
                headers=headers,
                timeout=(
                    self.timeout_config['connect_timeout'],
                    self.timeout_config['read_timeout']
                )
            )
            
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            test_result.update({
                'end_time': datetime.now(),
                'duration_ms': duration_ms,
                'status_code': response.status_code,
                'timeout_used': f"{self.timeout_config['connect_timeout']}/{self.timeout_config['read_timeout']}"
            })
            
            print(f"[OK] 请求完成")
            print(f"[TIME] 响应时间: {duration_ms:.2f}ms")
            print(f"[STATUS] HTTP状态码: {response.status_code}")
            
            # 检查响应
            if response.status_code == 200:
                test_result['success'] = True
                print(f"[OK] 连接测试成功")
                
                # 尝试解析响应
                try:
                    response_data = response.json()
                    print(f"[RESPONSE] 响应数据: {response_data}")
                    test_result['response_data'] = response_data
                except:
                    print(f"[WARN] 响应不是有效JSON格式")
                    test_result['response_text'] = response.text[:200]
            else:
                print(f"[FAIL] HTTP错误状态码: {response.status_code}")
                test_result['error'] = f"HTTP {response.status_code}"
                
        except requests.exceptions.ConnectTimeout:
            error_msg = f"连接超时 (>{self.timeout_config['connect_timeout']}s)"
            print(f"[FAIL] {error_msg}")
            test_result['error'] = error_msg
            
        except requests.exceptions.ReadTimeout:
            error_msg = f"读取超时 (>{self.timeout_config['read_timeout']}s)"
            print(f"[FAIL] {error_msg}")
            test_result['error'] = error_msg
            
        except requests.exceptions.Timeout:
            error_msg = "请求超时"
            print(f"[FAIL] {error_msg}")
            test_result['error'] = error_msg
            
        except requests.exceptions.ConnectionError as e:
            error_msg = f"连接错误: {str(e)}"
            print(f"[FAIL] {error_msg}")
            test_result['error'] = error_msg
            
        except Exception as e:
            error_msg = f"未知错误: {str(e)}"
            print(f"[FAIL] {error_msg}")
            test_result['error'] = error_msg
        
        if not test_result['end_time']:
            test_result['end_time'] = datetime.now()
            
        return test_result['success'], test_result
    
    def _build_test_headers(self) -> Dict[str, str]:
        """构建测试用的请求头"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        
        # 简化的签名（用于测试）
        import hashlib
        test_api_key = "3CNVizIjUq87IrczWqQB8SxjvPmVMTKM"
        sign_string = f"{test_api_key}{timestamp}"
        sign = hashlib.md5(sign_string.encode()).hexdigest()
        
        return {
            'Content-Type': 'application/json',
            'sign': sign,
            'timestamp': timestamp,
            'mic-code': 'MIC1.001E',
            'misc-id': 'MISC1.00001A'
        }
    
    def test_multiple_timeouts(self) -> Dict[str, Any]:
        """
        测试多种超时配置
        
        Returns:
            Dict: 测试结果
        """
        print("="*60)
        print("API超时配置测试")
        print("="*60)
        
        timeout_configs = [
            {'name': '保守配置', 'connect': 5, 'read': 30},
            {'name': '标准配置', 'connect': 10, 'read': 60},
            {'name': '宽松配置', 'connect': 15, 'read': 120},
        ]
        
        results = {}
        
        for config in timeout_configs:
            print(f"\n[TEST] 测试 {config['name']} (连接:{config['connect']}s, 读取:{config['read']}s)")
            
            # 临时修改超时配置
            original_config = self.timeout_config.copy()
            self.timeout_config['connect_timeout'] = config['connect']
            self.timeout_config['read_timeout'] = config['read']
            
            # 执行测试
            success, result = self.test_connection()
            results[config['name']] = result
            
            # 恢复配置
            self.timeout_config = original_config
            
            # 间隔等待
            if config != timeout_configs[-1]:
                print("[WAIT] 等待5秒后进行下一个测试...")
                time.sleep(5)
        
        return results
    
    def diagnose_connection_issues(self) -> Dict[str, Any]:
        """诊断连接问题"""
        print("="*60)
        print("API连接问题诊断")
        print("="*60)
        
        diagnosis = {
            'timestamp': datetime.now(),
            'tests': {},
            'recommendations': []
        }
        
        # 1. 基础连通性测试
        print("\n[STEP 1] 基础连通性测试")
        try:
            import socket
            host = "**************"
            port = 9300
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"[OK] 服务器 {host}:{port} 可达")
                diagnosis['tests']['connectivity'] = True
            else:
                print(f"[FAIL] 服务器 {host}:{port} 不可达")
                diagnosis['tests']['connectivity'] = False
                diagnosis['recommendations'].append("检查网络连接和防火墙设置")
                
        except Exception as e:
            print(f"[FAIL] 连通性测试异常: {e}")
            diagnosis['tests']['connectivity'] = False
        
        # 2. DNS解析测试
        print("\n[STEP 2] DNS解析测试")
        try:
            import socket
            ip = socket.gethostbyname("**************")
            print(f"[OK] DNS解析成功: ************** -> {ip}")
            diagnosis['tests']['dns'] = True
        except Exception as e:
            print(f"[FAIL] DNS解析失败: {e}")
            diagnosis['tests']['dns'] = False
            diagnosis['recommendations'].append("检查DNS设置")
        
        # 3. HTTP基础测试
        print("\n[STEP 3] HTTP基础测试")
        try:
            base_response = requests.get(
                f"{self.base_url}/",
                timeout=(5, 10)
            )
            print(f"[OK] HTTP基础连接成功，状态码: {base_response.status_code}")
            diagnosis['tests']['http_basic'] = True
        except Exception as e:
            print(f"[FAIL] HTTP基础连接失败: {e}")
            diagnosis['tests']['http_basic'] = False
            diagnosis['recommendations'].append("检查HTTP服务可用性")
        
        # 4. API端点测试
        print("\n[STEP 4] API端点测试")
        success, result = self.test_connection()
        diagnosis['tests']['api_endpoint'] = success
        
        if not success:
            if 'timeout' in str(result.get('error', '')).lower():
                diagnosis['recommendations'].extend([
                    "增加读取超时时间",
                    "检查API请求数据格式",
                    "联系API服务提供商检查服务器性能"
                ])
            elif 'connection' in str(result.get('error', '')).lower():
                diagnosis['recommendations'].extend([
                    "检查网络连接稳定性",
                    "尝试使用代理或VPN",
                    "检查本地防火墙设置"
                ])
        
        # 生成总结
        print("\n[SUMMARY] 诊断总结")
        all_tests_passed = all(diagnosis['tests'].values())
        
        if all_tests_passed:
            print("[OK] 所有测试通过，API连接正常")
        else:
            print("[FAIL] 发现连接问题，建议:")
            for i, rec in enumerate(diagnosis['recommendations'], 1):
                print(f"  {i}. {rec}")
        
        return diagnosis


def main():
    """主函数"""
    print("API连接超时优化工具")
    print("="*60)
    
    # API配置
    api_base_url = "http://**************:9300"
    
    # 创建优化的API客户端
    client = OptimizedAPIClient(api_base_url)
    
    # 执行诊断
    diagnosis = client.diagnose_connection_issues()
    
    # 测试多种超时配置
    if diagnosis['tests'].get('connectivity', False):
        print("\n" + "="*60)
        timeout_results = client.test_multiple_timeouts()
        
        # 找出最佳配置
        best_config = None
        best_time = float('inf')
        
        for config_name, result in timeout_results.items():
            if result['success'] and result['duration_ms'] < best_time:
                best_config = config_name
                best_time = result['duration_ms']
        
        if best_config:
            print(f"\n[RECOMMENDATION] 推荐使用: {best_config}")
            print(f"[PERFORMANCE] 响应时间: {best_time:.2f}ms")
        else:
            print(f"\n[WARN] 所有超时配置都未成功，建议检查API服务")
    
    return diagnosis


if __name__ == "__main__":
    main()