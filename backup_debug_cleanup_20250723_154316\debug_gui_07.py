#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试GUI中的07号接口接收端问题
"""

import sys
import traceback

def test_imports():
    """测试导入"""
    print("测试导入...")
    
    try:
        from flask import Flask, request, jsonify
        print("✅ Flask导入成功")
    except ImportError as e:
        print(f"❌ Flask导入失败: {e}")
        return False
    
    try:
        from PySide6.QtWidgets import QApplication, QMainWindow
        print("✅ PySide6导入成功")
    except ImportError as e:
        print(f"❌ PySide6导入失败: {e}")
        return False
    
    try:
        from database_service import DatabaseService
        print("✅ DatabaseService导入成功")
    except ImportError as e:
        print(f"❌ DatabaseService导入失败: {e}")
        return False
    
    try:
        from multi_org_config import get_org_config_by_hospital_code
        print("✅ multi_org_config导入成功")
    except ImportError as e:
        print(f"❌ multi_org_config导入失败: {e}")
        return False
    
    return True

def test_flask_basic():
    """测试Flask基本功能"""
    print("\n测试Flask基本功能...")
    
    try:
        from flask import Flask, jsonify
        import threading
        import time
        
        app = Flask(__name__)
        
        @app.route('/test', methods=['GET'])
        def test_route():
            return jsonify({'status': 'ok', 'message': 'Flask工作正常'})
        
        # 在后台线程中启动Flask
        def run_flask():
            try:
                app.run(host='127.0.0.1', port=5008, debug=False, use_reloader=False)
            except Exception as e:
                print(f"Flask启动失败: {e}")
        
        flask_thread = threading.Thread(target=run_flask, daemon=True)
        flask_thread.start()
        
        # 等待Flask启动
        time.sleep(2)
        
        # 测试请求
        import requests
        response = requests.get('http://127.0.0.1:5008/test', timeout=5)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Flask测试成功: {result['message']}")
            return True
        else:
            print(f"❌ Flask测试失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Flask测试异常: {e}")
        traceback.print_exc()
        return False

def test_gui_basic():
    """测试GUI基本功能"""
    print("\n测试GUI基本功能...")
    
    try:
        from PySide6.QtWidgets import QApplication, QMainWindow, QLabel
        from PySide6.QtCore import QTimer
        
        app = QApplication(sys.argv)
        
        class TestWindow(QMainWindow):
            def __init__(self):
                super().__init__()
                self.setWindowTitle("测试窗口")
                self.setFixedSize(300, 200)
                
                label = QLabel("GUI测试窗口", self)
                label.move(100, 80)
                
                # 2秒后自动关闭
                QTimer.singleShot(2000, self.close)
        
        window = TestWindow()
        window.show()
        
        # 运行2秒后退出
        QTimer.singleShot(2000, app.quit)
        app.exec()
        
        print("✅ GUI基本功能测试成功")
        return True
        
    except Exception as e:
        print(f"❌ GUI测试异常: {e}")
        traceback.print_exc()
        return False

def test_database_service():
    """测试数据库服务"""
    print("\n测试数据库服务...")
    
    try:
        from database_service import DatabaseService
        from config import Config
        
        # 获取数据库连接字符串
        connection_string = Config.get_interface_db_connection_string()
        print(f"数据库连接字符串: {connection_string[:50]}...")
        
        # 创建数据库服务实例
        db_service = DatabaseService(connection_string)
        print("✅ DatabaseService创建成功")
        
        # 测试连接（不实际连接，只是测试方法存在）
        if hasattr(db_service, 'connect'):
            print("✅ connect方法存在")
        else:
            print("❌ connect方法不存在")
            return False
        
        if hasattr(db_service, 'execute_update'):
            print("✅ execute_update方法存在")
        else:
            print("❌ execute_update方法不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库服务测试异常: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("GUI 07号接口接收端调试")
    print("=" * 50)
    
    all_passed = True
    
    # 测试导入
    if not test_imports():
        all_passed = False
    
    # 测试Flask
    if not test_flask_basic():
        all_passed = False
    
    # 测试数据库服务
    if not test_database_service():
        all_passed = False
    
    # 测试GUI（最后测试，因为会显示窗口）
    if not test_gui_basic():
        all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✅ 所有测试通过，可以尝试启动完整的GUI程序")
    else:
        print("❌ 部分测试失败，需要解决依赖问题")
    
    print("\n调试建议:")
    print("1. 确保所有依赖已安装: pip install flask pyside6 requests")
    print("2. 检查数据库连接配置")
    print("3. 确保端口5007没有被占用")
    print("4. 查看GUI程序的错误输出")

if __name__ == "__main__":
    main()
