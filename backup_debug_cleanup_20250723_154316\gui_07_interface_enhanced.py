#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI内置07号接口增强版实现
整合独立接收端的最新功能到GUI中
"""

import json
import hashlib
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from flask import Flask, request, jsonify
from database_service import get_database_service
from config import Config
from multi_org_config import get_org_config_by_shop_code

class GUI07InterfaceEnhanced:
    """GUI内置07号接口增强版"""
    
    def __init__(self, signal_emitter=None):
        """初始化"""
        self.signal_emitter = signal_emitter
        self.db_service = get_database_service()
    
    def log(self, level: str, message: str):
        """记录日志"""
        if self.signal_emitter:
            self.signal_emitter.log_signal.emit(level, message)
        else:
            print(f"[{level}] {message}")
    
    def process_conclusion_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理总检数据 - 增强版"""
        try:
            # 验证必要字段
            pe_no = data.get('peNo')
            if not pe_no:
                return {
                    'success': False,
                    'error': '体检号不能为空',
                    'code': 1001
                }

            hospital = data.get('hospital', {})
            shop_code = hospital.get('code', '')
            if not shop_code:
                return {
                    'success': False,
                    'error': '医院编码不能为空',
                    'code': 1002
                }

            # 获取机构配置
            org_config = get_org_config_by_shop_code(shop_code)
            if not org_config:
                self.log("错误", f"未找到门店编码 {shop_code} 对应的机构配置")
                return {
                    'success': False,
                    'error': f'未找到门店编码 {shop_code} 对应的机构配置',
                    'code': 1002
                }

            # 获取数据库连接
            from center_organization_service import CenterOrganizationConfig
            if isinstance(org_config, dict):
                config_obj = CenterOrganizationConfig()
                for key, value in org_config.items():
                    if hasattr(config_obj, key):
                        setattr(config_obj, key, value)
                db_connection_string = config_obj.get_db_connection_string()
            else:
                db_connection_string = org_config.get_db_connection_string()

            if not db_connection_string or not db_connection_string.strip():
                return {
                    'success': False,
                    'error': f'门店编码 {shop_code} 的数据库连接未配置',
                    'code': 1003
                }

            # 连接数据库
            from database_service import DatabaseService
            db_service = DatabaseService(db_connection_string)
            
            if not db_service.connect():
                return {
                    'success': False,
                    'error': '数据库连接失败',
                    'code': 1004
                }

            try:
                # 更新诊断信息
                result = self._update_diagnosis_info(pe_no, data, db_service)
                if result['success']:
                    self.log("接口调用", f"体检号 {pe_no} 总检信息更新成功")

                return result

            finally:
                db_service.disconnect()

        except Exception as e:
            self.log("错误", f"处理总检数据异常: {str(e)}")
            return {
                'success': False,
                'error': f'处理失败: {str(e)}',
                'code': 1999
            }

    def _update_diagnosis_info(self, pe_no: str, data: Dict[str, Any], db_service) -> Dict[str, Any]:
        """更新诊断信息到数据库"""
        try:
            main_check_finish_time = data.get('mainCheckFinishTime', '')
            main_check_finish_doctor = data.get('mainCheckFinishDoctor', {})
            first_check_finish_time = data.get('firstCheckFinishTime', '')
            first_check_finish_doctor = data.get('firstCheckFinishDoctor', {})
            conclusion_list = data.get('conclusionList', [])
            current_node_type = data.get('currentNodeType', 0)

            updated_records = 0
            conclusion_count = len(conclusion_list)

            # 1. 更新T_Register_Main表
            if main_check_finish_time and main_check_finish_doctor:
                update_main_sql = """
                UPDATE T_Register_Main
                SET cStatus = ?,
                    cOperCode = ?,
                    cOperName = ?,
                    dOperdate = ?
                WHERE cClientCode = ?
                """

                status = '6' if current_node_type == 4 else '4'
                
                try:
                    finish_time = datetime.strptime(main_check_finish_time, '%Y-%m-%d %H:%M:%S')
                except:
                    finish_time = datetime.now()

                main_params = (
                    status,
                    main_check_finish_doctor.get('code', ''),
                    main_check_finish_doctor.get('name', ''),
                    finish_time,
                    pe_no
                )

                db_service.execute_update(update_main_sql, main_params)
                updated_records += 1

            # 2. 清理旧的结论数据
            self._clear_old_conclusions(pe_no, db_service)

            # 3. 插入新的结论数据
            for i, conclusion in enumerate(conclusion_list, 1):
                try:
                    self.log("调试", f"开始处理第{i}个结论: {conclusion.get('conclusionName', '')}")
                    self._insert_conclusion_record(
                        pe_no, conclusion, 
                        main_check_finish_doctor, first_check_finish_doctor, 
                        main_check_finish_time, first_check_finish_time,
                        i, db_service
                    )
                    updated_records += 1
                    self.log("调试", f"第{i}个结论处理成功")
                except Exception as e:
                    self.log("错误", f"插入第{i}个结论记录失败: {e}")
                    self.log("错误", f"失败的结论: {conclusion.get('conclusionName', '')}")
                    continue

            return {
                'success': True,
                'message': '总检信息更新成功',
                'updated_records': updated_records,
                'conclusion_count': conclusion_count
            }

        except Exception as e:
            self.log("错误", f"更新诊断信息失败: {str(e)}")
            return {
                'success': False,
                'error': f'更新失败: {str(e)}'
            }

    def _clear_old_conclusions(self, pe_no: str, db_service):
        """清理旧的结论数据"""
        try:
            # 通过卡号查询客户编码
            client_code = self.get_client_code_by_card_no(pe_no, db_service)
            if not client_code:
                self.log("警告", f"未找到卡号 {pe_no} 对应的客户编码")
                return

            # 删除T_Diag_result中的旧记录
            delete_diag_sql = "DELETE FROM T_Diag_result WHERE cClientCode = ?"
            db_service.execute_update(delete_diag_sql, (client_code,))

            # 删除T_Check_Result_Illness中的旧记录
            delete_illness_sql = "DELETE FROM T_Check_Result_Illness WHERE cClientCode = ? AND cMainName = '总检结论'"
            db_service.execute_update(delete_illness_sql, (client_code,))

            self.log("调试", f"清理体检号 {pe_no} 的旧结论数据完成")

        except Exception as e:
            self.log("警告", f"清理旧结论数据失败: {e}")

    def get_client_code_by_card_no(self, card_no: str, db_service=None) -> str:
        """通过卡号查询客户编码"""
        try:
            service_to_use = db_service if db_service else self.db_service
            
            sql = "SELECT cClientCode FROM T_Register_Main WHERE cCardNo = ?"
            result = service_to_use.execute_query(sql, (card_no,))

            if result and len(result) > 0:
                return result[0].get('cClientCode', '')
            else:
                return ''

        except Exception as e:
            self.log("错误", f"查询客户编码失败: {e}")
            return ''

    def _insert_conclusion_record(self, pe_no: str, conclusion: Dict[str, Any],
                                 main_doctor: Dict[str, Any], first_doctor: Dict[str, Any],
                                 main_time: str, first_time: str, sequence: int, db_service):
        """插入单条结论记录 - 增强版"""

        # 提取基本字段
        conclusion_name = conclusion.get('conclusionName', '')
        conclusion_code = conclusion.get('conclusionCode', '')
        parent_code = conclusion.get('parentCode', '')
        suggest = conclusion.get('suggest', '')
        explain = conclusion.get('explain', '')
        check_result = conclusion.get('checkResult', '')
        level = conclusion.get('level', 3)

        # 新增字段处理
        mapping_id = conclusion.get('mappingId', '')
        children_code = conclusion.get('childrenCode', None)
        dept_id = conclusion.get('deptId', '')
        abnormal_level = conclusion.get('abnormalLevel', 9)
        display_sequence = conclusion.get('displaySequnce', sequence)

        # 通过卡号查询客户编码
        client_code = self.get_client_code_by_card_no(pe_no, db_service)
        if not client_code:
            self.log("警告", f"未找到卡号 {pe_no} 对应的客户编码")
            return

        # 获取机构编码
        try:
            from config import Config
            org_code = getattr(Config, 'ORG_CODE', '08')
        except:
            org_code = '08'

        # === 插入T_Diag_result表 ===
        insert_diag_sql = """
        INSERT INTO T_Diag_result (
            cClientCode, cDiag, cDiagDesc, cDoctCode, cDoctName, dDoctOperdate,
            cOperCode, cOpername, dOperDate, cShopCode
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """

        # 合并结论名称和说明作为总检结论
        diag_content = conclusion_name[:255] if conclusion_name else ""
        if explain:
            diag_desc = f"{explain}\n建议：{suggest}" if suggest else explain
        else:
            diag_desc = suggest if suggest else ""

        # 限制字段长度
        diag_desc = diag_desc[:500] if diag_desc else ""

        # 处理总检医生信息
        main_doctor_code = main_doctor.get('code', '')[:9] if main_doctor.get('code') else ""
        main_doctor_name = main_doctor.get('name', '')[:12] if main_doctor.get('name') else ""

        # 处理初审医生信息
        first_doctor_code = first_doctor.get('code', '')[:9] if first_doctor.get('code') else ""
        first_doctor_name = first_doctor.get('name', '')[:12] if first_doctor.get('name') else ""

        # 处理时间信息
        try:
            main_datetime = datetime.strptime(main_time, '%Y-%m-%d %H:%M:%S') if main_time else datetime.now()
        except:
            main_datetime = datetime.now()

        try:
            first_datetime = datetime.strptime(first_time, '%Y-%m-%d %H:%M:%S') if first_time else datetime.now()
        except:
            first_datetime = datetime.now()

        limited_org_code = org_code[:2] if org_code else ""

        # 记录新增字段信息到日志
        self.log("信息", f"新增字段信息 - mappingId: {mapping_id}, deptId: {dept_id}, abnormalLevel: {abnormal_level}")
        if children_code:
            self.log("信息", f"子结论词编码: {children_code}")
        self.log("信息", f"显示序号: {display_sequence}")
        self.log("信息", f"总检医生: {main_doctor_name}({main_doctor_code}), 时间: {main_time}")
        self.log("信息", f"初审医生: {first_doctor_name}({first_doctor_code}), 时间: {first_time}")

        diag_params = (
            client_code,        # cClientCode - 客户编码
            diag_content,       # cDiag - 总检结论
            diag_desc,          # cDiagDesc - 总检结论描述
            main_doctor_code,   # cDoctCode - 总检医生编码
            main_doctor_name,   # cDoctName - 总检医生姓名
            main_datetime,      # dDoctOperdate - 总检时间
            first_doctor_code,  # cOperCode - 初审医生编码
            first_doctor_name,  # cOpername - 初审医生姓名
            first_datetime,     # dOperDate - 初审时间
            limited_org_code    # cShopCode - 机构编码
        )

        # 打印调试信息
        self.log("调试", f"T_Diag_result插入SQL: {insert_diag_sql.strip()}")
        self.log("调试", "T_Diag_result参数详情:")
        param_names = ["cClientCode", "cDiag", "cDiagDesc", "cDoctCode", "cDoctName",
                      "dDoctOperdate", "cOperCode", "cOpername", "dOperDate", "cShopCode"]
        for i, (name, value) in enumerate(zip(param_names, diag_params)):
            value_str = str(value) if value is not None else "NULL"
            self.log("调试", f"  {i+1:2d}. {name:<15} = '{value_str}' (长度: {len(value_str)})")

        try:
            db_service.execute_update(insert_diag_sql, diag_params)
            self.log("调试", "T_Diag_result插入成功")
        except Exception as e:
            self.log("错误", f"T_Diag_result插入失败: {e}")
            self.log("错误", f"失败的SQL: {insert_diag_sql.strip()}")
            self.log("错误", f"失败的参数: {diag_params}")
            raise

        # === 插入T_Check_Result_Illness表 ===
        insert_illness_sql = """
        INSERT INTO T_Check_Result_Illness (
            cClientCode, cDeptcode, cMainName, cIllnessCode, cIllnessName,
            cIllExplain, cReason, cAdvice, cGrade, cDoctCode, cDoctName,
            dOperdate, nPrintIndex
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """

        # 处理新增字段 - 科室ID映射
        if dept_id:
            limited_dept_id = str(dept_id)[:6] if dept_id else parent_code[:20]
            dept_code_to_use = limited_dept_id
            self.log("信息", f"使用新增科室ID: {dept_id} -> {dept_code_to_use}")
        else:
            dept_code_to_use = (parent_code or 'MAIN')[:20] if parent_code else 'MAIN'

        # 处理异常等级映射
        abnormal_level_mapping = {
            1: '1',  # A级 -> 重要
            2: '2',  # B级 -> 次要
            3: '3',  # C级 -> 其他
            9: '3'   # OTHER -> 其他
        }
        mapped_grade = abnormal_level_mapping.get(abnormal_level, str(level))
        self.log("信息", f"异常等级映射: {abnormal_level} -> {mapped_grade}")

        # 限制字段长度
        limited_conclusion_code = conclusion_code[:6] if conclusion_code else ""
        limited_conclusion_name = conclusion_name[:100] if conclusion_name else ""
        limited_explain = explain[:500] if explain else ""
        limited_suggest = suggest[:500] if suggest else ""

        illness_params = (
            client_code,                                    # cClientCode - 客户编码
            dept_code_to_use,                              # cDeptcode - 科室代码
            '总检结论',                                      # cMainName - 申请项目名称
            limited_conclusion_code or f'AUTO_{sequence:03d}',  # cIllnessCode - 结论词代码
            limited_conclusion_name,                        # cIllnessName - 结论词名称
            limited_explain,                               # cIllExplain - 医学解释
            check_result[:200] if check_result else "",    # cReason - 检查结果汇总
            limited_suggest,                               # cAdvice - 建议
            mapped_grade,                                  # cGrade - 重要性等级
            main_doctor_code,                              # cDoctCode - 医生代码
            main_doctor_name,                              # cDoctName - 医生姓名
            datetime.now(),                                # dOperdate - 操作时间
            display_sequence                               # nPrintIndex - 显示序号
        )

        # 打印调试信息
        self.log("调试", f"T_Check_Result_Illness插入SQL: {insert_illness_sql.strip()}")
        self.log("调试", "T_Check_Result_Illness参数详情:")
        illness_param_names = ["cClientCode", "cDeptcode", "cMainName", "cIllnessCode", "cIllnessName",
                              "cIllExplain", "cReason", "cAdvice", "cGrade", "cDoctCode", "cDoctName",
                              "dOperdate", "nPrintIndex"]
        for i, (name, value) in enumerate(zip(illness_param_names, illness_params)):
            value_str = str(value) if value is not None else "NULL"
            self.log("调试", f"  {i+1:2d}. {name:<15} = '{value_str}' (长度: {len(value_str)})")

        try:
            db_service.execute_update(insert_illness_sql, illness_params)
            self.log("调试", "T_Check_Result_Illness插入成功")
        except Exception as e:
            self.log("错误", f"T_Check_Result_Illness插入失败: {e}")
            self.log("错误", f"失败的SQL: {insert_illness_sql.strip()}")
            self.log("错误", f"失败的参数: {illness_params}")
            # 不抛出异常，继续处理其他记录
            pass

        # 记录新增字段的处理结果
        self.log("信息", f"插入结论记录: {conclusion_name}")
        self.log("信息", f"健管系统映射ID: {mapping_id}")
        self.log("信息", f"科室ID: {dept_id} -> 使用科室代码: {dept_code_to_use}")
        self.log("信息", f"异常等级: {abnormal_level} -> 映射等级: {mapped_grade}")
        self.log("信息", f"显示序号: {display_sequence}")

        # 如果有子结论词编码，记录到日志
        if children_code:
            self.log("信息", f"子结论词编码集合: {children_code}")
            # 注意：子结论词编码是一个集合，当前表结构无法直接存储
            # 如需存储，建议创建单独的子结论词关联表
