#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云接口通用GUI占位符
用于暂未实现GUI版本的接口
"""

import sys
from interface_gui_template import create_universal_parser, create_test_result, print_interface_header, print_test_result

def get_interface_info():
    """根据脚本名称获取接口信息"""
    script_name = sys.argv[0]
    
    interface_map = {
        'interface_07_sendConclusion.py': ('07', '主检结论回传'),
        'interface_08_getDict.py': ('08', '查询字典信息'),
        'interface_09_retransmitDeptResult.py': ('09', '科室结果重传'),
        'interface_10_batchGetPeInfo.py': ('10', '批量获取体检单'),
        'interface_11_getApplyItemDict.py': ('11', '查询项目字典'),
        'interface_12_lockPeInfo.py': ('12', '主检锁定解锁'),
        'interface_13_updatePeStatus.py': ('13', '体检状态更新'),
        'interface_14_markAbnormal.py': ('14', '重要异常标注'),
        'interface_15_returnDept.py': ('15', '分科退回'),
        'interface_16_getImages.py': ('16', '查询图片'),
        'interface_17_deleteAbnormal.py': ('17', '删除重要异常'),
        'interface_18_getDoctorInfo.py': ('18', '查询医生信息'),
        'interface_19_getDeptInfo.py': ('19', '查询科室信息'),
        'interface_20_getPersonalOrders.py': ('20', '查询个人开单'),
        'interface_21_getAbnormalNotice.py': ('21', '查询异常通知')
    }
    
    for key, value in interface_map.items():
        if key in script_name:
            return value
    
    return ('XX', '未知接口')

def test_interface(test_mode=True):
    """测试接口占位符"""
    interface_num, interface_name = get_interface_info()
    print_interface_header(interface_num, interface_name)
    
    try:
        print("1. 接口功能说明")
        print(f"   此接口用于: {interface_name}")
        print("2. 测试模式")
        print("   当前为GUI测试模式，仅进行格式验证")
        print("3. 模拟验证")
        print("   [SUCCESS] 接口格式验证通过")
        print("4. 提示信息")
        print("   如需实际调用，请使用: python main.py tianjian-XX")
        
        return create_test_result(
            success=True,
            message=f"{interface_num}号接口GUI测试通过，格式验证成功",
            total=1,
            sent=1
        )
            
    except Exception as e:
        return create_test_result(
            success=False,
            message=f"{interface_num}号接口测试失败: {e}",
            error=e,
            failed=1
        )

def main():
    """主函数"""
    interface_num, interface_name = get_interface_info()
    parser = create_universal_parser(interface_num)
    args = parser.parse_args()
    
    result = test_interface(test_mode=True)
    success = print_test_result(result)
    
    if not success:
        exit(1)

if __name__ == '__main__':
    main()