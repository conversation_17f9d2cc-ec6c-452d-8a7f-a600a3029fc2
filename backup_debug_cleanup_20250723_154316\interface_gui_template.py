#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云接口GUI通用模板
为所有天健云接口提供统一的参数支持
"""

def create_universal_parser(interface_name):
    """创建通用的参数解析器"""
    import argparse
    
    parser = argparse.ArgumentParser(description=f'天健云{interface_name}接口测试')
    parser.add_argument('--test-mode', action='store_true', help='测试模式')
    parser.add_argument('--limit', type=int, default=10, help='限制条数')
    parser.add_argument('--days', type=int, default=7, help='天数')
    parser.add_argument('--batch-size', type=int, default=20, help='批量大小')
    
    # 可选参数（某些接口可能需要）
    parser.add_argument('--pe-no', type=str, help='体检号')
    parser.add_argument('--dict-type', type=str, help='字典类型')
    parser.add_argument('--doctor-id', type=str, help='医生ID')
    parser.add_argument('--dept-id', type=str, help='科室ID')
    
    return parser

def create_test_result(success, message, total=0, sent=0, failed=0, error=None):
    """创建标准测试结果"""
    result = {
        'success': success,
        'total': total,
        'sent': sent,
        'failed': failed,
        'message': message
    }
    
    if error:
        result['error'] = str(error)
    
    return result

def print_interface_header(interface_num, interface_name):
    """打印接口头部信息"""
    print(f"天健云{interface_num}号接口测试 - {interface_name}")
    print("=" * 60)

def print_test_result(result):
    """打印测试结果"""
    if result['success']:
        print(f"\n[OK] {result['message']}")
    else:
        print(f"\n[FAIL] {result['message']}")
    
    return result['success']