#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云接口报文输出工具
用于统一格式化和输出HTTP请求/响应报文
"""

import json
from typing import Dict, Any, List, Optional
from datetime import datetime


class MessageLogger:
    """报文日志记录器"""
    
    def __init__(self, interface_num: str, interface_name: str):
        """
        初始化报文记录器
        
        Args:
            interface_num: 接口编号
            interface_name: 接口名称
        """
        self.interface_num = interface_num
        self.interface_name = interface_name
        self.separator = "=" * 60
        
    def log_request(self, url: str, method: str, headers: Dict[str, str], 
                   data: Any, data_type: str = "json"):
        """
        记录HTTP请求信息
        
        Args:
            url: 请求URL
            method: 请求方法
            headers: 请求头
            data: 请求数据
            data_type: 数据类型 (json/form/text)
        """
        print(f"   {self.separator}")
        print(f"   【{self.interface_num}号接口】HTTP请求详情")
        print(f"   接口名称: {self.interface_name}")
        print(f"   时间戳: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   {self.separator}")
        
        print(f"   请求URL: {url}")
        print(f"   请求方法: {method}")
        
        print("   请求头:")
        for key, value in headers.items():
            # 隐藏敏感信息
            if key.lower() in ['sign', 'authorization', 'api-key']:
                masked_value = value[:8] + "*" * (len(value) - 8) if len(value) > 8 else "*" * len(value)
                print(f"     {key}: {masked_value}")
            else:
                print(f"     {key}: {value}")
        
        print("   请求体:")
        if data_type == "json":
            self._log_json_data(data)
        else:
            print(f"     {data}")
        
        print(f"   {self.separator}")
    
    def log_response(self, status_code: int, headers: Dict[str, str], 
                    response_data: Any, response_type: str = "json"):
        """
        记录HTTP响应信息
        
        Args:
            status_code: 响应状态码
            headers: 响应头
            response_data: 响应数据
            response_type: 响应类型 (json/text)
        """
        print(f"   【{self.interface_num}号接口】HTTP响应详情")
        print(f"   时间戳: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   {self.separator}")
        
        print(f"   响应状态码: {status_code}")
        
        print("   响应头:")
        for key, value in headers.items():
            print(f"     {key}: {value}")
        
        print("   响应体:")
        if response_type == "json" and isinstance(response_data, (dict, list)):
            print(f"     {json.dumps(response_data, ensure_ascii=False, indent=2)}")
        else:
            print(f"     {response_data}")
        
        print(f"   {self.separator}")
    
    def log_error(self, error_type: str, error_message: str, details: Optional[str] = None):
        """
        记录错误信息
        
        Args:
            error_type: 错误类型
            error_message: 错误消息
            details: 详细信息
        """
        print(f"   【{self.interface_num}号接口】错误详情")
        print(f"   时间戳: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   {self.separator}")
        
        print(f"   错误类型: {error_type}")
        print(f"   错误消息: {error_message}")
        
        if details:
            print("   详细信息:")
            print(f"     {details}")
        
        print(f"   {self.separator}")
    
    def log_summary(self, success: bool, total: int, sent: int, failed: int, 
                   message: str, execution_time: Optional[float] = None):
        """
        记录执行摘要
        
        Args:
            success: 是否成功
            total: 总数
            sent: 成功数
            failed: 失败数
            message: 消息
            execution_time: 执行时间（秒）
        """
        status = "成功" if success else "失败"
        status_icon = "✅" if success else "❌"
        
        print(f"   【{self.interface_num}号接口】执行摘要")
        print(f"   时间戳: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   {self.separator}")
        
        print(f"   执行状态: {status_icon} {status}")
        print(f"   处理总数: {total}")
        print(f"   成功数量: {sent}")
        print(f"   失败数量: {failed}")
        print(f"   结果消息: {message}")
        
        if execution_time:
            print(f"   执行时间: {execution_time:.3f}秒")
        
        print(f"   {self.separator}")
    
    def _log_json_data(self, data: Any):
        """记录JSON数据"""
        if isinstance(data, list):
            print(f"     数组长度: {len(data)}")
            print("     数据预览:")
            
            # 显示前2条完整数据
            for i, item in enumerate(data[:2]):
                print(f"       [{i+1}] {json.dumps(item, ensure_ascii=False, indent=6)}")
            
            # 如果有更多数据，只显示摘要
            if len(data) > 2:
                print(f"       ... 还有 {len(data)-2} 条数据")
                
        elif isinstance(data, dict):
            print(f"     {json.dumps(data, ensure_ascii=False, indent=2)}")
        else:
            print(f"     {data}")


def create_message_logger(interface_num: str, interface_name: str) -> MessageLogger:
    """
    创建报文记录器实例
    
    Args:
        interface_num: 接口编号
        interface_name: 接口名称
        
    Returns:
        MessageLogger实例
    """
    return MessageLogger(interface_num, interface_name)


# 便捷函数
def log_api_call(interface_num: str, interface_name: str, url: str, method: str,
                headers: Dict[str, str], request_data: Any, response_status: int,
                response_headers: Dict[str, str], response_data: Any):
    """
    一次性记录完整的API调用信息
    
    Args:
        interface_num: 接口编号
        interface_name: 接口名称
        url: 请求URL
        method: 请求方法
        headers: 请求头
        request_data: 请求数据
        response_status: 响应状态码
        response_headers: 响应头
        response_data: 响应数据
    """
    logger = create_message_logger(interface_num, interface_name)
    logger.log_request(url, method, headers, request_data)
    logger.log_response(response_status, response_headers, response_data)


if __name__ == "__main__":
    # 测试示例
    logger = create_message_logger("01", "体检信息传输")
    
    # 模拟请求
    test_headers = {
        "Content-Type": "application/json",
        "sign": "abcd1234567890abcd1234567890",
        "timestamp": "20250113120000",
        "mic-code": "MIC1.001E"
    }
    
    test_data = {
        "regCode": "TEST001",
        "name": "张三",
        "icCode": "110101198001010001"
    }
    
    logger.log_request("http://example.com/api", "POST", test_headers, test_data)
    
    # 模拟响应
    test_response = {
        "code": 0,
        "msg": "成功",
        "data": None
    }
    
    logger.log_response(200, {"Content-Type": "application/json"}, test_response)
    logger.log_summary(True, 1, 1, 0, "测试成功", 1.234)
