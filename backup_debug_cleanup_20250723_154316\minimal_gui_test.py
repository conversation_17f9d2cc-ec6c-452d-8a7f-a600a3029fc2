#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化GUI测试，包含07号接口接收端
"""

import sys
import threading
from datetime import datetime
from flask import Flask, request, jsonify
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QTextEdit
from PySide6.QtCore import QTimer

class SimpleLogWidget:
    """简单的日志组件"""
    
    def __init__(self, text_edit):
        self.text_edit = text_edit
    
    def add_log(self, level, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {level}: {message}"
        self.text_edit.append(log_message)
        print(log_message)  # 同时输出到控制台

class Simple07Receiver:
    """简化的07号接口接收端"""
    
    def __init__(self, log_widget):
        self.log_widget = log_widget
        self.app = Flask(__name__)
        self.server_thread = None
        self.is_running = False
        self.setup_routes()
    
    def setup_routes(self):
        """设置路由"""
        @self.app.route('/dx/inter/receiveConclusion', methods=['POST'])
        def receive_conclusion():
            try:
                data = request.get_json()
                if not data:
                    self.log_widget.add_log("错误", "请求数据为空")
                    return jsonify({'success': False, 'error': '请求数据为空'}), 400
                
                pe_no = data.get('peNo', 'Unknown')
                hospital = data.get('hospital', {})
                hospital_name = hospital.get('name', 'Unknown')
                conclusion_count = len(data.get('conclusionList', []))
                
                self.log_widget.add_log("接口调用", f"收到总检信息: 体检号={pe_no}, 医院={hospital_name}, 结论数={conclusion_count}")
                
                # 简单的成功响应
                return jsonify({
                    'success': True,
                    'message': '总检信息接收成功',
                    'code': 0,
                    'data': {
                        'peNo': pe_no,
                        'updated_records': 1,
                        'conclusion_count': conclusion_count
                    }
                }), 200
                
            except Exception as e:
                error_msg = f"处理异常: {str(e)}"
                self.log_widget.add_log("错误", error_msg)
                return jsonify({'success': False, 'error': error_msg}), 500
        
        @self.app.route('/health', methods=['GET'])
        def health_check():
            return jsonify({
                'status': 'healthy',
                'service': '简化版07号接口接收端',
                'timestamp': datetime.now().isoformat()
            })
    
    def start_service(self):
        """启动服务"""
        if self.is_running:
            return
        
        def run_server():
            try:
                self.log_widget.add_log("信息", "07号接口接收端启动中...")
                self.log_widget.add_log("信息", "监听端口: 5007")
                
                self.app.run(
                    host='0.0.0.0',
                    port=5007,
                    debug=False,
                    threaded=True,
                    use_reloader=False
                )
            except Exception as e:
                self.log_widget.add_log("错误", f"服务启动失败: {e}")
        
        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
        self.is_running = True
        
        # 延迟显示启动成功消息
        QTimer.singleShot(1000, lambda: self.log_widget.add_log("信息", "07号接口接收端启动成功"))

class MinimalMainWindow(QMainWindow):
    """最小化主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("最小化GUI测试 - 07号接口接收端")
        self.setFixedSize(800, 600)
        
        # 设置中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("07号接口接收端测试")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 日志显示
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        # 初始化日志组件
        self.log_widget = SimpleLogWidget(self.log_text)
        
        # 初始化07号接口接收端
        self.receiver = Simple07Receiver(self.log_widget)
        
        # 延迟启动服务
        QTimer.singleShot(1000, self.start_receiver)
        
        self.log_widget.add_log("信息", "GUI初始化完成")
    
    def start_receiver(self):
        """启动接收端服务"""
        try:
            self.receiver.start_service()
        except Exception as e:
            self.log_widget.add_log("错误", f"启动接收端失败: {e}")
    
    def closeEvent(self, event):
        """关闭事件"""
        self.log_widget.add_log("信息", "程序正在关闭...")
        event.accept()

def main():
    """主函数"""
    print("启动最小化GUI测试...")
    
    app = QApplication(sys.argv)
    app.setApplicationName("最小化GUI测试")
    
    window = MinimalMainWindow()
    window.show()
    
    print("GUI窗口已显示，07号接口接收端将在1秒后启动...")
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
