#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移除独立的07号接收端服务
统一使用GUI内置的接收端服务
"""

import os
import shutil
from datetime import datetime

def backup_and_remove_files():
    """备份并移除独立接收端相关文件"""
    
    print("移除独立07号接收端服务")
    print("=" * 50)
    
    # 需要处理的文件列表
    files_to_handle = [
        {
            'file': 'interface_07_receiveConclusion.py',
            'action': 'backup_and_remove',
            'description': '独立07号接收端主文件'
        },
        {
            'file': 'start_interface_07_receiver.py', 
            'action': 'backup_and_remove',
            'description': '07号接收端启动脚本'
        },
        {
            'file': '07号接口接收端说明.md',
            'action': 'update',
            'description': '07号接口说明文档'
        }
    ]
    
    # 创建备份目录
    backup_dir = f"backup_standalone_07_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        os.makedirs(backup_dir, exist_ok=True)
        print(f"✅ 创建备份目录: {backup_dir}")
    except Exception as e:
        print(f"❌ 创建备份目录失败: {e}")
        return False
    
    # 处理文件
    for file_info in files_to_handle:
        file_path = file_info['file']
        action = file_info['action']
        description = file_info['description']
        
        print(f"\n处理文件: {file_path} ({description})")
        
        if not os.path.exists(file_path):
            print(f"⚠️  文件不存在，跳过: {file_path}")
            continue
            
        if action == 'backup_and_remove':
            try:
                # 备份文件
                backup_path = os.path.join(backup_dir, file_path)
                shutil.copy2(file_path, backup_path)
                print(f"✅ 备份到: {backup_path}")
                
                # 移除原文件
                os.remove(file_path)
                print(f"✅ 移除原文件: {file_path}")
                
            except Exception as e:
                print(f"❌ 处理文件失败: {e}")
                
        elif action == 'update':
            try:
                # 备份原文件
                backup_path = os.path.join(backup_dir, file_path)
                shutil.copy2(file_path, backup_path)
                print(f"✅ 备份到: {backup_path}")
                
                # 更新文件内容
                update_documentation(file_path)
                print(f"✅ 更新文档: {file_path}")
                
            except Exception as e:
                print(f"❌ 更新文档失败: {e}")
    
    return True

def update_documentation(file_path):
    """更新文档内容"""
    
    if file_path == '07号接口接收端说明.md':
        # 在文档开头添加弃用说明
        deprecation_notice = """# ⚠️ 重要说明

**此独立接收端服务已弃用，请使用GUI内置的07号接口服务。**

- **新的服务位置**: GUI程序内置的 `TianjianInterfaceService`
- **启动方式**: 运行 `python gui_main.py`，服务会自动启动
- **服务端口**: 仍然是 5007
- **接收端点**: 仍然是 `/dx/inter/receiveConclusion`
- **优势**: 统一管理、GUI日志集成、更好的调试功能

---

# 原文档内容（仅供参考）

"""
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            updated_content = deprecation_notice + original_content
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)
                
        except Exception as e:
            print(f"更新文档内容失败: {e}")
            raise

def create_migration_summary():
    """创建迁移总结文档"""
    
    summary_content = f"""# 07号接口服务迁移总结

## 迁移时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 迁移内容

### 已移除的文件
1. `interface_07_receiveConclusion.py` - 独立07号接收端主文件
2. `start_interface_07_receiver.py` - 07号接收端启动脚本

### 已更新的文件
1. `07号接口接收端说明.md` - 添加了弃用说明

### 新增的文件
1. `gui_07_interface_enhanced.py` - 增强版实现代码
2. `GUI_07接口集成指南.md` - 集成指南
3. `07号接口服务迁移总结.md` - 本文档

## 迁移原因

1. **统一管理**: 所有接口服务统一在GUI中管理
2. **简化部署**: 无需单独启动和维护多个服务
3. **更好的监控**: GUI提供统一的日志和状态监控
4. **功能增强**: 集成了最新的字段处理和调试功能

## 新的使用方式

### 启动服务
```bash
# 旧方式（已弃用）
python interface_07_receiveConclusion.py

# 新方式
python gui_main.py
```

### 服务信息
- **端口**: 5007（不变）
- **接收端点**: `/dx/inter/receiveConclusion`（不变）
- **健康检查**: `/health`（不变）
- **管理界面**: GUI程序提供可视化管理

### 功能对比

| 功能 | 独立服务 | GUI内置服务 |
|------|----------|-------------|
| 基础接收功能 | ✅ | ✅ |
| 新增字段支持 | ✅ | ✅ |
| 调试日志 | ✅ | ✅ |
| 可视化管理 | ❌ | ✅ |
| 统一监控 | ❌ | ✅ |
| 服务状态显示 | ❌ | ✅ |

## 兼容性说明

- **API兼容**: 完全兼容，天健云无需修改调用方式
- **数据兼容**: 完全兼容，支持所有现有数据格式
- **功能兼容**: 完全兼容，并增加了新功能

## 回滚方案

如需回滚到独立服务，可以：

1. 从备份目录恢复文件
2. 停止GUI程序
3. 启动独立接收端服务

备份目录位置: `backup_standalone_07_*`

## 测试验证

### 基本功能测试
```bash
python test_interface_07_new_params.py
```

### 健康检查
```bash
curl http://localhost:5007/health
```

### GUI集成测试
```bash
python test_gui_with_07_receiver.py
```

## 注意事项

1. **端口占用**: 确保只有一个服务占用5007端口
2. **配置一致**: GUI和独立服务使用相同的数据库配置
3. **日志位置**: 日志现在显示在GUI界面中
4. **服务监控**: 通过GUI状态栏监控服务状态

## 技术支持

如遇到问题，请：
1. 检查GUI日志区域的错误信息
2. 验证数据库连接配置
3. 确认端口5007未被其他程序占用
4. 查看集成指南获取详细信息

---

迁移完成！现在请使用GUI程序来管理07号接口服务。
"""
    
    try:
        with open('07号接口服务迁移总结.md', 'w', encoding='utf-8') as f:
            f.write(summary_content)
        print("✅ 创建迁移总结文档成功")
        return True
    except Exception as e:
        print(f"❌ 创建迁移总结文档失败: {e}")
        return False

if __name__ == "__main__":
    print("07号接口服务迁移工具")
    print("=" * 60)
    print("将独立的07号接收端服务迁移到GUI内置服务")
    print("=" * 60)
    
    # 确认操作
    confirm = input("\n确认要移除独立的07号接收端服务吗？(y/N): ")
    if confirm.lower() != 'y':
        print("操作已取消")
        exit(0)
    
    # 执行迁移
    backup_success = backup_and_remove_files()
    summary_success = create_migration_summary()
    
    print("\n" + "=" * 60)
    if backup_success and summary_success:
        print("✅ 迁移完成！")
        print("\n重要提醒:")
        print("1. 独立的07号接收端服务已移除")
        print("2. 请使用 'python gui_main.py' 启动GUI程序")
        print("3. 07号接口服务会在GUI中自动启动")
        print("4. 查看 '07号接口服务迁移总结.md' 了解详细信息")
        print("5. 如需回滚，可从备份目录恢复文件")
    else:
        print("❌ 迁移过程中出现问题，请检查错误信息")
    print("=" * 60)
