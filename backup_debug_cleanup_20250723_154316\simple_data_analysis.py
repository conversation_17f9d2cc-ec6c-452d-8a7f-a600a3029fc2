#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的数据分析 - 找出数据分布问题
"""

def simple_data_analysis():
    """简单的数据分析"""
    try:
        print("="*80)
        print("简单数据分析 - 查找数据分布问题")
        print("="*80)
        
        from interface_01_sendPeInfo import TianjianInterface01
        interface = TianjianInterface01()
        
        # 1. 基本数据库信息
        print("1. 基本数据库信息:")
        basic_sql = "SELECT @@SERVERNAME as server_name, DB_NAME() as database_name, GETDATE() as current_time"
        
        basic_info = interface.db_service.connection_manager.execute_query_with_cache(
            interface.db_service.connection_string,
            basic_sql,
            cache_key="basic_info",
            use_cache=False
        )
        
        if basic_info:
            info = basic_info[0]
            print(f"   服务器: {info['server_name']}")
            print(f"   数据库: {info['database_name']}")
            print(f"   当前时间: {info['current_time']}")
        
        # 2. 检查不同时间范围的数据量
        print(f"\n2. 不同时间范围的数据统计:")
        
        time_ranges = [7, 30, 90, 180, 365, 730]  # 增加到2年
        
        for days in time_ranges:
            # 使用dAffirmdate查询
            count_sql = f"""
            SELECT COUNT(*) as count
            FROM T_Register_Main 
            WHERE dAffirmdate >= DATEADD(day, -{days}, GETDATE())
            AND dAffirmdate IS NOT NULL
            """
            
            count_result = interface.db_service.connection_manager.execute_query_with_cache(
                interface.db_service.connection_string,
                count_sql,
                cache_key=f"count_{days}days",
                use_cache=False
            )
            
            count = count_result[0]['count'] if count_result else 0
            print(f"   最近{days:3d}天: {count:4d} 条记录")
        
        # 3. 检查2025年每月的数据分布
        print(f"\n3. 2025年每月数据分布:")
        
        monthly_2025_sql = """
        SELECT 
            MONTH(dAffirmdate) as month,
            COUNT(*) as count
        FROM T_Register_Main 
        WHERE YEAR(dAffirmdate) = 2025
        AND dAffirmdate IS NOT NULL
        GROUP BY MONTH(dAffirmdate)
        ORDER BY month
        """
        
        monthly_2025 = interface.db_service.connection_manager.execute_query_with_cache(
            interface.db_service.connection_string,
            monthly_2025_sql,
            cache_key="monthly_2025",
            use_cache=False
        )
        
        if monthly_2025:
            for row in monthly_2025:
                print(f"   2025年{row['month']:2d}月: {row['count']:4d} 条")
        else:
            print("   2025年没有数据")
        
        # 4. 检查最近的实际记录
        print(f"\n4. 最近的实际记录（按dAffirmdate排序）:")
        
        recent_sql = """
        SELECT TOP 20
            cClientCode,
            cName,
            dAffirmdate,
            DATEDIFF(day, dAffirmdate, GETDATE()) as days_ago
        FROM T_Register_Main 
        WHERE dAffirmdate IS NOT NULL
        ORDER BY dAffirmdate DESC
        """
        
        recent_records = interface.db_service.connection_manager.execute_query_with_cache(
            interface.db_service.connection_string,
            recent_sql,
            cache_key="recent_records",
            use_cache=False
        )
        
        if recent_records:
            print(f"   最近20条记录:")
            for i, record in enumerate(recent_records, 1):
                print(f"     {i:2d}. {record['cName']} ({record['cClientCode']}) - {record['dAffirmdate']} ({record['days_ago']}天前)")
        
        # 5. 测试您建议的查询
        print(f"\n5. 测试您建议的查询（dAffirmdate >= 90天前）:")
        
        suggested_sql = """
        SELECT TOP 10
            rm.cClientCode as archiveNo,
            rm.cName as name,
            rm.cSex as sex,
            rm.dBornDate as dBirthday,
            rm.cIdCard as cert_id,
            rm.cTel as mobile,
            rm.dOperdate as exam_date,
            rm.cStatus,
            rm.cMarryFlag,
            rm.cCardNo as peno,
            rm.cSuitCode,
            rm.cContractCode,
            rm.cCommanSuitName,
            '健康体检' as cType
        FROM T_Register_Main rm
        WHERE rm.dAffirmdate >= DATEADD(day, -90, GETDATE())
        ORDER BY rm.dOperdate DESC
        """
        
        suggested_result = interface.db_service.connection_manager.execute_query_with_cache(
            interface.db_service.connection_string,
            suggested_sql,
            cache_key="suggested_query",
            use_cache=False
        )
        
        if suggested_result:
            print(f"   查询结果: {len(suggested_result)} 条记录")
            for i, record in enumerate(suggested_result, 1):
                print(f"     {i}. {record['name']} ({record['archiveNo']})")
        else:
            print(f"   查询结果: 0 条记录")
        
        # 6. 检查是否需要更大的时间范围
        print(f"\n6. 建议的时间范围:")
        
        # 找到有足够数据的最小时间范围
        for days in [180, 365, 730, 1095]:  # 6个月到3年
            count_sql = f"""
            SELECT COUNT(*) as count
            FROM T_Register_Main 
            WHERE dAffirmdate >= DATEADD(day, -{days}, GETDATE())
            AND dAffirmdate IS NOT NULL
            """
            
            count_result = interface.db_service.connection_manager.execute_query_with_cache(
                interface.db_service.connection_string,
                count_sql,
                cache_key=f"range_check_{days}",
                use_cache=False
            )
            
            count = count_result[0]['count'] if count_result else 0
            
            if count >= 10:  # 如果有10条以上的记录
                print(f"   建议使用 {days} 天范围，可获得 {count} 条记录")
                return days
        
        print(f"   即使使用3年范围也没有足够的数据")
        return None
        
    except Exception as e:
        print(f"分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    recommended_days = simple_data_analysis()
    
    print("\n" + "="*80)
    print("分析结论和建议:")
    print("="*80)
    
    if recommended_days:
        print(f"✅ 建议使用 {recommended_days} 天作为查询范围")
        print(f"\n修改方法:")
        print(f"1. 命令行: python interface_01_sendPeInfo.py --days {recommended_days} --limit 10 --test-mode")
        print(f"2. 或者修改代码中的默认天数为 {recommended_days}")
    else:
        print("⚠️ 数据库中的数据可能确实很少，或者需要检查:")
        print("1. 是否连接到了正确的数据库")
        print("2. 是否有权限访问所有数据")
        print("3. 数据是否在其他表中")

if __name__ == "__main__":
    main()
