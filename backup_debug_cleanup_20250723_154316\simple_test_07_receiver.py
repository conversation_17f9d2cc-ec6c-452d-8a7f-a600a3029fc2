#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版07号接口接收端测试
"""

import json
import requests
import time
import threading
from datetime import datetime

def test_basic_functionality():
    """测试基本功能"""
    print("=" * 60)
    print("07号接口接收端基本功能测试")
    print("=" * 60)
    
    # 测试数据
    test_data = {
        "hospital": {
            "code": "TEST001",
            "name": "测试医院"
        },
        "peNo": "TEST123456",
        "firstCheckFinishTime": "2025-07-22 14:30:00",
        "firstCheckFinishDoctor": {
            "code": "DOC001",
            "name": "张医生"
        },
        "mainCheckFinishTime": "2025-07-22 15:30:00",
        "mainCheckFinishDoctor": {
            "code": "DOC002",
            "name": "李主任"
        },
        "conclusionList": [
            {
                "conclusionName": "血压正常",
                "conclusionCode": "BP001",
                "parentCode": "CARDIO",
                "suggest": "继续保持良好生活习惯",
                "explain": "血压在正常范围内",
                "checkResult": "收缩压120mmHg，舒张压80mmHg",
                "level": 3,
                "displaySequnce": 1
            }
        ],
        "currentNodeType": 4
    }
    
    print("1. 测试数据准备完成")
    print(f"   体检号: {test_data['peNo']}")
    print(f"   医院: {test_data['hospital']['name']}")
    print(f"   结论数量: {len(test_data['conclusionList'])}")
    
    # 测试JSON序列化
    try:
        json_str = json.dumps(test_data, ensure_ascii=False, indent=2)
        print(f"\n2. JSON序列化测试通过")
        print(f"   数据大小: {len(json_str)} 字符")
    except Exception as e:
        print(f"\n2. JSON序列化失败: {e}")
        return False
    
    # 测试数据结构验证
    required_fields = ['hospital', 'peNo', 'conclusionList']
    missing_fields = []
    for field in required_fields:
        if field not in test_data:
            missing_fields.append(field)
    
    if missing_fields:
        print(f"\n3. 数据结构验证失败，缺少字段: {missing_fields}")
        return False
    else:
        print(f"\n3. 数据结构验证通过")
    
    # 测试结论数据结构
    if test_data['conclusionList']:
        conclusion = test_data['conclusionList'][0]
        conclusion_fields = ['conclusionName', 'suggest', 'explain', 'level']
        missing_conclusion_fields = []
        for field in conclusion_fields:
            if field not in conclusion:
                missing_conclusion_fields.append(field)
        
        if missing_conclusion_fields:
            print(f"4. 结论数据结构验证失败，缺少字段: {missing_conclusion_fields}")
            return False
        else:
            print(f"4. 结论数据结构验证通过")
    
    print(f"\n✅ 基本功能测试通过")
    return True

def test_database_operations():
    """测试数据库操作逻辑"""
    print("\n" + "=" * 60)
    print("数据库操作逻辑测试")
    print("=" * 60)
    
    try:
        from database_service import DatabaseService
        print("1. DatabaseService导入成功")
        
        # 测试SQL语句构建
        update_sql = """
        UPDATE T_Register_Main 
        SET cStatus = ?, 
            cOperCode = ?, 
            cOperName = ?, 
            dOperdate = ?
        WHERE cClientCode = ?
        """
        
        insert_diag_sql = """
        INSERT INTO T_Diag_result (
            cClientCode, cIllnessCode, cIllnessName, cIllExplain, 
            cAdvice, cGrade, cDoctCode, cDoctName, dOperdate
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        insert_illness_sql = """
        INSERT INTO T_Check_Result_Illness (
            cClientCode, cDeptcode, cMainName, cIllnessCode, cIllnessName,
            cIllExplain, cReason, cAdvice, cGrade, cDoctCode, cDoctName,
            dOperdate, nPrintIndex
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        print("2. SQL语句构建成功")
        print(f"   更新语句参数数量: {update_sql.count('?')}")
        print(f"   诊断插入语句参数数量: {insert_diag_sql.count('?')}")
        print(f"   结论插入语句参数数量: {insert_illness_sql.count('?')}")
        
        # 测试参数构建
        test_params = (
            '6',  # status
            'DOC002',  # doctor code
            '李主任',  # doctor name
            datetime.now(),  # operation date
            'TEST123456'  # client code
        )
        
        print(f"3. 参数构建测试通过")
        print(f"   参数数量: {len(test_params)}")
        
        print(f"\n✅ 数据库操作逻辑测试通过")
        return True
        
    except ImportError as e:
        print(f"1. DatabaseService导入失败: {e}")
        return False
    except Exception as e:
        print(f"数据库操作逻辑测试失败: {e}")
        return False

def test_multi_org_config():
    """测试多机构配置"""
    print("\n" + "=" * 60)
    print("多机构配置测试")
    print("=" * 60)
    
    try:
        from multi_org_config import get_org_config_by_hospital_code
        print("1. 多机构配置模块导入成功")
        
        # 测试获取机构配置
        test_hospital_code = "MIC1.001E"
        org_config = get_org_config_by_hospital_code(test_hospital_code)
        
        if org_config:
            print(f"2. 找到医院编码 {test_hospital_code} 的配置")
            print(f"   机构名称: {org_config.get('org_name', 'N/A')}")
            print(f"   数据库配置: {'已配置' if org_config.get('db_connection_string') else '未配置'}")
        else:
            print(f"2. 未找到医院编码 {test_hospital_code} 的配置（这是正常的）")
        
        print(f"\n✅ 多机构配置测试通过")
        return True
        
    except ImportError as e:
        print(f"1. 多机构配置模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"多机构配置测试失败: {e}")
        return False

def main():
    """主函数"""
    print("天健云07号接口接收端 - 简化测试")
    print("=" * 80)
    
    all_passed = True
    
    # 基本功能测试
    if not test_basic_functionality():
        all_passed = False
    
    # 数据库操作测试
    if not test_database_operations():
        all_passed = False
    
    # 多机构配置测试
    if not test_multi_org_config():
        all_passed = False
    
    print("\n" + "=" * 80)
    if all_passed:
        print("✅ 所有测试通过")
        print("\n接下来可以:")
        print("1. 启动接收端服务: python interface_07_receiveConclusion.py")
        print("2. 运行完整测试: python test_interface_07_receiver.py")
    else:
        print("❌ 部分测试失败")
        print("请检查依赖和配置")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
