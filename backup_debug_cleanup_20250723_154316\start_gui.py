#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI启动器 - 检查依赖并启动图形界面
"""

import sys
import subprocess
import importlib.util

def check_dependency(package_name: str) -> bool:
    """检查依赖包是否已安装"""
    spec = importlib.util.find_spec(package_name)
    return spec is not None

def install_dependency(package_name: str) -> bool:
    """安装依赖包"""
    try:
        print(f"正在安装 {package_name}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ {package_name} 安装成功")
            return True
        else:
            print(f"❌ {package_name} 安装失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {package_name} 安装超时")
        return False
    except Exception as e:
        print(f"❌ {package_name} 安装异常: {e}")
        return False

def check_and_install_gui_deps():
    """检查并安装GUI依赖"""
    required_packages = [
        ("PySide6", "PySide6"),
    ]
    
    print("检查GUI依赖...")
    
    missing_packages = []
    for package_name, import_name in required_packages:
        if not check_dependency(import_name):
            print(f"缺少依赖: {package_name}")
            missing_packages.append(package_name)
        else:
            print(f"依赖已安装: {package_name}")
    
    if missing_packages:
        print(f"\n需要安装 {len(missing_packages)} 个依赖包")
        for package in missing_packages:
            if not install_dependency(package):
                print(f"无法安装 {package}，GUI启动失败")
                return False
    
    print("所有GUI依赖已就绪")
    return True

def start_gui():
    """启动图形界面"""
    try:
        print("启动天健云数据同步系统图形界面...")
        
        # 导入GUI模块
        from gui_main import main
        
        # 启动GUI
        main()
        
    except ImportError as e:
        print(f"GUI模块导入失败: {e}")
        print("请确保所有依赖已正确安装")
        return False
    except Exception as e:
        print(f"GUI启动失败: {e}")
        return False

def show_fallback_menu():
    """显示备用命令行菜单"""
    print("\n" + "="*60)
    print("天健云数据同步系统 - 命令行界面")
    print("="*60)
    print("GUI启动失败，请使用以下命令行功能：")
    print()
    print("接口测试:")
    print("  python tests/test_db_connection.py")
    print("  python tests/test_api_connection.py")
    print("  python tests/run_all_tests.py")
    print()
    print("直接运行接口:")
    print("  python interface_01_sendPeInfo.py --test-mode")
    print("  python interface_02_syncApplyItem.py --test-mode")
    print()
    print("其他GUI选项:")
    print("  python gui_simple.py")
    print("  python gui_complete.py")
    print("="*60)

def main():
    """主函数"""
    print("天健云数据同步系统 GUI 启动器 v1.3.0")
    print("开发: 福能AI对接项目组")
    print()
    
    # 检查并安装依赖
    if check_and_install_gui_deps():
        # 启动GUI
        if not start_gui():
            show_fallback_menu()
    else:
        print("GUI依赖安装失败")
        show_fallback_menu()

if __name__ == "__main__":
    main()