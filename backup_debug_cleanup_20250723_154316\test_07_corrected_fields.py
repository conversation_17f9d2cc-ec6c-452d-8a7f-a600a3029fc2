#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试07号接口更正后的T_Diag_result字段处理
验证初审和总检医生信息的正确存储
"""

def test_field_mapping():
    """测试字段映射逻辑"""
    
    print("T_Diag_result表字段映射测试")
    print("=" * 50)
    
    # 模拟接收到的数据
    test_data = {
        "peNo": "5000006",
        "firstCheckFinishTime": "2025-07-23 10:30:00",
        "firstCheckFinishDoctor": {
            "code": "FIRST001",
            "name": "初审医生",
            "synonyms": None,
            "zero": None
        },
        "mainCheckFinishTime": "2025-07-23 11:00:00",
        "mainCheckFinishDoctor": {
            "code": "MAIN001",
            "name": "总检医生",
            "synonyms": None,
            "zero": None
        },
        "currentNodeType": 4,
        "conclusionList": [
            {
                "mappingId": "MAPPING_001",
                "conclusionName": "血压偏高",
                "conclusionCode": "BP001",
                "parentCode": "CARDIO",
                "suggest": "建议低盐饮食，适量运动",
                "explain": "收缩压超过正常范围，需要注意心血管健康",
                "checkResult": "收缩压150mmHg，舒张压95mmHg",
                "level": 1,
                "displaySequnce": 1,
                "childrenCode": ["BP001_1", "BP001_2"],
                "deptId": "DEPT01",
                "abnormalLevel": 1
            }
        ]
    }
    
    # 提取医生信息
    first_doctor = test_data.get('firstCheckFinishDoctor', {})
    main_doctor = test_data.get('mainCheckFinishDoctor', {})
    first_time = test_data.get('firstCheckFinishTime', '')
    main_time = test_data.get('mainCheckFinishTime', '')
    
    print("原始数据:")
    print(f"  初审医生: {first_doctor.get('name')} ({first_doctor.get('code')})")
    print(f"  初审时间: {first_time}")
    print(f"  总检医生: {main_doctor.get('name')} ({main_doctor.get('code')})")
    print(f"  总检时间: {main_time}")
    
    print("\nT_Diag_result表字段映射:")
    print("  字段名称                    -> 数据来源")
    print("  " + "-" * 45)
    print(f"  cClientCode (体检号)        -> {test_data.get('peNo')}")
    print(f"  cDiag (总检结论)            -> 结论名称")
    print(f"  cDiagDesc (总检结论描述)    -> 医学解释+建议")
    print(f"  cDoctCode (总检医生编码)    -> {main_doctor.get('code')}")
    print(f"  cDoctName (总检医生姓名)    -> {main_doctor.get('name')}")
    print(f"  dDoctOperdate (总检时间)    -> {main_time}")
    print(f"  cOperCode (初审医生编码)    -> {first_doctor.get('code')}")
    print(f"  cOpername (初审医生姓名)    -> {first_doctor.get('name')}")
    print(f"  dOperDate (初审时间)        -> {first_time}")
    print(f"  cShopCode (机构编码)        -> 从配置获取")
    
    # 验证字段长度限制
    print("\n字段长度验证:")
    main_doctor_code = main_doctor.get('code', '')[:9]
    main_doctor_name = main_doctor.get('name', '')[:12]
    first_doctor_code = first_doctor.get('code', '')[:9]
    first_doctor_name = first_doctor.get('name', '')[:12]
    
    print(f"  总检医生编码: '{main_doctor.get('code')}' -> '{main_doctor_code}' (限制9字符)")
    print(f"  总检医生姓名: '{main_doctor.get('name')}' -> '{main_doctor_name}' (限制12字符)")
    print(f"  初审医生编码: '{first_doctor.get('code')}' -> '{first_doctor_code}' (限制9字符)")
    print(f"  初审医生姓名: '{first_doctor.get('name')}' -> '{first_doctor_name}' (限制12字符)")
    
    # 验证时间格式处理
    print("\n时间格式验证:")
    from datetime import datetime
    try:
        main_datetime = datetime.strptime(main_time, '%Y-%m-%d %H:%M:%S')
        print(f"  总检时间解析: '{main_time}' -> {main_datetime}")
    except Exception as e:
        print(f"  总检时间解析失败: {e}")
        
    try:
        first_datetime = datetime.strptime(first_time, '%Y-%m-%d %H:%M:%S')
        print(f"  初审时间解析: '{first_time}' -> {first_datetime}")
    except Exception as e:
        print(f"  初审时间解析失败: {e}")
    
    print("\n✅ 字段映射逻辑验证完成!")

def test_sql_structure():
    """测试SQL插入语句结构"""
    
    print("\nSQL插入语句验证")
    print("=" * 50)
    
    expected_sql = """
    INSERT INTO T_Diag_result (
        cClientCode, cDiag, cDiagDesc, cDoctCode, cDoctName, dDoctOperdate, 
        cOperCode, cOpername, dOperDate, cShopCode
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """
    
    print("期望的SQL结构:")
    print(expected_sql.strip())
    
    print("\n参数顺序:")
    params_order = [
        "1. cClientCode - 客户编码",
        "2. cDiag - 总检结论", 
        "3. cDiagDesc - 总检结论描述",
        "4. cDoctCode - 总检医生编码",
        "5. cDoctName - 总检医生姓名",
        "6. dDoctOperdate - 总检时间",
        "7. cOperCode - 初审医生编码",
        "8. cOpername - 初审医生姓名", 
        "9. dOperDate - 初审时间",
        "10. cShopCode - 机构编码"
    ]
    
    for param in params_order:
        print(f"  {param}")
    
    print("\n✅ SQL结构验证完成!")

if __name__ == "__main__":
    print("07号接口T_Diag_result字段更正验证")
    print("=" * 60)
    
    # 测试字段映射
    test_field_mapping()
    
    # 测试SQL结构
    test_sql_structure()
    
    print("\n" + "=" * 60)
    print("✅ 所有验证完成!")
    print("\n主要更正内容:")
    print("1. 区分了初审医生和总检医生信息")
    print("2. 正确映射了时间字段 (dOperDate=初审时间, dDoctOperdate=总检时间)")
    print("3. 更新了SQL插入语句包含所有必要字段")
    print("4. 完善了字段长度限制和数据验证")
