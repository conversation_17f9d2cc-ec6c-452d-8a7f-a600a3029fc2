#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试07号接口字段长度问题
重现字符串截断错误并分析具体原因
"""

import requests
import json
from datetime import datetime

def test_with_debug():
    """发送测试数据并查看调试信息"""
    
    url = "http://localhost:5007/dx/inter/receiveConclusion"
    
    # 使用与错误日志相同的测试数据
    test_data = {
        "hospital": {
            "code": "09",
            "name": "测试医院"
        },
        "peNo": "5000006",
        "firstCheckFinishTime": "2025-07-23 10:30:00",
        "firstCheckFinishDoctor": {
            "code": "DOC001",
            "name": "张医生",
            "synonyms": None,
            "zero": None
        },
        "mainCheckFinishTime": "2025-07-23 11:00:00",
        "mainCheckFinishDoctor": {
            "code": "DOC002", 
            "name": "李主任",
            "synonyms": None,
            "zero": None
        },
        "currentNodeType": 4,
        "conclusionList": [
            {
                "mappingId": "MAPPING_001",
                "conclusionName": "血压偏高需要注意心血管健康状况",
                "conclusionCode": "BP001",
                "parentCode": "CARDIO",
                "suggest": "建议低盐饮食，适量运动，定期监测血压变化，必要时就医咨询专业医生意见",
                "explain": "收缩压超过正常范围，可能存在高血压风险，需要引起重视并采取相应的预防措施",
                "checkResult": "收缩压150mmHg，舒张压95mmHg，超出正常范围",
                "level": 1,
                "displaySequnce": 1,
                "childrenCode": ["BP001_1", "BP001_2"],
                "deptId": "DEPT01",
                "abnormalLevel": 1
            },
            {
                "mappingId": "MAPPING_002", 
                "conclusionName": "血脂轻度异常需要控制饮食",
                "conclusionCode": "LIPID001",
                "parentCode": "BIOCHEM",
                "suggest": "建议控制饮食，减少高脂肪食物摄入，增加运动量，定期复查血脂水平",
                "explain": "总胆固醇略高于正常值，可能与饮食习惯和生活方式有关",
                "checkResult": "总胆固醇6.2mmol/L，甘油三酯2.1mmol/L",
                "level": 2,
                "displaySequnce": 2,
                "childrenCode": None,
                "deptId": "DEPT02",
                "abnormalLevel": 2
            },
            {
                "mappingId": "MAPPING_003",
                "conclusionName": "轻微脂肪肝建议生活方式调整",
                "conclusionCode": "LIVER001", 
                "parentCode": "ULTRA",
                "suggest": "建议减重，避免饮酒，规律作息，适当运动，定期复查肝功能和超声检查",
                "explain": "肝脏回声增强，符合轻度脂肪肝表现，通常与肥胖、饮食不当等因素相关",
                "checkResult": "肝脏大小正常，回声增强，肝内血管显示欠清晰",
                "level": 3,
                "displaySequnce": 3,
                "childrenCode": ["LIVER001_A"],
                "deptId": "DEPT03",
                "abnormalLevel": 3
            },
            {
                "mappingId": "MAPPING_004",
                "conclusionName": "其他异常情况需要进一步检查确认",
                "conclusionCode": "OTHER001",
                "parentCode": "MISC",
                "suggest": "建议进一步检查，必要时咨询相关专科医生，制定个性化的治疗方案",
                "explain": "需要专科医生进一步评估，可能需要额外的检查项目来明确诊断",
                "checkResult": "检查结果待进一步分析，建议结合临床症状综合判断",
                "level": 3,
                "displaySequnce": 4,
                "childrenCode": [],
                "deptId": "DEPT99",
                "abnormalLevel": 9
            }
        ]
    }
    
    print("发送包含较长文本的测试数据...")
    print(f"体检号: {test_data['peNo']}")
    print(f"结论数量: {len(test_data['conclusionList'])}")
    
    # 显示每个结论的文本长度
    print("\n结论文本长度分析:")
    for i, conclusion in enumerate(test_data['conclusionList'], 1):
        print(f"结论{i}: {conclusion['conclusionName']}")
        print(f"  结论名称长度: {len(conclusion['conclusionName'])}")
        print(f"  建议长度: {len(conclusion['suggest'])}")
        print(f"  解释长度: {len(conclusion['explain'])}")
        print(f"  检查结果长度: {len(conclusion['checkResult'])}")
        print()
    
    try:
        headers = {
            'Content-Type': 'application/json',
            'mic-code': '09',
            'misc-id': 'DEBUG_TEST'
        }
        
        response = requests.post(
            url, 
            json=test_data, 
            headers=headers,
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 测试成功!")
            else:
                print("❌ 测试失败!")
                print(f"错误信息: {result.get('error')}")
        else:
            print("❌ 请求失败!")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败! 请确保07号接收端服务已启动")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    print("07号接口字段长度调试测试")
    print("=" * 50)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    test_with_debug()
