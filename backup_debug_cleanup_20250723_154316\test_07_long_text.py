#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试07号接口超长文本处理
故意使用超长文本来触发字符串截断错误
"""

import requests
import json
from datetime import datetime

def test_with_very_long_text():
    """使用超长文本测试"""
    
    url = "http://localhost:5007/dx/inter/receiveConclusion"
    
    # 创建超长文本来触发截断错误
    very_long_conclusion = "血压偏高需要注意心血管健康状况" * 20  # 300字符
    very_long_suggest = "建议低盐饮食，适量运动，定期监测血压变化，必要时就医咨询专业医生意见，同时要保持良好的生活习惯，规律作息，避免熬夜，减少压力，戒烟限酒，控制体重，定期体检" * 10  # 超长建议
    very_long_explain = "收缩压超过正常范围，可能存在高血压风险，需要引起重视并采取相应的预防措施，这种情况在现代人群中比较常见，主要与生活方式、饮食习惯、工作压力等因素有关" * 10  # 超长解释
    very_long_result = "收缩压150mmHg，舒张压95mmHg，超出正常范围，心率85次/分，血压波动较大，建议进一步监测" * 20  # 超长检查结果
    
    test_data = {
        "hospital": {
            "code": "09",
            "name": "测试医院"
        },
        "peNo": "5000006",
        "firstCheckFinishTime": "2025-07-23 10:30:00",
        "firstCheckFinishDoctor": {
            "code": "FIRST_DOCTOR_CODE_VERY_LONG_123456789",  # 超长医生编码
            "name": "初审医生姓名超长测试用例专用名称",  # 超长医生姓名
            "synonyms": None,
            "zero": None
        },
        "mainCheckFinishTime": "2025-07-23 11:00:00",
        "mainCheckFinishDoctor": {
            "code": "MAIN_DOCTOR_CODE_VERY_LONG_123456789",  # 超长医生编码
            "name": "总检医生姓名超长测试用例专用名称",  # 超长医生姓名
            "synonyms": None,
            "zero": None
        },
        "currentNodeType": 4,
        "conclusionList": [
            {
                "mappingId": "MAPPING_001_VERY_LONG_ID_FOR_TESTING_PURPOSE",
                "conclusionName": very_long_conclusion,
                "conclusionCode": "BP001_VERY_LONG_CODE_123456789",  # 超长结论编码
                "parentCode": "CARDIO_VERY_LONG_PARENT_CODE_123456789",  # 超长父类编码
                "suggest": very_long_suggest,
                "explain": very_long_explain,
                "checkResult": very_long_result,
                "level": 1,
                "displaySequnce": 1,
                "childrenCode": ["BP001_1", "BP001_2"],
                "deptId": "DEPT01_VERY_LONG_DEPARTMENT_ID_123456789",  # 超长科室ID
                "abnormalLevel": 1
            }
        ]
    }
    
    print("发送包含超长文本的测试数据...")
    print(f"体检号: {test_data['peNo']}")
    
    # 显示文本长度
    conclusion = test_data['conclusionList'][0]
    print(f"\n超长文本长度分析:")
    print(f"结论名称长度: {len(conclusion['conclusionName'])}")
    print(f"建议长度: {len(conclusion['suggest'])}")
    print(f"解释长度: {len(conclusion['explain'])}")
    print(f"检查结果长度: {len(conclusion['checkResult'])}")
    print(f"结论编码长度: {len(conclusion['conclusionCode'])}")
    print(f"父类编码长度: {len(conclusion['parentCode'])}")
    print(f"科室ID长度: {len(conclusion['deptId'])}")
    print(f"总检医生编码长度: {len(test_data['mainCheckFinishDoctor']['code'])}")
    print(f"总检医生姓名长度: {len(test_data['mainCheckFinishDoctor']['name'])}")
    print(f"初审医生编码长度: {len(test_data['firstCheckFinishDoctor']['code'])}")
    print(f"初审医生姓名长度: {len(test_data['firstCheckFinishDoctor']['name'])}")
    
    try:
        headers = {
            'Content-Type': 'application/json',
            'mic-code': '09',
            'misc-id': 'LONG_TEXT_TEST'
        }
        
        print(f"\n发送请求到: {url}")
        response = requests.post(
            url, 
            json=test_data, 
            headers=headers,
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 测试成功!")
            else:
                print("❌ 测试失败!")
                print(f"错误信息: {result.get('error')}")
        else:
            print("❌ 请求失败!")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败! 请确保07号接收端服务已启动")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    print("07号接口超长文本测试")
    print("=" * 50)
    print("目的：触发字符串截断错误以查看调试信息")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    test_with_very_long_text()
