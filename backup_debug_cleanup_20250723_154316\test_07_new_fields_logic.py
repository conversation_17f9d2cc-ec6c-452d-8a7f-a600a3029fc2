#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试07号接口新增字段处理逻辑
不依赖数据库连接，仅测试字段处理逻辑
"""

def test_new_fields_processing():
    """测试新增字段处理逻辑"""
    
    print("测试新增字段处理逻辑")
    print("=" * 40)
    
    # 模拟结论数据
    test_conclusions = [
        {
            "mappingId": "MAPPING_001",
            "conclusionName": "血压偏高",
            "conclusionCode": "BP001",
            "parentCode": "CARDIO",
            "suggest": "建议低盐饮食",
            "explain": "收缩压超过正常范围",
            "checkResult": "收缩压150mmHg",
            "level": 1,
            "displaySequnce": 1,
            "childrenCode": ["BP001_1", "BP001_2"],
            "deptId": "DEPT01",
            "abnormalLevel": 1
        },
        {
            "mappingId": "MAPPING_002",
            "conclusionName": "血脂异常",
            "conclusionCode": "LIPID001",
            "parentCode": "BIOCHEM",
            "suggest": "控制饮食",
            "explain": "胆固醇偏高",
            "checkResult": "总胆固醇6.2mmol/L",
            "level": 2,
            "displaySequnce": 2,
            "childrenCode": None,
            "deptId": "DEPT02",
            "abnormalLevel": 2
        },
        {
            "mappingId": "MAPPING_003",
            "conclusionName": "轻微脂肪肝",
            "conclusionCode": "LIVER001",
            "parentCode": "ULTRA",
            "suggest": "减重避免饮酒",
            "explain": "肝脏回声增强",
            "checkResult": "肝脏大小正常",
            "level": 3,
            "displaySequnce": 3,
            "childrenCode": ["LIVER001_A"],
            "deptId": "DEPT03",
            "abnormalLevel": 3
        },
        {
            "mappingId": "MAPPING_004",
            "conclusionName": "其他异常",
            "conclusionCode": "OTHER001",
            "parentCode": "MISC",
            "suggest": "进一步检查",
            "explain": "需要专科评估",
            "checkResult": "待进一步分析",
            "level": 3,
            "displaySequnce": 4,
            "childrenCode": [],
            "deptId": "DEPT99",
            "abnormalLevel": 9
        }
    ]
    
    # 测试字段提取和处理
    for i, conclusion in enumerate(test_conclusions, 1):
        print(f"\n测试结论 {i}: {conclusion['conclusionName']}")
        
        # 提取新增字段
        mapping_id = conclusion.get('mappingId', '')
        children_code = conclusion.get('childrenCode', None)
        dept_id = conclusion.get('deptId', '')
        abnormal_level = conclusion.get('abnormalLevel', 9)
        display_sequence = conclusion.get('displaySequnce', i)
        
        print(f"  mappingId: {mapping_id}")
        print(f"  deptId: {dept_id}")
        print(f"  abnormalLevel: {abnormal_level}")
        print(f"  displaySequnce: {display_sequence}")
        print(f"  childrenCode: {children_code}")
        
        # 测试科室代码处理逻辑
        parent_code = conclusion.get('parentCode', '')
        if dept_id:
            limited_dept_id = str(dept_id)[:6] if dept_id else parent_code[:20]
            dept_code_to_use = limited_dept_id
            print(f"  → 使用科室ID: {dept_id} -> {dept_code_to_use}")
        else:
            dept_code_to_use = parent_code[:20] if parent_code else 'MAIN'
            print(f"  → 使用父类编码: {parent_code} -> {dept_code_to_use}")
        
        # 测试异常等级映射
        abnormal_level_mapping = {
            1: '1',  # A级 -> 重要
            2: '2',  # B级 -> 次要
            3: '3',  # C级 -> 其他
            9: '3'   # OTHER -> 其他
        }
        level = conclusion.get('level', 3)
        mapped_grade = abnormal_level_mapping.get(abnormal_level, str(level))
        print(f"  → 异常等级映射: {abnormal_level} -> {mapped_grade}")
        
        # 测试子结论词处理
        if children_code:
            if isinstance(children_code, list) and len(children_code) > 0:
                print(f"  → 子结论词: {len(children_code)}个 - {children_code}")
            elif children_code is None:
                print(f"  → 子结论词: 无")
            else:
                print(f"  → 子结论词: 空列表")
        
        print(f"  ✅ 结论 {i} 字段处理完成")
    
    print(f"\n✅ 所有新增字段处理逻辑测试完成!")
    
    # 测试向后兼容性
    print(f"\n测试向后兼容性（缺少新增字段）")
    print("-" * 40)
    
    old_conclusion = {
        "conclusionName": "血压正常",
        "conclusionCode": "BP002",
        "parentCode": "CARDIO",
        "suggest": "继续保持",
        "explain": "血压正常",
        "checkResult": "120/80mmHg",
        "level": 3,
        "displaySequnce": 1
        # 注意：没有新增字段
    }
    
    # 使用默认值处理缺失字段
    mapping_id = old_conclusion.get('mappingId', '')
    children_code = old_conclusion.get('childrenCode', None)
    dept_id = old_conclusion.get('deptId', '')
    abnormal_level = old_conclusion.get('abnormalLevel', 9)
    display_sequence = old_conclusion.get('displaySequnce', 1)
    
    print(f"旧格式结论: {old_conclusion['conclusionName']}")
    print(f"  mappingId: '{mapping_id}' (默认空)")
    print(f"  deptId: '{dept_id}' (默认空)")
    print(f"  abnormalLevel: {abnormal_level} (默认9)")
    print(f"  displaySequnce: {display_sequence} (使用原值)")
    print(f"  childrenCode: {children_code} (默认None)")
    print(f"  ✅ 向后兼容性测试通过!")

if __name__ == "__main__":
    test_new_fields_processing()
