#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试07号接收端服务健康状态
"""

import requests
import json
from datetime import datetime

def test_health_check():
    """测试健康检查接口"""
    url = "http://localhost:5007/health"
    
    try:
        print("测试07号接收端健康检查...")
        response = requests.get(url, timeout=10)
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 07号接收端服务运行正常")
            print(f"服务: {result.get('service')}")
            print(f"状态: {result.get('status')}")
            return True
        else:
            print("❌ 健康检查失败")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到07号接收端服务")
        print("请确保服务已启动: python interface_07_receiveConclusion.py")
        return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_simple_request():
    """测试简单的接收请求"""
    url = "http://localhost:5007/dx/inter/receiveConclusion"
    
    # 最简单的测试数据
    simple_data = {
        "hospital": {
            "code": "TEST001",
            "name": "测试医院"
        },
        "peNo": "HEALTH_CHECK_001",
        "currentNodeType": 4,
        "conclusionList": [
            {
                "conclusionName": "健康检查测试",
                "level": 3,
                "displaySequnce": 1
            }
        ]
    }
    
    try:
        print("\n测试简单接收请求...")
        headers = {
            'Content-Type': 'application/json',
            'mic-code': 'TEST001',
            'misc-id': 'HEALTH_CHECK'
        }
        
        response = requests.post(
            url,
            json=simple_data,
            headers=headers,
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code in [200, 400]:  # 400也可能是正常的业务错误
            print("✅ 接收端接口响应正常")
            return True
        else:
            print("❌ 接收端接口异常")
            return False
            
    except Exception as e:
        print(f"❌ 请求测试异常: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("07号接收端服务健康检查")
    print("=" * 50)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 健康检查
    health_ok = test_health_check()
    
    if health_ok:
        # 简单请求测试
        test_simple_request()
    
    print("\n检查完成!")
