#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试01和03接口的卡号设置功能
"""

import subprocess
import sys
import os

def test_interface_01_with_card_no():
    """测试01号接口的卡号功能"""
    print("=" * 60)
    print("测试01号接口 - 卡号功能")
    print("=" * 60)
    
    # 测试不指定卡号的情况
    print("\n1. 测试不指定卡号（按天数和数量查询）")
    cmd = ["python", "interface_01_sendPeInfo.py", "--test-mode", "--limit", "3", "--days", "180"]
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30, encoding='utf-8')
        print("返回码:", result.returncode)
        if result.stdout:
            print("标准输出:")
            print(result.stdout)
        if result.stderr:
            print("标准错误:")
            print(result.stderr)
    except Exception as e:
        print(f"执行失败: {e}")
    
    # 测试指定卡号的情况
    print("\n2. 测试指定卡号")
    # 这里需要一个实际存在的卡号，我们先用一个示例
    test_card_no = "TEST001"  # 这个需要根据实际数据库中的数据来设置
    cmd = ["python", "interface_01_sendPeInfo.py", "--test-mode", "--card-no", test_card_no]
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30, encoding='utf-8')
        print("返回码:", result.returncode)
        if result.stdout:
            print("标准输出:")
            print(result.stdout)
        if result.stderr:
            print("标准错误:")
            print(result.stderr)
    except Exception as e:
        print(f"执行失败: {e}")

def test_interface_03_with_card_no():
    """测试03号接口的卡号功能"""
    print("=" * 60)
    print("测试03号接口 - 卡号功能")
    print("=" * 60)
    
    # 测试不指定卡号的情况
    print("\n1. 测试不指定卡号（按天数和数量查询）")
    cmd = ["python", "interface_03_deptInfo.py", "--test-mode", "--limit", "3", "--days", "180"]
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30, encoding='utf-8')
        print("返回码:", result.returncode)
        if result.stdout:
            print("标准输出:")
            print(result.stdout)
        if result.stderr:
            print("标准错误:")
            print(result.stderr)
    except Exception as e:
        print(f"执行失败: {e}")
    
    # 测试指定卡号的情况
    print("\n2. 测试指定卡号")
    test_card_no = "TEST001"  # 这个需要根据实际数据库中的数据来设置
    cmd = ["python", "interface_03_deptInfo.py", "--test-mode", "--card-no", test_card_no]
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30, encoding='utf-8')
        print("返回码:", result.returncode)
        if result.stdout:
            print("标准输出:")
            print(result.stdout)
        if result.stderr:
            print("标准错误:")
            print(result.stderr)
    except Exception as e:
        print(f"执行失败: {e}")

def main():
    """主函数"""
    print("测试01和03接口的卡号设置功能")
    print("=" * 80)
    
    # 测试01号接口
    test_interface_01_with_card_no()
    
    print("\n" + "=" * 80)
    
    # 测试03号接口
    test_interface_03_with_card_no()
    
    print("\n" + "=" * 80)
    print("测试完成")

if __name__ == "__main__":
    main()
