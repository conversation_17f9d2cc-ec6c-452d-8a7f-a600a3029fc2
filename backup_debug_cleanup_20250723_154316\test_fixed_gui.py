#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的GUI程序
"""

import subprocess
import time
import requests
import sys

def test_gui_startup():
    """测试GUI启动"""
    print("=" * 60)
    print("测试修复后的GUI程序启动")
    print("=" * 60)
    
    try:
        # 启动GUI程序
        print("1. 启动GUI程序...")
        process = subprocess.Popen(
            [sys.executable, "gui_main.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待程序启动
        print("2. 等待程序启动（10秒）...")
        time.sleep(10)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("   ✅ GUI程序启动成功，进程正在运行")
            
            # 测试07号接口健康检查
            print("\n3. 测试07号接口健康检查...")
            try:
                response = requests.get("http://localhost:5007/health", timeout=5)
                if response.status_code == 200:
                    result = response.json()
                    print(f"   ✅ 健康检查成功")
                    print(f"   服务: {result.get('service', 'N/A')}")
                    print(f"   状态: {result.get('status', 'N/A')}")
                    
                    # 测试接收总检信息
                    print("\n4. 测试接收总检信息...")
                    test_data = {
                        "hospital": {"code": "TEST", "name": "测试医院"},
                        "peNo": "TEST001",
                        "conclusionList": [{"conclusionName": "测试结论", "level": 3}],
                        "currentNodeType": 4
                    }
                    
                    response = requests.post(
                        "http://localhost:5007/dx/inter/receiveConclusion",
                        json=test_data,
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        print(f"   ✅ 总检信息接收成功")
                        print(f"   体检号: {result.get('data', {}).get('peNo', 'N/A')}")
                        print(f"   处理状态: {result.get('message', 'N/A')}")
                        
                        print(f"\n🎉 所有测试通过！GUI程序和07号接口接收端工作正常。")
                        return True
                    else:
                        print(f"   ❌ 总检信息接收失败，状态码: {response.status_code}")
                        return False
                        
                else:
                    print(f"   ❌ 健康检查失败，状态码: {response.status_code}")
                    return False
                    
            except requests.exceptions.ConnectionError:
                print(f"   ❌ 无法连接到07号接口，服务可能未启动")
                return False
            except Exception as e:
                print(f"   ❌ 测试07号接口时发生异常: {e}")
                return False
        else:
            # 进程已退出，检查错误
            stdout, stderr = process.communicate()
            print("   ❌ GUI程序启动失败，进程已退出")
            if stdout:
                print("   标准输出:")
                print(stdout)
            if stderr:
                print("   标准错误:")
                print(stderr)
            return False
            
    except Exception as e:
        print(f"   ❌ 启动GUI程序时发生异常: {e}")
        return False
    
    finally:
        # 清理进程
        try:
            if process.poll() is None:
                print("\n5. 清理进程...")
                process.terminate()
                time.sleep(2)
                if process.poll() is None:
                    process.kill()
                print("   进程已清理")
        except:
            pass

def main():
    """主函数"""
    print("GUI程序修复验证测试")
    print("=" * 80)
    print("此测试将:")
    print("1. 启动修复后的GUI程序")
    print("2. 验证程序是否正常运行（无线程错误）")
    print("3. 测试07号接口接收端是否正常工作")
    print("4. 自动清理测试进程")
    print("=" * 80)
    
    success = test_gui_startup()
    
    print("\n" + "=" * 80)
    if success:
        print("✅ 测试通过！线程安全修复成功。")
        print("\n现在可以正常使用:")
        print("  python gui_main.py")
    else:
        print("❌ 测试失败，可能还有问题需要解决。")
        print("\n请检查:")
        print("1. 是否有语法错误")
        print("2. 是否有未解决的线程安全问题")
        print("3. 依赖是否正确安装")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
