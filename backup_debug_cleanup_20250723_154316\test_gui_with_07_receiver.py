#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试内置07号接口接收端的GUI程序
"""

import json
import requests
import time
import threading
from datetime import datetime

def test_gui_07_receiver():
    """测试GUI内置的07号接口接收端"""
    print("=" * 60)
    print("测试GUI内置的07号接口接收端服务")
    print("=" * 60)
    
    base_url = "http://localhost:5007"
    
    # 等待服务启动
    print("1. 等待GUI和07号接口接收端服务启动...")
    time.sleep(5)
    
    # 测试健康检查
    print("\n2. 测试健康检查接口")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 健康检查通过")
            print(f"   服务: {result.get('service')}")
            print(f"   状态: {result.get('status')}")
        else:
            print(f"   ❌ 健康检查失败，状态码: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print(f"   ❌ 连接失败，请确保GUI程序已启动")
        return False
    except Exception as e:
        print(f"   ❌ 健康检查异常: {e}")
        return False
    
    # 测试接收总检信息
    print("\n3. 测试接收总检信息")
    test_data = {
        "hospital": {
            "code": "09",
            "name": "GUI测试医院"
        },
        "peNo": "5000002",
        "firstCheckFinishTime": "2025-07-22 14:30:00",
        "firstCheckFinishDoctor": {
            "code": "DOC001",
            "name": "张医生"
        },
        "mainCheckFinishTime": "2025-07-22 15:30:00",
        "mainCheckFinishDoctor": {
            "code": "DOC002",
            "name": "李主任"
        },
        "conclusionList": [
            {
                "conclusionName": "血压正常",
                "conclusionCode": "BP001",
                "parentCode": "CARDIO",
                "suggest": "继续保持良好生活习惯",
                "explain": "血压在正常范围内",
                "checkResult": "收缩压120mmHg，舒张压80mmHg",
                "level": 3,
                "displaySequnce": 1
            },
            {
                "conclusionName": "血脂轻度异常",
                "conclusionCode": "LIPID002",
                "parentCode": "LAB",
                "suggest": "注意饮食，定期复查",
                "explain": "总胆固醇略高于正常值",
                "checkResult": "总胆固醇5.8mmol/L",
                "level": 2,
                "displaySequnce": 2
            }
        ],
        "currentNodeType": 4
    }
    
    try:
        print(f"   发送测试数据...")
        print(f"   体检号: {test_data['peNo']}")
        print(f"   医院: {test_data['hospital']['name']}")
        print(f"   结论数量: {len(test_data['conclusionList'])}")
        
        response = requests.post(
            f"{base_url}/dx/inter/receiveConclusion",
            json=test_data,
            timeout=30
        )
        
        print(f"   响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 总检信息接收成功")
            print(f"   更新记录数: {result.get('data', {}).get('updated_records', 0)}")
            print(f"   结论数量: {result.get('data', {}).get('conclusion_count', 0)}")
            return True
        else:
            result = response.json()
            print(f"   ❌ 总检信息接收失败")
            print(f"   错误码: {result.get('code')}")
            print(f"   错误信息: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return False

def test_multiple_requests():
    """测试多个请求"""
    print("\n" + "=" * 60)
    print("测试多个并发请求")
    print("=" * 60)
    
    base_url = "http://localhost:5007"
    
    def send_request(request_id):
        """发送单个请求"""
        # 使用实际的卡号
        card_numbers = ["5000002", "5000003", "0000005", "0000007"]
        pe_no = card_numbers[(request_id - 1) % len(card_numbers)]

        test_data = {
            "hospital": {
                "code": "09",
                "name": "并发测试医院"
            },
            "peNo": pe_no,
            "mainCheckFinishTime": "2025-07-22 16:00:00",
            "mainCheckFinishDoctor": {
                "code": f"DOC{request_id:03d}",
                "name": f"医生{request_id}"
            },
            "conclusionList": [
                {
                    "conclusionName": f"卡号{pe_no}总检结论",
                    "conclusionCode": f"CARD_{pe_no}",
                    "suggest": f"针对卡号{pe_no}的专业建议",
                    "explain": f"卡号{pe_no}的详细医学解释",
                    "checkResult": f"卡号{pe_no}的检查结果",
                    "level": 3,
                    "displaySequnce": 1
                }
            ],
            "currentNodeType": 4
        }
        
        try:
            response = requests.post(
                f"{base_url}/dx/inter/receiveConclusion",
                json=test_data,
                timeout=10
            )
            
            if response.status_code == 200:
                print(f"   ✅ 卡号{pe_no}: 成功")
                return True
            else:
                print(f"   ❌ 卡号{pe_no}: 失败 (状态码: {response.status_code})")
                return False
                
        except Exception as e:
            print(f"   ❌ 卡号{pe_no}: 异常 - {e}")
            return False
    
    # 发送5个并发请求
    threads = []
    results = []
    
    for i in range(1, 6):
        thread = threading.Thread(target=lambda req_id=i: results.append(send_request(req_id)))
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    success_count = sum(1 for result in results if result)
    print(f"\n   并发测试结果: {success_count}/5 成功")
    
    return success_count == 5

def main():
    """主函数"""
    print("GUI内置07号接口接收端测试")
    print("=" * 80)
    print("请确保已启动GUI程序: python gui_main.py")
    print("=" * 80)
    
    # 基本功能测试
    basic_test_passed = test_gui_07_receiver()
    
    # 并发测试
    concurrent_test_passed = test_multiple_requests()
    
    print("\n" + "=" * 80)
    print("测试总结:")
    print(f"  基本功能测试: {'✅ 通过' if basic_test_passed else '❌ 失败'}")
    print(f"  并发测试: {'✅ 通过' if concurrent_test_passed else '❌ 失败'}")
    
    if basic_test_passed and concurrent_test_passed:
        print("\n🎉 所有测试通过！GUI内置的07号接口接收端工作正常。")
    else:
        print("\n⚠️  部分测试失败，请检查GUI程序和服务状态。")
    
    print("\n使用说明:")
    print("1. 启动GUI程序后，07号接口接收端会自动启动")
    print("2. 天健云可以向 http://your-server:5007/dx/inter/receiveConclusion 发送总检信息")
    print("3. 在GUI的日志区域可以看到接收和处理的详细信息")
    print("4. 状态栏会显示07号接口的运行状态")
    print("=" * 80)

if __name__ == "__main__":
    main()
