#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试天健云07号接口接收端
"""

import json
import requests
import hashlib
import uuid
from datetime import datetime

def create_test_headers(api_key: str, mic_code: str, misc_id: str) -> dict:
    """创建测试请求头"""
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    nonce = str(uuid.uuid4())
    sign_string = api_key + timestamp
    sign = hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    return {
        'Content-Type': 'application/json',
        'sign': sign,
        'timestamp': timestamp,
        'nonce': nonce,
        'mic-code': mic_code,
        'misc-id': misc_id
    }

def create_test_data() -> dict:
    """创建测试数据"""
    return {
        "hospital": {
            "code": "MIC1.001E",
            "name": "测试医院"
        },
        "peNo": "0825748754",
        "firstCheckFinishTime": "2025-07-22 14:30:00",
        "firstCheckFinishDoctor": {
            "code": "DOC001",
            "name": "张医生"
        },
        "mainCheckFinishTime": "2025-07-22 15:30:00",
        "mainCheckFinishDoctor": {
            "code": "DOC002",
            "name": "李主任"
        },
        "conclusionList": [
            {
                "conclusionName": "血压偏高",
                "conclusionCode": "BP001",
                "parentCode": "CARDIO",
                "suggest": "建议低盐饮食，适量运动",
                "explain": "收缩压超过正常范围",
                "checkResult": "收缩压150mmHg，舒张压95mmHg",
                "level": 2,
                "displaySequnce": 1
            },
            {
                "conclusionName": "血脂异常",
                "conclusionCode": "LIPID001",
                "parentCode": "LAB",
                "suggest": "建议控制饮食，定期复查",
                "explain": "总胆固醇和低密度脂蛋白胆固醇升高",
                "checkResult": "总胆固醇6.2mmol/L，LDL-C 4.1mmol/L",
                "level": 2,
                "displaySequnce": 2
            },
            {
                "conclusionName": "肝功能正常",
                "conclusionCode": "LIVER001",
                "parentCode": "LAB",
                "suggest": "继续保持良好生活习惯",
                "explain": "肝功能各项指标均在正常范围内",
                "checkResult": "ALT 25U/L，AST 28U/L，TBIL 12μmol/L",
                "level": 3,
                "displaySequnce": 3
            }
        ],
        "currentNodeType": 4
    }

def test_receive_conclusion(base_url: str = "http://localhost:5007"):
    """测试接收总检信息接口"""
    print("=" * 60)
    print("测试天健云07号接口接收端")
    print("=" * 60)
    
    # 创建测试数据
    test_data = create_test_data()
    
    # 创建请求头（如果需要签名验证）
    headers = create_test_headers(
        api_key="3CNVizIjUq87IrczWqQB8SxjvPmVMTKM",
        mic_code="MIC1.001E",
        misc_id="MISC1.00001A"
    )
    
    print(f"1. 发送测试数据到: {base_url}/dx/inter/receiveConclusion")
    print(f"   体检号: {test_data['peNo']}")
    print(f"   医院: {test_data['hospital']['name']} ({test_data['hospital']['code']})")
    print(f"   结论数量: {len(test_data['conclusionList'])}")
    
    try:
        # 发送请求
        response = requests.post(
            f"{base_url}/dx/inter/receiveConclusion",
            headers=headers,
            json=test_data,
            timeout=30
        )
        
        print(f"\n2. 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("3. 响应内容:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
            if result.get('success'):
                print(f"\n[SUCCESS] 总检信息接收成功")
                print(f"   更新记录数: {result.get('data', {}).get('updated_records', 0)}")
                print(f"   结论数量: {result.get('data', {}).get('conclusion_count', 0)}")
            else:
                print(f"\n[FAIL] 总检信息接收失败")
                print(f"   错误码: {result.get('code')}")
                print(f"   错误信息: {result.get('error')}")
        else:
            print("3. 响应内容:")
            print(response.text)
            print(f"\n[FAIL] HTTP请求失败，状态码: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print(f"\n[ERROR] 连接失败，请确保接收端服务已启动")
        print(f"   启动命令: python interface_07_receiveConclusion.py")
    except Exception as e:
        print(f"\n[ERROR] 请求异常: {e}")

def test_health_check(base_url: str = "http://localhost:5007"):
    """测试健康检查接口"""
    print("\n" + "=" * 60)
    print("测试健康检查接口")
    print("=" * 60)
    
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("响应内容:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            print("[SUCCESS] 健康检查通过")
        else:
            print(f"[FAIL] 健康检查失败，状态码: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("[ERROR] 连接失败，服务未启动")
    except Exception as e:
        print(f"[ERROR] 请求异常: {e}")

def test_invalid_data(base_url: str = "http://localhost:5007"):
    """测试无效数据处理"""
    print("\n" + "=" * 60)
    print("测试无效数据处理")
    print("=" * 60)
    
    # 测试空数据
    print("1. 测试空数据")
    try:
        response = requests.post(
            f"{base_url}/dx/inter/receiveConclusion",
            json={},
            timeout=10
        )
        print(f"   状态码: {response.status_code}")
        if response.status_code == 400:
            result = response.json()
            print(f"   错误信息: {result.get('error')}")
            print("   [SUCCESS] 空数据处理正确")
        else:
            print("   [FAIL] 空数据处理异常")
    except Exception as e:
        print(f"   [ERROR] 请求异常: {e}")
    
    # 测试缺少体检号
    print("\n2. 测试缺少体检号")
    try:
        invalid_data = {
            "hospital": {"code": "TEST", "name": "测试医院"},
            "conclusionList": []
        }
        response = requests.post(
            f"{base_url}/dx/inter/receiveConclusion",
            json=invalid_data,
            timeout=10
        )
        print(f"   状态码: {response.status_code}")
        if response.status_code == 400:
            result = response.json()
            print(f"   错误信息: {result.get('error')}")
            print("   [SUCCESS] 缺少体检号处理正确")
        else:
            print("   [FAIL] 缺少体检号处理异常")
    except Exception as e:
        print(f"   [ERROR] 请求异常: {e}")

def main():
    """主函数"""
    print("天健云07号接口接收端测试")
    print("=" * 80)
    
    base_url = "http://localhost:5007"
    
    # 测试健康检查
    test_health_check(base_url)
    
    # 测试接收总检信息
    test_receive_conclusion(base_url)
    
    # 测试无效数据处理
    test_invalid_data(base_url)
    
    print("\n" + "=" * 80)
    print("测试完成")
    print("\n使用说明:")
    print("1. 启动接收端服务: python interface_07_receiveConclusion.py")
    print("2. 运行测试脚本: python test_interface_07_receiver.py")
    print("3. 天健云可以向 http://your-server:5007/dx/inter/receiveConclusion 发送总检信息")

if __name__ == "__main__":
    main()
