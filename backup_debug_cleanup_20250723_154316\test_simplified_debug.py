#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化后的调试日志功能
验证只在出错时显示详细信息，正常情况下保持简洁
"""

import requests
import json
from datetime import datetime

def test_normal_case():
    """测试正常情况（不应该有详细调试信息）"""
    
    url = "http://localhost:5007/dx/inter/receiveConclusion"
    
    # 使用正常长度的测试数据
    test_data = {
        "hospital": {
            "code": "09",
            "name": "测试医院"
        },
        "peNo": "5000006",
        "firstCheckFinishTime": "2025-07-23 10:30:00",
        "firstCheckFinishDoctor": {
            "code": "DOC001",
            "name": "张医生",
            "synonyms": None,
            "zero": None
        },
        "mainCheckFinishTime": "2025-07-23 11:00:00",
        "mainCheckFinishDoctor": {
            "code": "DOC002", 
            "name": "李主任",
            "synonyms": None,
            "zero": None
        },
        "currentNodeType": 4,
        "conclusionList": [
            {
                "mappingId": "MAP001",
                "conclusionName": "血压正常",
                "conclusionCode": "BP002",
                "parentCode": "CARDIO",  # 正常长度
                "suggest": "继续保持健康生活方式",
                "explain": "血压在正常范围内",
                "checkResult": "收缩压120mmHg，舒张压80mmHg",
                "level": 3,
                "displaySequnce": 1,
                "childrenCode": ["BP002_1"],
                "deptId": "DEPT01",
                "abnormalLevel": 3
            }
        ]
    }
    
    print("测试正常情况（应该简洁输出）")
    print("=" * 50)
    
    try:
        headers = {
            'Content-Type': 'application/json',
            'mic-code': '09',
            'misc-id': 'NORMAL_TEST'
        }
        
        response = requests.post(url, json=test_data, headers=headers, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 正常情况测试成功")
                print("GUI日志应该显示简洁的成功信息，无详细调试")
            else:
                print(f"❌ 处理失败: {result.get('error')}")
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def test_error_case():
    """测试错误情况（应该显示详细调试信息）"""
    
    url = "http://localhost:5007/dx/inter/receiveConclusion"
    
    # 使用超长字段的测试数据来触发错误
    test_data = {
        "hospital": {
            "code": "09",
            "name": "测试医院"
        },
        "peNo": "5000003",
        "firstCheckFinishTime": "2025-07-23 10:30:00",
        "firstCheckFinishDoctor": {
            "code": "DOC001",
            "name": "张医生",
            "synonyms": None,
            "zero": None
        },
        "mainCheckFinishTime": "2025-07-23 11:00:00",
        "mainCheckFinishDoctor": {
            "code": "DOC002", 
            "name": "李主任",
            "synonyms": None,
            "zero": None
        },
        "currentNodeType": 4,
        "conclusionList": [
            {
                "mappingId": "MAP002",
                "conclusionName": "这是一个非常长的结论名称用来测试字符串截断错误的情况，这个名称故意设计得很长以便触发数据库字段长度限制错误",  # 超长结论名称
                "conclusionCode": "VERY_LONG_CODE_123456789",  # 超长结论编码
                "parentCode": "VERY_LONG_PARENT_CODE_123456789",  # 超长父类编码
                "suggest": "这是一个非常详细的建议内容，包含了大量的医学建议和生活指导，目的是测试字段长度限制" * 5,  # 超长建议
                "explain": "这是一个非常详细的医学解释内容，包含了大量的专业术语和医学知识" * 5,  # 超长解释
                "checkResult": "这是一个非常详细的检查结果描述" * 10,  # 超长检查结果
                "level": 1,
                "displaySequnce": 1,
                "childrenCode": ["CHILD001", "CHILD002"],
                "deptId": "VERY_LONG_DEPT_ID_123456789",  # 超长科室ID
                "abnormalLevel": 1
            }
        ]
    }
    
    print("\n测试错误情况（应该显示详细调试信息）")
    print("=" * 50)
    
    try:
        headers = {
            'Content-Type': 'application/json',
            'mic-code': '09',
            'misc-id': 'ERROR_TEST'
        }
        
        response = requests.post(url, json=test_data, headers=headers, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 请求成功（可能部分字段被截断）")
            else:
                print(f"❌ 处理失败: {result.get('error')}")
        
        print("GUI日志应该显示:")
        print("- 错误信息")
        print("- 字段长度检查")
        print("- 超出限制的字段标记为 ❌")
        print("- 接近限制的字段标记为 ⚠️")
        print("- 正常字段标记为 ✅")
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def test_childrencode_handling():
    """测试childrenCode字段处理"""
    
    print("\n测试childrenCode字段处理")
    print("=" * 50)
    
    test_cases = [
        {
            "name": "数组格式",
            "childrenCode": ["CHILD001", "CHILD002", "CHILD003"]
        },
        {
            "name": "空数组",
            "childrenCode": []
        },
        {
            "name": "null值",
            "childrenCode": None
        }
    ]
    
    for case in test_cases:
        print(f"测试 {case['name']}: {case['childrenCode']}")
    
    print("这些情况都应该被正确处理，不会导致错误")

if __name__ == "__main__":
    print("简化调试日志功能测试")
    print("=" * 60)
    print("目的：验证正常情况下日志简洁，错误时显示详细信息")
    print("=" * 60)
    
    # 测试正常情况
    test_normal_case()
    
    # 等待一下
    import time
    time.sleep(2)
    
    # 测试错误情况
    test_error_case()
    
    # 测试childrenCode处理
    test_childrencode_handling()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("现在GUI日志应该:")
    print("✅ 正常情况：简洁输出，无冗余调试信息")
    print("❌ 错误情况：详细的字段长度分析和问题定位")
    print("🔍 字段状态：清晰的长度检查和限制对比")
    print("=" * 60)
