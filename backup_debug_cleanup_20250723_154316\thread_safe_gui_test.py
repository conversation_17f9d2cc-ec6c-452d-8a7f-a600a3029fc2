#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
线程安全的GUI测试，包含07号接口接收端
"""

import sys
import threading
from datetime import datetime
from flask import Flask, request, jsonify
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QTextEdit
from PySide6.QtCore import QTimer, QObject, Signal

class ThreadSafeLogWidget:
    """线程安全的日志组件"""
    
    def __init__(self, text_edit):
        self.text_edit = text_edit
    
    def add_log(self, level, message):
        """添加日志（线程安全）"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {level}: {message}"
        
        # 在主线程中更新GUI
        QTimer.singleShot(0, lambda: self.text_edit.append(log_message))
        print(log_message)  # 同时输出到控制台

class Interface07SignalEmitter(QObject):
    """07号接口信号发射器 - 用于线程间通信"""
    
    # 定义信号
    log_signal = Signal(str, str)  # level, message
    status_signal = Signal(str, str)  # text, color

class ThreadSafe07Receiver:
    """线程安全的07号接口接收端"""
    
    def __init__(self):
        self.signal_emitter = Interface07SignalEmitter()
        self.app = Flask(__name__)
        self.server_thread = None
        self.is_running = False
        self.setup_routes()
    
    def setup_routes(self):
        """设置路由"""
        @self.app.route('/dx/inter/receiveConclusion', methods=['POST'])
        def receive_conclusion():
            try:
                data = request.get_json()
                if not data:
                    self.signal_emitter.log_signal.emit("错误", "请求数据为空")
                    return jsonify({'success': False, 'error': '请求数据为空'}), 400
                
                pe_no = data.get('peNo', 'Unknown')
                hospital = data.get('hospital', {})
                hospital_name = hospital.get('name', 'Unknown')
                conclusion_count = len(data.get('conclusionList', []))
                
                self.signal_emitter.log_signal.emit("接口调用", f"收到总检信息: 体检号={pe_no}, 医院={hospital_name}, 结论数={conclusion_count}")
                
                # 简单的成功响应
                return jsonify({
                    'success': True,
                    'message': '总检信息接收成功',
                    'code': 0,
                    'data': {
                        'peNo': pe_no,
                        'updated_records': 1,
                        'conclusion_count': conclusion_count
                    }
                }), 200
                
            except Exception as e:
                error_msg = f"处理异常: {str(e)}"
                self.signal_emitter.log_signal.emit("错误", error_msg)
                return jsonify({'success': False, 'error': error_msg}), 500
        
        @self.app.route('/health', methods=['GET'])
        def health_check():
            return jsonify({
                'status': 'healthy',
                'service': '线程安全07号接口接收端',
                'timestamp': datetime.now().isoformat()
            })
    
    def start_service(self):
        """启动服务"""
        if self.is_running:
            return
        
        def run_server():
            try:
                self.signal_emitter.log_signal.emit("信息", "07号接口接收端启动中...")
                self.signal_emitter.log_signal.emit("信息", "监听端口: 5007")
                self.signal_emitter.status_signal.emit("07号接口: 运行中", "green")
                
                self.app.run(
                    host='0.0.0.0',
                    port=5007,
                    debug=False,
                    threaded=True,
                    use_reloader=False
                )
            except Exception as e:
                self.signal_emitter.log_signal.emit("错误", f"服务启动失败: {e}")
                self.signal_emitter.status_signal.emit("07号接口: 启动失败", "red")
        
        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
        self.is_running = True
        
        # 延迟显示启动成功消息
        QTimer.singleShot(1000, lambda: self.signal_emitter.log_signal.emit("信息", "07号接口接收端启动成功"))

class ThreadSafeMainWindow(QMainWindow):
    """线程安全主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("线程安全GUI测试 - 07号接口接收端")
        self.setFixedSize(800, 600)
        
        # 设置中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("线程安全07号接口接收端测试")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 状态标签
        self.status_label = QLabel("07号接口: 启动中...")
        self.status_label.setStyleSheet("color: orange; font-weight: bold; margin: 5px;")
        layout.addWidget(self.status_label)
        
        # 日志显示
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        # 初始化日志组件
        self.log_widget = ThreadSafeLogWidget(self.log_text)
        
        # 初始化07号接口接收端
        self.receiver = ThreadSafe07Receiver()
        
        # 连接信号槽
        self.receiver.signal_emitter.log_signal.connect(self.log_widget.add_log)
        self.receiver.signal_emitter.status_signal.connect(self.update_status)
        
        # 延迟启动服务
        QTimer.singleShot(1000, self.start_receiver)
        
        self.log_widget.add_log("信息", "GUI初始化完成")
    
    def update_status(self, text: str, color: str):
        """更新状态显示"""
        self.status_label.setText(text)
        self.status_label.setStyleSheet(f"color: {color}; font-weight: bold; margin: 5px;")
    
    def start_receiver(self):
        """启动接收端服务"""
        try:
            self.receiver.start_service()
        except Exception as e:
            self.log_widget.add_log("错误", f"启动接收端失败: {e}")
    
    def closeEvent(self, event):
        """关闭事件"""
        self.log_widget.add_log("信息", "程序正在关闭...")
        event.accept()

def main():
    """主函数"""
    print("启动线程安全GUI测试...")
    
    app = QApplication(sys.argv)
    app.setApplicationName("线程安全GUI测试")
    
    window = ThreadSafeMainWindow()
    window.show()
    
    print("GUI窗口已显示，07号接口接收端将在1秒后启动...")
    print("测试方法:")
    print("1. 等待服务启动（状态变为绿色）")
    print("2. 访问 http://localhost:5007/health 测试健康检查")
    print("3. POST数据到 http://localhost:5007/dx/inter/receiveConclusion 测试接收功能")
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
