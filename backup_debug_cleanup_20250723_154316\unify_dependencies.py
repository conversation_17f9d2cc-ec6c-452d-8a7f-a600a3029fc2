#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖管理工具 - 统一项目依赖
"""

import os
import shutil
from pathlib import Path

def backup_old_requirements():
    """备份旧的requirements文件"""
    print("备份旧的requirements文件...")
    
    # 备份根目录的requirements.txt
    root_req = Path("requirements.txt")
    if root_req.exists():
        backup_path = Path("requirements_backup_root.txt")
        shutil.copy2(root_req, backup_path)
        print(f"  已备份: {root_req} -> {backup_path}")
    
    # 备份health_sync目录的requirements.txt
    health_req = Path("health_sync/requirements.txt")
    if health_req.exists():
        backup_path = Path("requirements_backup_health_sync.txt")
        shutil.copy2(health_req, backup_path)
        print(f"  已备份: {health_req} -> {backup_path}")

def create_unified_requirements():
    """创建统一的requirements.txt"""
    print("\n创建统一的requirements.txt...")
    
    unified_content = """# 天健云AI对接项目 - 统一依赖管理
# 福能AI对接系统 - 完整依赖包列表

# ===== 核心依赖 =====
sqlalchemy>=1.4.53,<2.0.0
pyodbc>=5.0.1
requests>=2.31.0
urllib3>=2.0.7,<3.0.0

# ===== 数据处理 =====
pandas>=2.0.3
pydantic>=1.10.15,<2.0.0

# ===== 配置管理 =====
PyYAML>=6.0.1
python-dotenv>=1.0.0

# ===== 日志系统 =====
loguru>=0.7.2

# ===== 测试框架 =====
pytest>=7.4.4
pytest-cov>=4.1.0
pytest-mock>=3.12.0

# ===== 开发工具 =====
black>=23.12.1
isort>=5.13.2
flake8>=6.1.0,<8.0.0

# ===== 命令行工具 =====
click>=8.1.7
tqdm>=4.66.1

# ===== 调度和网络 =====
APScheduler>=3.10.4
certifi>=2023.11.17
cryptography>=41.0.8

# ===== 工具库 =====
python-dateutil>=2.8.2
typing-extensions>=4.9.0
colorama>=0.4.6

# ===== 可选依赖 (手动安装) =====
# PySide6>=6.4.3              # GUI框架
# PyInstaller>=6.3.0          # 打包工具

# Python版本要求: 3.8+
# ODBC驱动要求: ODBC Driver 17 for SQL Server
"""
    
    with open("requirements.txt", "w", encoding="utf-8") as f:
        f.write(unified_content)
    
    print("  已创建: requirements.txt")

def create_dev_requirements():
    """创建开发环境专用requirements"""
    print("\n创建开发环境requirements...")
    
    dev_content = """# 开发环境专用依赖
-r requirements.txt

# ===== 额外开发工具 =====
mypy>=1.0.0                  # 类型检查
pre-commit>=3.0.0            # Git hooks
coverage>=7.0.0              # 代码覆盖率
bandit>=1.7.0                # 安全检查

# ===== 文档工具 =====
sphinx>=6.0.0               # 文档生成
sphinx-rtd-theme>=1.2.0     # 文档主题

# ===== 调试工具 =====
ipython>=8.0.0              # 交互式Python
ipdb>=0.13.0                # 调试器
"""
    
    with open("requirements-dev.txt", "w", encoding="utf-8") as f:
        f.write(dev_content)
    
    print("  已创建: requirements-dev.txt")

def create_gui_requirements():
    """创建GUI环境requirements"""
    print("\n创建GUI环境requirements...")
    
    gui_content = """# GUI环境专用依赖
-r requirements.txt

# ===== GUI框架 =====
PySide6>=6.4.3              # Qt6 Python绑定

# ===== 图表和可视化 =====
matplotlib>=3.6.0           # 图表库
plotly>=5.0.0               # 交互式图表

# ===== 系统托盘 =====
plyer>=2.1.0                # 跨平台通知
"""
    
    with open("requirements-gui.txt", "w", encoding="utf-8") as f:
        f.write(gui_content)
    
    print("  已创建: requirements-gui.txt")

def clean_old_health_sync_requirements():
    """清理health_sync目录的requirements.txt"""
    health_req = Path("health_sync/requirements.txt")
    if health_req.exists():
        health_req.unlink()
        print(f"\n已删除: {health_req}")
        print("  (依赖已合并到根目录requirements.txt)")

def show_installation_guide():
    """显示安装指南"""
    guide = """
📋 安装指南
============================================================

1. 基础环境安装 (生产环境):
   pip install -r requirements.txt

2. 开发环境安装:
   pip install -r requirements-dev.txt

3. GUI环境安装:
   pip install -r requirements-gui.txt

4. 最小化安装 (仅核心功能):
   pip install sqlalchemy>=1.4.53 pyodbc>=5.0.1 requests>=2.31.0 PyYAML>=6.0.1 loguru>=0.7.2

5. 系统要求:
   - Python 3.8+
   - Windows: ODBC Driver 17 for SQL Server
   - Linux/macOS: unixODBC + SQL Server ODBC Driver

6. 验证安装:
   python main.py status

============================================================
"""
    print(guide)

def main():
    """主函数"""
    print("天健云AI对接项目 - 依赖管理工具")
    print("="*50)
    
    try:
        # 1. 备份旧文件
        backup_old_requirements()
        
        # 2. 创建新的统一requirements
        create_unified_requirements()
        create_dev_requirements()
        create_gui_requirements()
        
        # 3. 清理旧文件
        clean_old_health_sync_requirements()
        
        # 4. 显示安装指南
        show_installation_guide()
        
        print("✅ 依赖管理统一完成！")
        print("\n📝 备份文件说明:")
        print("  - requirements_backup_root.txt: 原根目录requirements.txt的备份")
        print("  - requirements_backup_health_sync.txt: 原health_sync/requirements.txt的备份")
        
    except Exception as e:
        print(f"❌ 依赖管理统一失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)