#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量更新所有文件的数据库配置，统一使用config.py
"""

import os
import re
from pathlib import Path

def update_file_config(file_path: str):
    """更新单个文件的配置"""
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 1. 添加config导入（如果还没有）
        if 'from config import Config' not in content and 'import config' not in content:
            # 找到其他import语句的位置
            import_lines = []
            lines = content.split('\n')
            
            for i, line in enumerate(lines):
                if line.strip().startswith('import ') or line.strip().startswith('from '):
                    import_lines.append(i)
            
            if import_lines:
                # 在最后一个import语句后添加config导入
                last_import_line = max(import_lines)
                lines.insert(last_import_line + 1, 'from config import Config')
                content = '\n'.join(lines)
        
        # 2. 替换硬编码的数据库连接字符串
        patterns_to_replace = [
            # 完整连接字符串
            (r'DRIVER=\{[^}]+\};SERVER=[^;]+;DATABASE=[^;]+;UID=[^;]+;PWD=[^;]+', 
             'Config.get_interface_db_connection_string()'),
            
            # 分别的配置项
            (r'["\']27\.188\.65\.76["\']', '${INTERFACE_DB_HOST:-************}'),
            (r'["\']81\.70\.17\.88["\']', '${INTERFACE_DB_HOST:-***********}'),
            (r'["\']2025znzj/888["\']', '${INTERFACE_DB_PASSWORD:-2025znzj/888}'),
            (r'["\']jiarentijian["\']', '${INTERFACE_DB_PASSWORD:-jiarentijian}'),
            (r'["\']znzj["\']', '${INTERFACE_DB_USER:-znzj}'),
            (r'["\']tj["\']', '${INTERFACE_DB_USER:-tj}'),
            (r'["\']Examdb["\']', '${INTERFACE_DB_NAME:-Examdb}'),
            (r'["\']examdb["\']', '${INTERFACE_DB_NAME:-examdb}'),
            (r'["\']examdb_center["\']', '${INTERFACE_DB_NAME:-examdb_center}'),
        ]
        
        for pattern, replacement in patterns_to_replace:
            content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
        
        # 3. 特殊处理：替换connection_string变量赋值
        connection_string_patterns = [
            r'connection_string\s*=\s*["\'][^"\']*DRIVER[^"\']*["\']',
            r'CONNECTION_STRING\s*=\s*["\'][^"\']*DRIVER[^"\']*["\']',
        ]
        
        for pattern in connection_string_patterns:
            content = re.sub(pattern, 'connection_string = Config.get_interface_db_connection_string()', content, flags=re.IGNORECASE)
        
        # 4. 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"更新文件 {file_path} 失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 批量更新数据库配置...")
    print("=" * 60)
    
    # 要处理的文件类型
    file_extensions = ['.py']
    
    # 要排除的目录
    exclude_dirs = {'__pycache__', '.git', 'logs', 'venv', 'env', '.pytest_cache'}
    
    # 要排除的文件
    exclude_files = {'update_all_configs.py', 'verify_config_usage.py', 'config.py'}
    
    updated_files = []
    skipped_files = []
    
    # 遍历项目文件
    for root, dirs, files in os.walk('.'):
        # 排除特定目录
        dirs[:] = [d for d in dirs if d not in exclude_dirs]
        
        for file in files:
            if any(file.endswith(ext) for ext in file_extensions) and file not in exclude_files:
                file_path = os.path.join(root, file)
                
                # 检查文件是否包含数据库相关内容
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                    
                    # 只处理包含数据库相关内容的文件
                    if any(keyword in content.lower() for keyword in [
                        'database', 'connection_string', 'driver=', 'server=', 'uid=', 'pwd=',
                        '************', '***********', '2025znzj', 'jiarentijian', 'examdb'
                    ]):
                        if update_file_config(file_path):
                            updated_files.append(file_path)
                            print(f"✅ 已更新: {file_path}")
                        else:
                            skipped_files.append(file_path)
                            print(f"⏭️  跳过: {file_path} (无需更新)")
                    
                except Exception as e:
                    print(f"❌ 处理文件 {file_path} 时出错: {e}")
    
    print("\n📊 更新结果:")
    print(f"  已更新文件: {len(updated_files)} 个")
    print(f"  跳过文件: {len(skipped_files)} 个")
    
    if updated_files:
        print("\n✅ 已更新的文件:")
        for file_path in updated_files:
            print(f"  📁 {file_path}")
    
    print("\n🎉 配置更新完成！")
    print("💡 建议运行 'python verify_config_usage.py' 验证更新结果")

if __name__ == '__main__':
    main()
