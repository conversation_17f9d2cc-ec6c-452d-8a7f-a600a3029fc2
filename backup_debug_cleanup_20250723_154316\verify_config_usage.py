#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证所有数据库连接都使用统一的config.py配置
"""

import os
import re
from pathlib import Path

def find_hardcoded_db_configs():
    """查找硬编码的数据库配置"""
    
    # 要检查的文件扩展名
    extensions = ['.py', '.yaml', '.yml']
    
    # 要排除的目录
    exclude_dirs = {'__pycache__', '.git', 'logs', 'venv', 'env'}
    
    # 硬编码模式
    patterns = [
        r'27\.188\.65\.76',  # 旧的IP地址
        r'81\.70\.17\.88',   # 新的IP地址
        r'2025znzj/888',     # 旧密码
        r'jiarentijian',     # 新密码
        r'username.*znzj',   # 用户名
        r'username.*tj',     # 用户名
        r'server.*=.*[\'"][\d\.]+[\'"]',  # 服务器配置
        r'password.*=.*[\'"][^\'\"]+[\'"]',  # 密码配置
    ]
    
    compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in patterns]
    
    results = []
    
    # 遍历项目文件
    for root, dirs, files in os.walk('.'):
        # 排除特定目录
        dirs[:] = [d for d in dirs if d not in exclude_dirs]
        
        for file in files:
            if any(file.endswith(ext) for ext in extensions):
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        
                    for i, line in enumerate(content.split('\n'), 1):
                        for pattern in compiled_patterns:
                            if pattern.search(line):
                                results.append({
                                    'file': file_path,
                                    'line': i,
                                    'content': line.strip(),
                                    'pattern': pattern.pattern
                                })
                                
                except Exception as e:
                    print(f"无法读取文件 {file_path}: {e}")
    
    return results

def check_config_imports():
    """检查哪些文件正确导入了config.py"""
    
    python_files = []
    for root, dirs, files in os.walk('.'):
        dirs[:] = [d for d in dirs if d not in {'__pycache__', '.git', 'logs', 'venv', 'env'}]
        
        for file in files:
            if file.endswith('.py') and not file.startswith('verify_'):
                file_path = os.path.join(root, file)
                python_files.append(file_path)
    
    config_imports = []
    no_config_imports = []
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 检查是否导入了config
            if re.search(r'from config import|import config', content):
                config_imports.append(file_path)
            elif 'database' in content.lower() or 'db_' in content.lower():
                # 只有涉及数据库的文件才需要导入config
                no_config_imports.append(file_path)
                
        except Exception as e:
            print(f"无法读取文件 {file_path}: {e}")
    
    return config_imports, no_config_imports

def main():
    print("🔍 检查硬编码数据库配置...")
    print("=" * 60)
    
    # 查找硬编码配置
    hardcoded_configs = find_hardcoded_db_configs()
    
    if hardcoded_configs:
        print("❌ 发现硬编码数据库配置:")
        for config in hardcoded_configs:
            print(f"  📁 {config['file']}:{config['line']}")
            print(f"     {config['content']}")
            print(f"     匹配模式: {config['pattern']}")
            print()
    else:
        print("✅ 未发现硬编码数据库配置")
    
    print("\n🔍 检查config.py导入情况...")
    print("=" * 60)
    
    # 检查config导入
    config_imports, no_config_imports = check_config_imports()
    
    print(f"✅ 已导入config.py的文件 ({len(config_imports)}个):")
    for file_path in config_imports:
        print(f"  📁 {file_path}")
    
    if no_config_imports:
        print(f"\n⚠️  涉及数据库但未导入config.py的文件 ({len(no_config_imports)}个):")
        for file_path in no_config_imports:
            print(f"  📁 {file_path}")
    
    print("\n📊 统计结果:")
    print(f"  硬编码配置: {len(hardcoded_configs)} 个")
    print(f"  已使用统一配置: {len(config_imports)} 个文件")
    print(f"  需要检查: {len(no_config_imports)} 个文件")
    
    if len(hardcoded_configs) == 0 and len(no_config_imports) == 0:
        print("\n🎉 所有数据库连接已统一使用config.py配置！")
    else:
        print("\n⚠️  仍有部分文件需要修改")

if __name__ == '__main__':
    main()
