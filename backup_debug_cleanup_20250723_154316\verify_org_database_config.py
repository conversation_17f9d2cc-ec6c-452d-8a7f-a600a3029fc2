#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证机构数据库配置
"""

def verify_org_database_config():
    """验证机构数据库配置"""
    try:
        print("="*80)
        print("验证机构数据库配置")
        print("="*80)
        
        from center_organization_service import get_center_organization_service
        center_service = get_center_organization_service()
        
        # 1. 检查08机构的详细配置
        print("1. 检查08机构的详细配置:")
        org_08 = center_service.get_organization_by_code('08')
        
        if org_08:
            print(f"   机构编码: {org_08.org_code}")
            print(f"   机构名称: {org_08.org_name}")
            print(f"   机构类型: {org_08.org_type}")
            print(f"   状态: {org_08.status}")
            print(f"   数据库主机: {org_08.db_host}")
            print(f"   数据库端口: {org_08.db_port}")
            print(f"   数据库名称: {org_08.db_name}")
            print(f"   数据库用户: {org_08.db_user}")
            print(f"   创建时间: {org_08.create_time}")
            print(f"   更新时间: {org_08.update_time}")
        else:
            print("   ❌ 找不到08机构配置")
            return False
        
        # 2. 分析数据库中的数据分布
        print(f"\n2. 分析数据库中的数据分布:")
        
        from optimized_database_service import create_optimized_db_service
        
        connection_string = (
            f"DRIVER={{ODBC Driver 17 for SQL Server}};"
            f"SERVER={org_08.db_host},{org_08.db_port};"
            f"DATABASE={org_08.db_name};"
            f"UID={org_08.db_user};"
            f"PWD={org_08.db_password}"
        )
        
        db_service = create_optimized_db_service(connection_string)
        
        # 检查数据总量
        total_sql = "SELECT COUNT(*) as total FROM T_Register_Main"
        total_result = db_service.connection_manager.execute_query_with_cache(
            connection_string,
            total_sql,
            cache_key="total_records_verify",
            use_cache=False
        )
        
        if total_result:
            total_count = total_result[0]['total']
            print(f"   总记录数: {total_count}")
        
        # 检查客户编码分布
        code_pattern_sql = """
        SELECT 
            LEFT(cClientCode, 2) as code_prefix,
            COUNT(*) as count
        FROM T_Register_Main
        WHERE cClientCode IS NOT NULL AND cClientCode != ''
        GROUP BY LEFT(cClientCode, 2)
        ORDER BY count DESC
        """
        
        code_result = db_service.connection_manager.execute_query_with_cache(
            connection_string,
            code_pattern_sql,
            cache_key="code_pattern_verify",
            use_cache=False
        )
        
        if code_result:
            print(f"   客户编码前缀分布:")
            for row in code_result[:10]:  # 显示前10个
                print(f"     {row['code_prefix']}**: {row['count']} 条")
        
        # 检查刘俊华的详细信息
        liujunhua_sql = """
        SELECT TOP 5
            cClientCode,
            cName,
            dAffirmdate,
            cIdCard
        FROM T_Register_Main
        WHERE cName = '刘俊华'
        ORDER BY dAffirmdate DESC
        """
        
        liujunhua_result = db_service.connection_manager.execute_query_with_cache(
            connection_string,
            liujunhua_sql,
            cache_key="liujunhua_detail",
            use_cache=False
        )
        
        if liujunhua_result:
            print(f"\n   刘俊华的详细信息 (前5条):")
            for i, record in enumerate(liujunhua_result, 1):
                print(f"     {i}. {record['cClientCode']} - {record['cIdCard']} - {record['dAffirmdate']}")
        
        # 3. 检查是否有其他可能的数据库配置
        print(f"\n3. 检查是否需要修正数据库配置:")
        
        print(f"   当前配置分析:")
        print(f"   - 数据库: {org_08.db_host}:{org_08.db_port}/{org_08.db_name}")
        print(f"   - 总记录数: {total_count}")
        print(f"   - 包含刘俊华: 19条记录")
        
        print(f"\n   可能的情况:")
        print(f"   1. ************ 是正确的数据库，包含多个机构的数据")
        print(f"   2. 需要通过其他字段（如机构编码）来过滤东区数据")
        print(f"   3. 08机构的数据库配置需要修正")
        
        # 4. 检查是否有机构字段可以过滤
        print(f"\n4. 检查是否有机构字段可以过滤:")
        
        # 检查表结构中是否有机构相关字段
        columns_sql = """
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'T_Register_Main'
        AND (COLUMN_NAME LIKE '%org%' OR COLUMN_NAME LIKE '%机构%' OR COLUMN_NAME LIKE '%dept%' OR COLUMN_NAME LIKE '%shop%')
        """
        
        columns_result = db_service.connection_manager.execute_query_with_cache(
            connection_string,
            columns_sql,
            cache_key="org_columns",
            use_cache=False
        )
        
        if columns_result:
            print(f"   可能的机构相关字段:")
            for row in columns_result:
                print(f"     - {row['COLUMN_NAME']}")
        else:
            print(f"   没有找到明显的机构相关字段")
        
        return True
        
    except Exception as e:
        print(f"验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = verify_org_database_config()
    
    print("\n" + "="*80)
    print("验证结论:")
    print("="*80)
    
    if success:
        print("✅ 机构数据库配置验证完成")
        print("\n关键发现:")
        print("1. ************ 数据库包含多个机构的数据")
        print("2. 需要确认这是否是正确的配置")
        print("3. 可能需要通过其他方式过滤东区数据")
        
        print("\n建议:")
        print("1. 确认************是否是东区的正确数据库")
        print("2. 如果不是，需要修正08机构的数据库配置")
        print("3. 如果是，需要添加机构过滤条件")
    else:
        print("❌ 机构数据库配置验证失败")

if __name__ == "__main__":
    main()
