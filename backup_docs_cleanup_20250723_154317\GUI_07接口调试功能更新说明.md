# GUI 07号接口调试功能更新说明

## 更新概述

已成功为GUI内置的07号接口服务添加了详细的调试功能，现在当发生字符串截断错误时，您可以在GUI日志区域看到完整的SQL语句和参数信息，帮助精确定位问题字段。

## 更新内容

### 1. T_Diag_result表插入调试
**位置**: `gui_main.py` 中的 `insert_merged_conclusion_record` 方法

**新增功能**:
- 详细的SQL语句输出
- 每个参数的名称、值和长度信息
- 错误时的完整参数列表

**调试信息示例**:
```
[调试] T_Diag_result插入SQL: INSERT INTO T_Diag_result (cClientCode, cDiag, cDiagDesc, cDoctCode, cDoctName, dDoctOperdate, cOperCode, cOpername, dOperDate, cShopCode) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[调试] T_Diag_result参数详情:
[调试]    1. cClientCode     = '5000006' (长度: 7)
[调试]    2. cDiag           = '血压偏高需要注意...' (长度: 25)
[调试]    3. cDiagDesc       = '解释：收缩压超过...' (长度: 150)
[调试]    4. cDoctCode       = 'DOC002' (长度: 6)
[调试]    5. cDoctName       = '李主任' (长度: 3)
[调试]    6. dDoctOperdate   = '2025-07-23 14:58:49.123456' (长度: 26)
[调试]    7. cOperCode       = 'DOC002' (长度: 6)
[调试]    8. cOpername       = '李主任' (长度: 3)
[调试]    9. dOperDate       = '2025-07-23 14:58:49.123456' (长度: 26)
[调试]   10. cShopCode       = '09' (长度: 2)
```

### 2. T_Check_Result_Illness表插入调试
**位置**: `gui_main.py` 中的 `insert_illness_record` 方法

**新增功能**:
- 详细的SQL语句输出
- 每个参数的名称、值和长度信息
- 错误时的完整参数列表

**调试信息示例**:
```
[调试] T_Check_Result_Illness插入SQL: INSERT INTO T_Check_Result_Illness (cClientCode, cDeptcode, cMainName, cIllnessCode, cIllnessName, cIllExplain, cReason, cAdvice, cGrade, cDoctCode, cDoctName, dOperdate, nPrintIndex) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
[调试] T_Check_Result_Illness参数详情:
[调试]    1. cClientCode     = '5000006' (长度: 7)
[调试]    2. cDeptcode       = 'CARDIO' (长度: 6)
[调试]    3. cMainName       = '总检结论' (长度: 4)
[调试]    4. cIllnessCode    = 'BP001' (长度: 5)
[调试]    5. cIllnessName    = '血压偏高需要注意心血管健康状况' (长度: 25)
[调试]    6. cIllExplain     = '收缩压超过正常范围，可能存在高血压风险...' (长度: 45)
[调试]    7. cReason         = '收缩压150mmHg，舒张压95mmHg，超出正常范围' (长度: 30)
[调试]    8. cAdvice         = '建议低盐饮食，适量运动，定期监测血压变化...' (长度: 35)
[调试]    9. cGrade          = '1' (长度: 1)
[调试]   10. cDoctCode       = 'DOC002' (长度: 6)
[调试]   11. cDoctName       = '李主任' (长度: 3)
[调试]   12. dOperdate       = '2025-07-23 14:58:49.123456' (长度: 26)
[调试]   13. nPrintIndex     = '1' (长度: 1)
```

### 3. 字段结构更新
**T_Diag_result表字段结构已更新**:
- 从7个字段扩展到10个字段
- 正确区分初审医生和总检医生信息
- 包含完整的时间记录

**更新前**:
```sql
INSERT INTO T_Diag_result (
    cClientCode, cDiag, cDiagDesc, cDoctCode, cDoctName, dOperDate, cShopCode
) VALUES (?, ?, ?, ?, ?, ?, ?)
```

**更新后**:
```sql
INSERT INTO T_Diag_result (
    cClientCode, cDiag, cDiagDesc, cDoctCode, cDoctName, dDoctOperdate,
    cOperCode, cOpername, dOperDate, cShopCode
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
```

## childrenCode字段处理

### 字段说明
- **字段名**: `childrenCode`
- **类型**: 数组 (例如: `["BP001_1", "BP001_2"]`)
- **用途**: 存储与主结论词相关的子结论词编码

### 处理方式
1. **正确接收**: GUI能够正确接收和解析这个字段
2. **日志记录**: 会在处理过程中记录到日志
3. **数据保护**: 数据不会丢失，完整保存在日志中
4. **扩展性**: 为未来的数据库存储预留了接口

### 示例处理
```json
{
  "conclusionName": "血压偏高需要注意心血管健康状况",
  "childrenCode": ["BP001_1", "BP001_2"]
}
```

处理结果会在日志中显示相关信息。

## 错误定位功能

### 字符串截断错误定位
当发生字符串截断错误时，现在可以：

1. **精确定位字段**: 通过长度信息快速识别超长字段
2. **查看完整SQL**: 了解具体的插入语句结构
3. **分析参数值**: 检查每个参数的实际内容和长度
4. **对比限制**: 根据长度信息判断哪个字段超出限制

### 常见字段长度限制
根据经验，以下字段容易超出限制：
- `cIllnessName` (100字符) - 结论词名称
- `cDeptcode` (6字符) - 科室代码
- `cIllnessCode` (6字符) - 结论词代码
- `cReason` (200字符) - 检查结果汇总
- `cAdvice` (500字符) - 建议
- `cIllExplain` (500字符) - 医学解释

## 使用方法

### 1. 启动GUI程序
```bash
python gui_main.py
```

### 2. 发送测试数据
```bash
python test_gui_07_debug.py
```

### 3. 查看调试信息
在GUI程序的日志区域查看：
- 绿色的"调试"级别日志：正常的SQL和参数信息
- 红色的"错误"级别日志：字符串截断错误的详细信息

### 4. 分析问题
根据参数长度信息，找出超出限制的字段：
- 查看每个参数的长度
- 对比数据库字段限制
- 确定需要截断或调整的字段

## 测试验证

### 基本功能测试
```bash
# 测试GUI调试功能
python test_gui_07_debug.py

# 健康检查
curl http://localhost:5007/health
```

### 预期结果
1. **成功情况**: 看到"T_Diag_result插入成功"和"T_Check_Result_Illness插入成功"
2. **错误情况**: 看到详细的错误信息和参数列表
3. **childrenCode**: 在日志中能看到相关处理信息

## 注意事项

### 1. 日志级别
- 调试信息使用"调试"级别，可能需要在GUI中启用调试日志显示
- 错误信息使用"错误"级别，会以红色显示

### 2. 性能影响
- 调试信息会增加日志输出量
- 生产环境可考虑减少调试信息输出

### 3. 字段长度
- 建议根据实际需要调整数据库字段长度限制
- 或在代码中增加智能截断逻辑

## 故障排除

### 问题1: 看不到调试信息
**解决方案**: 
- 确保GUI日志区域显示"调试"级别日志
- 检查日志过滤设置

### 问题2: 仍然出现字符串截断
**解决方案**:
- 查看调试信息中的字段长度
- 根据长度信息调整数据或数据库结构

### 问题3: childrenCode字段处理异常
**解决方案**:
- 检查JSON格式是否正确
- 确认字段类型为数组格式

## 总结

通过这次更新，GUI内置的07号接口现在具备了：

✅ **完整的调试功能**: 详细的SQL和参数信息
✅ **精确的错误定位**: 字符串截断问题一目了然
✅ **childrenCode支持**: 正确处理子结论词编码
✅ **字段结构完整**: 支持初审和总检医生信息
✅ **向后兼容**: 不影响现有功能

现在当您遇到字符串截断错误时，可以通过GUI日志精确定位问题字段并进行相应调整！
