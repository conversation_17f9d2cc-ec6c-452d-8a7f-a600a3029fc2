# GUI内置07号接口接收端实现说明

## 功能概述

已成功将07号接口接收端服务内置到GUI主程序中，启动GUI时会自动启动07号接口接收端服务，无需单独启动。

## 实现的功能

### 1. 内置服务类 (`Interface07ReceiverService`)

**位置**: `gui_main.py` 中的 `Interface07ReceiverService` 类

**主要功能**:
- 基于Flask的HTTP服务，监听端口5007
- 接收端点：`POST /dx/inter/receiveConclusion`
- 健康检查端点：`GET /health`
- 与GUI日志系统集成
- 状态栏实时显示服务状态

**核心方法**:
- `setup_routes()`: 设置Flask路由
- `process_conclusion_data()`: 处理天健云回传的总检数据
- `update_diagnosis_info()`: 更新数据库信息
- `insert_conclusion_record()`: 插入结论记录
- `start_service()`: 启动服务
- `stop_service()`: 停止服务

### 2. GUI集成

**MainWindow增强**:
- 在`__init__`方法中自动初始化07号接口接收端
- 延迟2秒启动服务，确保GUI完全加载
- 状态栏显示服务运行状态
- 程序关闭时自动停止服务

**状态栏显示**:
- `07号接口: 启动中...` (橙色) - 服务启动中
- `07号接口: 运行中` (绿色) - 服务正常运行
- `07号接口: 启动失败` (红色) - 服务启动失败
- `07号接口: 已停止` (灰色) - 服务已停止

**日志集成**:
- 所有接收端活动都会记录到GUI的日志区域
- 包括服务启动、请求接收、数据处理等信息

### 3. 数据处理流程

1. **接收请求**: 天健云POST数据到 `/dx/inter/receiveConclusion`
2. **数据验证**: 检查必要字段（peNo、hospital等）
3. **机构路由**: 根据医院编码自动选择对应数据库
4. **数据库操作**:
   - 更新T_Register_Main表的总检状态
   - 清理旧的T_Diag_result和T_Check_Result_Illness记录
   - 插入新的结论数据
5. **响应返回**: 返回处理结果给天健云

### 4. 错误处理

**完善的异常处理**:
- 数据库连接失败处理
- 数据格式错误处理
- 机构配置缺失处理
- 服务启动失败处理

**错误日志**:
- 所有错误都会记录到GUI日志
- 同时返回标准化的错误响应给天健云

## 使用方法

### 1. 启动GUI程序

```bash
python gui_main.py
```

启动后会自动：
1. 显示GUI界面
2. 2秒后启动07号接口接收端服务
3. 在状态栏显示服务状态
4. 在日志区域显示启动信息

### 2. 天健云调用

天健云可以直接向以下地址发送总检信息：
```
POST http://your-server:5007/dx/inter/receiveConclusion
Content-Type: application/json
```

### 3. 监控服务状态

- **状态栏**: 实时显示服务运行状态
- **日志区域**: 查看详细的接收和处理日志
- **健康检查**: 访问 `http://your-server:5007/health`

## 测试验证

### 1. 最小化测试版本

提供了 `minimal_gui_test.py` 用于测试基本功能：
```bash
python minimal_gui_test.py
```

### 2. 完整测试脚本

使用 `test_gui_with_07_receiver.py` 进行完整测试：
```bash
python test_gui_with_07_receiver.py
```

### 3. 调试工具

使用 `debug_gui_07.py` 检查依赖和基本功能：
```bash
python debug_gui_07.py
```

## 技术特点

### 1. 无缝集成
- 07号接口接收端完全集成到GUI程序中
- 无需单独启动和管理服务进程
- 统一的日志和状态管理

### 2. 多线程架构
- Flask服务运行在后台线程中
- 不阻塞GUI主线程
- 支持并发请求处理

### 3. 状态同步
- 服务状态实时反映到GUI界面
- 通过回调函数更新状态栏显示
- 日志信息统一管理

### 4. 优雅关闭
- 程序关闭时自动停止服务
- 清理资源和线程
- 记录关闭日志

## 配置要求

### 1. 依赖包
```bash
pip install flask pyside6 requests
```

### 2. 数据库配置
- 需要配置数据库连接信息
- 支持多机构数据库路由
- 需要对相关表有读写权限

### 3. 网络配置
- 确保端口5007未被占用
- 防火墙允许5007端口访问
- 天健云能够访问服务器IP

## 部署建议

### 1. 开发环境
- 直接运行 `python gui_main.py`
- 适合开发和测试

### 2. 生产环境
- 考虑使用系统服务管理GUI程序
- 配置自动启动和重启
- 添加日志轮转和监控

### 3. 安全考虑
- 可以启用签名验证
- 配置HTTPS（需要反向代理）
- 限制访问IP范围

## 故障排除

### 1. 服务启动失败
- 检查端口5007是否被占用
- 确认Flask依赖已安装
- 查看GUI日志区域的错误信息

### 2. 数据库连接失败
- 检查数据库连接配置
- 确认数据库服务正常
- 验证数据库权限

### 3. 天健云无法访问
- 检查防火墙设置
- 确认服务器IP和端口
- 测试网络连通性

## 优势

1. **一体化部署**: GUI和接收端服务统一管理
2. **实时监控**: 状态和日志实时显示
3. **易于维护**: 单一程序，简化部署
4. **用户友好**: 图形界面，操作直观
5. **功能完整**: 支持所有07号接口功能

现在用户只需要启动GUI程序，就能同时获得完整的数据同步界面和07号接口接收端服务！
