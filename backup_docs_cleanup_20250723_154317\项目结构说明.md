# 天健云AI对接项目结构说明

## 📁 项目目录结构

```
tianjian-cloud-interface/
├── 📋 核心程序文件
│   ├── start_gui.py                    # 🚀 主界面程序（项目入口）
│   ├── config.py                       # ⚙️ 核心配置管理
│   ├── center_organization_service.py  # 🏥 中心库机构配置服务
│   ├── dynamic_config_manager.py       # 🔧 动态配置管理器
│   ├── optimized_database_service.py   # 💾 优化的数据库服务
│   ├── org_code_prefix_manager.py      # 🏷️ 机构编码前缀管理
│   └── organization_config_window.py   # 🖥️ 机构配置窗口
│
├── 🔌 接口实现文件（22个）
│   ├── interface_01_sendPeInfo.py      # 01号接口：体检人员信息传输
│   ├── interface_02_syncApplyItem.py   # 02号接口：申请项目同步
│   ├── interface_03_deptInfo.py        # 03号接口：科室信息查询
│   ├── interface_04_syncUser.py        # 04号接口：用户信息同步
│   ├── interface_05_syncDept.py        # 05号接口：科室信息同步
│   ├── interface_06_syncDict.py        # 06号接口：字典信息同步
│   ├── interface_07_sendConclusion.py  # 07号接口：结论信息传输
│   ├── interface_08_getDict.py         # 08号接口：字典信息获取
│   ├── interface_09_retransmitDeptInfo.py    # 09号接口：科室信息重传
│   ├── interface_09_retransmitDeptResult.py  # 09号接口：科室结果重传
│   ├── interface_10_batchGetPeInfo.py  # 10号接口：批量获取体检信息
│   ├── interface_11_getApplyItemDict.py # 11号接口：申请项目字典获取
│   ├── interface_12_lockPeInfo.py      # 12号接口：体检信息锁定
│   ├── interface_13_updatePeStatus.py  # 13号接口：体检状态更新
│   ├── interface_14_markAbnormal.py    # 14号接口：异常标记
│   ├── interface_15_returnDept.py      # 15号接口：科室退回
│   ├── interface_16_getImages.py       # 16号接口：图像获取
│   ├── interface_17_deleteAbnormal.py  # 17号接口：删除异常
│   ├── interface_18_getDoctorInfo.py   # 18号接口：医生信息获取
│   ├── interface_19_getDeptInfo.py     # 19号接口：科室信息获取
│   ├── interface_20_getPersonalOrders.py # 20号接口：个人订单获取
│   └── interface_21_getAbnormalNotice.py  # 21号接口：异常通知获取
│
├── 🛠️ 服务组件
│   ├── dict_sync_service.py            # 📚 字典同步服务
│   ├── incremental_sync_service.py     # 🔄 增量同步服务
│   └── organization_config_service.py  # 🏢 机构配置服务
│
├── 📁 数据库脚本
│   └── sql/
│       ├── create_center_organization_table.sql  # 中心库机构表创建
│       ├── create_multi_org_tables.sql           # 多机构表创建
│       └── create_organization_config_table.sql  # 机构配置表创建
│
├── 📁 测试文件
│   └── tests/
│       ├── test_*.py                   # 各种功能测试
│       ├── real_api_test.py           # 真实API测试
│       └── tianjian_interface_test_suite.py # 接口测试套件
│
├── 📁 查询示例
│   └── test/
│       ├── daily_report_queries.sql    # 日报查询示例
│       ├── personal_exam_queries.sql   # 个人体检查询示例
│       └── *.sql                      # 其他查询示例
│
├── 📁 日志文件
│   └── logs/
│       ├── health_sync.log            # 健康同步日志
│       └── interface_*.log            # 接口操作日志
│
├── 📁 文档说明
│   ├── README.md                      # 项目总体说明
│   ├── GUI使用说明.md                 # GUI操作指南
│   ├── 中心库机构配置使用说明.md       # 机构配置说明
│   ├── 报文.md                        # 报文格式说明
│   └── 项目结构说明.md                # 本文档
│
├── 📁 配置文件
│   ├── requirements.txt               # Python依赖包
│   ├── clear_cache_and_start.bat     # Windows启动脚本
│   ├── install.bat                   # Windows安装脚本
│   └── install.sh                    # Linux安装脚本
│
└── 📁 其他文件
    ├── cleanup_project.py             # 项目清理脚本
    └── sync_state.db                  # 同步状态数据库
```

## 🚀 快速开始

### 1. 启动主程序
```bash
python start_gui.py
```

### 2. 主要功能
- **接口测试**：测试所有21个天健云接口
- **数据同步**：同步体检数据到天健云
- **机构配置**：管理多机构配置信息
- **报文查看**：查看接口请求和响应报文
- **日志监控**：实时查看操作日志

## 📋 核心组件说明

### 🖥️ 主界面程序 (start_gui.py)
- 项目的主入口程序
- 提供图形化界面操作
- 集成所有接口功能
- 支持多机构配置

### ⚙️ 配置管理 (config.py)
- 数据库连接配置
- 天健云API配置
- 多机构动态配置支持

### 🏥 中心库服务 (center_organization_service.py)
- 连接中心库获取机构配置
- 管理机构信息和天健云配置
- 支持机构配置的增删改查

### 💾 数据库服务 (optimized_database_service.py)
- 优化的数据库连接池
- 支持多数据库连接
- 提供高性能数据访问

### 🔌 接口实现
- 21个天健云接口的完整实现
- 支持测试模式和实际发送
- 包含完整的错误处理和日志记录

## 🔧 维护说明

### 添加新接口
1. 在根目录创建 `interface_XX_功能名.py`
2. 实现接口逻辑
3. 在主界面添加对应按钮

### 修改配置
1. 编辑 `config.py` 文件
2. 或通过GUI的机构配置功能

### 查看日志
- 日志文件位于 `logs/` 目录
- 可通过GUI实时查看

## 📝 注意事项

1. **主程序入口**：始终使用 `start_gui.py` 启动
2. **配置管理**：通过GUI进行机构配置，避免直接修改代码
3. **测试模式**：新接口先使用测试模式验证
4. **日志监控**：定期查看日志文件排查问题
5. **备份重要**：定期备份配置和数据

## 🎯 项目优势

- ✅ **目录整洁**：删除了78个重复和无用文件
- ✅ **结构清晰**：核心文件分类明确
- ✅ **功能完整**：保留所有必要功能
- ✅ **易于维护**：代码结构优化，便于后续开发
- ✅ **文档完善**：提供详细的使用说明

---

*最后更新：2025-07-20*
*项目清理完成，删除78个文件，保留核心功能*
