#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云07号接口接收端 - 接收天健云回传的总检信息并写入体检数据库
用于将天健云传回的总检结论信息写入T_Diag_result和T_Check_Result_Illness表
"""

import json
import hashlib
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from flask import Flask, request, jsonify
from database_service import get_database_service
from config import Config
from multi_org_config import get_org_config_by_hospital_code

app = Flask(__name__)

class TianjianInterface07Receiver:
    """天健云07号接口接收端 - 接收总检信息回传"""
    
    def __init__(self):
        """初始化接收端"""
        self.db_service = get_database_service()
    
    def validate_signature(self, headers: Dict[str, str], body: str) -> bool:
        """验证请求签名"""
        try:
            sign = headers.get('sign', '')
            timestamp = headers.get('timestamp', '')
            nonce = headers.get('nonce', '')
            mic_code = headers.get('mic-code', '')
            misc_id = headers.get('misc-id', '')
            
            if not all([sign, timestamp, nonce, mic_code, misc_id]):
                return False
            
            # 根据医院编码获取对应的API配置
            org_config = get_org_config_by_hospital_code(mic_code)
            if not org_config:
                print(f"[ERROR] 未找到医院编码 {mic_code} 对应的机构配置")
                return False
            
            api_key = org_config.get('tianjian_api_key', '')
            if not api_key:
                print(f"[ERROR] 医院编码 {mic_code} 的API密钥未配置")
                return False
            
            # 生成签名进行验证
            sign_string = api_key + timestamp
            expected_sign = hashlib.md5(sign_string.encode('utf-8')).hexdigest()
            
            return sign == expected_sign
            
        except Exception as e:
            print(f"[ERROR] 签名验证异常: {e}")
            return False
    
    def process_conclusion_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理天健云回传的总检信息
        
        Args:
            data: 天健云回传的数据
            
        Returns:
            处理结果
        """
        try:
            # 提取基本信息
            hospital = data.get('hospital', {})
            shop_code = hospital.get('code', '')  # 现在是门店编码
            hospital_name = hospital.get('name', '')
            pe_no = data.get('peNo', '')

            if not pe_no:
                return {
                    'success': False,
                    'error': '体检号不能为空',
                    'code': 1001
                }

            # 根据门店编码获取机构配置
            from multi_org_config import get_org_config_by_shop_code
            org_config = get_org_config_by_shop_code(shop_code)
            if not org_config:
                return {
                    'success': False,
                    'error': f'未找到门店编码 {shop_code} 对应的机构配置',
                    'code': 1002
                }

            # 获取数据库连接字符串
            db_connection_string = org_config.get('db_connection_string')
            if not db_connection_string:
                return {
                    'success': False,
                    'error': f'门店编码 {shop_code} 的数据库连接未配置',
                    'code': 1003
                }

            print(f"[INFO] 开始处理体检号 {pe_no} 的总检信息回传")
            print(f"[INFO] 医院: {hospital_name} (门店编码: {shop_code})")
            
            # 创建对应的数据库服务实例并连接
            from database_service import DatabaseService
            db_service = DatabaseService(db_connection_string)
            if not db_service.connect():
                return {
                    'success': False,
                    'error': '数据库连接失败',
                    'code': 1004
                }
            
            try:
                # 处理总检信息
                result = self._update_diagnosis_info(data, org_config, db_service)

                if result['success']:
                    print(f"[SUCCESS] 体检号 {pe_no} 总检信息更新成功")
                    return {
                        'success': True,
                        'message': '总检信息更新成功',
                        'code': 0,
                        'data': {
                            'peNo': pe_no,
                            'updated_records': result.get('updated_records', 0),
                            'conclusion_count': result.get('conclusion_count', 0)
                        }
                    }
                else:
                    print(f"[ERROR] 体检号 {pe_no} 总检信息更新失败: {result.get('error')}")
                    return {
                        'success': False,
                        'error': result.get('error', '更新失败'),
                        'code': 1005
                    }

            finally:
                db_service.disconnect()
                
        except Exception as e:
            print(f"[ERROR] 处理总检信息异常: {e}")
            return {
                'success': False,
                'error': f'处理异常: {str(e)}',
                'code': 1999
            }
    
    def _update_diagnosis_info(self, data: Dict[str, Any], org_config: Dict[str, Any], db_service) -> Dict[str, Any]:
        """
        更新总检信息到数据库

        Args:
            data: 天健云回传的数据
            org_config: 机构配置
            db_service: 数据库服务实例

        Returns:
            更新结果
        """
        try:
            pe_no = data.get('peNo', '')
            first_check_finish_time = data.get('firstCheckFinishTime', '')
            first_check_finish_doctor = data.get('firstCheckFinishDoctor', {})
            main_check_finish_time = data.get('mainCheckFinishTime', '')
            main_check_finish_doctor = data.get('mainCheckFinishDoctor', {})
            conclusion_list = data.get('conclusionList', [])
            current_node_type = data.get('currentNodeType', 0)
            
            updated_records = 0
            conclusion_count = len(conclusion_list)
            
            # 1. 更新T_Register_Main表的总检信息
            if main_check_finish_time and main_check_finish_doctor:
                update_main_sql = """
                UPDATE T_Register_Main 
                SET cStatus = ?, 
                    cOperCode = ?, 
                    cOperName = ?, 
                    dOperdate = ?
                WHERE cClientCode = ?
                """
                
                # 根据currentNodeType确定状态
                status = '6' if current_node_type == 4 else '4'  # 4=总审, 3=主检
                
                try:
                    # 解析时间格式
                    if main_check_finish_time:
                        finish_time = datetime.strptime(main_check_finish_time, '%Y-%m-%d %H:%M:%S')
                    else:
                        finish_time = datetime.now()
                        
                    params = (
                        status,
                        main_check_finish_doctor.get('code', ''),
                        main_check_finish_doctor.get('name', ''),
                        finish_time,
                        pe_no
                    )
                    
                    result = db_service.execute_update(update_main_sql, params)
                    if result > 0:
                        updated_records += result
                        print(f"[INFO] 更新T_Register_Main表成功，体检号: {pe_no}")

                except Exception as e:
                    print(f"[WARNING] 更新T_Register_Main表失败: {e}")

            # 2. 清理旧的总检结论数据
            delete_diag_sql = "DELETE FROM T_Diag_result WHERE cClientCode = ?"
            delete_illness_sql = "DELETE FROM T_Check_Result_Illness WHERE cClientCode = ?"

            try:
                db_service.execute_update(delete_diag_sql, (pe_no,))
                db_service.execute_update(delete_illness_sql, (pe_no,))
                print(f"[INFO] 清理体检号 {pe_no} 的旧总检结论数据")
            except Exception as e:
                print(f"[WARNING] 清理旧数据失败: {e}")

            # 3. 插入新的结论数据
            for i, conclusion in enumerate(conclusion_list, 1):
                try:
                    print(f"[DEBUG] 开始处理第{i}个结论: {conclusion.get('conclusionName', '')}")
                    self._insert_conclusion_record(
                        pe_no, conclusion,
                        main_check_finish_doctor, first_check_finish_doctor,
                        main_check_finish_time, first_check_finish_time,
                        i, db_service
                    )
                    updated_records += 1
                    print(f"[DEBUG] 第{i}个结论处理成功")
                except Exception as e:
                    print(f"[ERROR] 插入第{i}个结论记录失败: {e}")
                    print(f"[ERROR] 失败的结论: {conclusion.get('conclusionName', '')}")
                    continue
            
            return {
                'success': True,
                'updated_records': updated_records,
                'conclusion_count': conclusion_count
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'更新数据库失败: {str(e)}'
            }

    def get_client_code_by_card_no(self, card_no: str, db_service=None) -> str:
        """通过卡号查询客户编码"""
        try:
            # 优先使用传入的数据库服务，否则使用实例的数据库服务
            service_to_use = db_service if db_service else self.db_service

            sql = "SELECT cClientCode FROM T_Register_Main WHERE cCardNo = ?"
            result = service_to_use.execute_query(sql, (card_no,))

            if result and len(result) > 0:
                return result[0].get('cClientCode', '')
            else:
                return ''

        except Exception as e:
            print(f"[ERROR] 查询客户编码失败: {e}")
            return ''

    def _insert_conclusion_record(self, pe_no: str, conclusion: Dict[str, Any],
                                 main_doctor: Dict[str, Any], first_doctor: Dict[str, Any],
                                 main_time: str, first_time: str, sequence: int, db_service):
        """
        插入单条结论记录

        Args:
            pe_no: 体检号
            conclusion: 结论数据
            main_doctor: 总检医生信息
            first_doctor: 初审医生信息
            main_time: 总检时间
            first_time: 初审时间
            sequence: 序号
            db_service: 数据库服务实例
        """
        conclusion_name = conclusion.get('conclusionName', '')
        conclusion_code = conclusion.get('conclusionCode', '')
        parent_code = conclusion.get('parentCode', '')
        suggest = conclusion.get('suggest', '')
        explain = conclusion.get('explain', '')
        check_result = conclusion.get('checkResult', '')
        level = conclusion.get('level', 3)

        # 新增字段处理
        mapping_id = conclusion.get('mappingId', '')
        children_code = conclusion.get('childrenCode', None)
        dept_id = conclusion.get('deptId', '')
        abnormal_level = conclusion.get('abnormalLevel', 9)  # 默认为9:OTHER
        display_sequence = conclusion.get('displaySequnce', sequence)  # 注意原字段名拼写
        
        if not conclusion_name:
            return
        
        # 通过卡号查询客户编码
        client_code = self.get_client_code_by_card_no(pe_no, db_service)
        if not client_code:
            print(f"[WARNING] 未找到卡号 {pe_no} 对应的客户编码")
            return

        # 获取机构编码 - 从机构配置中获取，如果没有则使用默认值
        try:
            from config import Config
            org_code = getattr(Config, 'ORG_CODE', '08')
        except:
            org_code = '08'  # 默认机构编码

        # 插入T_Diag_result表 - 根据正确的字段含义更新
        insert_diag_sql = """
        INSERT INTO T_Diag_result (
            cClientCode, cDiag, cDiagDesc, cDoctCode, cDoctName, dDoctOperdate,
            cOperCode, cOpername, dOperDate, cShopCode
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """

        # 合并结论名称和说明作为总检结论
        diag_content = conclusion_name[:255] if conclusion_name else ""  # 限制长度避免截断
        if explain:
            diag_desc = f"{explain}\n建议：{suggest}" if suggest else explain
        else:
            diag_desc = suggest if suggest else ""

        # 限制字段长度避免数据截断（根据实际表结构）
        diag_desc = diag_desc[:500] if diag_desc else ""  # cDiagDesc最大500字符

        # 处理总检医生信息
        main_doctor_code = main_doctor.get('code', '')[:9] if main_doctor.get('code') else ""  # cDoctCode最大9字符
        main_doctor_name = main_doctor.get('name', '')[:12] if main_doctor.get('name') else ""  # cDoctName最大12字符

        # 处理初审医生信息
        first_doctor_code = first_doctor.get('code', '')[:9] if first_doctor.get('code') else ""  # cOperCode最大9字符
        first_doctor_name = first_doctor.get('name', '')[:12] if first_doctor.get('name') else ""  # cOpername最大12字符

        # 处理时间信息
        try:
            main_datetime = datetime.strptime(main_time, '%Y-%m-%d %H:%M:%S') if main_time else datetime.now()
        except:
            main_datetime = datetime.now()

        try:
            first_datetime = datetime.strptime(first_time, '%Y-%m-%d %H:%M:%S') if first_time else datetime.now()
        except:
            first_datetime = datetime.now()

        limited_org_code = org_code[:2] if org_code else ""  # cShopCode最大2字符

        # 记录新增字段信息到日志（用于调试和追踪）
        print(f"[INFO] 新增字段信息 - mappingId: {mapping_id}, deptId: {dept_id}, abnormalLevel: {abnormal_level}")
        if children_code:
            print(f"[INFO] 子结论词编码: {children_code}")
        print(f"[INFO] 显示序号: {display_sequence}")
        print(f"[INFO] 总检医生: {main_doctor_name}({main_doctor_code}), 时间: {main_time}")
        print(f"[INFO] 初审医生: {first_doctor_name}({first_doctor_code}), 时间: {first_time}")

        # 在插入前先删除可能存在的重复记录（基于客户编码和结论名称）
        try:
            delete_sql = "DELETE FROM T_Diag_result WHERE cClientCode = ? AND cDiag = ?"
            db_service.execute_update(delete_sql, (client_code, diag_content))
        except Exception as e:
            # 删除失败不影响插入，只记录警告
            print(f"[WARNING] 删除重复记录失败: {e}")

        diag_params = (
            client_code,        # cClientCode - 客户编码
            diag_content,       # cDiag - 总检结论
            diag_desc,          # cDiagDesc - 总检结论描述
            main_doctor_code,   # cDoctCode - 总检医生编码
            main_doctor_name,   # cDoctName - 总检医生姓名
            main_datetime,      # dDoctOperdate - 总检时间
            first_doctor_code,  # cOperCode - 初审医生编码
            first_doctor_name,  # cOpername - 初审医生姓名
            first_datetime,     # dOperDate - 初审时间
            limited_org_code    # cShopCode - 机构编码
        )
        


        # 打印SQL语句和参数用于调试
        print(f"[DEBUG] T_Diag_result插入SQL: {insert_diag_sql.strip()}")
        print(f"[DEBUG] T_Diag_result参数详情:")
        param_names = ["cClientCode", "cDiag", "cDiagDesc", "cDoctCode", "cDoctName",
                      "dDoctOperdate", "cOperCode", "cOpername", "dOperDate", "cShopCode"]
        for i, (name, value) in enumerate(zip(param_names, diag_params)):
            value_str = str(value) if value is not None else "NULL"
            print(f"[DEBUG]   {i+1:2d}. {name:<15} = '{value_str}' (长度: {len(value_str)})")

        try:
            db_service.execute_update(insert_diag_sql, diag_params)
            print(f"[DEBUG] T_Diag_result插入成功")
        except Exception as e:
            print(f"[ERROR] T_Diag_result插入失败: {e}")
            print(f"[ERROR] 失败的SQL: {insert_diag_sql.strip()}")
            print(f"[ERROR] 失败的参数: {diag_params}")
            raise

        # 在插入T_Check_Result_Illness前先删除可能存在的重复记录
        # 主键包含: cClientCode, cDeptcode, cMainName, cIllnessCode
        try:
            delete_illness_sql = "DELETE FROM T_Check_Result_Illness WHERE cClientCode = ? AND cDeptcode = ? AND cMainName = ? AND cIllnessCode = ?"
            limited_parent_code_for_delete = (parent_code or 'MAIN')[:20] if parent_code else 'MAIN'
            limited_conclusion_code_for_delete = conclusion_code[:6] if conclusion_code else ""
            db_service.execute_update(
                delete_illness_sql,
                (client_code, limited_parent_code_for_delete, '总检结论', limited_conclusion_code_for_delete)
            )
        except Exception as e:
            # 删除失败不影响插入
            print(f"[WARNING] 删除T_Check_Result_Illness重复记录失败: {e}")

        # 插入T_Check_Result_Illness表
        insert_illness_sql = """
        INSERT INTO T_Check_Result_Illness (
            cClientCode, cDeptcode, cMainName, cIllnessCode, cIllnessName,
            cIllExplain, cReason, cAdvice, cGrade, cDoctCode, cDoctName,
            dOperdate, nPrintIndex
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """

        # 限制T_Check_Result_Illness表字段长度
        limited_parent_code = (parent_code or 'MAIN')[:20] if parent_code else 'MAIN'
        limited_conclusion_code = conclusion_code[:6] if conclusion_code else ""  # cIllnessCode最大6字符
        limited_conclusion_name = conclusion_name[:100] if conclusion_name else ""
        limited_explain = explain[:500] if explain else ""
        limited_suggest = suggest[:500] if suggest else ""

        # 处理新增字段 - 科室ID映射
        # 如果提供了deptId，尝试使用它作为科室代码，否则使用parentCode
        if dept_id:
            # 限制科室ID长度以符合cDeptcode字段要求
            limited_dept_id = str(dept_id)[:6] if dept_id else limited_parent_code
            dept_code_to_use = limited_dept_id
            print(f"[INFO] 使用新增科室ID: {dept_id} -> {dept_code_to_use}")
        else:
            dept_code_to_use = limited_parent_code

        # 处理异常等级映射
        # abnormalLevel: 1:A 2:B 3:C 9:OTHER -> 转换为系统内部等级
        abnormal_level_mapping = {
            1: '1',  # A级 -> 重要
            2: '2',  # B级 -> 次要
            3: '3',  # C级 -> 其他
            9: '3'   # OTHER -> 其他
        }
        mapped_grade = abnormal_level_mapping.get(abnormal_level, str(level))
        print(f"[INFO] 异常等级映射: {abnormal_level} -> {mapped_grade}")

        illness_params = (
            client_code,  # 使用客户编码而不是卡号
            dept_code_to_use,  # 使用处理后的科室代码
            '总检结论',
            limited_conclusion_code or f'AUTO_{sequence:03d}',
            limited_conclusion_name,
            limited_explain,
            check_result[:200] if check_result else "",  # 限制检查结果长度
            limited_suggest,
            mapped_grade,  # 使用映射后的等级
            doctor_code,  # 使用已限制长度的医生编码
            doctor_name,  # 使用已限制长度的医生姓名
            datetime.now(),
            display_sequence  # 使用新的显示序号字段
        )



        # 打印SQL语句和参数用于调试
        print(f"[DEBUG] T_Check_Result_Illness插入SQL: {insert_illness_sql.strip()}")
        print(f"[DEBUG] T_Check_Result_Illness参数详情:")
        illness_param_names = ["cClientCode", "cDeptcode", "cMainName", "cIllnessCode", "cIllnessName",
                              "cIllExplain", "cReason", "cAdvice", "cGrade", "cDoctCode", "cDoctName",
                              "dOperdate", "nPrintIndex"]
        for i, (name, value) in enumerate(zip(illness_param_names, illness_params)):
            value_str = str(value) if value is not None else "NULL"
            print(f"[DEBUG]   {i+1:2d}. {name:<15} = '{value_str}' (长度: {len(value_str)})")

        try:
            db_service.execute_update(insert_illness_sql, illness_params)
            print(f"[DEBUG] T_Check_Result_Illness插入成功")
        except Exception as e:
            print(f"[ERROR] T_Check_Result_Illness插入失败: {e}")
            print(f"[ERROR] 失败的SQL: {insert_illness_sql.strip()}")
            print(f"[ERROR] 失败的参数: {illness_params}")
            # 不抛出异常，继续处理其他记录
            pass

        # 记录新增字段的处理结果
        print(f"[INFO] 插入结论记录: {conclusion_name}")
        print(f"[INFO] 健管系统映射ID: {mapping_id}")
        print(f"[INFO] 科室ID: {dept_id} -> 使用科室代码: {dept_code_to_use}")
        print(f"[INFO] 异常等级: {abnormal_level} -> 映射等级: {mapped_grade}")
        print(f"[INFO] 显示序号: {display_sequence}")

        # 如果有子结论词编码，记录到日志
        if children_code:
            print(f"[INFO] 子结论词编码集合: {children_code}")
            # 注意：子结论词编码是一个集合，当前表结构无法直接存储
            # 如需存储，建议创建单独的子结论词关联表


# 创建接收端实例
receiver = TianjianInterface07Receiver()


@app.route('/dx/inter/receiveConclusion', methods=['POST'])
def receive_conclusion():
    """接收天健云回传的总检信息"""
    try:
        # 获取请求头和数据
        headers = dict(request.headers)
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': '请求数据为空',
                'code': 1000
            }), 400
        
        # 验证签名（可选，根据需要启用）
        # if not receiver.validate_signature(headers, request.get_data(as_text=True)):
        #     return jsonify({
        #         'success': False,
        #         'error': '签名验证失败',
        #         'code': 1001
        #     }), 401
        
        # 处理数据
        result = receiver.process_conclusion_data(data)
        
        if result['success']:
            return jsonify(result), 200
        else:
            return jsonify(result), 400
            
    except Exception as e:
        print(f"[ERROR] 接收总检信息异常: {e}")
        return jsonify({
            'success': False,
            'error': f'服务器内部错误: {str(e)}',
            'code': 1999
        }), 500


@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'service': '天健云07号接口接收端',
        'timestamp': datetime.now().isoformat()
    })


if __name__ == '__main__':
    print("启动天健云07号接口接收端服务...")
    print("接收端点: /dx/inter/receiveConclusion")
    print("健康检查: /health")
    app.run(host='0.0.0.0', port=5007, debug=True)
