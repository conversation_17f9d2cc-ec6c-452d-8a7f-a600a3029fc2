#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动天健云07号接口接收端服务
"""

import sys
import os
import threading
import time
from interface_07_receiveConclusion import app

def start_receiver_service():
    """启动接收端服务"""
    print("=" * 60)
    print("天健云07号接口接收端服务")
    print("=" * 60)
    print("功能: 接收天健云回传的总检信息并写入体检数据库")
    print("端口: 5007")
    print("接收端点: /dx/inter/receiveConclusion")
    print("健康检查: /health")
    print("=" * 60)
    
    try:
        # 启动Flask应用
        app.run(
            host='0.0.0.0',
            port=5007,
            debug=False,  # 生产环境关闭debug
            threaded=True,
            use_reloader=False
        )
    except KeyboardInterrupt:
        print("\n[INFO] 接收到停止信号，正在关闭服务...")
    except Exception as e:
        print(f"[ERROR] 服务启动失败: {e}")
        sys.exit(1)

def test_service_health():
    """测试服务健康状态"""
    import requests
    import time
    
    print("\n[INFO] 等待服务启动...")
    time.sleep(2)
    
    try:
        response = requests.get("http://localhost:5007/health", timeout=5)
        if response.status_code == 200:
            result = response.json()
            print(f"[SUCCESS] 服务健康检查通过")
            print(f"   服务: {result.get('service')}")
            print(f"   状态: {result.get('status')}")
            print(f"   时间: {result.get('timestamp')}")
        else:
            print(f"[WARNING] 健康检查失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"[WARNING] 健康检查异常: {e}")

def main():
    """主函数"""
    print("启动天健云07号接口接收端...")
    
    # 检查依赖
    try:
        import flask
        import requests
        print(f"[OK] Flask版本: {flask.__version__}")
    except ImportError as e:
        print(f"[ERROR] 缺少依赖: {e}")
        print("请安装依赖: pip install flask requests")
        sys.exit(1)
    
    # 在后台线程中测试健康状态
    health_thread = threading.Thread(target=test_service_health, daemon=True)
    health_thread.start()
    
    # 启动服务
    start_receiver_service()

if __name__ == "__main__":
    main()
