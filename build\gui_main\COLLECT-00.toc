([('天健云数据同步系统.exe',
   'D:\\python\\福能AI对接\\build\\gui_main\\天健云数据同步系统.exe',
   'EXECUTABLE'),
  ('pyarrow\\arrow_python_flight.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\arrow_python_flight.dll',
   'BINARY'),
  ('pyarrow\\arrow_acero.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\arrow_acero.dll',
   'BINARY'),
  ('pyarrow\\parquet.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\parquet.dll',
   'BINARY'),
  ('pyarrow\\arrow_flight.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\arrow_flight.dll',
   'BINARY'),
  ('pyarrow\\arrow_python_parquet_encryption.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\arrow_python_parquet_encryption.dll',
   'BINARY'),
  ('pyarrow\\arrow_dataset.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\arrow_dataset.dll',
   'BINARY'),
  ('pyarrow\\arrow_substrait.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\arrow_substrait.dll',
   'BINARY'),
  ('pyarrow\\arrow.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\arrow.dll',
   'BINARY'),
  ('pyarrow\\arrow_python.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\arrow_python.dll',
   'BINARY'),
  ('pyarrow\\msvcp140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\msvcp140.dll',
   'BINARY'),
  ('python38.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\python38.dll',
   'BINARY'),
  ('libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('scipy.libs\\libopenblas-802f9ed1179cb9c9b03d67ff79f48187.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy.libs\\libopenblas-802f9ed1179cb9c9b03d67ff79f48187.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qpdf.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qdirect2d.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\plugins\\platforms\\qdirect2d.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PySide6\\opengl32sw.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PySide6\\opengl32sw.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qcertonlybackend.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\plugins\\tls\\qcertonlybackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qopensslbackend.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\plugins\\tls\\qopensslbackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qschannelbackend.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\plugins\\tls\\qschannelbackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\resultproxy.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\sqlalchemy\\cyextension\\resultproxy.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\util.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\sqlalchemy\\cyextension\\util.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\processors.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\sqlalchemy\\cyextension\\processors.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\immutabledict.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\sqlalchemy\\cyextension\\immutabledict.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\collections.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\sqlalchemy\\cyextension\\collections.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\greenlet\\_greenlet.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\markupsafe\\_speedups.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\yaml\\_yaml.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\_cffi_backend.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\lapack_lite.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\numpy\\linalg\\lapack_lite.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\numpy\\random\\mtrand.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\numpy\\random\\_sfc64.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\numpy\\random\\_philox.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\numpy\\random\\_pcg64.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\numpy\\random\\_mt19937.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\numpy\\random\\bit_generator.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\numpy\\random\\_generator.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\numpy\\random\\_common.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_imagingft.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_webp.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_imagingtk.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_imagingcms.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PIL\\_imaging.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pydantic_core\\_pydantic_core.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pydantic_core\\_pydantic_core.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\writers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\window\\indexers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\testing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\sparse.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\reshape.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reduction.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\reduction.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\properties.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\parsers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\ops.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\missing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\json.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\join.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\interval.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\internals.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\indexing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\index.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\hashing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\groupby.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\arrays.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\algos.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_compute.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_compute.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_parquet.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_parquet.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_feather.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_feather.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_s3fs.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_s3fs.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_gcsfs.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_gcsfs.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_hdfs.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_hdfs.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_fs.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_fs.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\lib.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\lib.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\sip.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('pyarrow\\_dataset_parquet_encryption.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_dataset_parquet_encryption.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_dataset_parquet.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_dataset_parquet.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_dataset_orc.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_dataset_orc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_dataset.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_dataset.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_ccallback_c.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\_lib\\_ccallback_c.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_biasedurn.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\stats\\_biasedurn.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_tools.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\sparse\\csgraph\\_tools.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_reordering.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\sparse\\csgraph\\_reordering.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_matching.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\sparse\\csgraph\\_matching.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_flow.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\sparse\\csgraph\\_flow.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_min_spanning_tree.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\sparse\\csgraph\\_min_spanning_tree.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_traversal.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\sparse\\csgraph\\_traversal.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_shortest_path.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\sparse\\csgraph\\_shortest_path.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_fblas.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\linalg\\_fblas.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_flapack.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\linalg\\_flapack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_lapack.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\linalg\\cython_lapack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_blas.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\linalg\\cython_blas.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_update.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\linalg\\_decomp_update.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ellip_harm_2.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\special\\_ellip_harm_2.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_group_columns.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\optimize\\_group_columns.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rgi_cython.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\interpolate\\_rgi_cython.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\interpnd.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\interpolate\\interpnd.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\transform\\_rotation.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\spatial\\transform\\_rotation.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_pybind.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\spatial\\_distance_pybind.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_hausdorff.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\spatial\\_hausdorff.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_wrap.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\spatial\\_distance_wrap.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_voronoi.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\spatial\\_voronoi.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_qhull.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\spatial\\_qhull.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_ckdtree.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\spatial\\_ckdtree.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rbfinterp_pythran.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\interpolate\\_rbfinterp_pythran.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_ppoly.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\interpolate\\_ppoly.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_bspl.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\interpolate\\_bspl.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\dfitpack.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\interpolate\\dfitpack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_fitpack.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\interpolate\\_fitpack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_lsoda.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\integrate\\_lsoda.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_dop.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\integrate\\_dop.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_vode.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\integrate\\_vode.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_quadpack.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\integrate\\_quadpack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_odepack.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\integrate\\_odepack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_comb.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\special\\_comb.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_specfun.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\special\\_specfun.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\special\\_ufuncs.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs_cxx.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\special\\_ufuncs_cxx.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_expm.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\linalg\\_matfuncs_expm.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_sqrtm_triu.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm_triu.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\fft\\_pocketfft\\pypocketfft.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\fft\\_pocketfft\\pypocketfft.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_uarray\\_uarray.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\_lib\\_uarray\\_uarray.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_solve_toeplitz.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\linalg\\_solve_toeplitz.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_flinalg.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\linalg\\_flinalg.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_cythonized_array_utils.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\linalg\\_cythonized_array_utils.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_zpropack.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_zpropack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_cpropack.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_cpropack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_dpropack.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_dpropack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_spropack.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_spropack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_dsolve\\_superlu.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_superlu.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_isolve\\_iterative.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\_iterative.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_csparsetools.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\sparse\\_csparsetools.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_sparsetools.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\sparse\\_sparsetools.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highs\\_highs_constants.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\optimize\\_highs\\_highs_constants.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highs\\_highs_wrapper.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\optimize\\_highs\\_highs_wrapper.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_direct.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\optimize\\_direct.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsq\\givens_elimination.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\optimize\\_lsq\\givens_elimination.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsap.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\optimize\\_lsap.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_interpolative.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\linalg\\_interpolative.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_bglu_dense.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\optimize\\_bglu_dense.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\__nnls.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\optimize\\__nnls.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_slsqp.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\optimize\\_slsqp.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_zeros.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\optimize\\_zeros.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_minpack.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\optimize\\_minpack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_trlib\\_trlib.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\optimize\\_trlib\\_trlib.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_cobyla.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\optimize\\_cobyla.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_moduleTNC.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\optimize\\_moduleTNC.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lbfgsb.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\optimize\\_lbfgsb.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_minpack2.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\optimize\\_minpack2.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats_pythran.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\stats\\_stats_pythran.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_rcont\\rcont.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\stats\\_rcont\\rcont.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_mvn.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\stats\\_mvn.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_statlib.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\stats\\_statlib.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_nd_image.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\ndimage\\_nd_image.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_ni_label.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\ndimage\\_ni_label.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_levy_stable\\levyst.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\stats\\_levy_stable\\levyst.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\invgauss_ufunc.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\stats\\_boost\\invgauss_ufunc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\skewnorm_ufunc.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\stats\\_boost\\skewnorm_ufunc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\nct_ufunc.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\stats\\_boost\\nct_ufunc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\ncx2_ufunc.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\stats\\_boost\\ncx2_ufunc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\ncf_ufunc.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\stats\\_boost\\ncf_ufunc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\hypergeom_ufunc.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\stats\\_boost\\hypergeom_ufunc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\nbinom_ufunc.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\stats\\_boost\\nbinom_ufunc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\binom_ufunc.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\stats\\_boost\\binom_ufunc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\beta_ufunc.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\stats\\_boost\\beta_ufunc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\stats\\_stats.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\cython_special.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\special\\cython_special.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_qmc_cy.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\stats\\_qmc_cy.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_sobol.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\stats\\_sobol.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_fpumode.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\_lib\\_fpumode.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\messagestream.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy\\_lib\\messagestream.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\io\\sas\\_sas.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\io\\sas\\_sas.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\io\\sas\\_byteswap.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\io\\sas\\_byteswap.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\tslib.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\lib.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\hashtable.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pyodbc.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyodbc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32wnet.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\win32\\win32wnet.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp38-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\charset_normalizer\\md.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PySide6\\QtGui.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\QtGui.pyd',
   'EXTENSION'),
  ('PySide6\\QtNetwork.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\QtNetwork.pyd',
   'EXTENSION'),
  ('shiboken6\\Shiboken.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\shiboken6\\Shiboken.pyd',
   'EXTENSION'),
  ('PySide6\\QtCore.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\QtCore.pyd',
   'EXTENSION'),
  ('PySide6\\QtWidgets.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\QtWidgets.pyd',
   'EXTENSION'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PySide6\\Qt6Core.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PySide6\\Qt6Core.dll',
   'BINARY'),
  ('PySide6\\Qt6Gui.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PySide6\\Qt6Gui.dll',
   'BINARY'),
  ('PySide6\\Qt6Svg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PySide6\\Qt6Svg.dll',
   'BINARY'),
  ('PySide6\\Qt6Network.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PySide6\\Qt6Network.dll',
   'BINARY'),
  ('PySide6\\Qt6Pdf.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PySide6\\Qt6Pdf.dll',
   'BINARY'),
  ('PySide6\\Qt6VirtualKeyboard.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PySide6\\Qt6VirtualKeyboard.dll',
   'BINARY'),
  ('PySide6\\Qt6Widgets.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PySide6\\Qt6Widgets.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes38.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pywin32_system32\\pywintypes38.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libffi-7.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\python3.dll',
   'BINARY'),
  ('numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'BINARY'),
  ('pandas\\_libs\\window\\MSVCP140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\window\\MSVCP140.dll',
   'BINARY'),
  ('pandas\\_libs\\window\\VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\_libs\\window\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('PySide6\\MSVCP140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\MSVCP140.dll',
   'BINARY'),
  ('PySide6\\MSVCP140_2.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\MSVCP140_2.dll',
   'BINARY'),
  ('shiboken6\\shiboken6.abi3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\shiboken6\\shiboken6.abi3.dll',
   'BINARY'),
  ('PySide6\\VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PySide6\\VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\VCRUNTIME140.dll',
   'BINARY'),
  ('PySide6\\pyside6.abi3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\pyside6.abi3.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\shiboken6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\shiboken6\\VCRUNTIME140.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\WINDOWS\\system32\\ucrtbase.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('PySide6\\MSVCP140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PySide6\\MSVCP140_1.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('PySide6\\Qt6Quick.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PySide6\\Qt6Quick.dll',
   'BINARY'),
  ('PySide6\\Qt6Qml.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PySide6\\Qt6Qml.dll',
   'BINARY'),
  ('shiboken6\\MSVCP140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\shiboken6\\MSVCP140.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlModels.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PySide6\\Qt6QmlModels.dll',
   'BINARY'),
  ('PySide6\\Qt6OpenGL.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PySide6\\Qt6OpenGL.dll',
   'BINARY'),
  ('config.yaml', 'D:\\python\\福能AI对接\\config.yaml', 'DATA'),
  ('env_template.txt', 'D:\\python\\福能AI对接\\env_template.txt', 'DATA'),
  ('health_sync\\config\\defaults.yaml',
   'D:\\python\\福能AI对接\\health_sync\\config\\defaults.yaml',
   'DATA'),
  ('sql\\create_center_organization_table.sql',
   'D:\\python\\福能AI对接\\sql\\create_center_organization_table.sql',
   'DATA'),
  ('sql\\create_multi_org_tables.sql',
   'D:\\python\\福能AI对接\\sql\\create_multi_org_tables.sql',
   'DATA'),
  ('sql\\create_organization_config_table.sql',
   'D:\\python\\福能AI对接\\sql\\create_organization_config_table.sql',
   'DATA'),
  ('test\\daily_report_queries.sql',
   'D:\\python\\福能AI对接\\test\\daily_report_queries.sql',
   'DATA'),
  ('test\\personal_exam_queries.sql',
   'D:\\python\\福能AI对接\\test\\personal_exam_queries.sql',
   'DATA'),
  ('test\\query_person_exam_detail.sql',
   'D:\\python\\福能AI对接\\test\\query_person_exam_detail.sql',
   'DATA'),
  ('test\\query_shop_exam_count.sql',
   'D:\\python\\福能AI对接\\test\\query_shop_exam_count.sql',
   'DATA'),
  ('test\\query_today_group_exam.sql',
   'D:\\python\\福能AI对接\\test\\query_today_group_exam.sql',
   'DATA'),
  ('test\\unit_query_by_year.sql',
   'D:\\python\\福能AI对接\\test\\unit_query_by_year.sql',
   'DATA'),
  ('test\\unit_query_example.sql',
   'D:\\python\\福能AI对接\\test\\unit_query_example.sql',
   'DATA'),
  ('base_library.zip',
   'D:\\python\\福能AI对接\\build\\gui_main\\base_library.zip',
   'DATA'),
  ('importlib_metadata-6.8.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\importlib_metadata-6.8.0.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-6.8.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\importlib_metadata-6.8.0.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-6.8.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\importlib_metadata-6.8.0.dist-info\\top_level.txt',
   'DATA'),
  ('importlib_metadata-6.8.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\importlib_metadata-6.8.0.dist-info\\WHEEL',
   'DATA'),
  ('importlib_metadata-6.8.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\importlib_metadata-6.8.0.dist-info\\RECORD',
   'DATA'),
  ('importlib_metadata-6.8.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\importlib_metadata-6.8.0.dist-info\\LICENSE',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\LICENSE.BSD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography-41.0.7.dist-info\\LICENSE.BSD',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\LICENSE.APACHE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography-41.0.7.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography-41.0.7.dist-info\\RECORD',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography-41.0.7.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography-41.0.7.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography-41.0.7.dist-info\\top_level.txt',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography-41.0.7.dist-info\\METADATA',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography-41.0.7.dist-info\\LICENSE',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\transport.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\transport.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\future_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\future_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\logger.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\logger.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\converter.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\converter.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\python_test.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\python_test.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\writer.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\map_node.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\map_node.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\datum.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\datum.h',
   'DATA'),
  ('pyarrow\\tests\\data\\parquet\\v0.7.1.parquet',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\tests\\data\\parquet\\v0.7.1.parquet',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\visibility.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\udf.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\udf.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\fixed-dtoa.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\fixed-dtoa.h',
   'DATA'),
  ('pyarrow\\_dataset.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_dataset.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\windows_compatibility.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\windows_compatibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\xxhash\\xxhash.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\xxhash\\xxhash.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_feather.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\includes\\libarrow_feather.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\uniform_real.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\uniform_real.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\file_key_wrapper.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\file_key_wrapper.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\io.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\io.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\type_fwd.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\type_fwd.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pyarrow_lib.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\pyarrow_lib.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\inference.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\inference.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\tz_private.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\tz_private.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\type_traits.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\type_traits.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\arrow_to_python_internal.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\arrow_to_python_internal.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_to_arrow.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_to_arrow.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\async_test_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\async_test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\row\\grouper.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\row\\grouper.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\init.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\init.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\s3_test_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\s3_test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\checked_cast.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\checked_cast.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\order_by_impl.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\order_by_impl.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\api_aggregate.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\api_aggregate.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\column_decoder.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\column_decoder.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.emptyFile.orc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.emptyFile.orc',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\type_fwd.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\api\\writer.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\api\\writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\type_traits.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\type_traits.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\test_common.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\test_common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\strptime.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\strptime.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\util.h',
   'DATA'),
  ('pyarrow\\arrow_flight.lib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\arrow_flight.lib',
   'DATA'),
  ('pyarrow\\include\\parquet\\parquet_version.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\parquet_version.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\level_comparison.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\level_comparison.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\hash_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\hash_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\filesystem.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\filesystem.h',
   'DATA'),
  ('pyarrow\\tests\\bound_function_visit_strings.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\tests\\bound_function_visit_strings.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\api.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\regex.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\regex.h',
   'DATA'),
  ('pyarrow\\_hdfs.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_hdfs.pyx',
   'DATA'),
  ('pyarrow\\_azurefs.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_azurefs.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\fast-dtoa.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\fast-dtoa.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\util.h',
   'DATA'),
  ('pyarrow\\_acero.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_acero.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\unmaterialized_table.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\unmaterialized_table.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\api.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_binary.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_binary.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\mman.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\mman.h',
   'DATA'),
  ('pyarrow\\tests\\data\\parquet\\v0.7.1.column-metadata-handling.parquet',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\tests\\data\\parquet\\v0.7.1.column-metadata-handling.parquet',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\gdb.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\gdb.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\api.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\dataset.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\dataset.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\test_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\chunked_builder.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\chunked_builder.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\encryption.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\encryption.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\pch.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\visibility.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client_cookie_middleware.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client_cookie_middleware.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\decimal.orc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\tests\\data\\orc\\decimal.orc',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\windows_fixup.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\windows_fixup.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\arrow_to_pandas.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\arrow_to_pandas.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\kms_client_factory.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\kms_client_factory.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_visit.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_visit.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\test_common.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\test_common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\api.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\type_fwd.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\type_fwd.h',
   'DATA'),
  ('pyarrow\\_s3fs.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_s3fs.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\print.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\print.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_ops.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_ops.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\file_writer.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\file_writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bit_stream_utils.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bit_stream_utils.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\interfaces.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\interfaces.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\concatenate.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\concatenate.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\stream_writer.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\stream_writer.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\test_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\time.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\time.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\common.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\common.h',
   'DATA'),
  ('pyarrow\\compat.pxi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\compat.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\vector.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\vector.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\api.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\tracing.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\tracing.h',
   'DATA'),
  ('pyarrow\\includes\\libparquet_encryption.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\includes\\libparquet_encryption.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\binary_view_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\binary_view_util.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\gdb.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\gdb.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\adapters\\orc\\options.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\adapters\\orc\\options.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\array.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\statistics.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\statistics.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\pcg\\pcg_uint128.hpp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\pcg\\pcg_uint128.hpp',
   'DATA'),
  ('pyarrow\\include\\arrow\\visit_array_inline.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\visit_array_inline.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\metadata.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\metadata.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\cpu_info.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\cpu_info.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\extension_type.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\extension_type.h',
   'DATA'),
  ('pyarrow\\parquet.lib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\parquet.lib',
   'DATA'),
  ('pyarrow\\benchmark.pxi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\benchmark.pxi',
   'DATA'),
  ('pyarrow\\tests\\data\\parquet\\v0.7.1.some-named-index.parquet',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\tests\\data\\parquet\\v0.7.1.some-named-index.parquet',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_adaptive.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_adaptive.h',
   'DATA'),
  ('pyarrow\\_json.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_json.pyx',
   'DATA'),
  ('pyarrow\\includes\\__init__.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\includes\\__init__.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client_auth.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client_auth.h',
   'DATA'),
  ('pyarrow\\arrow_python_parquet_encryption.lib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\arrow_python_parquet_encryption.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\c\\bridge.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\c\\bridge.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\gcsfs.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\gcsfs.h',
   'DATA'),
  ('pyarrow\\io.pxi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\io.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\strtod.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\strtod.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\column_scanner.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\column_scanner.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\api\\schema.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\api\\schema.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\benchmark.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\benchmark.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\pyarrow_lib.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\pyarrow_lib.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\scalar.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\scalar.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\type_fwd.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\type_fwd.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_fs.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\includes\\libarrow_fs.pxd',
   'DATA'),
  ('pyarrow\\_feather.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_feather.pyx',
   'DATA'),
  ('pyarrow\\gandiva.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\gandiva.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\serde.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\serde.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\test_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\benchmark_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\benchmark_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitset_stack.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitset_stack.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\cached-powers.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\cached-powers.h',
   'DATA'),
  ('pyarrow\\arrow_acero.lib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\arrow_acero.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_union.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_union.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\aligned_storage.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\aligned_storage.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\partition.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\partition.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\visit_data_inline.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\visit_data_inline.h',
   'DATA'),
  ('pyarrow\\_parquet_encryption.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_parquet_encryption.pxd',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\file_system_key_material_store.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\file_system_key_material_store.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\middleware.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\middleware.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_builders.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_builders.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\projector.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\projector.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\iterators.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\iterators.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\basic_decimal.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\basic_decimal.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\key_metadata.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\key_metadata.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\tpch_node.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\tpch_node.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\arrow\\test_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\arrow\\test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\options.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\options.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\types.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\types.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\memory_pool.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\memory_pool.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\c\\helpers.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\c\\helpers.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\pch.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\memory.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\memory.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\local_wrap_kms_client.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\local_wrap_kms_client.h',
   'DATA'),
  ('pyarrow\\_flight.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_flight.pyx',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\udf.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\udf.cc',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.test1.orc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.test1.orc',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\inference.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\inference.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\invalid_row.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\invalid_row.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\sparse_tensor.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\sparse_tensor.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\parquet_encryption_config.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\parquet_encryption_config.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\level_comparison_inc.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\level_comparison_inc.h',
   'DATA'),
  ('pyarrow\\_orc.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_orc.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\test_common.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\test_common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\cancel.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\cancel.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_substrait.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\includes\\libarrow_substrait.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\iterator.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\iterator.h',
   'DATA'),
  ('pyarrow\\memory.pxi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\memory.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\mockfs.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\mockfs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\helpers.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\helpers.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\api.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_decimal.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_decimal.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_internal.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_internal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\record_batch.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\record_batch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\numpy_to_arrow.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\numpy_to_arrow.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\file_key_material_store.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\file_key_material_store.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\portable-snippets\\safe-math.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\portable-snippets\\safe-math.h',
   'DATA'),
  ('pyarrow\\_pyarrow_cpp_tests.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_pyarrow_cpp_tests.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\type_fwd.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\type_fwd.h',
   'DATA'),
  ('pyarrow\\arrow_dataset.lib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\arrow_dataset.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\string_builder.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\string_builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\serialize.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\serialize.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\thread_pool.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\thread_pool.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\hash_join_node.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\hash_join_node.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\datetime.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\datetime.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\pcg\\pcg_random.hpp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\pcg\\pcg_random.hpp',
   'DATA'),
  ('pyarrow\\include\\arrow\\type.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\type.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\exception.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\exception.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\visibility.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\task_group.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\task_group.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_reader.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_reader.h',
   'DATA'),
  ('pyarrow\\_dataset_parquet.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_dataset_parquet.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\functional.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\functional.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\generator.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\generator.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\type_fwd.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\type_fwd.h',
   'DATA'),
  ('pyarrow\\tests\\data\\feather\\v0.17.0.version.2-compression.lz4.feather',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\tests\\data\\feather\\v0.17.0.version.2-compression.lz4.feather',
   'DATA'),
  ('pyarrow\\config.pxi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\config.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\server_tracing_middleware.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\server_tracing_middleware.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\compare.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\compare.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\ProducerConsumerQueue.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\ProducerConsumerQueue.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_writer.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_writer.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\deserialize.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\deserialize.h',
   'DATA'),
  ('pyarrow\\device.pxi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\device.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\tdigest.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\tdigest.h',
   'DATA'),
  ('pyarrow\\includes\\libgandiva.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\includes\\libgandiva.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\value_parsing.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\value_parsing.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\s3fs.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\s3fs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\byte_size.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\byte_size.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\union_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\union_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\data.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\data.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\hash_join_dict.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\hash_join_dict.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\int_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\int_util.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\visibility.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\type_fwd.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\counting_semaphore.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\counting_semaphore.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_base.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_base.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\macros.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\macros.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\types.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\types.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encoding.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\encoding.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\test_nodes.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\test_nodes.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\type_fwd.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\type_fwd.h',
   'DATA'),
  ('pyarrow\\_parquet.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_parquet.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\server.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\server.h',
   'DATA'),
  ('pyarrow\\_fs.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_fs.pxd',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\benchmark.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\benchmark.h',
   'DATA'),
  ('pyarrow\\_csv.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_csv.pyx',
   'DATA'),
  ('pyarrow\\_dlpack.pxi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_dlpack.pxi',
   'DATA'),
  ('pyarrow\\_parquet.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_parquet.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\compressed.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\compressed.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\registry.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\registry.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\stl_allocator.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\stl_allocator.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\relation.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\relation.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\portable-snippets\\debug-trap.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\portable-snippets\\debug-trap.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\decimal.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\decimal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\visibility.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\time_series_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\time_series_util.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\arrow\\schema.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\arrow\\schema.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\async.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\async.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\ree_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\ree_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\pch.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\utf8.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\utf8.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\hasher.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\hasher.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\simd.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\simd.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\helpers.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\helpers.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\align_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\align_util.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\platform.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\platform.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\file.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\file.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\filesystem.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\filesystem.cc',
   'DATA'),
  ('pyarrow\\_dataset_parquet_encryption.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_dataset_parquet_encryption.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\list_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\list_util.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\api.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\api.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\config.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\config.h',
   'DATA'),
  ('pyarrow\\_cuda.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_cuda.pxd',
   'DATA'),
  ('pyarrow\\tests\\pyarrow_cython_example.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\tests\\pyarrow_cython_example.pyx',
   'DATA'),
  ('pyarrow\\scalar.pxi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\scalar.pxi',
   'DATA'),
  ('pyarrow\\include\\parquet\\stream_reader.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\stream_reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\tensor.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\tensor.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\stdio.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\stdio.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\api\\io.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\api\\io.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\python_test.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\python_test.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pch.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\int_util_overflow.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\int_util_overflow.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\serialize.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\serialize.cc',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\key_material.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\key_material.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\benchmark_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\benchmark_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\async_generator.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\async_generator.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\logging.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\logging.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\matchers.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\matchers.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\message.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\message.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\concurrency.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\concurrency.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_base.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_base.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_run_end.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_run_end.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking_avx512.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking_avx512.h',
   'DATA'),
  ('pyarrow\\lib_api.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\lib_api.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_convert.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_convert.h',
   'DATA'),
  ('pyarrow\\_compute.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_compute.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\python_test.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\python_test.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_parquet.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_parquet.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\pyarrow.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\pyarrow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\numpy_interop.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\numpy_interop.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\async_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\async_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\base64.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\base64.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\parquet_encryption.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\parquet_encryption.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\CMakeLists.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\CMakeLists.txt',
   'DATA'),
  ('pyarrow\\include\\parquet\\column_writer.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\column_writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\deserialize.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\deserialize.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\dispatch.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\dispatch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bit_block_counter.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bit_block_counter.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_cuda.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\includes\\libarrow_cuda.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_json.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_json.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\c\\abi.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\c\\abi.h',
   'DATA'),
  ('pyarrow\\public-api.pxi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\public-api.pxi',
   'DATA'),
  ('pyarrow\\_dataset_orc.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_dataset_orc.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pyarrow.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\pyarrow.cc',
   'DATA'),
  ('pyarrow\\_substrait.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_substrait.pyx',
   'DATA'),
  ('pyarrow\\include\\parquet\\level_conversion.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\level_conversion.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\test_common.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\test_common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\double_conversion.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\double_conversion.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\launder.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\launder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\ubsan.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\ubsan.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\extension_type.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\extension_type.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\ios.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\ios.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\test_plan_builder.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\test_plan_builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client_tracing_middleware.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client_tracing_middleware.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\decimal.jsn.gz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\tests\\data\\orc\\decimal.jsn.gz',
   'DATA'),
  ('pyarrow\\lib.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\lib.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\visitor_generate.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\visitor_generate.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_primitive.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_primitive.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\extension_type.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\extension_type.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\reader.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\reader.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\test_in_memory_kms.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\test_in_memory_kms.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\bloom_filter.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\bloom_filter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_run_end.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_run_end.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\two_level_cache_with_expiration.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\two_level_cache_with_expiration.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\mutex.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\mutex.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\api.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\api.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\datetime.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\datetime.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\column_builder.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\column_builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_binary.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_binary.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\extension_types.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\extension_types.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\type_traits.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\type_traits.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\api.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\config.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\config.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\bignum-dtoa.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\bignum-dtoa.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\key_toolkit.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\key_toolkit.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\extension_set.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\extension_set.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\random.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\random.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\visibility.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\uri.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\uri.h',
   'DATA'),
  ('pyarrow\\table.pxi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\table.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\test_definitions.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\test_definitions.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\cast.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\cast.h',
   'DATA'),
  ('pyarrow\\_acero.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_acero.pxd',
   'DATA'),
  ('pyarrow\\_compute.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_compute.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\pch.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\spaced.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\spaced.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\lib.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\lib.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\visit_type_inline.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\visit_type_inline.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pyarrow.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\pyarrow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\double-conversion.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\double-conversion.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\hash_join.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\hash_join.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_interop.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_interop.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\gdb.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\gdb.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\kms_client.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\kms_client.h',
   'DATA'),
  ('pyarrow\\error.pxi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\error.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\server_middleware.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\server_middleware.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\tz.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\tz.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\flight.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\flight.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\backpressure_handler.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\backpressure_handler.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\chunk_resolver.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\chunk_resolver.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_decimal.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_decimal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\arrow_to_pandas.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\arrow_to_pandas.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\api.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\bignum.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\bignum.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.testDate1900.jsn.gz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.testDate1900.jsn.gz',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\visibility.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\converter.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\converter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\pcg\\pcg_extras.hpp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\pcg\\pcg_extras.hpp',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\task_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\task_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking64_default.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking64_default.h',
   'DATA'),
  ('pyarrow\\_parquet_encryption.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_parquet_encryption.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\chunker.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\chunker.h',
   'DATA'),
  ('pyarrow\\_cuda.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_cuda.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\trie.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\trie.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\buffered.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\buffered.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\api.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\test_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_base.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_base.h',
   'DATA'),
  ('pyarrow\\builder.pxi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\builder.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\unreachable.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\unreachable.h',
   'DATA'),
  ('pyarrow\\tests\\extensions.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\tests\\extensions.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\math_constants.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\math_constants.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_convert.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_convert.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_nested.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_nested.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\platform.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\platform.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.emptyFile.jsn.gz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.emptyFile.jsn.gz',
   'DATA'),
  ('pyarrow\\lib.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\lib.pyx',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\decimal.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\decimal.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\xxhash.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\xxhash.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\file_reader.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\file_reader.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_acero.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\includes\\libarrow_acero.pxd',
   'DATA'),
  ('pyarrow\\include\\parquet\\windows_fixup.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\windows_fixup.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\endian.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\endian.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_generate.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_generate.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\crypto_factory.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\crypto_factory.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\algorithm.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\algorithm.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\parser.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\parser.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\ipc.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\ipc.cc',
   'DATA'),
  ('pyarrow\\_pyarrow_cpp_tests.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_pyarrow_cpp_tests.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\async_generator_fwd.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\async_generator_fwd.h',
   'DATA'),
  ('pyarrow\\includes\\common.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\includes\\common.pxd',
   'DATA'),
  ('pyarrow\\include\\parquet\\level_conversion_inc.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\level_conversion_inc.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\rapidjson_defs.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\rapidjson_defs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\python_to_arrow.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\python_to_arrow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\iterators.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\iterators.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.test1.jsn.gz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.test1.jsn.gz',
   'DATA'),
  ('pyarrow\\includes\\libarrow.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\includes\\libarrow.pxd',
   'DATA'),
  ('pyarrow\\types.pxi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\types.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\server_auth.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\server_auth.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\stopwatch.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\stopwatch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\async.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\async.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\future.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\future.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\type_fwd.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\type_fwd.h',
   'DATA'),
  ('pyarrow\\_dataset.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_dataset.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\memory.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\memory.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\transform.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\transform.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bit_run_reader.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bit_run_reader.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\platform.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\platform.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\benchmark.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\benchmark.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\numpy_convert.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\numpy_convert.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\plan.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\plan.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\platform.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\platform.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\stl_iterator.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\stl_iterator.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\python_to_arrow.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\python_to_arrow.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\gtest_compat.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\gtest_compat.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\parquet_encryption.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\parquet_encryption.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\c\\dlpack.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\c\\dlpack.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\io_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\io_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\concurrent_map.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\concurrent_map.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\chunker.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\chunker.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\filesystem_library.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\filesystem_library.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\io.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\io.cc',
   'DATA'),
  ('pyarrow\\include\\parquet\\api\\reader.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\api\\reader.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\arrow_to_pandas.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\arrow_to_pandas.cc',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\common.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\common.cc',
   'DATA'),
  ('pyarrow\\ipc.pxi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\ipc.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\executor_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\executor_util.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\README.md',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\tests\\data\\orc\\README.md',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\csv.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\csv.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\arrow\\reader.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\arrow\\reader.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_flight.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\includes\\libarrow_flight.pxd',
   'DATA'),
  ('pyarrow\\include\\parquet\\pch.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\pch.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\filesystem.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\filesystem.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_primitive.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_primitive.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_python.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\includes\\libarrow_python.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\function_options.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\function_options.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\exec_plan.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\exec_plan.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking_avx2.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking_avx2.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\page_index.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\page_index.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\ipc.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\ipc.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\decimal.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\decimal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\visitor.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\visitor.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\feather.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\feather.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\test_common.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\test_common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\float16.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\float16.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\table_builder.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\table_builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\parser.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\parser.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\expression.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\expression.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\hashing.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\hashing.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\properties.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\properties.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\dict_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\dict_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_orc.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_orc.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compare.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\compare.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pyarrow_api.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\pyarrow_api.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\csv.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\csv.cc',
   'DATA'),
  ('pyarrow\\include\\parquet\\column_page.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\column_page.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\type_fwd.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\utils.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\utils.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\api.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\compression.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\compression.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\key_encryption_key.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\key_encryption_key.h',
   'DATA'),
  ('pyarrow\\_fs.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_fs.pyx',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_to_arrow.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_to_arrow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\visibility.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\reader.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\visit_scalar_inline.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\visit_scalar_inline.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\string-to-double.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\string-to-double.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\memory_pool_test.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\memory_pool_test.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\datetime.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\datetime.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\init.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\init.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\object_parser.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\object_parser.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\type_fwd.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\extension_type.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\extension_type.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\schema_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\schema_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client_middleware.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client_middleware.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\options.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\options.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_dict.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_dict.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\sort.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\sort.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\json_simple.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\json_simple.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\buffer.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\buffer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\adapters\\tensorflow\\convert.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\adapters\\tensorflow\\convert.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\ipc.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\ipc.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\diy-fp.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\diy-fp.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\reader.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\pcg_random.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\pcg_random.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\localfs.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\localfs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\aggregate_node.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\aggregate_node.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\asof_join_node.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\asof_join_node.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\arrow\\writer.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\arrow\\writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\transport_server.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\transport_server.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\c\\dlpack_abi.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\c\\dlpack_abi.h',
   'DATA'),
  ('pyarrow\\arrow_python.lib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\arrow_python.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\span.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\span.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\builder.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\builder.h',
   'DATA'),
  ('pyarrow\\lib.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\lib.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\deserialize.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\deserialize.cc',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\type_traits.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\type_traits.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_csv.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_csv.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\type_fwd.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\type_fwd.h',
   'DATA'),
  ('pyarrow\\_dataset_parquet.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_dataset_parquet.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\api_vector.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\api_vector.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\parquet_encryption.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\parquet_encryption.cc',
   'DATA'),
  ('pyarrow\\includes\\libarrow_dataset.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\includes\\libarrow_dataset.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_ipc.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_ipc.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\small_vector.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\small_vector.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\type_fwd.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\options.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\options.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\api_scalar.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\api_scalar.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\windows_compatibility.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\windows_compatibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\range.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\range.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\chunked_array.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\chunked_array.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\discovery.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\discovery.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\kernel.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\kernel.h',
   'DATA'),
  ('pyarrow\\_gcsfs.pyx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_gcsfs.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\pyarrow_api.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\pyarrow_api.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\flight.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\flight.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\object_writer.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\object_writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\common.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\caching.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\caching.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\validate.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\validate.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\extension\\fixed_shape_tensor.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\extension\\fixed_shape_tensor.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\schema.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\schema.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\bloom_filter_reader.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\bloom_filter_reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\options.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\options.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\delimiting.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\delimiting.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_dict.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_dict.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\pch.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\status.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\status.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\parallel.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\parallel.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\benchmark_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\benchmark_util.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\test_encryption_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\test_encryption_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\api.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\double-to-string.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\double-to-string.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\diff.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\diff.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\map.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\map.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\key_value_metadata.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\key_value_metadata.h',
   'DATA'),
  ('pyarrow\\tensor.pxi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\tensor.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\query_context.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\query_context.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\types_async.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\types_async.h',
   'DATA'),
  ('pyarrow\\__init__.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\__init__.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\partition_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\partition_util.h',
   'DATA'),
  ('pyarrow\\_json.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_json.pxd',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\helpers.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\helpers.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\azurefs.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\azurefs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_time.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_time.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\date.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\date.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\options.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\options.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\fixed_width_test_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\fixed_width_test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\buffer_builder.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\buffer_builder.h',
   'DATA'),
  ('pyarrow\\arrow.lib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\arrow.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\dataset_writer.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\dataset_writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\adapters\\orc\\adapter.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\adapters\\orc\\adapter.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\printer.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\printer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\dictionary.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\dictionary.h',
   'DATA'),
  ('pyarrow\\_orc.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_orc.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\pch.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\pch.h',
   'DATA'),
  ('pyarrow\\arrow_python_flight.lib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\arrow_python_flight.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\result.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\result.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\formatting.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\formatting.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_dataset_parquet.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\includes\\libarrow_dataset_parquet.pxd',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\flight.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\flight.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\inference.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\inference.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\ieee.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\ieee.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\gtest_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\gtest_util.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\serialize.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\serialize.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bit_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bit_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\otel_logging.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\otel_logging.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\stl.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\stl.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\column_reader.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\column_reader.h',
   'DATA'),
  ('pyarrow\\_csv.pxd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\_csv.pxd',
   'DATA'),
  ('pyarrow\\tests\\data\\parquet\\v0.7.1.all-named-index.parquet',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\tests\\data\\parquet\\v0.7.1.all-named-index.parquet',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\bloom_filter.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\bloom_filter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_nested.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_nested.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.testDate1900.orc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.testDate1900.orc',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\type_fwd.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\scanner.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\scanner.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\tensor\\converter.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\tensor\\converter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\filesystem.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\filesystem.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\pretty_print.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\pretty_print.h',
   'DATA'),
  ('pyarrow\\pandas-shim.pxi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\pandas-shim.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\debug.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\debug.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\accumulation_queue.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\accumulation_queue.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\slow.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\slow.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\xxhasher.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\xxhasher.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\string.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\string.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\extension_type.cc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\extension_type.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\ordering.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\ordering.h',
   'DATA'),
  ('pyarrow\\array.pxi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\array.pxi',
   'DATA'),
  ('pyarrow\\arrow_substrait.lib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\arrow_substrait.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\init.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\init.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\queue.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\queue.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\pch.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\pch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking_neon.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking_neon.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\builder.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\device.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\device.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\function.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\function.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\udf.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\udf.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\rows_to_batches.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\rows_to_batches.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\hdfs.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\hdfs.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\io.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\io.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\path_util.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\path_util.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\file_key_unwrapper.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\file_key_unwrapper.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\python_to_arrow.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\python_to_arrow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\lib_api.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\lib_api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bpacking_default.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bpacking_default.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\rle_encoding.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\rle_encoding.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\csv.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\csv.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\table.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\table.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\converter.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\converter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\crc32.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\crc32.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\decimal.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\decimal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\visibility.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\writer.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\hdfs.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\hdfs.h',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('scipy.libs\\.load-order-scipy-1.10.1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\scipy.libs\\.load-order-scipy-1.10.1',
   'DATA'),
  ('PySide6\\translations\\qt_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_BR.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_sl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_he.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_fi.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_lv.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fa.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qt_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_gl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_nl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_es.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_nn.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_he.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_he.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pt_BR.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qt_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_da.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pt_BR.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lt.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_lt.qm',
   'DATA'),
  ('PySide6\\translations\\qt_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_gd.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nn.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fa.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_fa.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PySide6\\translations\\qt_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qt_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_sv.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_PT.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PySide6\\translations\\qt_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_de.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_nl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_nn.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qt_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('blinker-1.8.2.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\blinker-1.8.2.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.43.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\wheel-0.43.0.dist-info\\WHEEL',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.43.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\wheel-0.43.0.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.43.0.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\wheel-0.43.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('blinker-1.8.2.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\blinker-1.8.2.dist-info\\RECORD',
   'DATA'),
  ('attrs-23.2.0.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\attrs-23.2.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('attrs-23.2.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\attrs-23.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.0.3.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\flask-3.0.3.dist-info\\METADATA',
   'DATA'),
  ('werkzeug-3.0.6.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\werkzeug-3.0.6.dist-info\\METADATA',
   'DATA'),
  ('flask-3.0.3.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\flask-3.0.3.dist-info\\INSTALLER',
   'DATA'),
  ('blinker-1.8.2.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\blinker-1.8.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('werkzeug-3.0.6.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\werkzeug-3.0.6.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.43.0.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\wheel-0.43.0.dist-info\\entry_points.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.0.3.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\flask-3.0.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.43.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\wheel-0.43.0.dist-info\\INSTALLER',
   'DATA'),
  ('blinker-1.8.2.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\blinker-1.8.2.dist-info\\METADATA',
   'DATA'),
  ('attrs-23.2.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\attrs-23.2.0.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.43.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\wheel-0.43.0.dist-info\\RECORD',
   'DATA'),
  ('werkzeug-3.0.6.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\werkzeug-3.0.6.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.43.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\wheel-0.43.0.dist-info\\REQUESTED',
   'DATA'),
  ('werkzeug-3.0.6.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\werkzeug-3.0.6.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('blinker-1.8.2.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\blinker-1.8.2.dist-info\\WHEEL',
   'DATA'),
  ('flask-3.0.3.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\flask-3.0.3.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.0.6.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\werkzeug-3.0.6.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.0.3.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\flask-3.0.3.dist-info\\entry_points.txt',
   'DATA'),
  ('attrs-23.2.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\attrs-23.2.0.dist-info\\WHEEL',
   'DATA'),
  ('attrs-23.2.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\attrs-23.2.0.dist-info\\RECORD',
   'DATA'),
  ('flask-3.0.3.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\flask-3.0.3.dist-info\\RECORD',
   'DATA')],)
