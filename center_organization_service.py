#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中心库机构配置服务
连接到中心库(***********/Examdb)管理机构配置信息
"""

import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass, asdict
from optimized_database_service import create_optimized_db_service


@dataclass
class CenterOrganizationConfig:
    """中心库机构配置数据类"""
    id: int = 0
    org_code: str = ""
    org_name: str = ""
    org_type: str = "HOSPITAL"
    status: str = "1"
    
    # 天健云API配置
    tianjian_mic_code: str = ""
    tianjian_misc_id: str = ""
    tianjian_api_key: str = ""
    tianjian_base_url: str = "http://**************:9300"
    
    # 数据库配置
    db_host: str = ""
    db_port: int = 1433
    db_name: str = ""
    db_user: str = ""
    db_password: str = ""
    db_driver: str = "ODBC Driver 17 for SQL Server"
    
    # 业务配置
    shop_code: str = ""
    default_dept_code: str = ""
    sync_enabled: str = "1"
    sync_intervals: int = 60

    # 企业信息
    business_license: str = ""
    legal_person: str = ""
    business_scope: str = ""

    # 联系信息
    contact_person: str = ""
    contact_phone: str = ""
    contact_email: str = ""
    address: str = ""

    # 系统字段
    create_time: str = ""
    update_time: str = ""
    create_user: str = "SYSTEM"
    update_user: str = "SYSTEM"
    remark: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    def get_db_connection_string(self) -> str:
        """获取数据库连接字符串"""
        return (
            f"DRIVER={{{self.db_driver}}};"
            f"SERVER={self.db_host},{self.db_port};"
            f"DATABASE={self.db_name};"
            f"UID={self.db_user};"
            f"PWD={self.db_password}"
        )
    
    def get_tianjian_api_config(self) -> Dict[str, Any]:
        """获取天健云API配置"""
        return {
            'base_url': self.tianjian_base_url,
            'api_key': self.tianjian_api_key,
            'mic_code': self.tianjian_mic_code,
            'misc_id': self.tianjian_misc_id,
            'timeout': 30
        }


class CenterOrganizationService:
    """中心库机构配置服务类"""
    
    def __init__(self):
        """初始化服务（延迟初始化）"""
        self.logger = logging.getLogger(__name__)

        # 中心库连接配置
        self.center_db_config = {
            'host': '***********',
            'port': 1433,
            'database': 'Examdb',
            'username': 'tj',
            'password': 'jiarentijian',
            'driver': 'ODBC Driver 17 for SQL Server'
        }

        # 创建中心库连接
        self.center_connection_string = (
            f"DRIVER={{{self.center_db_config['driver']}}};"
            f"SERVER={self.center_db_config['host']},{self.center_db_config['port']};"
            f"DATABASE={self.center_db_config['database']};"
            f"UID={self.center_db_config['username']};"
            f"PWD={self.center_db_config['password']};"
            f"TrustServerCertificate=yes;"
        )

        # 延迟初始化的组件
        self.db_service = None
        self._initialized = False

    def _ensure_initialized(self):
        """确保服务已初始化"""
        if self._initialized:
            return

        # 创建数据库服务
        self.db_service = create_optimized_db_service(self.center_connection_string)

        # 测试连接
        try:
            test_result = self.db_service.connection_manager.execute_query_with_cache(
                self.center_connection_string, "SELECT DB_NAME() as current_db",
                cache_key='test_center_db', use_cache=False
            )
            if test_result:
                current_db = test_result[0]['current_db']
                self.logger.info(f"中心库连接成功，数据库: {current_db}")
            else:
                self.logger.warning("无法获取当前数据库名称")
        except Exception as e:
            self.logger.error(f"中心库连接测试失败: {e}")

        self._initialized = True

    def _execute_non_query(self, sql: str, params: dict = None) -> int:
        """执行非查询语句（INSERT/UPDATE/DELETE）"""
        try:
            import pyodbc
            import re

            # 直接使用pyodbc连接执行非查询语句
            with pyodbc.connect(self.center_connection_string) as conn:
                cursor = conn.cursor()
                try:
                    if params:
                        # 按照SQL中参数出现的顺序替换参数
                        param_pattern = r':(\w+)'
                        param_names = re.findall(param_pattern, sql)

                        # 替换所有命名参数为?
                        modified_sql = re.sub(param_pattern, '?', sql)

                        # 按照参数在SQL中出现的顺序构建参数值列表
                        param_values = []
                        for param_name in param_names:
                            if param_name in params:
                                param_values.append(params[param_name])
                            else:
                                raise ValueError(f"缺少参数: {param_name}")

                        self.logger.debug(f"执行SQL: {modified_sql}")
                        self.logger.debug(f"参数: {param_values}")

                        cursor.execute(modified_sql, param_values)
                    else:
                        cursor.execute(sql)

                    affected_rows = cursor.rowcount
                    conn.commit()

                    self.logger.debug(f"影响行数: {affected_rows}")
                    return affected_rows

                finally:
                    cursor.close()

        except Exception as e:
            self.logger.error(f"执行非查询语句失败: {e}")
            raise
    
    def get_all_organizations(self) -> List[CenterOrganizationConfig]:
        """获取所有机构配置"""
        self._ensure_initialized()
        try:
            sql = """
            SELECT
                id, cOrgCode, cOrgName, cOrgType, cStatus,
                cTianjianMicCode, cTianjianMiscId, cTianjianApiKey, cTianjianBaseUrl,
                cDbHost, cDbPort, cDbName, cDbUser, cDbPassword, cDbDriver,
                cShopCode, cDefaultDeptCode, cSyncEnabled, cSyncIntervals,
                dCreateTime, dUpdateTime, cCreateUser, cUpdateUser
            FROM T_Center_Organization_Config
            ORDER BY cOrgCode
            """

            result = self.db_service.connection_manager.execute_query_with_cache(
                self.center_connection_string, sql, 
                cache_key='center_all_organizations', use_cache=False
            )
            
            organizations = []
            for row in result:
                org = CenterOrganizationConfig(
                    id=row.get('id', 0),
                    org_code=row.get('cOrgCode', ''),
                    org_name=row.get('cOrgName', ''),
                    org_type=row.get('cOrgType', 'HOSPITAL'),
                    status=row.get('cStatus', '1'),
                    tianjian_mic_code=row.get('cTianjianMicCode', ''),
                    tianjian_misc_id=row.get('cTianjianMiscId', ''),
                    tianjian_api_key=row.get('cTianjianApiKey', ''),
                    tianjian_base_url=row.get('cTianjianBaseUrl', 'http://**************:9300'),
                    db_host=row.get('cDbHost', ''),
                    db_port=row.get('cDbPort', 1433),
                    db_name=row.get('cDbName', ''),
                    db_user=row.get('cDbUser', ''),
                    db_password=row.get('cDbPassword', ''),
                    db_driver=row.get('cDbDriver', 'ODBC Driver 17 for SQL Server'),
                    shop_code=row.get('cShopCode', ''),
                    default_dept_code=row.get('cDefaultDeptCode', ''),
                    sync_enabled=row.get('cSyncEnabled', '1'),
                    sync_intervals=row.get('cSyncIntervals', 60),
                    create_time=str(row.get('dCreateTime', '')),
                    update_time=str(row.get('dUpdateTime', '')),
                    create_user=row.get('cCreateUser', 'SYSTEM'),
                    update_user=row.get('cUpdateUser', 'SYSTEM')
                )
                organizations.append(org)
            
            return organizations
            
        except Exception as e:
            self.logger.error(f"获取机构配置失败: {e}")
            return []
    
    def get_organization_by_code(self, org_code: str) -> Optional[CenterOrganizationConfig]:
        """根据机构编码获取配置"""
        self._ensure_initialized()
        try:
            sql = """
            SELECT
                id, cOrgCode, cOrgName, cOrgType, cStatus,
                cTianjianMicCode, cTianjianMiscId, cTianjianApiKey, cTianjianBaseUrl,
                cDbHost, cDbPort, cDbName, cDbUser, cDbPassword, cDbDriver,
                cShopCode, cDefaultDeptCode, cSyncEnabled, cSyncIntervals,
                dCreateTime, dUpdateTime, cCreateUser, cUpdateUser
            FROM T_Center_Organization_Config
            WHERE cOrgCode = :org_code
            """

            result = self.db_service.connection_manager.execute_query_with_cache(
                self.center_connection_string, sql, {'org_code': org_code},
                cache_key=f'center_organization_{org_code}', use_cache=True
            )
            
            if result:
                row = result[0]
                return CenterOrganizationConfig(
                    id=row.get('id', 0),
                    org_code=row.get('cOrgCode', ''),
                    org_name=row.get('cOrgName', ''),
                    org_type=row.get('cOrgType', 'HOSPITAL'),
                    status=row.get('cStatus', '1'),
                    tianjian_mic_code=row.get('cTianjianMicCode', ''),
                    tianjian_misc_id=row.get('cTianjianMiscId', ''),
                    tianjian_api_key=row.get('cTianjianApiKey', ''),
                    tianjian_base_url=row.get('cTianjianBaseUrl', 'http://**************:9300'),
                    db_host=row.get('cDbHost', ''),
                    db_port=row.get('cDbPort', 1433),
                    db_name=row.get('cDbName', ''),
                    db_user=row.get('cDbUser', ''),
                    db_password=row.get('cDbPassword', ''),
                    db_driver=row.get('cDbDriver', 'ODBC Driver 17 for SQL Server'),
                    shop_code=row.get('cShopCode', ''),
                    default_dept_code=row.get('cDefaultDeptCode', ''),
                    sync_enabled=row.get('cSyncEnabled', '1'),
                    sync_intervals=row.get('cSyncIntervals', 60),
                    create_time=str(row.get('dCreateTime', '')),
                    update_time=str(row.get('dUpdateTime', '')),
                    create_user=row.get('cCreateUser', 'SYSTEM'),
                    update_user=row.get('cUpdateUser', 'SYSTEM')
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取机构配置失败: {e}")
            return None
    
    def save_organization(self, org: CenterOrganizationConfig) -> Tuple[bool, str]:
        """保存机构配置"""
        try:
            if org.id > 0:
                # 更新现有记录
                sql = """
                UPDATE T_Center_Organization_Config SET
                    cOrgName = :org_name, cOrgType = :org_type, cStatus = :status,
                    cTianjianMicCode = :tianjian_mic_code, cTianjianMiscId = :tianjian_misc_id,
                    cTianjianApiKey = :tianjian_api_key, cTianjianBaseUrl = :tianjian_base_url,
                    cDbHost = :db_host, cDbPort = :db_port, cDbName = :db_name,
                    cDbUser = :db_user, cDbPassword = :db_password, cDbDriver = :db_driver,
                    cShopCode = :shop_code, cDefaultDeptCode = :default_dept_code,
                    cSyncEnabled = :sync_enabled, cSyncIntervals = :sync_intervals,
                    dUpdateTime = GETDATE(), cUpdateUser = :update_user
                WHERE id = :id
                """

                params = {
                    'org_name': org.org_name, 'org_type': org.org_type, 'status': org.status,
                    'tianjian_mic_code': org.tianjian_mic_code, 'tianjian_misc_id': org.tianjian_misc_id,
                    'tianjian_api_key': org.tianjian_api_key, 'tianjian_base_url': org.tianjian_base_url,
                    'db_host': org.db_host, 'db_port': org.db_port, 'db_name': org.db_name,
                    'db_user': org.db_user, 'db_password': org.db_password, 'db_driver': org.db_driver,
                    'shop_code': org.shop_code, 'default_dept_code': org.default_dept_code,
                    'sync_enabled': org.sync_enabled, 'sync_intervals': org.sync_intervals,
                    'update_user': org.update_user, 'id': org.id
                }
            else:
                # 插入新记录
                sql = """
                INSERT INTO T_Center_Organization_Config (
                    cOrgCode, cOrgName, cOrgType, cStatus,
                    cTianjianMicCode, cTianjianMiscId, cTianjianApiKey, cTianjianBaseUrl,
                    cDbHost, cDbPort, cDbName, cDbUser, cDbPassword, cDbDriver,
                    cShopCode, cDefaultDeptCode, cSyncEnabled, cSyncIntervals,
                    cCreateUser, cUpdateUser
                ) VALUES (
                    :org_code, :org_name, :org_type, :status,
                    :tianjian_mic_code, :tianjian_misc_id, :tianjian_api_key, :tianjian_base_url,
                    :db_host, :db_port, :db_name, :db_user, :db_password, :db_driver,
                    :shop_code, :default_dept_code, :sync_enabled, :sync_intervals,
                    :create_user, :update_user
                )
                """

                params = {
                    'org_code': org.org_code, 'org_name': org.org_name, 'org_type': org.org_type, 'status': org.status,
                    'tianjian_mic_code': org.tianjian_mic_code, 'tianjian_misc_id': org.tianjian_misc_id,
                    'tianjian_api_key': org.tianjian_api_key, 'tianjian_base_url': org.tianjian_base_url,
                    'db_host': org.db_host, 'db_port': org.db_port, 'db_name': org.db_name,
                    'db_user': org.db_user, 'db_password': org.db_password, 'db_driver': org.db_driver,
                    'shop_code': org.shop_code, 'default_dept_code': org.default_dept_code,
                    'sync_enabled': org.sync_enabled, 'sync_intervals': org.sync_intervals,
                    'create_user': org.create_user, 'update_user': org.update_user
                }
            
            # 执行INSERT/UPDATE语句
            affected_rows = self._execute_non_query(sql, params)

            if affected_rows > 0:
                if org.id > 0:
                    return True, f"更新成功，影响 {affected_rows} 条记录"
                else:
                    return True, f"新增成功，影响 {affected_rows} 条记录"
            else:
                return False, "保存失败，没有记录被影响"
            
        except Exception as e:
            error_msg = f"保存机构配置失败: {e}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def delete_organization(self, org_code: str) -> Tuple[bool, str]:
        """删除机构配置"""
        try:
            # 暂时跳过关联数据检查，因为相关表可能不存在
            # TODO: 如果需要检查关联数据，需要先创建相关表

            # 删除机构配置
            sql = "DELETE FROM T_Center_Organization_Config WHERE cOrgCode = :org_code"
            affected_rows = self._execute_non_query(sql, {'org_code': org_code})

            if affected_rows > 0:
                return True, f"删除成功，影响 {affected_rows} 条记录"
            else:
                return False, "未找到要删除的机构配置"
            
        except Exception as e:
            error_msg = f"删除机构配置失败: {e}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def test_organization_connection(self, org: CenterOrganizationConfig) -> Tuple[bool, str]:
        """测试机构连接"""
        try:
            # 测试数据库连接
            connection_string = org.get_db_connection_string()
            test_db_service = create_optimized_db_service(connection_string)
            
            # 执行测试查询
            test_sql = "SELECT COUNT(*) as count FROM T_Register_Main"
            result = test_db_service.connection_manager.execute_query_with_cache(
                connection_string, test_sql, 
                cache_key='test_connection', use_cache=False
            )
            
            if result:
                count = result[0]['count']
                return True, f"连接成功，共有 {count} 条体检记录"
            else:
                return False, "连接失败，无法获取数据"
                
        except Exception as e:
            return False, f"连接测试失败: {e}"
    
    def get_organization_summary(self) -> List[Dict[str, Any]]:
        """获取机构统计摘要"""
        self._ensure_initialized()
        try:
            sql = """
            SELECT
                cOrgCode, cOrgName, cOrgType, cStatus,
                cContactPerson, cContactPhone, dCreateTime,
                InterfaceCount, EnabledInterfaceCount,
                LastSyncTime, TotalSyncCount, SuccessSyncCount
            FROM V_Center_Organization_Summary
            ORDER BY cOrgCode
            """

            result = self.db_service.connection_manager.execute_query_with_cache(
                self.center_connection_string, sql,
                cache_key='center_org_summary', use_cache=False
            )
            
            return result or []
            
        except Exception as e:
            self.logger.error(f"获取机构统计摘要失败: {e}")
            return []


# 全局服务实例
_center_org_service = None

def get_center_organization_service() -> CenterOrganizationService:
    """获取中心库机构配置服务实例（单例模式）"""
    global _center_org_service
    if _center_org_service is None:
        _center_org_service = CenterOrganizationService()
    return _center_org_service
