#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查09机构数据库中的AI诊断相关数据
用于确认当前机构、检查必要字段、统计AI诊断数据情况

主要功能：
1. 确认当前机构是否为09（可通过--org-code参数指定其他机构）
2. 检查T_Register_Main表中的AIDiagnosisStatus和cCanDiagDate字段
3. 统计T_Register_Main表中的数据情况
4. 显示样本数据帮助理解数据结构
5. 查找今天的AI诊断记录（cCanDiagDate为今天且AIDiagnosisStatus=1）
6. 支持导出结果到JSON文件

使用方法：
- 默认检查当前机构: python check_09_ai_diagnosis_data.py
- 检查指定机构: python check_09_ai_diagnosis_data.py --org-code 09
- 指定门店编码: python check_09_ai_diagnosis_data.py --shop-code 09
- 自定义样本数量: python check_09_ai_diagnosis_data.py --sample-limit 10
- 导出JSON结果: python check_09_ai_diagnosis_data.py --export-json result.json
- 查看详细帮助: python check_09_ai_diagnosis_data.py --help

示例：
python check_09_ai_diagnosis_data.py --org-code 09 --sample-limit 5 --export-json ai_check.json
"""

import json
from datetime import datetime
from typing import Dict, Any, List, Optional
from optimized_database_service import create_optimized_db_service
from config import Config
from multi_org_config import get_current_org_config


def check_column_exists(db_service, table_name: str, column_name: str) -> bool:
    """
    检查表中是否存在指定字段
    
    Args:
        db_service: 数据库服务实例
        table_name: 表名
        column_name: 字段名
        
    Returns:
        是否存在该字段
    """
    try:
        sql = f"""
        SELECT COUNT(*) as column_exists
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = '{table_name}' 
        AND COLUMN_NAME = '{column_name}'
        """
        
        result = db_service.connection_manager.execute_query_with_cache(
            db_service.connection_string,
            sql,
            cache_key=f"column_check_{table_name}_{column_name}",
            use_cache=True
        )
        
        return result and result[0]['column_exists'] > 0
        
    except Exception as e:
        print(f"[ERROR] 检查字段 {table_name}.{column_name} 失败: {e}")
        return False


def get_data_statistics(db_service, org_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    获取T_Register_Main表中的数据统计
    
    Args:
        db_service: 数据库服务实例
        org_config: 机构配置
        
    Returns:
        统计数据字典
    """
    try:
        shop_code = org_config.get('shop_code', '')
        today = datetime.now().strftime('%Y-%m-%d')
        
        # 基础统计查询
        base_sql = f"""
        SELECT 
            COUNT(*) as total_records,
            COUNT(cCanDiagDate) as has_cCanDiagDate_count,
            SUM(CASE WHEN CONVERT(date, cCanDiagDate) = CONVERT(date, GETDATE()) THEN 1 ELSE 0 END) as today_cCanDiagDate_count,
            SUM(CASE WHEN AIDiagnosisStatus = 1 THEN 1 ELSE 0 END) as ai_status_1_count,
            SUM(CASE WHEN CONVERT(date, cCanDiagDate) = CONVERT(date, GETDATE()) AND AIDiagnosisStatus = 1 THEN 1 ELSE 0 END) as today_and_ai_status_1_count
        FROM T_Register_Main
        WHERE cShopCode = '{shop_code}'
        """
        
        result = db_service.connection_manager.execute_query_with_cache(
            db_service.connection_string,
            base_sql,
            cache_key=f"data_statistics_{shop_code}_{today}",
            use_cache=False
        )
        
        if result:
            # 确保所有值都不为None
            stats = result[0]
            return {
                'total_records': stats.get('total_records', 0) or 0,
                'has_cCanDiagDate_count': stats.get('has_cCanDiagDate_count', 0) or 0,
                'today_cCanDiagDate_count': stats.get('today_cCanDiagDate_count', 0) or 0,
                'ai_status_1_count': stats.get('ai_status_1_count', 0) or 0,
                'today_and_ai_status_1_count': stats.get('today_and_ai_status_1_count', 0) or 0
            }
        else:
            return {}
            
    except Exception as e:
        print(f"[ERROR] 获取数据统计失败: {e}")
        return {}


def get_sample_data(db_service, org_config: Dict[str, Any], limit: int = 5) -> List[Dict[str, Any]]:
    """
    获取样本数据
    
    Args:
        db_service: 数据库服务实例
        org_config: 机构配置
        limit: 获取记录数限制
        
    Returns:
        样本数据列表
    """
    try:
        shop_code = org_config.get('shop_code', '')
        
        # 获取最近的样本数据
        sample_sql = f"""
        SELECT TOP ({limit})
            cClientCode,
            cName,
            cStatus,
            dOperdate,
            cCanDiagDate,
            AIDiagnosisStatus,
            CASE 
                WHEN AIDiagnosisStatus IS NULL THEN 'NULL'
                WHEN AIDiagnosisStatus = 0 THEN '待诊断'
                WHEN AIDiagnosisStatus = 1 THEN '待传输'
                WHEN AIDiagnosisStatus = 2 THEN '已传输'
                ELSE CAST(AIDiagnosisStatus AS VARCHAR)
            END as ai_status_desc
        FROM T_Register_Main
        WHERE cShopCode = '{shop_code}'
            AND cCanDiagDate IS NOT NULL
        ORDER BY dOperdate DESC
        """
        
        result = db_service.connection_manager.execute_query_with_cache(
            db_service.connection_string,
            sample_sql,
            cache_key=f"sample_data_{shop_code}_{limit}",
            use_cache=False
        )
        
        return result or []
        
    except Exception as e:
        print(f"[ERROR] 获取样本数据失败: {e}")
        return []


def get_today_ai_diagnosis_records(db_service, org_config: Dict[str, Any], limit: int = 10) -> List[Dict[str, Any]]:
    """
    获取今天的AI诊断记录（cCanDiagDate为今天且AIDiagnosisStatus=1）
    
    Args:
        db_service: 数据库服务实例
        org_config: 机构配置
        limit: 获取记录数限制
        
    Returns:
        今天的AI诊断记录列表
    """
    try:
        shop_code = org_config.get('shop_code', '')
        
        today_ai_sql = f"""
        SELECT TOP ({limit})
            cClientCode,
            cName,
            cStatus,
            dOperdate,
            cCanDiagDate,
            AIDiagnosisStatus,
            DATEDIFF(HOUR, cCanDiagDate, GETDATE()) as hours_since_diagnosis
        FROM T_Register_Main
        WHERE cShopCode = '{shop_code}'
            AND CONVERT(date, cCanDiagDate) = CONVERT(date, GETDATE())
            AND AIDiagnosisStatus = 1
        ORDER BY cCanDiagDate DESC
        """
        
        result = db_service.connection_manager.execute_query_with_cache(
            db_service.connection_string,
            today_ai_sql,
            cache_key=f"today_ai_diagnosis_{shop_code}_{limit}",
            use_cache=False
        )
        
        return result or []
        
    except Exception as e:
        print(f"[ERROR] 获取今天AI诊断记录失败: {e}")
        return []


def check_database_info(db_service) -> Dict[str, Any]:
    """
    检查数据库基本信息
    
    Args:
        db_service: 数据库服务实例
        
    Returns:
        数据库信息字典
    """
    try:
        # 获取数据库服务器详细信息
        db_info_query = """
        SELECT 
            @@SERVERNAME as server_name, 
            DB_NAME() as database_name,
            GETDATE() as current_datetime,
            @@VERSION as sql_version
        """

        result = db_service.connection_manager.execute_query_with_cache(
            db_service.connection_string,
            db_info_query,
            cache_key="db_info_check",
            use_cache=False
        )

        if result:
            return result[0]
        else:
            return {}
            
    except Exception as e:
        print(f"[ERROR] 获取数据库信息失败: {e}")
        return {}


def main():
    """主函数"""
    import argparse
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='机构数据库AI诊断数据检查工具')
    parser.add_argument('--org-code', type=str, help='指定机构编码（默认使用当前机构）')
    parser.add_argument('--shop-code', type=str, help='指定门店编码（覆盖机构配置）')
    parser.add_argument('--sample-limit', type=int, default=5, help='样本数据显示数量（默认5条）')
    parser.add_argument('--today-limit', type=int, default=10, help='今天AI诊断记录显示数量（默认10条）')
    parser.add_argument('--export-json', type=str, help='导出结果到JSON文件')
    parser.add_argument('--verbose', action='store_true', help='显示详细信息')
    
    args = parser.parse_args()
    
    print("=" * 80)
    if args.org_code:
        print(f"{args.org_code}机构数据库AI诊断数据检查工具")
    else:
        print("09机构数据库AI诊断数据检查工具")
    print("=" * 80)
    
    result_data = {}  # 用于存储结果数据
    
    try:
        # 1. 获取机构配置
        print("\n1. 检查机构配置")
        print("-" * 40)
        
        if args.org_code:
            # 使用指定的机构编码
            from multi_org_config import get_org_config_by_code
            org_config = get_org_config_by_code(args.org_code)
            if not org_config:
                print(f"[ERROR] 无法找到机构编码 {args.org_code} 的配置")
                return
            print(f"[INFO] 使用指定的机构编码: {args.org_code}")
        else:
            # 使用当前机构配置
            org_config = get_current_org_config()
            if not org_config:
                print("[ERROR] 无法获取机构配置")
                return
        
        org_code = org_config.get('org_code', '未知')
        org_name = org_config.get('org_name', '未知')
        shop_code = args.shop_code or org_config.get('shop_code', '未知')
        
        # 如果命令行指定了shop_code，更新配置
        if args.shop_code:
            org_config['shop_code'] = args.shop_code
            print(f"[INFO] 使用指定的门店编码: {args.shop_code}")
        
        print(f"机构编码: {org_code}")
        print(f"机构名称: {org_name}")
        print(f"门店编码: {shop_code}")
        
        result_data['org_info'] = {
            'org_code': org_code,
            'org_name': org_name,
            'shop_code': shop_code
        }
        
        # 确认是否为09机构
        if org_code != '09':
            print(f"[WARNING] 当前机构编码为 {org_code}，不是09机构")
            print("[INFO] 将继续检查当前机构的AI诊断数据")
        else:
            print("[OK] 当前机构为09机构")
        
        # 2. 创建数据库连接
        print("\n2. 建立数据库连接")
        print("-" * 40)
        
        if org_config.get('db_host'):
            connection_string = (
                f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                f"SERVER={org_config['db_host']},{org_config.get('db_port', 1433)};"
                f"DATABASE={org_config['db_name']};"
                f"UID={org_config['db_user']};"
                f"PWD={org_config['db_password']}"
            )
        else:
            # 兜底使用统一配置
            connection_string = Config.get_interface_db_connection_string()
        
        db_service = create_optimized_db_service(connection_string)
        
        # 3. 检查数据库连接信息
        print("\n3. 数据库连接信息")
        print("-" * 40)
        
        db_info = check_database_info(db_service)
        if db_info:
            print(f"服务器名称: {db_info.get('server_name', 'N/A')}")
            print(f"数据库名称: {db_info.get('database_name', 'N/A')}")
            print(f"当前时间: {db_info.get('current_datetime', 'N/A')}")
            print(f"SQL版本: {db_info.get('sql_version', 'N/A')[:50]}...")
            
            result_data['db_info'] = db_info
        else:
            print("[ERROR] 无法获取数据库信息")
            return
        
        # 4. 检查必要字段是否存在
        print("\n4. 检查T_Register_Main表必要字段")
        print("-" * 40)
        
        required_fields = ['AIDiagnosisStatus', 'cCanDiagDate']
        field_status = {}
        
        for field in required_fields:
            exists = check_column_exists(db_service, 'T_Register_Main', field)
            field_status[field] = exists
            status_text = "[OK]" if exists else "[MISSING]"
            print(f"{status_text} {field}: {'存在' if exists else '不存在'}")
        
        result_data['field_status'] = field_status
        
        # 如果必要字段不存在，给出警告
        missing_fields = [field for field, exists in field_status.items() if not exists]
        if missing_fields:
            print(f"\n[WARNING] 缺少必要字段: {', '.join(missing_fields)}")
            print("请确认数据库表结构是否正确")
        
        # 5. 获取数据统计
        print("\n5. T_Register_Main表数据统计")
        print("-" * 40)
        
        if all(field_status.values()):
            statistics = get_data_statistics(db_service, org_config)
            
            if statistics:
                total = statistics.get('total_records', 0)
                has_can_diag = statistics.get('has_cCanDiagDate_count', 0)
                today_can_diag = statistics.get('today_cCanDiagDate_count', 0)
                ai_status_1 = statistics.get('ai_status_1_count', 0)
                today_ai_1 = statistics.get('today_and_ai_status_1_count', 0)
                
                print(f"总记录数: {total:,}")
                print(f"有cCanDiagDate的记录数: {has_can_diag:,}")
                print(f"cCanDiagDate为今天的记录数: {today_can_diag:,}")
                print(f"AIDiagnosisStatus=1的记录数: {ai_status_1:,}")
                print(f"今天且AIDiagnosisStatus=1的记录数: {today_ai_1:,}")
                
                # 计算比例
                if total > 0:
                    print(f"\n比例统计:")
                    print(f"有cCanDiagDate比例: {has_can_diag / total * 100:.2f}%")
                    print(f"今天AI待传输比例: {today_ai_1 / total * 100:.4f}%")
                else:
                    print(f"\n比例统计:")
                    print(f"有cCanDiagDate比例: 0.00%")
                    print(f"今天AI待传输比例: 0.0000%")
                
                result_data['statistics'] = statistics
            else:
                print("[ERROR] 无法获取统计数据")
        else:
            print("[SKIP] 由于缺少必要字段，跳过数据统计")
        
        # 6. 显示样本数据
        print(f"\n6. 样本数据（最近{args.sample_limit}条有cCanDiagDate的记录）")
        print("-" * 40)
        
        if all(field_status.values()):
            sample_data = get_sample_data(db_service, org_config, args.sample_limit)
            
            if sample_data:
                print(f"{'客户编码':<15} {'姓名':<10} {'状态':<5} {'体检日期':<20} {'AI诊断时间':<20} {'AI状态':<10}")
                print("-" * 100)
                
                for record in sample_data:
                    client_code = record.get('cClientCode', '')[:14]
                    name = record.get('cName', '')[:9]
                    status = record.get('cStatus', '')
                    exam_date = str(record.get('dOperdate', ''))[:19]
                    diag_date = str(record.get('cCanDiagDate', ''))[:19]
                    ai_status = record.get('ai_status_desc', '')
                    
                    print(f"{client_code:<15} {name:<10} {status:<5} {exam_date:<20} {diag_date:<20} {ai_status:<10}")
                
                result_data['sample_data'] = sample_data
            else:
                print("[INFO] 没有找到样本数据")
        else:
            print("[SKIP] 由于缺少必要字段，跳过样本数据显示")
        
        # 7. 显示今天的AI诊断记录
        print(f"\n7. 今天的AI诊断记录（cCanDiagDate为今天且AIDiagnosisStatus=1，限制{args.today_limit}条）")
        print("-" * 40)
        
        if all(field_status.values()):
            today_records = get_today_ai_diagnosis_records(db_service, org_config, args.today_limit)
            
            if today_records:
                print(f"找到 {len(today_records)} 条今天的AI诊断记录:")
                print(f"{'客户编码':<15} {'姓名':<10} {'体检日期':<20} {'AI诊断时间':<20} {'距离现在':<10}")
                print("-" * 100)
                
                for record in today_records:
                    client_code = record.get('cClientCode', '')[:14]
                    name = record.get('cName', '')[:9]
                    exam_date = str(record.get('dOperdate', ''))[:19]
                    diag_date = str(record.get('cCanDiagDate', ''))[:19]
                    hours_since = record.get('hours_since_diagnosis', 0)
                    
                    print(f"{client_code:<15} {name:<10} {exam_date:<20} {diag_date:<20} {hours_since}小时前")
                
                result_data['today_records'] = today_records
            else:
                print("[INFO] 今天没有待传输的AI诊断记录")
        else:
            print("[SKIP] 由于缺少必要字段，跳过今天AI诊断记录显示")
        
        # 8. 总结和建议
        print("\n8. 检查总结和建议")
        print("-" * 40)
        
        summary = []
        if org_code == '09':
            print("[OK] 机构编码确认为09")
            summary.append("机构编码确认为09")
        else:
            print(f"[INFO] 机构编码为 {org_code}（非09机构）")
            summary.append(f"机构编码为 {org_code}（非09机构）")
        
        if all(field_status.values()):
            print("[OK] 所有必要字段都存在")
            summary.append("所有必要字段都存在")
            
            if statistics:
                today_count = statistics.get('today_and_ai_status_1_count', 0) or 0
                if today_count > 0:
                    print(f"[INFO] 今天有 {today_count} 条记录待传输AI诊断结果")
                    print("[建议] 可以运行AI诊断轮询接口进行数据传输")
                    summary.append(f"今天有 {today_count} 条记录待传输AI诊断结果")
                else:
                    print("[INFO] 今天没有待传输的AI诊断记录")
                    summary.append("今天没有待传输的AI诊断记录")
        else:
            print(f"[WARNING] 缺少必要字段: {', '.join(missing_fields)}")
            print("[建议] 请检查数据库表结构，确认是否需要添加相应字段")
            summary.append(f"缺少必要字段: {', '.join(missing_fields)}")
        
        result_data['summary'] = summary
        result_data['check_time'] = datetime.now().isoformat()
        
        # 9. 导出JSON结果
        if args.export_json:
            try:
                with open(args.export_json, 'w', encoding='utf-8') as f:
                    json.dump(result_data, f, ensure_ascii=False, indent=2, default=str)
                print(f"\n[INFO] 检查结果已导出到: {args.export_json}")
            except Exception as e:
                print(f"\n[ERROR] 导出JSON文件失败: {e}")
        
        print("\n" + "=" * 80)
        print("检查完成")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n[ERROR] 检查过程中发生异常: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()


if __name__ == '__main__':
    main()