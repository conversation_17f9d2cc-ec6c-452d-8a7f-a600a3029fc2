#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查10号接口返回报文格式是否符合标准
"""

import requests
import json
from datetime import datetime, timedelta

def test_response_format():
    """测试响应格式是否符合标准"""
    print("10号接口报文格式检查")
    print("=" * 50)
    
    base_url = "http://localhost:5007"
    
    # 构建测试请求 - 限制返回1条记录便于检查
    end_time = datetime.now()
    start_time = end_time - timedelta(days=7)
    
    request_data = {
        "start": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "peNo": "",
        "hospitalCode": ""
    }
    
    try:
        response = requests.post(
            f"{base_url}/dx/inter/batchGetPeInfo",
            json=request_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print("顶层字段检查:")
            print(f"  code: {result.get('code')} (类型: {type(result.get('code')).__name__})")
            print(f"  msg: '{result.get('msg')}' (类型: {type(result.get('msg')).__name__})")
            print(f"  data: 数组长度 {len(result.get('data', []))} (类型: {type(result.get('data')).__name__})")
            print(f"  reponseTime: {result.get('reponseTime')} (类型: {type(result.get('reponseTime')).__name__})")
            
            if result.get('data') and len(result['data']) > 0:
                first_record = result['data'][0]
                
                print("\n第一条记录结构:")
                print(f"  peUserInfo: 存在 (字段数: {len(first_record.get('peUserInfo', {}))})")
                print(f"  archiveInfo: 存在 (字段数: {len(first_record.get('archiveInfo', {}))})")
                print(f"  hospital: 存在 (字段数: {len(first_record.get('hospital', {}))})")
                
                # 重点检查关键字段
                pe_user_info = first_record.get('peUserInfo', {})
                
                print("\npeUserInfo关键字段检查:")
                critical_fields = [
                    'archiveNo', 'name', 'icCode', 'sex', 'birthday', 'peno', 
                    'peDate', 'phone', 'ms', 'pregnantState', 'vipLevel', 
                    'medicalType', 'isGroup', 'peStates', 'age', 'applyItemList', 
                    'currentNodeType', 'pePackage'
                ]
                
                missing_fields = []
                for field in critical_fields:
                    if field in pe_user_info:
                        value = pe_user_info[field]
                        if isinstance(value, dict):
                            print(f"  {field}: 对象 (子字段: {list(value.keys())})")
                        elif isinstance(value, list):
                            print(f"  {field}: 数组 (长度: {len(value)})")
                        else:
                            print(f"  {field}: {value}")
                    else:
                        missing_fields.append(field)
                        print(f"  {field}: [缺失]")
                
                # 检查嵌套对象的code/name结构
                print("\n嵌套对象code/name结构检查:")
                nested_objects = ['sex', 'ms', 'pregnantState', 'vipLevel', 'medicalType', 'peStates', 'pePackage']
                for obj_name in nested_objects:
                    if obj_name in pe_user_info and isinstance(pe_user_info[obj_name], dict):
                        obj = pe_user_info[obj_name]
                        has_code = 'code' in obj
                        has_name = 'name' in obj
                        print(f"  {obj_name}: code={has_code}, name={has_name} - {obj}")
                    else:
                        print(f"  {obj_name}: [结构错误或缺失]")
                
                # 显示完整的第一条记录（格式化）
                print(f"\n完整记录样例 (仅显示前500字符):")
                record_json = json.dumps(first_record, ensure_ascii=False, indent=2)
                print(record_json[:800] + "..." if len(record_json) > 800 else record_json)
                
                # 总结
                print(f"\n检查总结:")
                print(f"  顶层结构: 符合标准")
                print(f"  记录结构: 符合标准")
                print(f"  关键字段: {len(critical_fields) - len(missing_fields)}/{len(critical_fields)} 完整")
                if missing_fields:
                    print(f"  缺失字段: {missing_fields}")
                else:
                    print(f"  字段完整性: 100%")
                    
        else:
            print(f"请求失败: HTTP {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"检查异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_response_format()