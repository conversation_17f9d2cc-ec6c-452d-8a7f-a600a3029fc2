#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查10号接口返回报文格式是否符合标准
"""

import requests
import json
from datetime import datetime, timedelta

def test_response_format():
    """测试响应格式是否符合标准"""
    print("10号接口报文格式检查")
    print("=" * 50)
    
    base_url = "http://localhost:5007"
    
    # 构建测试请求
    end_time = datetime.now()
    start_time = end_time - timedelta(days=7)
    
    request_data = {
        "start": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "peNo": "",
        "hospitalCode": ""
    }
    
    try:
        response = requests.post(
            f"{base_url}/dx/inter/batchGetPeInfo",
            json=request_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print("1. 顶层字段检查")
            print("-" * 30)
            
            # 检查顶层必需字段
            required_top_fields = ['code', 'msg', 'data', 'reponseTime']
            for field in required_top_fields:
                if field in result:
                    print(f"✅ {field}: {type(result[field]).__name__}")
                else:
                    print(f"❌ 缺少字段: {field}")
            
            if result.get('data') and len(result['data']) > 0:
                first_record = result['data'][0]
                
                print("\n2. 数据记录结构检查")
                print("-" * 30)
                
                # 检查第一层结构
                required_record_fields = ['peUserInfo', 'archiveInfo', 'hospital']
                for field in required_record_fields:
                    if field in first_record:
                        print(f"✅ {field}: 存在")
                    else:
                        print(f"❌ 缺少字段: {field}")
                
                # 检查peUserInfo结构
                if 'peUserInfo' in first_record:
                    pe_user_info = first_record['peUserInfo']
                    
                    print("\n3. peUserInfo字段检查")
                    print("-" * 30)
                    
                    required_pe_fields = [
                        'archiveNo', 'name', 'icCode', 'sex', 'birthday', 'peno', 
                        'peDate', 'phone', 'ms', 'pregnantState', 'vipLevel', 
                        'medicalType', 'isGroup', 'company', 'workDept', 'teamNo',
                        'professional', 'workAge', 'peStates', 'deptCount', 'age',
                        'deptFinishTime', 'firstCheckFinishTime', 'firstCheckFinishDoctor',
                        'mainCheckFinishTime', 'mainCheckFinishDoctor', 'forbidGoCheck',
                        'reportPrint', 'reportGot', 'replacementInspectionMark',
                        'applyItemList', 'currentNodeType', 'pePackage'
                    ]
                    
                    missing_fields = []
                    for field in required_pe_fields:
                        if field in pe_user_info:
                            print(f"✅ {field}")
                        else:
                            print(f"❌ 缺少字段: {field}")
                            missing_fields.append(field)
                    
                    # 检查嵌套对象结构
                    print("\n4. 嵌套对象结构检查")
                    print("-" * 30)
                    
                    nested_objects = {
                        'sex': ['code', 'name'],
                        'ms': ['code', 'name'],
                        'pregnantState': ['code', 'name'],
                        'vipLevel': ['code', 'name'],
                        'medicalType': ['code', 'name'],
                        'peStates': ['code', 'name'],
                        'firstCheckFinishDoctor': ['code', 'name'],
                        'mainCheckFinishDoctor': ['code', 'name'],
                        'pePackage': ['code', 'name']
                    }
                    
                    for obj_name, required_subfields in nested_objects.items():
                        if obj_name in pe_user_info and isinstance(pe_user_info[obj_name], dict):
                            obj = pe_user_info[obj_name]
                            for subfield in required_subfields:
                                if subfield in obj:
                                    print(f"✅ {obj_name}.{subfield}")
                                else:
                                    print(f"❌ 缺少字段: {obj_name}.{subfield}")
                        else:
                            print(f"❌ {obj_name} 不是对象结构")
                    
                    # 检查archiveInfo结构
                    if 'archiveInfo' in first_record:
                        archive_info = first_record['archiveInfo']
                        
                        print("\n5. archiveInfo字段检查")
                        print("-" * 30)
                        
                        required_archive_fields = ['name', 'icCode', 'sex', 'birthday', 'peNoList']
                        for field in required_archive_fields:
                            if field in archive_info:
                                print(f"✅ {field}")
                            else:
                                print(f"❌ 缺少字段: {field}")
                    
                    # 检查hospital结构
                    if 'hospital' in first_record:
                        hospital = first_record['hospital']
                        
                        print("\n6. hospital字段检查")
                        print("-" * 30)
                        
                        required_hospital_fields = ['code', 'name']
                        for field in required_hospital_fields:
                            if field in hospital:
                                print(f"✅ {field}")
                            else:
                                print(f"❌ 缺少字段: {field}")
                    
                    # 显示实际数据样例
                    print("\n7. 实际数据样例")
                    print("-" * 30)
                    print("部分关键字段的实际值:")
                    print(f"  姓名: {pe_user_info.get('name', 'N/A')}")
                    print(f"  体检号: {pe_user_info.get('peno', 'N/A')}")
                    print(f"  性别: {pe_user_info.get('sex', {}).get('name', 'N/A')}")
                    print(f"  年龄: {pe_user_info.get('age', 'N/A')}")
                    print(f"  currentNodeType: {pe_user_info.get('currentNodeType', 'N/A')}")
                    print(f"  申请项目数量: {len(pe_user_info.get('applyItemList', []))}")
                    
                    if missing_fields:
                        print(f"\n⚠️ 总共缺少 {len(missing_fields)} 个字段:")
                        for field in missing_fields:
                            print(f"   - {field}")
                    else:
                        print(f"\n✅ 所有必需字段都存在")
            
            else:
                print("❌ 没有数据记录可供检查")
                
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 检查过程异常: {e}")

if __name__ == "__main__":
    test_response_format()