#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看中心库连接配置
"""

from config import Config
from database_service import get_database_service
from multi_org_config import get_current_org_config
from optimized_database_service import create_optimized_db_service

def check_database_connections():
    """检查各种数据库连接配置"""
    
    print("=" * 60)
    print("数据库连接配置检查")
    print("=" * 60)
    
    print("\n1. 中心库连接配置 (Config.INTERFACE_DB_CONFIG)")
    config = Config.INTERFACE_DB_CONFIG
    print(f"   服务器: {config['host']}:{config['port']}")
    print(f"   数据库: {config['database']}")
    print(f"   用户名: {config['username']}")
    print(f"   密码: {config['password'][:4]}***")
    print(f"   驱动: {config['driver']}")
    
    center_connection_string = Config.get_interface_db_connection_string()
    print(f"\n   完整连接字符串: {center_connection_string}")
    
    print("\n2. 当前机构配置的数据库连接")
    org_config = get_current_org_config()
    print(f"   机构编码: {org_config.get('org_code')}")
    print(f"   机构名称: {org_config.get('org_name')}")
    
    if hasattr(org_config, 'get_db_connection_string'):
        local_connection_string = org_config.get_db_connection_string()
        print(f"   本部库连接字符串: {local_connection_string}")
    else:
        print("   本部库连接字符串: 未配置")
    
    print("\n3. 测试中心库连接")
    try:
        center_db_service = get_database_service()
        if center_db_service.connect():
            print("   [OK] 中心库连接成功")
            
            # 查询数据库信息
            result = center_db_service.execute_query("SELECT @@SERVERNAME as server_name, DB_NAME() as db_name")
            if result:
                print(f"   服务器名称: {result[0]['server_name']}")
                print(f"   数据库名称: {result[0]['db_name']}")
            
            center_db_service.disconnect()
        else:
            print("   [ERROR] 中心库连接失败")
    except Exception as e:
        print(f"   [ERROR] 中心库连接异常: {e}")
    
    print("\n4. 测试本部库连接")
    try:
        # 使用机构配置创建本部库连接
        if hasattr(org_config, 'get_db_connection_string'):
            local_connection_string = org_config.get_db_connection_string()
            local_db_service = create_optimized_db_service(local_connection_string)
            
            print("   [OK] 本部库连接成功")
            
            # 查询数据库信息
            result = local_db_service.connection_manager.execute_query_with_cache(
                local_connection_string,
                "SELECT @@SERVERNAME as server_name, DB_NAME() as db_name",
                cache_key="db_info_check",
                use_cache=False
            )
            if result:
                print(f"   服务器名称: {result[0]['server_name']}")
                print(f"   数据库名称: {result[0]['db_name']}")
        else:
            print("   [ERROR] 本部库连接未配置")
    except Exception as e:
        print(f"   [ERROR] 本部库连接异常: {e}")
    
    print("\n5. 连接对比")
    try:
        center_conn = Config.get_interface_db_connection_string()
        if hasattr(org_config, 'get_db_connection_string'):
            local_conn = org_config.get_db_connection_string()
            
            if center_conn == local_conn:
                print("   [WARNING] 中心库和本部库使用相同的连接 - 这可能是问题所在！")
            else:
                print("   [OK] 中心库和本部库使用不同的连接")
                
                # 提取关键信息对比
                print("\n   详细对比:")
                print(f"   中心库: {center_conn}")
                print(f"   本部库: {local_conn}")
        else:
            print("   无法对比：本部库连接未配置")
    except Exception as e:
        print(f"   对比失败: {e}")
    
    print("\n" + "=" * 60)

if __name__ == '__main__':
    check_database_connections()