#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查询数据库表结构
"""

from database_service import get_database_service

def check_table_structure():
    """检查Code_Operator_dict表结构"""
    db_service = get_database_service()
    
    if db_service.connect():
        print("数据库连接成功!")
        
        try:
            # 查询Code_Operator_dict表结构
            sql = """
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'Code_Operator_dict'
            ORDER BY ORDINAL_POSITION
            """
            
            result = db_service.execute_query(sql)
            print("\nCode_Operator_dict 表结构:")
            print("-" * 50)
            for row in result:
                print(f"{row['COLUMN_NAME']:20} | {row['DATA_TYPE']:15} | {row['IS_NULLABLE']}")
            
            # 查询Code_Dept_dict表结构
            sql = """
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'Code_Dept_dict'
            ORDER BY ORDINAL_POSITION
            """
            
            result = db_service.execute_query(sql)
            print("\nCode_Dept_dict 表结构:")
            print("-" * 50)
            for row in result:
                print(f"{row['COLUMN_NAME']:20} | {row['DATA_TYPE']:15} | {row['IS_NULLABLE']}")
                
        except Exception as e:
            print(f"查询表结构时出错: {e}")
        finally:
            db_service.disconnect()
    else:
        print("数据库连接失败!")

if __name__ == "__main__":
    check_table_structure()