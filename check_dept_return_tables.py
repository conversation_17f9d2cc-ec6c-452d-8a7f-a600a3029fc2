#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查分科退回表是否存在
"""

from database_service import get_database_service

def check_dept_return_tables():
    """检查分科退回相关表是否存在"""
    print("[INFO] 检查分科退回数据库表...")
    
    db_service = get_database_service()
    
    if not db_service.connect():
        print("[ERROR] 数据库连接失败")
        return False
    
    try:
        # 检查表是否存在
        check_tables_sql = """
        SELECT TABLE_NAME, TABLE_TYPE
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_NAME IN ('T_Check_Result_Return', 'T_Check_Result_Return_File')
        ORDER BY TABLE_NAME
        """
        
        result = db_service.execute_query(check_tables_sql)
        
        if result:
            print("[INFO] 找到以下相关表:")
            for row in result:
                print(f"  - {row['TABLE_NAME']} ({row['TABLE_TYPE']})")
        else:
            print("[WARN] 未找到分科退回相关表")
        
        # 检查用户权限
        check_permissions_sql = """
        SELECT 
            p.permission_name,
            p.state_desc,
            pr.name AS principal_name
        FROM sys.database_permissions p
        LEFT JOIN sys.objects o ON p.major_id = o.object_id
        LEFT JOIN sys.database_principals pr ON p.grantee_principal_id = pr.principal_id
        WHERE o.name = 'T_Check_Result_Return'
        OR p.class = 0  -- Database level permissions
        """
        
        try:
            permissions = db_service.execute_query(check_permissions_sql)
            if permissions:
                print("[INFO] T_Check_Result_Return表相关权限:")
                for perm in permissions:
                    print(f"  - {perm.get('principal_name', 'N/A')}: {perm.get('permission_name', 'N/A')} ({perm.get('state_desc', 'N/A')})")
        except Exception as e:
            print(f"[WARN] 无法查询权限信息: {e}")
        
        # 检查表结构
        check_structure_sql = """
        SELECT 
            COLUMN_NAME,
            DATA_TYPE,
            IS_NULLABLE,
            CHARACTER_MAXIMUM_LENGTH
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'T_Check_Result_Return'
        ORDER BY ORDINAL_POSITION
        """
        
        try:
            columns = db_service.execute_query(check_structure_sql)
            if columns:
                print("[INFO] T_Check_Result_Return表结构:")
                for col in columns:
                    nullable = "NULL" if col['IS_NULLABLE'] == 'YES' else "NOT NULL"
                    max_len = f"({col['CHARACTER_MAXIMUM_LENGTH']})" if col['CHARACTER_MAXIMUM_LENGTH'] else ""
                    print(f"  - {col['COLUMN_NAME']}: {col['DATA_TYPE']}{max_len} {nullable}")
            else:
                print("[WARN] 无法获取表结构信息")
        except Exception as e:
            print(f"[WARN] 无法查询表结构: {e}")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] 检查表失败: {e}")
        return False
        
    finally:
        db_service.disconnect()

if __name__ == "__main__":
    check_dept_return_tables()