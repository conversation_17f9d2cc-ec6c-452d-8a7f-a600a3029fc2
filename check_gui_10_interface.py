#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查GUI程序是否正在运行，并测试10号接口
"""

import requests
import json
from datetime import datetime, timedelta

def check_gui_status():
    """检查GUI程序状态"""
    print("检查GUI程序状态")
    print("=" * 40)
    
    base_url = "http://localhost:5007"
    
    try:
        # 测试健康检查
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print(f"GUI程序状态: 运行中")
            print(f"服务名称: {health_data.get('service')}")
            print(f"服务状态: {health_data.get('status')}")
            
            interfaces = health_data.get('interfaces', {})
            if '10' in interfaces:
                print(f"10号接口: {interfaces['10']}")
                return True
            else:
                print("10号接口未在健康检查中注册")
                return False
        else:
            print(f"GUI程序响应异常: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("GUI程序未运行 - 无法连接到5007端口")
        return False
    except Exception as e:
        print(f"检查GUI状态异常: {e}")
        return False

def test_gui_10_interface():
    """测试GUI中的10号接口"""
    print("\n测试GUI中的10号接口")
    print("=" * 40)
    
    base_url = "http://localhost:5007"
    
    # 构建测试请求
    end_time = datetime.now()
    start_time = end_time - timedelta(days=7)
    
    request_data = {
        "start": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "peNo": "",
        "hospitalCode": ""
    }
    
    print(f"请求参数: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(
            f"{base_url}/dx/inter/batchGetPeInfo",
            json=request_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"返回码: {result.get('code')}")
            print(f"消息: {result.get('msg')}")
            print(f"数据数量: {len(result.get('data', []))}")
            
            if result.get('data'):
                first_record = result['data'][0]
                pe_user_info = first_record.get('peUserInfo', {})
                print(f"示例数据:")
                print(f"  姓名: {pe_user_info.get('name')}")
                print(f"  体检号: {pe_user_info.get('peno')}")
                print(f"  性别: {pe_user_info.get('sex', {}).get('name')}")
                print(f"  年龄: {pe_user_info.get('age')}")
                print(f"  体检状态: {pe_user_info.get('peStates', {}).get('name')}")
                
                return True
            else:
                print("返回数据为空")
                return False
        else:
            print(f"请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"请求异常: {e}")
        return False

if __name__ == "__main__":
    print("GUI程序和10号接口状态检查")
    print("=" * 60)
    
    # 检查GUI状态
    gui_running = check_gui_status()
    
    if gui_running:
        # 测试10号接口
        interface_working = test_gui_10_interface()
        
        if interface_working:
            print("\n结论: GUI程序运行正常，10号接口功能完整")
        else:
            print("\n结论: GUI程序运行正常，但10号接口存在问题")
    else:
        print("\n结论: GUI程序未运行，无法测试10号接口")
        print("请启动GUI程序: python gui_main.py")
        print("然后启动天健云接口服务")