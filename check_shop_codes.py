#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查当前数据库中的shop_code配置
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from center_organization_service import get_center_organization_service


def check_shop_codes():
    """检查当前数据库中的shop_code配置"""
    print("检查数据库中的shop_code配置")
    print("=" * 60)
    
    try:
        # 获取中心机构服务
        org_service = get_center_organization_service()
        
        # 获取所有机构配置
        organizations = org_service.get_all_organizations()
        
        if not organizations:
            print("[WARNING] 数据库中没有找到任何机构配置")
            return
        
        print(f"[INFO] 找到 {len(organizations)} 个机构配置:")
        print()
        
        # 显示机构配置信息
        print(f"{'机构编码':<10} {'机构名称':<20} {'门店编码':<10} {'状态':<6} {'数据库':<15}")
        print("-" * 70)
        
        for org in organizations:
            status_text = "启用" if org.status == "1" else "停用"
            print(f"{org.org_code:<10} {org.org_name:<20} {org.shop_code:<10} {status_text:<6} {org.db_name:<15}")
        
        print()
        
        # 统计shop_code
        shop_codes = [org.shop_code for org in organizations if org.shop_code]
        unique_shop_codes = list(set(shop_codes))
        unique_shop_codes.sort()
        
        print(f"[INFO] 可用的shop_code列表: {unique_shop_codes}")
        
        # 检查请求中的shop_code是否存在
        request_shop_code = "08"  # 请求报文中的cshopcode
        if request_shop_code in unique_shop_codes:
            print(f"[SUCCESS] 请求的shop_code '{request_shop_code}' 在配置中存在")
            
            # 找到对应的机构
            matching_org = next((org for org in organizations if org.shop_code == request_shop_code), None)
            if matching_org:
                print(f"[INFO] 对应机构: {matching_org.org_code} - {matching_org.org_name}")
                print(f"[INFO] 数据库: {matching_org.db_host}:{matching_org.db_port}/{matching_org.db_name}")
        else:
            print(f"[ERROR] 请求的shop_code '{request_shop_code}' 在配置中不存在")
            print(f"[SUGGESTION] 请使用以下可用的shop_code之一: {unique_shop_codes}")
        
    except Exception as e:
        print(f"[ERROR] 检查shop_code配置失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    check_shop_codes()
