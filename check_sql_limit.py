#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证SQL查询是否移除了TOP限制
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_sql_query():
    """检查SQL查询语句"""
    print("[CHECK] 检查18号接口的SQL查询语句")
    print("=" * 50)
    
    try:
        from interface_18_getDoctorInfo import TianjianInterface18
        from config import Config
        
        # 创建接口实例
        api_config = Config.get_tianjian_api_config()
        interface = TianjianInterface18(api_config)
        
        # 检查get_doctor_info_from_db方法的源代码
        import inspect
        source = inspect.getsource(interface.get_doctor_info_from_db)
        
        print("[SOURCE] get_doctor_info_from_db方法源代码片段:")
        lines = source.split('\n')
        for i, line in enumerate(lines):
            if 'TOP' in line.upper() or 'LIMIT' in line.upper() or 'SELECT' in line.upper():
                print(f"  {i+1:2d}: {line}")
        
        # 检查是否还有TOP限制
        if 'SELECT TOP' in source:
            print(f"\n[警告] 发现SQL查询中仍有TOP限制")
            return False
        elif '# if limit:' in source and '# sql = sql.replace' in source:
            print(f"\n[成功] TOP限制已被注释掉")
            return True
        else:
            print(f"\n[信息] 未发现明确的TOP限制代码")
            return True
            
    except Exception as e:
        print(f"[异常] 检查失败: {str(e)}")
        return False

def test_query_logic():
    """测试查询逻辑"""
    print(f"\n{'-' * 30}")
    print("[LOGIC] 测试查询逻辑")
    
    try:
        from interface_18_getDoctorInfo import TianjianInterface18
        from config import Config
        
        api_config = Config.get_tianjian_api_config()
        interface = TianjianInterface18(api_config)
        
        # 模拟调用query_doctor_info方法
        print("[模拟] 调用query_doctor_info方法")
        
        # 检查方法参数
        import inspect
        sig = inspect.signature(interface.query_doctor_info)
        print(f"[参数] query_doctor_info参数: {sig}")
        
        # 检查get_doctor_info_from_db调用
        source = inspect.getsource(interface.query_doctor_info)
        lines = source.split('\n')
        
        for i, line in enumerate(lines):
            if 'get_doctor_info_from_db' in line:
                print(f"[调用] 第{i+1}行: {line.strip()}")
        
        return True
        
    except Exception as e:
        print(f"[异常] 逻辑测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("验证18号接口限制移除情况...")
    
    success_count = 0
    
    # 检查SQL查询
    if check_sql_query():
        success_count += 1
    
    # 测试查询逻辑
    if test_query_logic():
        success_count += 1
    
    print(f"\n" + "=" * 50)
    print(f"[总结] 检查结果: {success_count}/2 项检查通过")
    
    if success_count == 2:
        print("[结论] 代码修改正确，TOP限制已被移除")
        print("[说明] 如果仍返回100条记录，可能是:")
        print("  1. 数据库中确实只有100条医生记录")
        print("  2. GUI服务器需要重启以加载新代码")
        print("  3. 数据库查询本身的性能限制")
    else:
        print("[结论] 可能存在其他限制未被移除")