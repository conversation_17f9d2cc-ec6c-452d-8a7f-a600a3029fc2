#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步日志表检查和初始化脚本
"""

from database_service import get_database_service
from sync_logger import create_sync_logger

def check_sync_log_table():
    """检查同步日志表是否存在"""
    
    print("检查中心库同步日志表...")
    
    # 连接中心库
    db_service = get_database_service()
    if not db_service.connect():
        print("[ERROR] 中心库连接失败")
        return False
    
    try:
        # 检查表是否存在
        check_table_sql = """
        SELECT COUNT(*) as table_exists
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_NAME = 'T_TianJian_Sync_Log'
        """
        result = db_service.execute_query(check_table_sql)
        
        if result and result[0]['table_exists'] > 0:
            print("[SUCCESS] T_TianJian_Sync_Log 表已存在")
            
            # 检查表结构
            structure_sql = """
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'T_TianJian_Sync_Log'
            ORDER BY ORDINAL_POSITION
            """
            columns = db_service.execute_query(structure_sql)
            print(f"[INFO] 表结构包含 {len(columns)} 个字段:")
            for col in columns[:5]:  # 显示前5个字段
                print(f"   - {col['COLUMN_NAME']}: {col['DATA_TYPE']}")
            if len(columns) > 5:
                print(f"   ... 还有 {len(columns)-5} 个字段")
            
            return True
        else:
            print("[ERROR] T_TianJian_Sync_Log 表不存在")
            print("")
            print("[GUIDE] 请执行以下操作创建同步日志表:")
            print("   1. 在数据库管理工具中连接到中心库")
            print("   2. 执行 create_center_sync_log_table.sql 脚本")
            print("   3. 重新运行此检查脚本")
            return False
        
    except Exception as e:
        print(f"[ERROR] 检查表失败: {e}")
        return False
    finally:
        db_service.disconnect()

def test_sync_logger_basic():
    """基础测试同步日志记录器（不实际插入数据）"""
    
    print("\n[TEST] 测试同步日志记录器初始化...")
    
    try:
        # 创建日志记录器
        logger = create_sync_logger("09", "DEFAULT")
        print(f"[SUCCESS] 同步日志记录器创建成功")
        print(f"   - 门店编码: {logger.shop_code}")
        print(f"   - 机构编码: {logger.org_code}")
        print(f"   - 批次ID: {logger.batch_id}")
        print(f"   - 源IP: {logger.source_ip}")
        
        # 测试表存在检查
        table_exists = logger._ensure_table_exists()
        if table_exists:
            print("[SUCCESS] 同步日志表检查通过")
            return True
        else:
            print("[ERROR] 同步日志表检查失败")
            return False
            
    except Exception as e:
        print(f"[ERROR] 测试失败: {e}")
        return False

def show_sql_script():
    """显示建表SQL脚本路径"""
    import os
    
    print("\n[INFO] SQL建表脚本信息:")
    script_path = "create_center_sync_log_table.sql"
    if os.path.exists(script_path):
        print(f"   [SUCCESS] 脚本文件: {os.path.abspath(script_path)}")
        print(f"   [INFO] 文件大小: {os.path.getsize(script_path)} 字节")
        
        # 显示脚本内容概要
        with open(script_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"   [INFO] 脚本包含 {len(lines)} 行")
        print("   [INFO] 主要内容:")
        print("      - CREATE TABLE T_TianJian_Sync_Log")
        print("      - CREATE INDEX (6个索引)")
        print("      - ALTER TABLE (4个约束)")
        print("      - CREATE PROCEDURE SP_CleanTianJianSyncLog")
        print("      - CREATE VIEW V_TianJian_Sync_Summary")
    else:
        print(f"   [ERROR] 脚本文件不存在: {script_path}")

if __name__ == "__main__":
    print("=" * 60)
    print("天健云同步日志表 - 检查和初始化")
    print("=" * 60)
    
    # 1. 检查表是否存在
    table_exists = check_sync_log_table()
    
    # 2. 测试同步日志记录器
    if table_exists:
        test_sync_logger_basic()
    
    # 3. 显示SQL脚本信息
    show_sql_script()
    
    print("\n" + "=" * 60)
    if table_exists:
        print("[SUCCESS] 同步日志功能已就绪!")
        print("AI诊断轮询时将自动记录同步日志到中心库")
    else:
        print("[WARNING] 需要手动创建同步日志表")
        print("请使用SQL Server Management Studio等工具执行建表脚本")
    print("=" * 60)