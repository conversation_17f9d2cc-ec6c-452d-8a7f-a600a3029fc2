#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
"""

import os
from datetime import timedelta

class Config:
    """基础配置类"""
    
    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'
    DEBUG = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    HOST = os.environ.get('FLASK_HOST', '0.0.0.0')
    PORT = int(os.environ.get('FLASK_PORT', 5000))
    
    # 数据库配置
    DB_HOST = os.environ.get('DB_HOST', '************')
    DB_PORT = int(os.environ.get('DB_PORT', 1433))
    DB_NAME = os.environ.get('DB_NAME', 'Examdb')
    DB_USER = os.environ.get('DB_USER', 'znzj')
    DB_PASSWORD = os.environ.get('DB_PASSWORD', '2025znzj/888')
    DB_DRIVER = os.environ.get('DB_DRIVER', 'ODBC Driver 17 for SQL Server')
    
    # 数据库连接字符串
    DATABASE_URL = (
        f"mssql+pyodbc://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
        f"?driver={DB_DRIVER.replace(' ', '+')}&charset=utf8"
    )
    
    # API认证配置
    API_KEY_HEADER = 'Authorization'
    API_KEY_PREFIX = 'Bearer '
    TIMESTAMP_HEADER = 'X-Timestamp'
    SIGNATURE_HEADER = 'X-Signature'
    REQUEST_ID_HEADER = 'X-Request-ID'
    
    # 签名配置
    SIGNATURE_SECRET = os.environ.get('SIGNATURE_SECRET', 'your-signature-secret')
    SIGNATURE_ALGORITHM = 'sha256'
    TIMESTAMP_TOLERANCE = 300  # 5分钟时间差容忍度
    
    # API密钥配置（生产环境应从数据库或配置中心获取）
    VALID_API_KEYS = {
        'dx_platform_key_001': {
            'name': 'DX数字交换平台',
            'permissions': ['read', 'write'],
            'rate_limit': 1000,  # 每小时请求限制
            'active': True
        },
        'test_key_001': {
            'name': '测试密钥',
            'permissions': ['read'],
            'rate_limit': 100,
            'active': True
        }
    }
    
    # 分页配置
    DEFAULT_PAGE_SIZE = 20
    MAX_PAGE_SIZE = 100
    
    # 缓存配置
    CACHE_TYPE = 'simple'
    CACHE_DEFAULT_TIMEOUT = 300  # 5分钟
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = os.environ.get('LOG_FILE', 'dx_api.log')
    LOG_MAX_BYTES = int(os.environ.get('LOG_MAX_BYTES', 10485760))  # 10MB
    LOG_BACKUP_COUNT = int(os.environ.get('LOG_BACKUP_COUNT', 5))
    
    # CORS配置
    CORS_ORIGINS = os.environ.get('CORS_ORIGINS', '*').split(',')
    CORS_METHODS = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
    CORS_HEADERS = ['Content-Type', 'Authorization', 'X-Timestamp', 'X-Signature', 'X-Request-ID']
    
    # 请求限制配置
    RATE_LIMIT_ENABLED = os.environ.get('RATE_LIMIT_ENABLED', 'True').lower() == 'true'
    RATE_LIMIT_DEFAULT = os.environ.get('RATE_LIMIT_DEFAULT', '100/hour')
    
    # 数据验证配置
    MAX_CLIENT_CODE_LENGTH = 20
    MAX_NAME_LENGTH = 50
    MAX_PHONE_LENGTH = 20
    MAX_ADDRESS_LENGTH = 200
    
    # 日期格式配置
    DATE_FORMAT = '%Y-%m-%d'
    DATETIME_FORMAT = '%Y-%m-%dT%H:%M:%SZ'
    
    # 业务配置
    EXAM_STATUS_MAPPING = {
        '0': '已登记',
        '1': '已确认', 
        '2': '已总检',
        '3': '已打印'
    }
    
    GENDER_MAPPING = {
        '1': '男',
        '2': '女'
    }
    
    EXAM_TYPE_MAPPING = {
        'individual': '散客体检',
        'group': '团检体检',
        'insurance': '保险体检'
    }
    
    ABNORMAL_FLAG_MAPPING = {
        '0': '正常',
        '1': '异常',
        '2': '未检'
    }

    # 天健云API配置
    TIANJIAN_API_CONFIG = {
        'base_url': os.environ.get('TIANJIAN_BASE_URL', 'http://**************:9300'),
        'api_key': os.environ.get('TIANJIAN_API_KEY', '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM'),
        'mic_code': os.environ.get('TIANJIAN_MIC_CODE', 'MIC1.001E'),
        'misc_id': os.environ.get('TIANJIAN_MISC_ID', 'MISC1.00001A'),
        'timeout': int(os.environ.get('TIANJIAN_TIMEOUT', 30))
    }

    # 医院配置（默认值，建议使用机构配置服务）
    HOSPITAL_CONFIG = {
        'default_code': os.environ.get('HOSPITAL_CODE', 'DEFAULT'),
        'default_name': os.environ.get('HOSPITAL_NAME', '默认医院'),
        'use_org_config': os.environ.get('USE_ORG_CONFIG', 'true').lower() == 'true'
    }

    # 数据库连接配置（用于接口脚本）
    INTERFACE_DB_CONFIG = {
        'host': os.environ.get('INTERFACE_DB_HOST', '************'),
        'port': int(os.environ.get('INTERFACE_DB_PORT', 1433)),
        'database': os.environ.get('INTERFACE_DB_NAME', 'Examdb'),
        'username': os.environ.get('INTERFACE_DB_USER', 'znzj'),
        'password': os.environ.get('INTERFACE_DB_PASSWORD', '2025znzj/888'),
        'driver': os.environ.get('INTERFACE_DB_DRIVER', 'ODBC Driver 17 for SQL Server')
    }

    @classmethod
    def get_interface_db_connection_string(cls):
        """获取接口数据库连接字符串"""
        config = cls.INTERFACE_DB_CONFIG
        return (
            f"DRIVER={{{config['driver']}}};"
            f"SERVER={config['host']},{config['port']};"
            f"DATABASE={config['database']};"
            f"UID={config['username']};"
            f"PWD={config['password']}"
        )

    @classmethod
    def get_tianjian_api_config(cls):
        """获取天健云API配置"""
        return cls.TIANJIAN_API_CONFIG.copy()

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    LOG_LEVEL = 'INFO'
    
    # 生产环境安全配置
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DEBUG = True
    LOG_LEVEL = 'DEBUG'
    
    # 测试数据库配置
    DB_NAME = 'Examdb_test'

# 配置映射
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config(config_name=None):
    """获取配置对象"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')
    
    return config.get(config_name, config['default'])
