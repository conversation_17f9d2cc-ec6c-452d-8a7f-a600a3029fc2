#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理工具
用于管理和验证系统配置
"""

import os
import sys
from config import Config

def show_current_config():
    """显示当前配置"""
    print("当前系统配置")
    print("=" * 60)
    
    print("\n1. 天健云API配置:")
    api_config = Config.get_tianjian_api_config()
    for key, value in api_config.items():
        if 'key' in key.lower():
            # 隐藏敏感信息
            display_value = value[:8] + "..." + value[-4:] if len(value) > 12 else "***"
        else:
            display_value = value
        print(f"   {key}: {display_value}")
    
    print("\n2. 接口数据库配置:")
    db_config = Config.INTERFACE_DB_CONFIG
    for key, value in db_config.items():
        if key in ['password']:
            # 隐藏密码
            display_value = "***"
        else:
            display_value = value
        print(f"   {key}: {display_value}")
    
    print(f"\n3. 数据库连接字符串:")
    conn_str = Config.get_interface_db_connection_string()
    # 隐藏密码部分
    safe_conn_str = conn_str.replace(db_config['password'], "***")
    print(f"   {safe_conn_str}")

def test_database_connection():
    """测试数据库连接"""
    print("\n测试数据库连接")
    print("=" * 30)
    
    try:
        from optimized_database_service import create_optimized_db_service
        
        connection_string = Config.get_interface_db_connection_string()
        print(f"连接字符串: {connection_string.replace(Config.INTERFACE_DB_CONFIG['password'], '***')}")
        
        db_service = create_optimized_db_service(connection_string)
        
        # 测试查询
        test_query = "SELECT TOP 1 cName FROM T_Register_Main"
        result = db_service.connection_manager.execute_query_with_cache(
            connection_string,
            test_query,
            cache_key="connection_test",
            use_cache=False
        )
        
        if result:
            print("✅ 数据库连接成功")
            print(f"✅ 测试查询成功，获取到 {len(result)} 条记录")
            if result:
                print(f"✅ 示例数据: {result[0].get('cName', '未知')}")
        else:
            print("⚠️  数据库连接成功，但没有数据")
            
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        print("\n可能的解决方案:")
        print("1. 检查数据库服务器是否运行")
        print("2. 检查网络连接")
        print("3. 检查用户名和密码")
        print("4. 检查数据库名称")

def test_api_connection():
    """测试天健云API连接"""
    print("\n测试天健云API连接")
    print("=" * 30)
    
    try:
        import requests
        
        api_config = Config.get_tianjian_api_config()
        test_url = f"{api_config['base_url']}/dx/inter-info/syncDict"
        
        print(f"测试URL: {test_url}")
        
        # 简单的连接测试
        response = requests.get(test_url, timeout=10)
        
        if response.status_code == 200:
            print("✅ 天健云API服务器连接成功")
        elif response.status_code == 404:
            print("✅ 天健云API服务器连接成功（接口路径可能需要验证）")
        else:
            print(f"⚠️  天健云API服务器响应异常: {response.status_code}")
            
    except requests.exceptions.ConnectTimeout:
        print("❌ 连接超时，请检查网络或服务器地址")
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请检查服务器地址和网络")
    except Exception as e:
        print(f"❌ API连接测试失败: {e}")

def create_env_file():
    """创建环境变量文件"""
    print("\n创建环境变量文件")
    print("=" * 30)
    
    if os.path.exists('.env'):
        overwrite = input("⚠️  .env文件已存在，是否覆盖？(y/N): ")
        if overwrite.lower() != 'y':
            print("已取消创建")
            return
    
    if os.path.exists('.env.example'):
        import shutil
        shutil.copy('.env.example', '.env')
        print("✅ 已从 .env.example 创建 .env 文件")
        print("请编辑 .env 文件以配置您的环境变量")
    else:
        print("❌ 未找到 .env.example 文件")

def validate_config():
    """验证配置完整性"""
    print("\n验证配置完整性")
    print("=" * 30)
    
    issues = []
    
    # 检查天健云API配置
    api_config = Config.get_tianjian_api_config()
    required_api_keys = ['base_url', 'api_key', 'mic_code', 'misc_id']
    
    for key in required_api_keys:
        if not api_config.get(key):
            issues.append(f"天健云API配置缺少: {key}")
    
    # 检查数据库配置
    db_config = Config.INTERFACE_DB_CONFIG
    required_db_keys = ['host', 'port', 'database', 'username', 'password']
    
    for key in required_db_keys:
        if not db_config.get(key):
            issues.append(f"数据库配置缺少: {key}")
    
    if issues:
        print("❌ 发现配置问题:")
        for issue in issues:
            print(f"   - {issue}")
    else:
        print("✅ 配置验证通过")

def main():
    """主函数"""
    print("天健云接口配置管理工具")
    print("=" * 60)
    
    while True:
        print("\n请选择操作:")
        print("1. 显示当前配置")
        print("2. 测试数据库连接")
        print("3. 测试天健云API连接")
        print("4. 创建环境变量文件")
        print("5. 验证配置完整性")
        print("6. 退出")
        
        choice = input("\n请输入选项 (1-6): ").strip()
        
        if choice == '1':
            show_current_config()
        elif choice == '2':
            test_database_connection()
        elif choice == '3':
            test_api_connection()
        elif choice == '4':
            create_env_file()
        elif choice == '5':
            validate_config()
        elif choice == '6':
            print("再见！")
            break
        else:
            print("❌ 无效选项，请重新选择")

if __name__ == "__main__":
    main()
