-- ==================================================
-- 天健云同步日志表设计
-- 用于记录所有接口的同步操作日志
-- ==================================================

-- 创建同步日志表
CREATE TABLE T_TianJian_Sync_Log (
    -- 主键自增ID
    ID BIGINT IDENTITY(1,1) PRIMARY KEY,
    
    -- 基本信息
    LogTime DATETIME NOT NULL DEFAULT GETDATE(),           -- 日志时间
    ShopCode NVARCHAR(20) NOT NULL,                       -- 门店编码 (如: 09)
    OrgCode NVARCHAR(20) NOT NULL,                        -- 机构编码 (如: DEFAULT)
    
    -- 接口信息
    InterfaceType NVARCHAR(10) NOT NULL,                  -- 接口类型 (01, 02, 03等)
    InterfaceName NVARCHAR(100) NOT NULL,                 -- 接口名称
    SyncType NVARCHAR(20) NOT NULL,                       -- 同步类型 (AUTO自动, MANUAL手动, TEST测试)
    
    -- 业务数据
    ClientCode NVARCHAR(50),                              -- 客户编码
    ClientName NVARCHAR(100),                             -- 客户姓名
    CardNo NVARCHAR(50),                                  -- 卡号
    DeptStatusType NVARCHAR(20),                          -- 分科状态类型 (completed_dept完成, incomplete_dept未完成)
    
    -- 同步结果
    SyncStatus NVARCHAR(20) NOT NULL,                     -- 同步状态 (SUCCESS成功, FAILED失败, PROCESSING处理中)
    Interface01Status NVARCHAR(20),                       -- 01接口状态 (SUCCESS, FAILED, SKIPPED)
    Interface03Status NVARCHAR(20),                       -- 03接口状态 (SUCCESS, FAILED, SKIPPED)
    StatusUpdated BIT DEFAULT 0,                          -- 是否更新了本地状态
    
    -- 详细信息
    RequestData NTEXT,                                     -- 请求数据 (JSON格式)
    ResponseData NTEXT,                                    -- 响应数据 (JSON格式)
    ErrorMessage NTEXT,                                    -- 错误信息
    
    -- 性能指标
    ProcessingTime INT,                                    -- 处理时间(毫秒)
    RetryCount INT DEFAULT 0,                             -- 重试次数
    
    -- 扩展字段
    BatchId NVARCHAR(50),                                 -- 批次ID (用于批量操作)
    SourceIP NVARCHAR(50),                                -- 源IP地址
    UserAgent NVARCHAR(500),                              -- 用户代理
    Remarks NTEXT                                         -- 备注信息
);

-- 创建索引
CREATE INDEX IX_TianJian_Sync_Log_LogTime ON T_TianJian_Sync_Log(LogTime);
CREATE INDEX IX_TianJian_Sync_Log_ShopCode ON T_TianJian_Sync_Log(ShopCode);
CREATE INDEX IX_TianJian_Sync_Log_InterfaceType ON T_TianJian_Sync_Log(InterfaceType);
CREATE INDEX IX_TianJian_Sync_Log_ClientCode ON T_TianJian_Sync_Log(ClientCode);
CREATE INDEX IX_TianJian_Sync_Log_SyncStatus ON T_TianJian_Sync_Log(SyncStatus);
CREATE INDEX IX_TianJian_Sync_Log_Date_Shop ON T_TianJian_Sync_Log(LogTime, ShopCode);

-- 添加约束
ALTER TABLE T_TianJian_Sync_Log 
ADD CONSTRAINT CK_TianJian_Sync_Log_InterfaceType 
CHECK (InterfaceType IN ('01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21'));

ALTER TABLE T_TianJian_Sync_Log 
ADD CONSTRAINT CK_TianJian_Sync_Log_SyncType 
CHECK (SyncType IN ('AUTO', 'MANUAL', 'TEST'));

ALTER TABLE T_TianJian_Sync_Log 
ADD CONSTRAINT CK_TianJian_Sync_Log_SyncStatus 
CHECK (SyncStatus IN ('SUCCESS', 'FAILED', 'PROCESSING'));

ALTER TABLE T_TianJian_Sync_Log 
ADD CONSTRAINT CK_TianJian_Sync_Log_InterfaceStatus 
CHECK (Interface01Status IN ('SUCCESS', 'FAILED', 'SKIPPED', NULL) 
   AND Interface03Status IN ('SUCCESS', 'FAILED', 'SKIPPED', NULL));

-- 添加表注释
EXEC sp_addextendedproperty 
@name = N'MS_Description', 
@value = N'天健云同步日志表 - 记录所有接口的同步操作日志', 
@level0type = N'SCHEMA', @level0name = N'dbo', 
@level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log';

-- 添加字段注释
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'主键自增ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'ID';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'日志时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'LogTime';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'门店编码', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'ShopCode';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'机构编码', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'OrgCode';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'接口类型(01-21)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'InterfaceType';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'接口名称', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'InterfaceName';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'同步类型(AUTO/MANUAL/TEST)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'SyncType';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'客户编码', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'ClientCode';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'客户姓名', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'ClientName';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'卡号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'CardNo';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'分科状态类型', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'DeptStatusType';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'同步状态(SUCCESS/FAILED/PROCESSING)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'SyncStatus';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'01接口状态', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'Interface01Status';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'03接口状态', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'Interface03Status';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'是否更新了本地状态', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'StatusUpdated';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'请求数据JSON', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'RequestData';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'响应数据JSON', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'ResponseData';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'错误信息', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'ErrorMessage';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'处理时间毫秒', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'ProcessingTime';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'重试次数', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'RetryCount';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'批次ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'BatchId';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'源IP地址', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'SourceIP';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'用户代理', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'UserAgent';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'备注信息', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'T_TianJian_Sync_Log', @level2type = N'COLUMN', @level2name = N'Remarks';

-- 创建数据清理存储过程 (保留最近6个月的数据)
CREATE PROCEDURE SP_CleanTianJianSyncLog
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @CleanupDate DATETIME = DATEADD(MONTH, -6, GETDATE());
    DECLARE @DeletedCount INT;
    
    DELETE FROM T_TianJian_Sync_Log 
    WHERE LogTime < @CleanupDate;
    
    SET @DeletedCount = @@ROWCOUNT;
    
    PRINT '清理完成，删除了 ' + CAST(@DeletedCount AS VARCHAR(10)) + ' 条记录';
    
    -- 重建索引
    ALTER INDEX ALL ON T_TianJian_Sync_Log REBUILD;
END;

-- 创建查询统计视图
CREATE VIEW V_TianJian_Sync_Summary AS
SELECT 
    ShopCode,
    InterfaceType,
    CAST(LogTime AS DATE) AS SyncDate,
    COUNT(*) AS TotalCount,
    SUM(CASE WHEN SyncStatus = 'SUCCESS' THEN 1 ELSE 0 END) AS SuccessCount,
    SUM(CASE WHEN SyncStatus = 'FAILED' THEN 1 ELSE 0 END) AS FailedCount,
    ROUND(AVG(CAST(ProcessingTime AS FLOAT)), 2) AS AvgProcessingTime,
    MAX(LogTime) AS LastSyncTime
FROM T_TianJian_Sync_Log
GROUP BY ShopCode, InterfaceType, CAST(LogTime AS DATE);

-- 添加视图注释
EXEC sp_addextendedproperty 
@name = N'MS_Description', 
@value = N'天健云同步统计视图 - 按门店、接口、日期统计同步情况', 
@level0type = N'SCHEMA', @level0name = N'dbo', 
@level1type = N'VIEW', @level1name = N'V_TianJian_Sync_Summary';