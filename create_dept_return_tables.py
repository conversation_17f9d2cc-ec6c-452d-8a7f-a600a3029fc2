#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建分科退回数据库表
"""

from database_service import get_database_service

def create_dept_return_tables():
    """创建分科退回相关数据库表"""
    
    db_service = get_database_service()
    
    if not db_service.connect():
        print("[ERROR] 数据库连接失败")
        return False
    
    try:
        print("[INFO] 开始创建分科退回数据库表...")
        
        # 创建主表
        create_main_table_sql = """
        CREATE TABLE [dbo].[T_Check_Result_Return](
            [id] [nvarchar](50) NOT NULL,
            [cShopCode] [nvarchar](10) NULL,
            [cClientCode] [nvarchar](10) NULL,
            [cMainCode] [nvarchar](10) NULL,
            [cMainName] [nvarchar](50) NULL,
            [cDoctCode] [nvarchar](10) NULL,
            [cDoctName] [nvarchar](50) NULL,
            [cAuditDoctCode] [nvarchar](10) NULL,
            [cAuditDoctName] [nvarchar](50) NULL,
            [cReturnReason] [nvarchar](300) NULL,
            [cReturnType] [nvarchar](50) NULL,
            [cReturnStatus] [nvarchar](20) NULL,
            [cReturnTimes] [int] NULL,
            [cReturnTime] [datetime] NULL,
            [cReturnDoctCode] [nvarchar](50) NULL,
            [cReturnDoctName] [nvarchar](50) NULL,
            [cModifyRemark] [nvarchar](300) NULL,
            [cModifyDoctCode] [nvarchar](10) NULL,
            [cModifyDoctName] [nvarchar](50) NULL,
            [cModifyTime] [datetime] NULL,
            [fModifyUsedHour] [int] NULL,
            CONSTRAINT [PK_T_Check_Result_Return] PRIMARY KEY CLUSTERED ([id] ASC)
        ) ON [PRIMARY]
        """
        
        result = db_service.execute_update(create_main_table_sql)
        if result is not None:
            print("[OK] 分科退回主表创建成功")
        else:
            print("[ERROR] 分科退回主表创建失败")
            return False
        
        # 创建附件表
        create_file_table_sql = """
        CREATE TABLE [dbo].[T_Check_Result_Return_File](
            [id] [nvarchar](50) NOT NULL,
            [cReturnId] [nvarchar](50) NULL,
            [cFileName] [nvarchar](100) NULL,
            [pFile] [image] NULL,
            CONSTRAINT [PK_T_Check_Result_Return_File] PRIMARY KEY CLUSTERED ([id] ASC)
        ) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
        """
        
        result = db_service.execute_update(create_file_table_sql)
        if result is not None:
            print("[OK] 分科退回附件表创建成功")
        else:
            print("[ERROR] 分科退回附件表创建失败")
            return False
        
        print("[SUCCESS] 分科退回数据库表创建完成")
        return True
        
    except Exception as e:
        print(f"[ERROR] 创建数据库表失败: {e}")
        return False
        
    finally:
        db_service.disconnect()

if __name__ == "__main__":
    create_dept_return_tables()