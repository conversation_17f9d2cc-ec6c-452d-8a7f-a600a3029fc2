#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建中心库同步日志表的脚本
"""

from database_service import get_database_service
import os

def create_sync_log_table():
    """创建同步日志表"""
    
    # 创建表的SQL语句
    create_table_sql = """
    CREATE TABLE T_TianJian_Sync_Log (
        -- 主键自增ID
        ID BIGINT IDENTITY(1,1) PRIMARY KEY,
        
        -- 基本信息
        LogTime DATETIME NOT NULL DEFAULT GETDATE(),           -- 日志时间
        ShopCode NVARCHAR(20) NOT NULL,                       -- 门店编码 (如: 09)
        OrgCode NVARCHAR(20) NOT NULL,                        -- 机构编码 (如: DEFAULT)
        
        -- 接口信息
        InterfaceType NVARCHAR(10) NOT NULL,                  -- 接口类型 (01, 02, 03等)
        InterfaceName NVARCHAR(100) NOT NULL,                 -- 接口名称
        SyncType NVARCHAR(20) NOT NULL,                       -- 同步类型 (AUTO自动, MANUAL手动, TEST测试)
        
        -- 业务数据
        ClientCode NVARCHAR(50),                              -- 客户编码
        ClientName NVARCHAR(100),                             -- 客户姓名
        CardNo NVARCHAR(50),                                  -- 卡号
        DeptStatusType NVARCHAR(20),                          -- 分科状态类型 (completed_dept完成, incomplete_dept未完成)
        
        -- 同步结果
        SyncStatus NVARCHAR(20) NOT NULL,                     -- 同步状态 (SUCCESS成功, FAILED失败, PROCESSING处理中)
        Interface01Status NVARCHAR(20),                       -- 01接口状态 (SUCCESS, FAILED, SKIPPED)
        Interface03Status NVARCHAR(20),                       -- 03接口状态 (SUCCESS, FAILED, SKIPPED)
        StatusUpdated BIT DEFAULT 0,                          -- 是否更新了本地状态
        
        -- 详细信息
        RequestData NTEXT,                                     -- 请求数据 (JSON格式)
        ResponseData NTEXT,                                    -- 响应数据 (JSON格式)
        ErrorMessage NTEXT,                                    -- 错误信息
        
        -- 性能指标
        ProcessingTime INT,                                    -- 处理时间(毫秒)
        RetryCount INT DEFAULT 0,                             -- 重试次数
        
        -- 扩展字段
        BatchId NVARCHAR(50),                                 -- 批次ID (用于批量操作)
        SourceIP NVARCHAR(50),                                -- 源IP地址
        UserAgent NVARCHAR(500),                              -- 用户代理
        Remarks NTEXT                                         -- 备注信息
    )
    """
    
    # 创建索引的SQL语句
    create_indexes_sql = [
        "CREATE INDEX IX_TianJian_Sync_Log_LogTime ON T_TianJian_Sync_Log(LogTime)",
        "CREATE INDEX IX_TianJian_Sync_Log_ShopCode ON T_TianJian_Sync_Log(ShopCode)",
        "CREATE INDEX IX_TianJian_Sync_Log_InterfaceType ON T_TianJian_Sync_Log(InterfaceType)",
        "CREATE INDEX IX_TianJian_Sync_Log_ClientCode ON T_TianJian_Sync_Log(ClientCode)",
        "CREATE INDEX IX_TianJian_Sync_Log_SyncStatus ON T_TianJian_Sync_Log(SyncStatus)",
        "CREATE INDEX IX_TianJian_Sync_Log_Date_Shop ON T_TianJian_Sync_Log(LogTime, ShopCode)"
    ]
    
    # 连接中心库
    db_service = get_database_service()
    if not db_service.connect():
        print("中心库连接失败")
        return False
    
    try:
        # 检查表是否已存在
        check_table_sql = """
        SELECT COUNT(*) as table_exists
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_NAME = 'T_TianJian_Sync_Log'
        """
        result = db_service.execute_query(check_table_sql)
        
        if result and result[0]['table_exists'] > 0:
            print("T_TianJian_Sync_Log 表已存在，跳过创建")
            return True
        
        # 创建主表
        print("正在创建 T_TianJian_Sync_Log 表...")
        db_service.execute_update(create_table_sql)
        print("表创建成功")
        
        # 创建索引
        print("正在创建索引...")
        for i, index_sql in enumerate(create_indexes_sql, 1):
            try:
                db_service.execute_update(index_sql)
                print(f"索引 {i} 创建成功")
            except Exception as e:
                print(f"索引 {i} 创建失败: {e}")
        
        print("同步日志表创建完成!")
        return True
        
    except Exception as e:
        print(f"创建表失败: {e}")
        return False
    finally:
        db_service.disconnect()

def test_sync_logger():
    """测试同步日志记录器"""
    from sync_logger import create_sync_logger
    
    print("\n测试同步日志记录器...")
    
    # 创建日志记录器
    logger = create_sync_logger("09", "DEFAULT")
    
    # 记录测试日志
    log_id = logger.log_sync_start(
        interface_type="01",
        interface_name="体检基本信息传输测试",
        client_code="TEST001",
        client_name="测试用户",
        card_no="TEST_CARD_001",
        dept_status_type="completed_dept",
        sync_type="TEST",
        request_data={"test": "data"}
    )
    
    if log_id:
        print(f"测试日志记录成功，ID: {log_id}")
        
        # 记录成功
        logger.log_sync_success(
            log_id=log_id,
            response_data={"code": 0, "msg": "测试成功"},
            interface_01_status="SUCCESS",
            interface_03_status="SKIPPED",
            status_updated=True,
            processing_time=1500,
            remarks="这是一个测试记录"
        )
        print("测试成功日志记录完成")
        
        # 查询日志
        logs = logger.query_sync_logs(days=1, limit=5)
        print(f"查询到 {len(logs)} 条日志记录")
        
        return True
    else:
        print("测试日志记录失败")
        return False

if __name__ == "__main__":
    print("开始创建中心库同步日志表...")
    
    # 创建表
    if create_sync_log_table():
        # 测试功能
        test_sync_logger()
    else:
        print("表创建失败，跳过测试")