#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试数据并测试AI诊断轮询报文显示
"""

from interface_01_sendPeInfo import TianjianInterface01
from multi_org_config import switch_organization
from database_service import DatabaseService
from optimized_database_service import create_optimized_db_service
from config import Config

def create_test_data_and_test():
    """创建测试数据并测试AI诊断轮询"""
    
    print("=" * 60)
    print("创建测试数据并测试AI诊断轮询报文显示")
    print("=" * 60)
    
    # 切换到09机构
    print("\n1. 切换到09机构")
    success, message = switch_organization('09')
    print(f"   切换结果: {message}")
    
    if success:
        # 获取当前机构配置
        from multi_org_config import get_current_org_config
        org_config = get_current_org_config()
        
        # 创建数据库连接
        if org_config.get('db_host'):
            connection_string = (
                f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                f"SERVER={org_config['db_host']},{org_config.get('db_port', 1433)};"
                f"DATABASE={org_config['db_name']};"
                f"UID={org_config['db_user']};"
                f"PWD={org_config['db_password']};TrustServerCertificate=yes;"
            )
        else:
            connection_string = Config.get_interface_db_connection_string()
        
        print("\n2. 查看现有记录")
        
        # 查看今天是否有记录
        local_db_service = DatabaseService(connection_string)
        if local_db_service.connect():
            try:
                # 查询今天有cCanDiagDate的记录
                check_sql = """
                SELECT TOP 5
                    cClientCode, cName, cCanDiagDate, AIDiagnosisStatus
                FROM T_Register_Main 
                WHERE CONVERT(date, cCanDiagDate) = CONVERT(date, GETDATE())
                ORDER BY cCanDiagDate DESC
                """
                
                print(f"   查询SQL: {check_sql.strip()}")
                existing_records = local_db_service.execute_query(check_sql)
                
                print(f"   找到 {len(existing_records)} 条今天的记录:")
                for record in existing_records:
                    client_code = record.get('cClientCode', '')
                    name = record.get('cName', '')
                    can_diag_date = record.get('cCanDiagDate', '')
                    ai_status = record.get('AIDiagnosisStatus', '')
                    print(f"     {client_code} | {name} | {can_diag_date} | AI状态={ai_status}")
                
                if existing_records:
                    # 更新第一条记录为AI诊断状态=1
                    first_record = existing_records[0]
                    client_code = first_record.get('cClientCode', '')
                    
                    if client_code:
                        print(f"\n3. 更新记录 {client_code} 的AI诊断状态为1")
                        update_sql = """
                        UPDATE T_Register_Main 
                        SET AIDiagnosisStatus = 1
                        WHERE cClientCode = ?
                        """
                        
                        affected_rows = local_db_service.execute_update(update_sql, (client_code,))
                        print(f"   更新了 {affected_rows} 条记录")
                        
                        if affected_rows > 0:
                            print("\n4. 测试AI诊断轮询（测试模式 - 显示模拟报文）")
                            print("=" * 60)
                            
                            # 创建接口实例并测试
                            interface = TianjianInterface01()
                            result = interface.poll_and_send_ai_diagnosis(limit=1, test_mode=True)
                            
                            print("=" * 60)
                            print("\n5. 轮询结果:")
                            print(f"   总记录数: {result.get('total', 0)}")
                            print(f"   发送成功: {result.get('sent', 0)}")
                            print(f"   处理消息: {result.get('message', '无')}")
                        else:
                            print("   更新失败")
                    else:
                        print("   无法获取客户编码")
                else:
                    print("\n   没有找到今天的记录")
                    print("   建议:")
                    print("   1. 检查数据库中是否有今天的体检记录")
                    print("   2. 确认cCanDiagDate字段存在且有数据")
                    print("   3. 确认AIDiagnosisStatus字段存在")
                    
            finally:
                local_db_service.disconnect()
        else:
            print("   数据库连接失败")
    else:
        print("   机构切换失败")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == '__main__':
    create_test_data_and_test()