#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库访问服务层
用于访问examdb数据库，为天健云接口提供数据支持
"""

import pyodbc
import json
from datetime import datetime, date
from decimal import Decimal
from typing import List, Dict, Any, Optional
from config import Config


class DatabaseService:
    """数据库访问服务"""
    
    def __init__(self, connection_string: str):
        """
        初始化数据库连接
        
        Args:
            connection_string: 数据库连接字符串
        """
        self.connection_string = connection_string
        self.connection = None
    
    def connect(self):
        """建立数据库连接"""
        try:
            self.connection = pyodbc.connect(self.connection_string)
            return True
        except Exception as e:
            print(f"数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def execute_query(self, sql: str, params: tuple = None) -> List[Dict[str, Any]]:
        """
        执行查询语句并返回结果
        
        Args:
            sql: SQL查询语句
            params: 查询参数
            
        Returns:
            查询结果列表
        """
        if not self.connection:
            raise Exception("数据库未连接")
        
        cursor = self.connection.cursor()
        try:
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)
            
            # 获取列名
            columns = [column[0] for column in cursor.description]
            
            # 获取所有行数据
            rows = cursor.fetchall()
            
            # 转换为字典列表
            result = []
            for row in rows:
                row_dict = {}
                for i, value in enumerate(row):
                    # 处理特殊数据类型
                    if isinstance(value, datetime):
                        row_dict[columns[i]] = value.strftime('%Y-%m-%d %H:%M:%S')
                    elif isinstance(value, date):
                        row_dict[columns[i]] = value.strftime('%Y-%m-%d')
                    elif isinstance(value, Decimal):
                        row_dict[columns[i]] = float(value)
                    else:
                        row_dict[columns[i]] = value
                result.append(row_dict)
            
            return result
            
        except Exception as e:
            print(f"查询执行失败: {e}")
            raise
        finally:
            cursor.close()

    def execute_update(self, sql: str, params: tuple = None) -> int:
        """
        执行更新语句（INSERT、UPDATE、DELETE）

        Args:
            sql: SQL更新语句
            params: 更新参数

        Returns:
            影响的行数
        """
        if not self.connection:
            raise Exception("数据库未连接")

        cursor = self.connection.cursor()
        try:
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)

            affected_rows = cursor.rowcount
            self.connection.commit()
            return affected_rows

        except Exception as e:
            self.connection.rollback()
            print(f"更新执行失败: {e}")
            raise
        finally:
            cursor.close()

    def execute_update_with_cursor(self, sql: str, params: tuple = None):
        """
        执行更新语句并返回游标（用于获取插入的ID等）

        Args:
            sql: SQL更新语句
            params: 更新参数

        Returns:
            数据库游标对象，需要手动关闭
        """
        if not self.connection:
            raise Exception("数据库未连接")

        cursor = self.connection.cursor()
        try:
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)

            self.connection.commit()
            return cursor

        except Exception as e:
            self.connection.rollback()
            cursor.close()
            print(f"更新执行失败: {e}")
            raise

    def get_exam_basic_info(self, client_code: str = None, days: int = None, limit: int = None) -> List[Dict[str, Any]]:
        """
        获取体检基本信息（01号接口数据）
        
        Args:
            client_code: 特定客户编号
            days: 获取最近N天的数据
            limit: 限制返回条数
            
        Returns:
            体检基本信息列表
        """
        sql = """
        SELECT 
            rm.cClientCode as archiveNo,
            rm.cName as name,
            rm.cIDCard as icCode,
            rm.cSex as sex_code,
            CASE rm.cSex 
                WHEN '1' THEN '男' 
                WHEN '2' THEN '女' 
                ELSE '未知' 
            END as sex_name,
            rm.dBirthday,
            rm.cClientCode as peno,
            rm.dOperdate as peDate,
            rm.cMobile as phone,
            rm.cMarry as ms_code,
            CASE rm.cMarry
                WHEN '1' THEN 'married'
                WHEN '2' THEN 'unmarried'
                WHEN '3' THEN 'divorce'
                WHEN '4' THEN 'widowhood'
                ELSE 'unknown'
            END as ms_key,
            CASE rm.cMarry
                WHEN '1' THEN '已婚'
                WHEN '2' THEN '未婚'
                WHEN '3' THEN '离婚'
                WHEN '4' THEN '丧偶'
                ELSE '未知'
            END as ms_name,
            rm.cVipLevel as vipLevel_code,
            ISNULL(rm.cVipLevel, 'NORMAL') as vipLevel_name,
            rm.cType as medicalType_code,
            ISNULL(rm.cType, 'PERSONAL') as medicalType_name,
            CASE WHEN (rm.cContractCode IS NULL OR rm.cContractCode = '') THEN 0 ELSE 1 END as isGroup,
            tc.cUnitsName as company,
            rm.cWorkDept as workDept,
            tc.cCode as teamNo,
            rm.cProfession as professional,
            rm.cWorkAge as workAge,
            rm.cStatus as peStates_code,
            CASE rm.cStatus
                WHEN '0' THEN '登记完成'
                WHEN '1' THEN '分科未完成'
                WHEN '2' THEN '分科完成'
                WHEN '3' THEN '主检初审中'
                WHEN '4' THEN '主检初审完成'
                WHEN '5' THEN '主检终审中'
                WHEN '6' THEN '主检终审完成'
                WHEN '7' THEN '主检终审完成'
                ELSE '未知状态'
            END as peStates_name,
            rm.dOperdate as deptFinishTime,
            rm.dOperdate as firstCheckFinishTime,
            rm.cOperName as firstCheckFinishDoctor_code,
            rm.cOperName as firstCheckFinishDoctor_name,
            rm.dOperdate as mainCheckFinishTime,
            rm.cOperName as mainCheckFinishDoctor_code,
            rm.cOperName as mainCheckFinishDoctor_name,
            0 as forbidGoCheck,
            0 as reportPrint,
            0 as reportGot,
            0 as replacementInspectionMark,
            DATEDIFF(YEAR, rm.dBirthday, GETDATE()) as age,
            CASE 
                WHEN rm.cStatus >= '2' THEN 4
                WHEN rm.cStatus >= '1' THEN 3
                ELSE 1
            END as currentNodeType,
            rm.cSuitCode as pePackage_code,
            us.cName as pePackage_name,
            rm.cStatus as urgentStatus
        FROM T_Register_Main rm
        LEFT JOIN T_Contract tc ON rm.cContractCode = tc.cCode
        LEFT JOIN T_UnitsSuit_Master us ON rm.cSuitCode = us.cSuitCode
        WHERE 1=1
        """
        
        params = []
        
        # 添加客户编号条件
        if client_code:
            sql += " AND rm.cClientCode = ?"
            params.append(client_code)
        
        # 添加时间范围条件
        if days:
            sql += " AND rm.dOperdate >= DATEADD(day, -?, GETDATE())"
            params.append(days)
        
        # 排序
        sql += " ORDER BY rm.dOperdate DESC"
        
        # 添加限制条数
        if limit:
            # 直接在主查询中使用TOP
            sql = sql.replace("SELECT ", f"SELECT TOP {limit} ")
        
        return self.execute_query(sql, tuple(params) if params else None)
    
    def get_exam_apply_items(self, client_code: str) -> List[str]:
        """
        获取体检申请项目列表
        
        Args:
            client_code: 客户编号
            
        Returns:
            申请项目ID列表
        """
        sql = """
        SELECT DISTINCT ip.cMainCode
        FROM T_Register_Detail rd
        INNER JOIN code_Item_Price ip ON rd.cPriceCode = ip.cCode
        WHERE rd.cClientCode = ?
        ORDER BY ip.cMainCode
        """
        
        result = self.execute_query(sql, (client_code,))
        return [row['cMainCode'] for row in result]
    
    def get_dept_count(self, client_code: str) -> int:
        """
        获取体检科室数量
        
        Args:
            client_code: 客户编号
            
        Returns:
            科室数量
        """
        sql = """
        SELECT COUNT(DISTINCT dm.cDeptCode) as dept_count
        FROM T_Register_Detail rd
        INNER JOIN code_Item_Price ip ON rd.cPriceCode = ip.cCode
        INNER JOIN Code_Item_Main im ON ip.cMainCode = im.cCode
        INNER JOIN Code_Dept_Main dm ON im.cCode = dm.cMainCode
        WHERE rd.cClientCode = ?
        """
        
        result = self.execute_query(sql, (client_code,))
        return result[0]['dept_count'] if result else 0
    
    def get_archive_pe_list(self, client_code: str) -> List[str]:
        """
        获取档案下的所有体检号
        
        Args:
            client_code: 客户编号
            
        Returns:
            体检号列表
        """
        # 根据身份证号查找同一人的所有体检记录
        sql = """
        SELECT DISTINCT cClientCode
        FROM T_Register_Main 
        WHERE cIDCard = (
            SELECT cIDCard FROM T_Register_Main WHERE cClientCode = ?
        )
        ORDER BY cClientCode
        """
        
        result = self.execute_query(sql, (client_code,))
        return [row['cClientCode'] for row in result]
    
    def get_apply_items_dict(self, limit: int = None) -> List[Dict[str, Any]]:
        """
        获取申请项目字典数据（02号接口数据）
        
        Args:
            limit: 限制返回条数
            
        Returns:
            申请项目字典列表
        """
        sql = """
        SELECT 
            im.cCode as applyItemId,
            im.cName as applyItemName,
            CAST(ROW_NUMBER() OVER (ORDER BY im.cCode) AS VARCHAR) as displaySequence,
            ISNULL(dm.cDeptCode, 'UNKNOWN') as deptId
        FROM Code_Item_Main im
        LEFT JOIN Code_Dept_Main dm ON im.cCode = dm.cMainCode
        WHERE im.cStopTag = '0'
        ORDER BY im.cCode
        """
        
        if limit:
            # 直接在主查询中使用TOP
            sql = sql.replace("SELECT ", f"SELECT TOP {limit} ")
        
        return self.execute_query(sql)
    
    def get_check_items_for_apply_item(self, apply_item_id: str) -> List[Dict[str, Any]]:
        """
        获取申请项目下的检查项目列表
        
        Args:
            apply_item_id: 申请项目ID
            
        Returns:
            检查项目列表
        """
        sql = """
        SELECT 
            id.cCode as checkItemId,
            id.cName as checkItemName,
            CAST(ROW_NUMBER() OVER (ORDER BY id.cCode) AS VARCHAR) as displaySequence
        FROM Code_Item_Detail id
        WHERE id.cMainCode = ?
        AND id.cStopTag = '0'
        ORDER BY id.cCode
        """
        
        return self.execute_query(sql, (apply_item_id,))
    
    def get_operators_dict(self) -> List[Dict[str, Any]]:
        """
        获取操作员字典数据（04号接口数据）
        
        Returns:
            操作员字典列表
        """
        sql = """
        SELECT 
            od.cCode as accountId,
            od.cName as name,
            od.cCardNo as icCode,
            od.cMobil as phoneNo,
            '3' as sex_code,
            '未知' as sex_name,
            od.cCode as accountCode
        FROM Code_Operator_dict od
        WHERE od.cStopTag = '0' 
            AND od.cDoctorTag = '1'
        ORDER BY od.cCode
        """
        
        return self.execute_query(sql)
    
    def get_departments_dict(self) -> List[Dict[str, Any]]:
        """
        获取科室字典数据（05号接口数据）
        
        Returns:
            科室字典列表
        """
        sql = """
        SELECT 
            dd.cCode as id,
            dd.cName as name,
            CAST(ROW_NUMBER() OVER (ORDER BY dd.cCode) AS VARCHAR) as displaySequence
        FROM Code_Dept_dict dd
        WHERE dd.cStopTag = '0'
        ORDER BY dd.cCode
        """
        
        return self.execute_query(sql)
    
    def get_dict_info(self) -> List[Dict[str, Any]]:
        """
        获取字典信息数据（06号接口数据）
        
        Returns:
            字典信息列表
        """
        # 获取体检服务类型字典（从Code_Cust_Type表）
        vip_dicts = []
        sql_vip = """
        SELECT
            cCode as id,
            cName as name,
            'OPEVIP' as type
        FROM Code_Cust_Type
        WHERE cStopTag = '0'
        """
        vip_result = self.execute_query(sql_vip)
        vip_dicts.extend(vip_result)
        
        # 获取体检类型字典
        type_dicts = []
        sql_type = """
        SELECT
            cCode as id,
            cName as name,
            'OPBET' as type
        FROM Code_Comm_Dict
        WHERE iNameCode = 39 AND cStopTag = '0'
        """
        type_result = self.execute_query(sql_type)
        type_dicts.extend(type_result)
        
        return vip_dicts + type_dicts


def get_database_service() -> DatabaseService:
    """
    获取数据库服务实例
    使用统一的config.py配置

    Returns:
        DatabaseService实例
    """
    # 使用统一配置管理
    connection_string = Config.get_interface_db_connection_string()
    # 添加TrustServerCertificate参数
    connection_string += ";TrustServerCertificate=yes;"

    return DatabaseService(connection_string)


if __name__ == '__main__':
    # 测试数据库连接
    db_service = get_database_service()
    
    if db_service.connect():
        print("数据库连接成功!")
        
        # 测试查询
        try:
            # 测试获取体检数据
            exam_data = db_service.get_exam_basic_info(limit=5)
            print(f"获取到 {len(exam_data)} 条体检记录")
            
            if exam_data:
                print(f"第一条记录: {exam_data[0]}")
            
            # 测试获取申请项目
            apply_items = db_service.get_apply_items_dict(limit=5)
            print(f"获取到 {len(apply_items)} 个申请项目")
            
            # 测试获取科室信息
            departments = db_service.get_departments_dict()
            print(f"获取到 {len(departments)} 个科室")
            
        except Exception as e:
            print(f"测试查询失败: {e}")
        finally:
            db_service.disconnect()
    else:
        print("数据库连接失败!")