#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试15号接口异常处理
"""

from interface_15_returnDept import TianjianInterface15
from config import Config

def debug_interface_15():
    """调试15号接口"""
    print("[DEBUG] 调试15号接口异常处理")
    print("=" * 50)
    
    # 构建测试数据
    test_data = {
        "peNo": "5000003",
        "markDoctor": "DOC001",
        "errorItem": "ITEM001",
        "returnDept": {
            "code": "DEPT001",
            "name": "内科"
        },
        "receiveDoctor": {
            "code": "DOC002",
            "name": "李医生"
        },
        "remark": "检查结果需要重新确认",
        "currentNodeType": 2
    }
    
    # 创建接口实例
    interface = TianjianInterface15(Config.get_tianjian_api_config())
    
    try:
        print("[INFO] 直接调用return_dept_service方法...")
        result = interface.return_dept_service(test_data)
        print(f"[RESULT] 返回结果: {result}")
        
    except Exception as e:
        print(f"[EXCEPTION] 异常类型: {type(e)}")
        print(f"[EXCEPTION] 异常内容: {str(e)}")
        error_str = str(e)
        print(f"[CHECK] INSERT in error: {'INSERT' in error_str}")
        print(f"[CHECK] 权限 in error: {'权限' in error_str}")
        print(f"[CHECK] 權限 in error: {'權限' in error_str}")
        print(f"[CHECK] permission in error: {'permission' in error_str.lower()}")

if __name__ == "__main__":
    debug_interface_15()