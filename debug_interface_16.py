#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试16号接口的JSON解析问题
"""

import json
import traceback
from flask import Flask, request, jsonify

app = Flask(__name__)

@app.route('/dx/inter/getImages', methods=['POST'])
def get_images():
    """16号接口 - 查询图片接口（调试版本）"""
    print("\n" + "="*60)
    print("[DEBUG] 16号接口收到请求")
    
    try:
        # 1. 打印请求详情
        print(f"[DEBUG] Request Method: {request.method}")
        print(f"[DEBUG] Content-Type: {request.content_type}")
        print(f"[DEBUG] Content-Length: {request.content_length}")
        print(f"[DEBUG] Is JSON: {request.is_json}")
        
        # 2. 获取原始数据
        raw_data = request.get_data()
        print(f"[DEBUG] Raw data length: {len(raw_data)} bytes")
        print(f"[DEBUG] Raw data (first 500 chars): {raw_data[:500]}")
        
        # 3. 尝试解析数据
        data = None
        parse_error = None
        
        if not raw_data:
            parse_error = "请求体为空"
            print(f"[ERROR] {parse_error}")
        else:
            # 尝试JSON解析
            if request.is_json:
                try:
                    data = request.get_json()
                    print(f"[DEBUG] Successfully parsed JSON using get_json()")
                except Exception as e:
                    parse_error = f"get_json()失败: {str(e)}"
                    print(f"[ERROR] {parse_error}")
            
            # 如果失败，尝试手动解析
            if not data and raw_data:
                # 尝试多种编码
                encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']
                for encoding in encodings:
                    try:
                        text_data = raw_data.decode(encoding)
                        print(f"[DEBUG] Decoded with {encoding}, text length: {len(text_data)}")
                        print(f"[DEBUG] Text data (first 500 chars): {text_data[:500]}")
                        
                        # 尝试JSON解析
                        data = json.loads(text_data)
                        print(f"[SUCCESS] Successfully parsed JSON with {encoding} encoding")
                        break
                    except UnicodeDecodeError as e:
                        print(f"[WARN] Failed to decode with {encoding}: {str(e)}")
                        continue
                    except json.JSONDecodeError as e:
                        parse_error = f"JSON解析失败 ({encoding}): {str(e)}"
                        print(f"[ERROR] {parse_error}")
                        # 打印更详细的错误位置
                        if hasattr(e, 'pos'):
                            error_pos = e.pos
                            start = max(0, error_pos - 50)
                            end = min(len(text_data), error_pos + 50)
                            print(f"[ERROR] Error position {error_pos}, context: ...{text_data[start:end]}...")
                        continue
        
        # 4. 如果解析失败，返回详细错误
        if not data:
            error_response = {
                'code': -1,
                'msg': f'请求数据解析失败: {parse_error}',
                'debug': {
                    'content_type': request.content_type,
                    'content_length': request.content_length,
                    'is_json': request.is_json,
                    'raw_data_length': len(raw_data),
                    'raw_data_sample': str(raw_data[:100]) if raw_data else None,
                    'parse_error': parse_error
                }
            }
            print(f"[RESPONSE] Error: {json.dumps(error_response, ensure_ascii=False)}")
            return jsonify(error_response)
        
        # 5. 成功解析，打印数据内容
        print(f"[SUCCESS] Data parsed successfully")
        print(f"[DEBUG] Data type: {type(data)}")
        print(f"[DEBUG] Data keys: {data.keys() if isinstance(data, dict) else 'Not a dict'}")
        print(f"[DEBUG] Data content: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        # 6. 提取关键字段
        pe_no = data.get('peNo', '')
        dept_id = data.get('deptId', '')
        apply_item_ids = data.get('applyItemId', [])
        shop_code = data.get('hospitalCode') or data.get('cshopcode') or data.get('shopcode', '08')
        
        print(f"[INFO] Request parameters:")
        print(f"  - peNo: {pe_no}")
        print(f"  - deptId: {dept_id}")
        print(f"  - applyItemId: {apply_item_ids}")
        print(f"  - shop_code: {shop_code}")
        
        # 7. 验证必要字段
        if not pe_no:
            response = {'code': -1, 'msg': 'peNo不能为空', 'data': []}
            print(f"[RESPONSE] Validation error: {json.dumps(response, ensure_ascii=False)}")
            return jsonify(response)
        
        # 8. 模拟成功响应
        response = {
            'code': 0,
            'msg': '查询成功（调试模式）',
            'data': [
                {
                    'fileName': 'test_image.jpg',
                    'fileUri': '/pacs/images/test/test_image.jpg',
                    'applyItemId': 'TEST001',
                    'deptId': 'XRAY',
                    'base64Url': ''
                }
            ],
            'debug': {
                'received_pe_no': pe_no,
                'received_shop_code': shop_code,
                'received_dept_id': dept_id,
                'received_items': apply_item_ids
            }
        }
        
        print(f"[RESPONSE] Success: {json.dumps(response, ensure_ascii=False)}")
        return jsonify(response)
        
    except Exception as e:
        # 9. 捕获所有未处理的异常
        error_trace = traceback.format_exc()
        print(f"[FATAL ERROR] Unhandled exception: {str(e)}")
        print(f"[TRACEBACK]\n{error_trace}")
        
        error_response = {
            'code': -1,
            'msg': f'服务器内部错误: {str(e)}',
            'debug': {
                'error': str(e),
                'traceback': error_trace.split('\n')
            }
        }
        return jsonify(error_response)


def test_client():
    """测试客户端，模拟发送请求"""
    import requests
    
    print("\n[TEST CLIENT] 开始测试16号接口")
    
    # 测试数据
    test_data = {
        'peNo': '5000003',
        'deptId': '',
        'applyItemId': [],
        'hospitalCode': '09'
    }
    
    # 测试不同的Content-Type
    test_cases = [
        ('application/json', json.dumps(test_data)),
        ('application/json;charset=utf-8', json.dumps(test_data)),
        ('text/plain', json.dumps(test_data)),
        ('', json.dumps(test_data)),  # 无Content-Type
    ]
    
    for content_type, data in test_cases:
        print(f"\n[TEST] Testing with Content-Type: '{content_type}'")
        headers = {}
        if content_type:
            headers['Content-Type'] = content_type
        
        try:
            response = requests.post(
                'http://localhost:5001/dx/inter/getImages',
                data=data,
                headers=headers,
                timeout=5
            )
            print(f"[RESULT] Status: {response.status_code}")
            print(f"[RESULT] Response: {response.text[:500]}")
        except Exception as e:
            print(f"[ERROR] Request failed: {str(e)}")


if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        # 运行测试客户端
        test_client()
    else:
        # 启动调试服务器
        print("[START] 启动16号接口调试服务器...")
        print("[INFO] 监听端口: 5001")
        print("[INFO] 测试命令: python debug_interface_16.py test")
        app.run(host='0.0.0.0', port=5001, debug=True)
