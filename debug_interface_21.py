#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试21号接口数据库连接问题
"""

import sys
import traceback
from interface_21_getAbnormalNotice import TianjianInterface21
from config import Config

def debug_interface_21():
    """调试21号接口"""
    print("开始调试21号接口")
    print("=" * 50)
    
    try:
        # 1. 测试API配置
        api_config = Config.get_tianjian_api_config()
        print(f"[OK] API配置获取成功: {api_config.get('base_url', 'N/A')}")
        
        # 2. 创建接口实例
        interface = TianjianInterface21(api_config)
        print("[OK] 接口实例创建成功")
        
        # 3. 测试数据库服务获取
        print("\n测试数据库服务获取:")
        
        # 测试获取09门店的数据库服务
        hospital_code = "09"
        print(f"请求hospitalCode: {hospital_code}")
        
        db_service = interface._get_database_service_by_hospital_code(hospital_code)
        print(f"数据库服务对象: {db_service}")
        print(f"数据库服务类型: {type(db_service)}")
        
        if db_service is None:
            print("[FAIL] 数据库服务为None!")
            return
        
        # 4. 测试数据库连接
        print(f"\n测试数据库连接:")
        if hasattr(db_service, 'connect'):
            connect_result = db_service.connect()
            print(f"连接结果: {connect_result}")
            
            if connect_result:
                print("[OK] 数据库连接成功")
                
                # 5. 测试简单查询
                try:
                    test_sql = "SELECT TOP 1 cClientCode FROM T_Register_Main"
                    result = db_service.execute_query(test_sql)
                    print(f"[OK] 测试查询成功，结果数量: {len(result) if result else 0}")
                    
                    db_service.disconnect()
                    print("[OK] 数据库断开连接成功")
                except Exception as e:
                    print(f"[FAIL] 测试查询失败: {str(e)}")
                    traceback.print_exc()
            else:
                print("[FAIL] 数据库连接失败")
        else:
            print("[FAIL] 数据库服务没有connect方法")
        
        # 6. 测试完整的接口调用
        print(f"\n测试完整接口调用:")
        test_data = {
            'peNo': '0825751692',
            'hospital': {
                'code': '09',
                'name': 'wu'
            }
        }
        
        print(f"测试数据: {test_data}")
        result = interface.get_abnormal_notice(test_data)
        print(f"接口调用结果: {result}")
        
    except Exception as e:
        print(f"[FAIL] 调试过程中发生异常: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    debug_interface_21()