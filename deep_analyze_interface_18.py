#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深入分析18号接口信息（使用项目内临时文件）
"""

import shutil
import os
import re

def deep_analyze_interface_18():
    try:
        # 源文件路径
        src_path = r"D:\python\福能AI对接\接口文档.html"
        # 目标路径（项目内临时文件）
        dst_path = r"D:\python\福能AI对接\temp_api_doc.html"
        
        # 复制文件
        shutil.copy2(src_path, dst_path)
        print(f"文件已复制到: {dst_path}")
        
        # 读取整个文件内容
        with open(dst_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 查找18号接口的相关信息
        # 首先找到接口名称的位置
        interface_name_pattern = r'"name"\s*:\s*"18\.查询医生信息接口"'
        name_match = re.search(interface_name_pattern, content)
        
        if name_match:
            print("找到18号接口信息:")
            start_pos = name_match.start()
            
            # 向前查找包含此接口的完整对象
            obj_start = content.rfind('{', 0, start_pos)
            if obj_start != -1:
                # 向后查找对象结束
                brace_count = 1
                pos = obj_start + 1
                while pos < len(content) and brace_count > 0:
                    if content[pos] == '{':
                        brace_count += 1
                    elif content[pos] == '}':
                        brace_count -= 1
                    pos += 1
                
                if brace_count == 0:
                    # 提取接口对象
                    interface_obj_str = content[obj_start:pos]
                    print("接口对象内容预览:")
                    print(interface_obj_str[:500] + "..." if len(interface_obj_str) > 500 else interface_obj_str)
                    
                    # 尝试提取关键信息
                    # URL
                    url_match = re.search(r'"url"\s*:\s*"([^"]*)"', interface_obj_str)
                    if url_match:
                        print(f"\n接口URL: {url_match.group(1)}")
                    
                    # Method
                    method_match = re.search(r'"method"\s*:\s*"([^"]*)"', interface_obj_str)
                    if method_match:
                        print(f"请求方法: {method_match.group(1)}")
                    
                    # 参数
                    params_match = re.search(r'"params"\s*:\s*$(.*?)$', interface_obj_str, re.DOTALL)
                    if params_match:
                        params_str = params_match.group(1)
                        param_names = re.findall(r'"name"\s*:\s*"([^"]*)"', params_str)
                        if param_names:
                            print("请求参数:")
                            for param in param_names:
                                print(f"  - {param}")
        else:
            print("未找到18号接口的完整信息")
            
    except Exception as e:
        print(f"处理文件时出错: {e}")
    finally:
        # 清理临时文件
        if os.path.exists(dst_path):
            try:
                os.remove(dst_path)
            except:
                pass

if __name__ == "__main__":
    deep_analyze_interface_18()