#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字典数据同步服务
用于将体检系统的字典数据（项目、科室、操作员等）同步到天健云平台
"""

import json
import logging
from datetime import datetime
from typing import List, Dict, Optional
from sqlalchemy import create_engine, text
from test_config import TestConfig, get_connection_string
from test_api_connection import APITester

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DictSyncService:
    """字典数据同步服务类"""
    
    def __init__(self):
        """初始化字典同步服务"""
        self.db_config = TestConfig.MAIN_DB_CONFIG
        self.api_tester = APITester()
        self.engine = create_engine(get_connection_string(self.db_config))
    
    def get_exam_items(self) -> List[Dict]:
        """获取体检项目字典"""
        logger.info("获取体检项目字典...")
        
        try:
            with self.engine.connect() as conn:
                query = text("""
                    SELECT 
                        cCode,              -- 项目代码
                        cName,              -- 项目名称
                        cSearchIndex,       -- 检索码
                        nIndex,             -- 排序号
                        cSetType,           -- 项目类型
                        cMeaning,           -- 意义
                        cStopTag,           -- 停用标志
                        cBravoTag,          -- 异常标志
                        cIllCureTag,        -- 疾病标志
                        cPacsReportTag,     -- PACS报告标志
                        cTypeName           -- 类型名称
                    FROM Code_Item_Main
                    WHERE cStopTag != '1' OR cStopTag IS NULL  -- 排除停用项目
                    ORDER BY nIndex, cCode
                """)
                
                result = conn.execute(query)
                items = []
                
                for row in result:
                    item = {
                        'code': row[0] or '',
                        'name': row[1] or '',
                        'search_index': row[2] or '',
                        'sort_index': int(row[3]) if row[3] else 0,
                        'set_type': row[4] or '',
                        'meaning': row[5] or '',
                        'stop_flag': row[6] or '0',
                        'abnormal_flag': row[7] or '0',
                        'illness_flag': row[8] or '0',
                        'pacs_report_flag': row[9] or '0',
                        'type_name': row[10] or ''
                    }
                    
                    # 只添加有效的项目（有代码和名称）
                    if item['code'] and item['name']:
                        items.append(item)
                
                logger.info(f"成功获取 {len(items)} 个体检项目")
                return items
                
        except Exception as e:
            logger.error(f"获取体检项目失败: {e}")
            return []
    
    def get_departments(self) -> List[Dict]:
        """获取科室字典"""
        logger.info("获取科室字典...")
        
        try:
            with self.engine.connect() as conn:
                query = text("""
                    SELECT 
                        cCode,              -- 科室代码
                        cName,              -- 科室名称
                        cSearchIndex,       -- 检索码
                        cShotName,          -- 简称
                        nCheckTag,          -- 检查标志
                        nCheckIndex,        -- 检查序号
                        cPrintMemo,         -- 打印备注
                        cTel,               -- 电话
                        cEMAIL,             -- 邮箱
                        cMemo,              -- 备注
                        cStopTag,           -- 停用标志
                        cupcode             -- 上级代码
                    FROM Code_Dept_dict
                    WHERE cStopTag != '1' OR cStopTag IS NULL  -- 排除停用科室
                    ORDER BY nCheckIndex, cCode
                """)
                
                result = conn.execute(query)
                departments = []
                
                for row in result:
                    dept = {
                        'code': row[0] or '',
                        'name': row[1] or '',
                        'search_index': row[2] or '',
                        'short_name': row[3] or '',
                        'check_flag': row[4] or '0',
                        'check_index': int(row[5]) if row[5] else 0,
                        'print_memo': row[6] or '',
                        'tel': row[7] or '',
                        'email': row[8] or '',
                        'memo': row[9] or '',
                        'stop_flag': row[10] or '0',
                        'parent_code': row[11] or ''
                    }
                    
                    # 只添加有效的科室（有代码和名称）
                    if dept['code'] and dept['name']:
                        departments.append(dept)
                
                logger.info(f"成功获取 {len(departments)} 个科室")
                return departments
                
        except Exception as e:
            logger.error(f"获取科室字典失败: {e}")
            return []
    
    def get_operators(self) -> List[Dict]:
        """获取操作员字典"""
        logger.info("获取操作员字典...")
        
        try:
            with self.engine.connect() as conn:
                query = text("""
                    SELECT 
                        cCode,              -- 操作员代码
                        cName,              -- 操作员姓名
                        cSearchIndex,       -- 检索码
                        cPassword,          -- 密码（加密）
                        cDoctorTag,         -- 医生标志
                        cStopTag,           -- 停用标志
                        cDepartmentCode,    -- 科室代码
                        cOfficeTel,         -- 办公电话
                        cMemo               -- 备注
                    FROM Code_Operator_dict
                    WHERE cStopTag != '1' OR cStopTag IS NULL  -- 排除停用操作员
                    ORDER BY cCode
                """)
                
                result = conn.execute(query)
                operators = []
                
                for row in result:
                    operator = {
                        'code': row[0] or '',
                        'name': row[1] or '',
                        'search_index': row[2] or '',
                        'is_doctor': row[4] == '1',
                        'stop_flag': row[5] or '0',
                        'dept_code': row[6] or '',
                        'tel': row[7] or '',
                        'memo': row[8] or ''
                        # 注意：不传输密码信息
                    }
                    
                    # 只添加有效的操作员（有代码和名称）
                    if operator['code'] and operator['name']:
                        operators.append(operator)
                
                logger.info(f"成功获取 {len(operators)} 个操作员")
                return operators
                
        except Exception as e:
            logger.error(f"获取操作员字典失败: {e}")
            return []
    
    def format_items_for_tianjian_api(self, items: List[Dict]) -> Dict:
        """将体检项目格式化为天健云API格式"""
        api_data = {
            "mic_code": TestConfig.API_AUTH_CONFIG.get('mic_code', 'MIC1.001E'),
            "misc_id": TestConfig.API_AUTH_CONFIG.get('misc_id', 'MISC1.00001A'),
            "dict_type": "exam_items",
            "dict_name": "体检项目字典",
            "data": items,
            "sync_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "total_count": len(items)
        }
        return api_data
    
    def format_departments_for_tianjian_api(self, departments: List[Dict]) -> Dict:
        """将科室信息格式化为天健云API格式"""
        api_data = {
            "mic_code": TestConfig.API_AUTH_CONFIG.get('mic_code', 'MIC1.001E'),
            "misc_id": TestConfig.API_AUTH_CONFIG.get('misc_id', 'MISC1.00001A'),
            "dict_type": "departments",
            "dict_name": "科室字典",
            "data": departments,
            "sync_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "total_count": len(departments)
        }
        return api_data
    
    def format_operators_for_tianjian_api(self, operators: List[Dict]) -> Dict:
        """将操作员信息格式化为天健云API格式"""
        api_data = {
            "mic_code": TestConfig.API_AUTH_CONFIG.get('mic_code', 'MIC1.001E'),
            "misc_id": TestConfig.API_AUTH_CONFIG.get('misc_id', 'MISC1.00001A'),
            "dict_type": "operators",
            "dict_name": "操作员字典",
            "data": operators,
            "sync_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "total_count": len(operators)
        }
        return api_data
    
    def sync_dict_to_tianjian(self, dict_type: str, data: List[Dict]) -> Dict:
        """同步字典数据到天健云"""
        logger.info(f"开始同步{dict_type}到天健云...")
        
        try:
            # 为天健云API准备数据 - 按照接口文档格式
            api_data = []
            
            if dict_type == "exam_items":
                # 格式化为申请项目字典格式 (syncApplyItem接口)
                for item in data:
                    api_item = {
                        "applyItemId": item['code'],
                        "applyItemName": item['name'],
                        "displaySequence": str(item.get('sort_index', 0)),
                        "deptId": "000323",  # 默认科室 - 可从数据库获取
                        "checkItemList": [
                            {
                                "checkItemId": item['code'],
                                "checkItemName": item['name'],
                                "displaySequence": str(item.get('sort_index', 0))
                            }
                        ]
                    }
                    api_data.append(api_item)
                    
                # 调用真实的天健云API - 申请项目字典接口
                response = self.api_tester.test_sync_apply_item_api(api_data)
                
            elif dict_type == "departments":
                # 格式化为科室信息格式 (syncDept接口)
                for dept in data:
                    api_dept = {
                        "name": dept['name'],
                        "id": dept['code'],
                        "displaySequence": str(dept.get('check_index', 0))
                    }
                    api_data.append(api_dept)
                    
                # 调用真实的天健云API - 科室信息接口
                response = self.api_tester.test_sync_dept_api(api_data)
                
            elif dict_type == "operators":
                # 格式化为医生信息格式 (syncUser接口)
                for op in data:
                    api_user = {
                        "name": op['name'],
                        "icCode": "",  # 身份证号暂无
                        "phoneNo": op.get('tel', ''),
                        "sex": {
                            "code": "3",  # 默认未知
                            "name": "未知"
                        },
                        "accountCode": op['code'],
                        "accountId": op['code']
                    }
                    api_data.append(api_user)
                    
                # 调用真实的天健云API - 医生信息接口
                response = self.api_tester.test_sync_user_api(api_data)
                
            else:
                raise ValueError(f"不支持的字典类型: {dict_type}")
            
            # 检查API响应
            if response and response.get('success'):
                logger.info(f"真实同步{dict_type}成功，共{len(data)}条记录")
                success_count = len(data)
                error_count = 0
                errors = []
            else:
                error_msg = response.get('error', '未知错误') if response else 'API调用失败'
                logger.error(f"同步{dict_type}失败: {error_msg}")
                success_count = 0
                error_count = len(data)
                errors = [error_msg]
            
            result = {
                'dict_type': dict_type,
                'total': len(data),
                'success': success_count,
                'error': error_count,
                'errors': errors,
                'sync_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            return result
            
        except Exception as e:
            logger.error(f"同步{dict_type}失败: {e}")
            return {
                'dict_type': dict_type,
                'total': len(data),
                'success': 0,
                'error': len(data),
                'errors': [str(e)],
                'sync_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
    
    def sync_all_dicts(self) -> Dict:
        """同步所有字典数据"""
        logger.info("开始同步所有字典数据...")
        
        results = {
            'sync_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'results': []
        }
        
        # 同步体检项目
        items = self.get_exam_items()
        if items:
            result = self.sync_dict_to_tianjian("exam_items", items)
            results['results'].append(result)
        
        # 同步科室
        departments = self.get_departments()
        if departments:
            result = self.sync_dict_to_tianjian("departments", departments)
            results['results'].append(result)
        
        # 同步操作员
        operators = self.get_operators()
        if operators:
            result = self.sync_dict_to_tianjian("operators", operators)
            results['results'].append(result)
        
        # 统计总结果
        total_success = sum(r['success'] for r in results['results'])
        total_error = sum(r['error'] for r in results['results'])
        
        results['summary'] = {
            'total_dicts': len(results['results']),
            'total_records': total_success + total_error,
            'total_success': total_success,
            'total_error': total_error
        }
        
        logger.info(f"字典同步完成 - 成功: {total_success}, 失败: {total_error}")
        return results

def main():
    """主函数 - 演示字典同步服务的使用"""
    print("="*60)
    print("字典数据同步服务测试")
    print("="*60)
    
    # 创建字典同步服务
    dict_service = DictSyncService()
    
    # 测试获取体检项目
    print("\n1. 获取体检项目字典:")
    items = dict_service.get_exam_items()
    if items:
        print(f"找到 {len(items)} 个体检项目:")
        for i, item in enumerate(items[:5], 1):
            print(f"  {i}. {item['code']} - {item['name']} - {item['type_name']}")
        if len(items) > 5:
            print(f"  ... 还有 {len(items) - 5} 个项目")
    
    # 测试获取科室
    print("\n2. 获取科室字典:")
    departments = dict_service.get_departments()
    if departments:
        print(f"找到 {len(departments)} 个科室:")
        for i, dept in enumerate(departments[:5], 1):
            print(f"  {i}. {dept['code']} - {dept['name']} - {dept['short_name']}")
        if len(departments) > 5:
            print(f"  ... 还有 {len(departments) - 5} 个科室")
    
    # 测试获取操作员
    print("\n3. 获取操作员字典:")
    operators = dict_service.get_operators()
    if operators:
        print(f"找到 {len(operators)} 个操作员:")
        for i, op in enumerate(operators[:5], 1):
            doctor_flag = "医生" if op['is_doctor'] else "操作员"
            print(f"  {i}. {op['code']} - {op['name']} - {doctor_flag}")
        if len(operators) > 5:
            print(f"  ... 还有 {len(operators) - 5} 个操作员")
    
    # 测试数据格式化
    if items:
        print("\n4. 测试体检项目API格式化:")
        api_data = dict_service.format_items_for_tianjian_api(items[:3])
        print(f"  字典类型: {api_data['dict_type']}")
        print(f"  字典名称: {api_data['dict_name']}")
        print(f"  数据条数: {api_data['total_count']}")
        print(f"  机构代码: {api_data['org_code']}")
    
    # 测试同步所有字典
    print("\n5. 测试同步所有字典:")
    sync_result = dict_service.sync_all_dicts()
    print(f"  同步的字典类型数: {sync_result['summary']['total_dicts']}")
    print(f"  总记录数: {sync_result['summary']['total_records']}")
    print(f"  成功: {sync_result['summary']['total_success']}")
    print(f"  失败: {sync_result['summary']['total_error']}")
    
    for result in sync_result['results']:
        print(f"    {result['dict_type']}: {result['success']}/{result['total']}")
    
    print("\n" + "="*60)
    print("字典同步测试完成")

if __name__ == '__main__':
    main() 