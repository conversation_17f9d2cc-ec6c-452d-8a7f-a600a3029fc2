api:
  base_url: http://**************:9300
  key: 3CNVizIjUq87IrczWqQB8SxjvPmVMTKM
  mic_code: MIC1.001E
  misc_id: MISC1.00001A
  retry_delay: 5
  retry_times: 3
  timeout: 30
database:
  main:
    charset: utf8
    database: ${INTERFACE_DB_NAME:-Examdb}
    driver: ${INTERFACE_DB_DRIVER:-ODBC Driver 17 for SQL Server}
    password: ${INTERFACE_DB_PASSWORD:-2025znzj/888}
    port: ${INTERFACE_DB_PORT:-1433}
    server: ${INTERFACE_DB_HOST:-************}
    username: ${INTERFACE_DB_USER:-znzj}
  pacs:
    charset: utf8
    database: ${INTERFACE_DB_NAME:-Examdb}
    driver: ${INTERFACE_DB_DRIVER:-ODBC Driver 17 for SQL Server}
    password: ${INTERFACE_DB_PASSWORD:-2025znzj/888}
    port: ${INTERFACE_DB_PORT:-1433}
    server: ${INTERFACE_DB_HOST:-************}
    username: ${INTERFACE_DB_USER:-znzj}
filters:
  exclude_pe_states: []
  max_batch_size: 1000
  min_sync_date: '2024-01-01'
gui:
  refresh_interval: 5
  theme: light
  window_size:
  - 1200
  - 800
  window_title: 体检系统 ↔ 天健云数据同步工具
hospital:
  code: ''
  is_multi_site: false
  name: ''
logging:
  backup_count: 5
  console_output: true
  file_path: logs/health_sync.log
  level: INFO
  max_file_size: 10MB
monitoring:
  email_alerts: false
  email_recipients: []
  error_threshold: 10
sync:
  batch_size: 100
  incremental_window: 30
  schedules:
    apply_item_sync: 0 2 * * *
    dict_sync: 0 2 * * 0
    incremental_sync: '*/5 * * * *'
    retry_failed: '*/10 * * * *'
