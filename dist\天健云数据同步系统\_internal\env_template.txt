# =========================================================================
# 健康同步系统 - 环境变量配置模板
# =========================================================================
# 使用说明：
# 1. 将此文件内容复制到 .env 文件中
# 2. 根据实际情况填写下面的配置参数
# 3. 不要将包含真实密码的 .env 文件提交到版本控制系统
# =========================================================================

# =========================================================================
# 主数据库配置（体检系统数据库）
# =========================================================================
# 数据库服务器地址
MAIN_DB_HOST=localhost
# 数据库端口（SQL Server默认1433）
MAIN_DB_PORT=1433
# 数据库名称
MAIN_DB_NAME=examdb_center
# 数据库用户名
MAIN_DB_USER=sa
# 数据库密码（必须填写）
MAIN_DB_PASSWORD=your_main_database_password
# ODBC驱动名称
MAIN_DB_DRIVER=ODBC Driver 17 for SQL Server

# =========================================================================
# PACS数据库配置（影像系统数据库）
# =========================================================================
# PACS数据库服务器地址（通常与主数据库相同）
PACS_DB_HOST=localhost
# PACS数据库端口
PACS_DB_PORT=1433
# PACS数据库名称
PACS_DB_NAME=ExamDB_Pacs
# PACS数据库用户名
PACS_DB_USER=sa
# PACS数据库密码（必须填写）
PACS_DB_PASSWORD=your_pacs_database_password
# PACS ODBC驱动名称
PACS_DB_DRIVER=ODBC Driver 17 for SQL Server

# =========================================================================
# 天健云API配置
# =========================================================================
# API基础地址（测试环境）
API_BASE_URL=http://**************:9300
# API请求超时时间（秒）
API_TIMEOUT=30
# API重试次数
API_RETRY_TIMES=3
# API重试延迟（秒）
API_RETRY_DELAY=1

# =========================================================================
# 天健云认证配置（从天健云平台获取）
# =========================================================================
# 机构代码（由天健云分配）
API_MIC_CODE=MIC1.0001
# 系统ID（由天健云分配）
API_MISC_ID=MISC1.000001
# API密钥（由天健云分配，必须填写）
API_KEY=your_api_key_from_tianjian_cloud

# =========================================================================
# 系统配置（可选）
# =========================================================================
# 日志级别（DEBUG/INFO/WARNING/ERROR）
LOG_LEVEL=INFO
# 同步批次大小
SYNC_BATCH_SIZE=100
# 同步间隔（秒）
SYNC_INTERVAL=300
# 是否启用自动同步（true/false）
ENABLE_AUTO_SYNC=true

# =========================================================================
# 配置说明
# =========================================================================
# 
# 主数据库配置说明：
# - MAIN_DB_HOST: 体检系统数据库服务器IP地址或域名
# - MAIN_DB_NAME: 通常为 examdb_center 或类似名称
# - MAIN_DB_PASSWORD: 必须填写正确的数据库密码
#
# PACS数据库配置说明：
# - 如果没有独立的PACS系统，可以与主数据库配置相同
# - PACS_DB_NAME: 通常为 ExamDB_Pacs 或类似名称
#
# 天健云API配置说明：
# - API_MIC_CODE: 机构代码，由天健云分配
# - API_MISC_ID: 系统标识，由天健云分配
# - API_KEY: API密钥，由天健云分配，用于生成MD5签名
#
# 安全提醒：
# - 请妥善保管数据库密码和API密钥
# - 不要将包含真实密码的配置文件分享给他人
# - 定期更换密码以确保系统安全
#
# 获取帮助：
# - 数据库配置问题：联系系统管理员
# - API配置问题：联系天健云技术支持
# - 其他问题：查看项目文档或联系开发团队
# ========================================================================= 