# 天健云接口配置 - 默认值
api:
  # 接口基础配置
  base_url: "http://**************:9300"
  key: ""  # 需要用户填写分配的key
  mic_code: ""  # 需要用户填写平台分配的mic-code
  misc_id: ""   # 需要用户填写平台分配的misc-id
  timeout: 30   # 请求超时时间（秒）
  retry_times: 3  # 失败重试次数
  retry_delay: 5  # 重试间隔（秒）

# 数据库配置 - 使用环境变量，与根目录config.py保持一致
database:
  # 主数据库 (Examdb)
  main:
    server: "${INTERFACE_DB_HOST:-************}"
    database: "${INTERFACE_DB_NAME:-Examdb}"
    username: "${INTERFACE_DB_USER:-znzj}"
    password: "${INTERFACE_DB_PASSWORD:-2025znzj/888}"
    port: 1433
    driver: "${INTERFACE_DB_DRIVER:-ODBC Driver 17 for SQL Server}"
    charset: "utf8"

  # PACS数据库 (Examdb)
  pacs:
    server: "${INTERFACE_DB_HOST:-************}"
    database: "${INTERFACE_DB_NAME:-Examdb}"
    username: "${INTERFACE_DB_USER:-znzj}"
    password: "${INTERFACE_DB_PASSWORD:-2025znzj/888}"
    port: 1433
    driver: "${INTERFACE_DB_DRIVER:-ODBC Driver 17 for SQL Server}"
    charset: "utf8"

# 同步任务配置
sync:
  # 批量处理大小
  batch_size: 100
  
  # 定时任务 (cron表达式)
  schedules:
    # 申请项目字典 - 每日02:00全量
    apply_item_sync: "0 2 * * *"
    # 字典信息 - 每周日02:00
    dict_sync: "0 2 * * 0"
    # 失败重传 - 每10分钟
    retry_failed: "*/10 * * * *"
    # 增量同步 - 每5分钟
    incremental_sync: "*/5 * * * *"
  
  # 增量同步时间窗口（分钟）
  incremental_window: 30

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file_path: "logs/health_sync.log"
  max_file_size: "10MB"
  backup_count: 5
  console_output: true

# 医院配置（多院区支持）
hospital:
  code: ""
  name: ""
  is_multi_site: false  # 是否多院区模式

# GUI配置
gui:
  window_title: "体检系统 ↔ 天健云数据同步工具"
  window_size: [1200, 800]
  theme: "light"  # light, dark
  refresh_interval: 5  # 界面刷新间隔（秒）

# 监控与报警
monitoring:
  # 失败阈值报警
  error_threshold: 10  # 连续失败次数
  email_alerts: false
  email_recipients: []

# 数据过滤配置
filters:
  # 排除的体检状态
  exclude_pe_states: []
  # 最小同步日期
  min_sync_date: "2024-01-01"
  # 最大批次处理数量
  max_batch_size: 1000 