// Licensed to the Apache Software Foundation (ASF) under one
// or more contributor license agreements.  See the NOTICE file
// distributed with this work for additional information
// regarding copyright ownership.  The ASF licenses this file
// to you under the Apache License, Version 2.0 (the
// "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.

// This file was generated by script which is modified from its original version in
// GitHub. Original source:
// https://github.com/lemire/FrameOfReference/blob/146948b6058a976bc7767262ad3a2ce201486b93/scripts/turbopacking64.py
// The original copyright notice follows.

// This code is released under the
// Apache License Version 2.0 http://www.apache.org/licenses/.
// (c) <PERSON> 2013

#pragma once

#include "arrow/util/bit_util.h"
#include "arrow/util/ubsan.h"

namespace arrow {
namespace internal {

inline const uint8_t* unpack0_64(const uint8_t* in, uint64_t* out) {
  for (int k = 0; k < 32; k += 1) {
    out[k] = 0;
  }
  return in;
}

inline const uint8_t* unpack1_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 1ULL;
  uint64_t w0 = util::SafeLoadAs<uint32_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 1) & mask;
  out[2] = (w0 >> 2) & mask;
  out[3] = (w0 >> 3) & mask;
  out[4] = (w0 >> 4) & mask;
  out[5] = (w0 >> 5) & mask;
  out[6] = (w0 >> 6) & mask;
  out[7] = (w0 >> 7) & mask;
  out[8] = (w0 >> 8) & mask;
  out[9] = (w0 >> 9) & mask;
  out[10] = (w0 >> 10) & mask;
  out[11] = (w0 >> 11) & mask;
  out[12] = (w0 >> 12) & mask;
  out[13] = (w0 >> 13) & mask;
  out[14] = (w0 >> 14) & mask;
  out[15] = (w0 >> 15) & mask;
  out[16] = (w0 >> 16) & mask;
  out[17] = (w0 >> 17) & mask;
  out[18] = (w0 >> 18) & mask;
  out[19] = (w0 >> 19) & mask;
  out[20] = (w0 >> 20) & mask;
  out[21] = (w0 >> 21) & mask;
  out[22] = (w0 >> 22) & mask;
  out[23] = (w0 >> 23) & mask;
  out[24] = (w0 >> 24) & mask;
  out[25] = (w0 >> 25) & mask;
  out[26] = (w0 >> 26) & mask;
  out[27] = (w0 >> 27) & mask;
  out[28] = (w0 >> 28) & mask;
  out[29] = (w0 >> 29) & mask;
  out[30] = (w0 >> 30) & mask;
  out[31] = (w0 >> 31) & mask;

  return in;
}

inline const uint8_t* unpack2_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 3ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 2) & mask;
  out[2] = (w0 >> 4) & mask;
  out[3] = (w0 >> 6) & mask;
  out[4] = (w0 >> 8) & mask;
  out[5] = (w0 >> 10) & mask;
  out[6] = (w0 >> 12) & mask;
  out[7] = (w0 >> 14) & mask;
  out[8] = (w0 >> 16) & mask;
  out[9] = (w0 >> 18) & mask;
  out[10] = (w0 >> 20) & mask;
  out[11] = (w0 >> 22) & mask;
  out[12] = (w0 >> 24) & mask;
  out[13] = (w0 >> 26) & mask;
  out[14] = (w0 >> 28) & mask;
  out[15] = (w0 >> 30) & mask;
  out[16] = (w0 >> 32) & mask;
  out[17] = (w0 >> 34) & mask;
  out[18] = (w0 >> 36) & mask;
  out[19] = (w0 >> 38) & mask;
  out[20] = (w0 >> 40) & mask;
  out[21] = (w0 >> 42) & mask;
  out[22] = (w0 >> 44) & mask;
  out[23] = (w0 >> 46) & mask;
  out[24] = (w0 >> 48) & mask;
  out[25] = (w0 >> 50) & mask;
  out[26] = (w0 >> 52) & mask;
  out[27] = (w0 >> 54) & mask;
  out[28] = (w0 >> 56) & mask;
  out[29] = (w0 >> 58) & mask;
  out[30] = (w0 >> 60) & mask;
  out[31] = w0 >> 62;

  return in;
}

inline const uint8_t* unpack3_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 7ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint32_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 3) & mask;
  out[2] = (w0 >> 6) & mask;
  out[3] = (w0 >> 9) & mask;
  out[4] = (w0 >> 12) & mask;
  out[5] = (w0 >> 15) & mask;
  out[6] = (w0 >> 18) & mask;
  out[7] = (w0 >> 21) & mask;
  out[8] = (w0 >> 24) & mask;
  out[9] = (w0 >> 27) & mask;
  out[10] = (w0 >> 30) & mask;
  out[11] = (w0 >> 33) & mask;
  out[12] = (w0 >> 36) & mask;
  out[13] = (w0 >> 39) & mask;
  out[14] = (w0 >> 42) & mask;
  out[15] = (w0 >> 45) & mask;
  out[16] = (w0 >> 48) & mask;
  out[17] = (w0 >> 51) & mask;
  out[18] = (w0 >> 54) & mask;
  out[19] = (w0 >> 57) & mask;
  out[20] = (w0 >> 60) & mask;
  out[21] = ((w0 >> 63) | (w1 << 1)) & mask;
  out[22] = (w1 >> 2) & mask;
  out[23] = (w1 >> 5) & mask;
  out[24] = (w1 >> 8) & mask;
  out[25] = (w1 >> 11) & mask;
  out[26] = (w1 >> 14) & mask;
  out[27] = (w1 >> 17) & mask;
  out[28] = (w1 >> 20) & mask;
  out[29] = (w1 >> 23) & mask;
  out[30] = (w1 >> 26) & mask;
  out[31] = (w1 >> 29) & mask;

  return in;
}

inline const uint8_t* unpack4_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 15ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 4) & mask;
  out[2] = (w0 >> 8) & mask;
  out[3] = (w0 >> 12) & mask;
  out[4] = (w0 >> 16) & mask;
  out[5] = (w0 >> 20) & mask;
  out[6] = (w0 >> 24) & mask;
  out[7] = (w0 >> 28) & mask;
  out[8] = (w0 >> 32) & mask;
  out[9] = (w0 >> 36) & mask;
  out[10] = (w0 >> 40) & mask;
  out[11] = (w0 >> 44) & mask;
  out[12] = (w0 >> 48) & mask;
  out[13] = (w0 >> 52) & mask;
  out[14] = (w0 >> 56) & mask;
  out[15] = w0 >> 60;
  out[16] = (w1)&mask;
  out[17] = (w1 >> 4) & mask;
  out[18] = (w1 >> 8) & mask;
  out[19] = (w1 >> 12) & mask;
  out[20] = (w1 >> 16) & mask;
  out[21] = (w1 >> 20) & mask;
  out[22] = (w1 >> 24) & mask;
  out[23] = (w1 >> 28) & mask;
  out[24] = (w1 >> 32) & mask;
  out[25] = (w1 >> 36) & mask;
  out[26] = (w1 >> 40) & mask;
  out[27] = (w1 >> 44) & mask;
  out[28] = (w1 >> 48) & mask;
  out[29] = (w1 >> 52) & mask;
  out[30] = (w1 >> 56) & mask;
  out[31] = w1 >> 60;

  return in;
}

inline const uint8_t* unpack5_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 31ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint32_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 5) & mask;
  out[2] = (w0 >> 10) & mask;
  out[3] = (w0 >> 15) & mask;
  out[4] = (w0 >> 20) & mask;
  out[5] = (w0 >> 25) & mask;
  out[6] = (w0 >> 30) & mask;
  out[7] = (w0 >> 35) & mask;
  out[8] = (w0 >> 40) & mask;
  out[9] = (w0 >> 45) & mask;
  out[10] = (w0 >> 50) & mask;
  out[11] = (w0 >> 55) & mask;
  out[12] = ((w0 >> 60) | (w1 << 4)) & mask;
  out[13] = (w1 >> 1) & mask;
  out[14] = (w1 >> 6) & mask;
  out[15] = (w1 >> 11) & mask;
  out[16] = (w1 >> 16) & mask;
  out[17] = (w1 >> 21) & mask;
  out[18] = (w1 >> 26) & mask;
  out[19] = (w1 >> 31) & mask;
  out[20] = (w1 >> 36) & mask;
  out[21] = (w1 >> 41) & mask;
  out[22] = (w1 >> 46) & mask;
  out[23] = (w1 >> 51) & mask;
  out[24] = (w1 >> 56) & mask;
  out[25] = ((w1 >> 61) | (w2 << 3)) & mask;
  out[26] = (w2 >> 2) & mask;
  out[27] = (w2 >> 7) & mask;
  out[28] = (w2 >> 12) & mask;
  out[29] = (w2 >> 17) & mask;
  out[30] = (w2 >> 22) & mask;
  out[31] = (w2 >> 27) & mask;

  return in;
}

inline const uint8_t* unpack6_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 63ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 6) & mask;
  out[2] = (w0 >> 12) & mask;
  out[3] = (w0 >> 18) & mask;
  out[4] = (w0 >> 24) & mask;
  out[5] = (w0 >> 30) & mask;
  out[6] = (w0 >> 36) & mask;
  out[7] = (w0 >> 42) & mask;
  out[8] = (w0 >> 48) & mask;
  out[9] = (w0 >> 54) & mask;
  out[10] = ((w0 >> 60) | (w1 << 4)) & mask;
  out[11] = (w1 >> 2) & mask;
  out[12] = (w1 >> 8) & mask;
  out[13] = (w1 >> 14) & mask;
  out[14] = (w1 >> 20) & mask;
  out[15] = (w1 >> 26) & mask;
  out[16] = (w1 >> 32) & mask;
  out[17] = (w1 >> 38) & mask;
  out[18] = (w1 >> 44) & mask;
  out[19] = (w1 >> 50) & mask;
  out[20] = (w1 >> 56) & mask;
  out[21] = ((w1 >> 62) | (w2 << 2)) & mask;
  out[22] = (w2 >> 4) & mask;
  out[23] = (w2 >> 10) & mask;
  out[24] = (w2 >> 16) & mask;
  out[25] = (w2 >> 22) & mask;
  out[26] = (w2 >> 28) & mask;
  out[27] = (w2 >> 34) & mask;
  out[28] = (w2 >> 40) & mask;
  out[29] = (w2 >> 46) & mask;
  out[30] = (w2 >> 52) & mask;
  out[31] = w2 >> 58;

  return in;
}

inline const uint8_t* unpack7_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 127ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint32_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 7) & mask;
  out[2] = (w0 >> 14) & mask;
  out[3] = (w0 >> 21) & mask;
  out[4] = (w0 >> 28) & mask;
  out[5] = (w0 >> 35) & mask;
  out[6] = (w0 >> 42) & mask;
  out[7] = (w0 >> 49) & mask;
  out[8] = (w0 >> 56) & mask;
  out[9] = ((w0 >> 63) | (w1 << 1)) & mask;
  out[10] = (w1 >> 6) & mask;
  out[11] = (w1 >> 13) & mask;
  out[12] = (w1 >> 20) & mask;
  out[13] = (w1 >> 27) & mask;
  out[14] = (w1 >> 34) & mask;
  out[15] = (w1 >> 41) & mask;
  out[16] = (w1 >> 48) & mask;
  out[17] = (w1 >> 55) & mask;
  out[18] = ((w1 >> 62) | (w2 << 2)) & mask;
  out[19] = (w2 >> 5) & mask;
  out[20] = (w2 >> 12) & mask;
  out[21] = (w2 >> 19) & mask;
  out[22] = (w2 >> 26) & mask;
  out[23] = (w2 >> 33) & mask;
  out[24] = (w2 >> 40) & mask;
  out[25] = (w2 >> 47) & mask;
  out[26] = (w2 >> 54) & mask;
  out[27] = ((w2 >> 61) | (w3 << 3)) & mask;
  out[28] = (w3 >> 4) & mask;
  out[29] = (w3 >> 11) & mask;
  out[30] = (w3 >> 18) & mask;
  out[31] = (w3 >> 25) & mask;

  return in;
}

inline const uint8_t* unpack8_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 255ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 8) & mask;
  out[2] = (w0 >> 16) & mask;
  out[3] = (w0 >> 24) & mask;
  out[4] = (w0 >> 32) & mask;
  out[5] = (w0 >> 40) & mask;
  out[6] = (w0 >> 48) & mask;
  out[7] = w0 >> 56;
  out[8] = (w1)&mask;
  out[9] = (w1 >> 8) & mask;
  out[10] = (w1 >> 16) & mask;
  out[11] = (w1 >> 24) & mask;
  out[12] = (w1 >> 32) & mask;
  out[13] = (w1 >> 40) & mask;
  out[14] = (w1 >> 48) & mask;
  out[15] = w1 >> 56;
  out[16] = (w2)&mask;
  out[17] = (w2 >> 8) & mask;
  out[18] = (w2 >> 16) & mask;
  out[19] = (w2 >> 24) & mask;
  out[20] = (w2 >> 32) & mask;
  out[21] = (w2 >> 40) & mask;
  out[22] = (w2 >> 48) & mask;
  out[23] = w2 >> 56;
  out[24] = (w3)&mask;
  out[25] = (w3 >> 8) & mask;
  out[26] = (w3 >> 16) & mask;
  out[27] = (w3 >> 24) & mask;
  out[28] = (w3 >> 32) & mask;
  out[29] = (w3 >> 40) & mask;
  out[30] = (w3 >> 48) & mask;
  out[31] = w3 >> 56;

  return in;
}

inline const uint8_t* unpack9_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 511ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint32_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 9) & mask;
  out[2] = (w0 >> 18) & mask;
  out[3] = (w0 >> 27) & mask;
  out[4] = (w0 >> 36) & mask;
  out[5] = (w0 >> 45) & mask;
  out[6] = (w0 >> 54) & mask;
  out[7] = ((w0 >> 63) | (w1 << 1)) & mask;
  out[8] = (w1 >> 8) & mask;
  out[9] = (w1 >> 17) & mask;
  out[10] = (w1 >> 26) & mask;
  out[11] = (w1 >> 35) & mask;
  out[12] = (w1 >> 44) & mask;
  out[13] = (w1 >> 53) & mask;
  out[14] = ((w1 >> 62) | (w2 << 2)) & mask;
  out[15] = (w2 >> 7) & mask;
  out[16] = (w2 >> 16) & mask;
  out[17] = (w2 >> 25) & mask;
  out[18] = (w2 >> 34) & mask;
  out[19] = (w2 >> 43) & mask;
  out[20] = (w2 >> 52) & mask;
  out[21] = ((w2 >> 61) | (w3 << 3)) & mask;
  out[22] = (w3 >> 6) & mask;
  out[23] = (w3 >> 15) & mask;
  out[24] = (w3 >> 24) & mask;
  out[25] = (w3 >> 33) & mask;
  out[26] = (w3 >> 42) & mask;
  out[27] = (w3 >> 51) & mask;
  out[28] = ((w3 >> 60) | (w4 << 4)) & mask;
  out[29] = (w4 >> 5) & mask;
  out[30] = (w4 >> 14) & mask;
  out[31] = (w4 >> 23) & mask;

  return in;
}

inline const uint8_t* unpack10_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 1023ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 10) & mask;
  out[2] = (w0 >> 20) & mask;
  out[3] = (w0 >> 30) & mask;
  out[4] = (w0 >> 40) & mask;
  out[5] = (w0 >> 50) & mask;
  out[6] = ((w0 >> 60) | (w1 << 4)) & mask;
  out[7] = (w1 >> 6) & mask;
  out[8] = (w1 >> 16) & mask;
  out[9] = (w1 >> 26) & mask;
  out[10] = (w1 >> 36) & mask;
  out[11] = (w1 >> 46) & mask;
  out[12] = ((w1 >> 56) | (w2 << 8)) & mask;
  out[13] = (w2 >> 2) & mask;
  out[14] = (w2 >> 12) & mask;
  out[15] = (w2 >> 22) & mask;
  out[16] = (w2 >> 32) & mask;
  out[17] = (w2 >> 42) & mask;
  out[18] = (w2 >> 52) & mask;
  out[19] = ((w2 >> 62) | (w3 << 2)) & mask;
  out[20] = (w3 >> 8) & mask;
  out[21] = (w3 >> 18) & mask;
  out[22] = (w3 >> 28) & mask;
  out[23] = (w3 >> 38) & mask;
  out[24] = (w3 >> 48) & mask;
  out[25] = ((w3 >> 58) | (w4 << 6)) & mask;
  out[26] = (w4 >> 4) & mask;
  out[27] = (w4 >> 14) & mask;
  out[28] = (w4 >> 24) & mask;
  out[29] = (w4 >> 34) & mask;
  out[30] = (w4 >> 44) & mask;
  out[31] = w4 >> 54;

  return in;
}

inline const uint8_t* unpack11_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 2047ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint32_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 11) & mask;
  out[2] = (w0 >> 22) & mask;
  out[3] = (w0 >> 33) & mask;
  out[4] = (w0 >> 44) & mask;
  out[5] = ((w0 >> 55) | (w1 << 9)) & mask;
  out[6] = (w1 >> 2) & mask;
  out[7] = (w1 >> 13) & mask;
  out[8] = (w1 >> 24) & mask;
  out[9] = (w1 >> 35) & mask;
  out[10] = (w1 >> 46) & mask;
  out[11] = ((w1 >> 57) | (w2 << 7)) & mask;
  out[12] = (w2 >> 4) & mask;
  out[13] = (w2 >> 15) & mask;
  out[14] = (w2 >> 26) & mask;
  out[15] = (w2 >> 37) & mask;
  out[16] = (w2 >> 48) & mask;
  out[17] = ((w2 >> 59) | (w3 << 5)) & mask;
  out[18] = (w3 >> 6) & mask;
  out[19] = (w3 >> 17) & mask;
  out[20] = (w3 >> 28) & mask;
  out[21] = (w3 >> 39) & mask;
  out[22] = (w3 >> 50) & mask;
  out[23] = ((w3 >> 61) | (w4 << 3)) & mask;
  out[24] = (w4 >> 8) & mask;
  out[25] = (w4 >> 19) & mask;
  out[26] = (w4 >> 30) & mask;
  out[27] = (w4 >> 41) & mask;
  out[28] = (w4 >> 52) & mask;
  out[29] = ((w4 >> 63) | (w5 << 1)) & mask;
  out[30] = (w5 >> 10) & mask;
  out[31] = (w5 >> 21) & mask;

  return in;
}

inline const uint8_t* unpack12_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 4095ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 12) & mask;
  out[2] = (w0 >> 24) & mask;
  out[3] = (w0 >> 36) & mask;
  out[4] = (w0 >> 48) & mask;
  out[5] = ((w0 >> 60) | (w1 << 4)) & mask;
  out[6] = (w1 >> 8) & mask;
  out[7] = (w1 >> 20) & mask;
  out[8] = (w1 >> 32) & mask;
  out[9] = (w1 >> 44) & mask;
  out[10] = ((w1 >> 56) | (w2 << 8)) & mask;
  out[11] = (w2 >> 4) & mask;
  out[12] = (w2 >> 16) & mask;
  out[13] = (w2 >> 28) & mask;
  out[14] = (w2 >> 40) & mask;
  out[15] = w2 >> 52;
  out[16] = (w3)&mask;
  out[17] = (w3 >> 12) & mask;
  out[18] = (w3 >> 24) & mask;
  out[19] = (w3 >> 36) & mask;
  out[20] = (w3 >> 48) & mask;
  out[21] = ((w3 >> 60) | (w4 << 4)) & mask;
  out[22] = (w4 >> 8) & mask;
  out[23] = (w4 >> 20) & mask;
  out[24] = (w4 >> 32) & mask;
  out[25] = (w4 >> 44) & mask;
  out[26] = ((w4 >> 56) | (w5 << 8)) & mask;
  out[27] = (w5 >> 4) & mask;
  out[28] = (w5 >> 16) & mask;
  out[29] = (w5 >> 28) & mask;
  out[30] = (w5 >> 40) & mask;
  out[31] = w5 >> 52;

  return in;
}

inline const uint8_t* unpack13_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 8191ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint32_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 13) & mask;
  out[2] = (w0 >> 26) & mask;
  out[3] = (w0 >> 39) & mask;
  out[4] = ((w0 >> 52) | (w1 << 12)) & mask;
  out[5] = (w1 >> 1) & mask;
  out[6] = (w1 >> 14) & mask;
  out[7] = (w1 >> 27) & mask;
  out[8] = (w1 >> 40) & mask;
  out[9] = ((w1 >> 53) | (w2 << 11)) & mask;
  out[10] = (w2 >> 2) & mask;
  out[11] = (w2 >> 15) & mask;
  out[12] = (w2 >> 28) & mask;
  out[13] = (w2 >> 41) & mask;
  out[14] = ((w2 >> 54) | (w3 << 10)) & mask;
  out[15] = (w3 >> 3) & mask;
  out[16] = (w3 >> 16) & mask;
  out[17] = (w3 >> 29) & mask;
  out[18] = (w3 >> 42) & mask;
  out[19] = ((w3 >> 55) | (w4 << 9)) & mask;
  out[20] = (w4 >> 4) & mask;
  out[21] = (w4 >> 17) & mask;
  out[22] = (w4 >> 30) & mask;
  out[23] = (w4 >> 43) & mask;
  out[24] = ((w4 >> 56) | (w5 << 8)) & mask;
  out[25] = (w5 >> 5) & mask;
  out[26] = (w5 >> 18) & mask;
  out[27] = (w5 >> 31) & mask;
  out[28] = (w5 >> 44) & mask;
  out[29] = ((w5 >> 57) | (w6 << 7)) & mask;
  out[30] = (w6 >> 6) & mask;
  out[31] = (w6 >> 19) & mask;

  return in;
}

inline const uint8_t* unpack14_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 16383ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 14) & mask;
  out[2] = (w0 >> 28) & mask;
  out[3] = (w0 >> 42) & mask;
  out[4] = ((w0 >> 56) | (w1 << 8)) & mask;
  out[5] = (w1 >> 6) & mask;
  out[6] = (w1 >> 20) & mask;
  out[7] = (w1 >> 34) & mask;
  out[8] = (w1 >> 48) & mask;
  out[9] = ((w1 >> 62) | (w2 << 2)) & mask;
  out[10] = (w2 >> 12) & mask;
  out[11] = (w2 >> 26) & mask;
  out[12] = (w2 >> 40) & mask;
  out[13] = ((w2 >> 54) | (w3 << 10)) & mask;
  out[14] = (w3 >> 4) & mask;
  out[15] = (w3 >> 18) & mask;
  out[16] = (w3 >> 32) & mask;
  out[17] = (w3 >> 46) & mask;
  out[18] = ((w3 >> 60) | (w4 << 4)) & mask;
  out[19] = (w4 >> 10) & mask;
  out[20] = (w4 >> 24) & mask;
  out[21] = (w4 >> 38) & mask;
  out[22] = ((w4 >> 52) | (w5 << 12)) & mask;
  out[23] = (w5 >> 2) & mask;
  out[24] = (w5 >> 16) & mask;
  out[25] = (w5 >> 30) & mask;
  out[26] = (w5 >> 44) & mask;
  out[27] = ((w5 >> 58) | (w6 << 6)) & mask;
  out[28] = (w6 >> 8) & mask;
  out[29] = (w6 >> 22) & mask;
  out[30] = (w6 >> 36) & mask;
  out[31] = w6 >> 50;

  return in;
}

inline const uint8_t* unpack15_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 32767ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint32_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 15) & mask;
  out[2] = (w0 >> 30) & mask;
  out[3] = (w0 >> 45) & mask;
  out[4] = ((w0 >> 60) | (w1 << 4)) & mask;
  out[5] = (w1 >> 11) & mask;
  out[6] = (w1 >> 26) & mask;
  out[7] = (w1 >> 41) & mask;
  out[8] = ((w1 >> 56) | (w2 << 8)) & mask;
  out[9] = (w2 >> 7) & mask;
  out[10] = (w2 >> 22) & mask;
  out[11] = (w2 >> 37) & mask;
  out[12] = ((w2 >> 52) | (w3 << 12)) & mask;
  out[13] = (w3 >> 3) & mask;
  out[14] = (w3 >> 18) & mask;
  out[15] = (w3 >> 33) & mask;
  out[16] = (w3 >> 48) & mask;
  out[17] = ((w3 >> 63) | (w4 << 1)) & mask;
  out[18] = (w4 >> 14) & mask;
  out[19] = (w4 >> 29) & mask;
  out[20] = (w4 >> 44) & mask;
  out[21] = ((w4 >> 59) | (w5 << 5)) & mask;
  out[22] = (w5 >> 10) & mask;
  out[23] = (w5 >> 25) & mask;
  out[24] = (w5 >> 40) & mask;
  out[25] = ((w5 >> 55) | (w6 << 9)) & mask;
  out[26] = (w6 >> 6) & mask;
  out[27] = (w6 >> 21) & mask;
  out[28] = (w6 >> 36) & mask;
  out[29] = ((w6 >> 51) | (w7 << 13)) & mask;
  out[30] = (w7 >> 2) & mask;
  out[31] = (w7 >> 17) & mask;

  return in;
}

inline const uint8_t* unpack16_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 65535ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 16) & mask;
  out[2] = (w0 >> 32) & mask;
  out[3] = w0 >> 48;
  out[4] = (w1)&mask;
  out[5] = (w1 >> 16) & mask;
  out[6] = (w1 >> 32) & mask;
  out[7] = w1 >> 48;
  out[8] = (w2)&mask;
  out[9] = (w2 >> 16) & mask;
  out[10] = (w2 >> 32) & mask;
  out[11] = w2 >> 48;
  out[12] = (w3)&mask;
  out[13] = (w3 >> 16) & mask;
  out[14] = (w3 >> 32) & mask;
  out[15] = w3 >> 48;
  out[16] = (w4)&mask;
  out[17] = (w4 >> 16) & mask;
  out[18] = (w4 >> 32) & mask;
  out[19] = w4 >> 48;
  out[20] = (w5)&mask;
  out[21] = (w5 >> 16) & mask;
  out[22] = (w5 >> 32) & mask;
  out[23] = w5 >> 48;
  out[24] = (w6)&mask;
  out[25] = (w6 >> 16) & mask;
  out[26] = (w6 >> 32) & mask;
  out[27] = w6 >> 48;
  out[28] = (w7)&mask;
  out[29] = (w7 >> 16) & mask;
  out[30] = (w7 >> 32) & mask;
  out[31] = w7 >> 48;

  return in;
}

inline const uint8_t* unpack17_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 131071ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint32_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 17) & mask;
  out[2] = (w0 >> 34) & mask;
  out[3] = ((w0 >> 51) | (w1 << 13)) & mask;
  out[4] = (w1 >> 4) & mask;
  out[5] = (w1 >> 21) & mask;
  out[6] = (w1 >> 38) & mask;
  out[7] = ((w1 >> 55) | (w2 << 9)) & mask;
  out[8] = (w2 >> 8) & mask;
  out[9] = (w2 >> 25) & mask;
  out[10] = (w2 >> 42) & mask;
  out[11] = ((w2 >> 59) | (w3 << 5)) & mask;
  out[12] = (w3 >> 12) & mask;
  out[13] = (w3 >> 29) & mask;
  out[14] = (w3 >> 46) & mask;
  out[15] = ((w3 >> 63) | (w4 << 1)) & mask;
  out[16] = (w4 >> 16) & mask;
  out[17] = (w4 >> 33) & mask;
  out[18] = ((w4 >> 50) | (w5 << 14)) & mask;
  out[19] = (w5 >> 3) & mask;
  out[20] = (w5 >> 20) & mask;
  out[21] = (w5 >> 37) & mask;
  out[22] = ((w5 >> 54) | (w6 << 10)) & mask;
  out[23] = (w6 >> 7) & mask;
  out[24] = (w6 >> 24) & mask;
  out[25] = (w6 >> 41) & mask;
  out[26] = ((w6 >> 58) | (w7 << 6)) & mask;
  out[27] = (w7 >> 11) & mask;
  out[28] = (w7 >> 28) & mask;
  out[29] = (w7 >> 45) & mask;
  out[30] = ((w7 >> 62) | (w8 << 2)) & mask;
  out[31] = (w8 >> 15) & mask;

  return in;
}

inline const uint8_t* unpack18_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 262143ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 18) & mask;
  out[2] = (w0 >> 36) & mask;
  out[3] = ((w0 >> 54) | (w1 << 10)) & mask;
  out[4] = (w1 >> 8) & mask;
  out[5] = (w1 >> 26) & mask;
  out[6] = (w1 >> 44) & mask;
  out[7] = ((w1 >> 62) | (w2 << 2)) & mask;
  out[8] = (w2 >> 16) & mask;
  out[9] = (w2 >> 34) & mask;
  out[10] = ((w2 >> 52) | (w3 << 12)) & mask;
  out[11] = (w3 >> 6) & mask;
  out[12] = (w3 >> 24) & mask;
  out[13] = (w3 >> 42) & mask;
  out[14] = ((w3 >> 60) | (w4 << 4)) & mask;
  out[15] = (w4 >> 14) & mask;
  out[16] = (w4 >> 32) & mask;
  out[17] = ((w4 >> 50) | (w5 << 14)) & mask;
  out[18] = (w5 >> 4) & mask;
  out[19] = (w5 >> 22) & mask;
  out[20] = (w5 >> 40) & mask;
  out[21] = ((w5 >> 58) | (w6 << 6)) & mask;
  out[22] = (w6 >> 12) & mask;
  out[23] = (w6 >> 30) & mask;
  out[24] = ((w6 >> 48) | (w7 << 16)) & mask;
  out[25] = (w7 >> 2) & mask;
  out[26] = (w7 >> 20) & mask;
  out[27] = (w7 >> 38) & mask;
  out[28] = ((w7 >> 56) | (w8 << 8)) & mask;
  out[29] = (w8 >> 10) & mask;
  out[30] = (w8 >> 28) & mask;
  out[31] = w8 >> 46;

  return in;
}

inline const uint8_t* unpack19_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 524287ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint32_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 19) & mask;
  out[2] = (w0 >> 38) & mask;
  out[3] = ((w0 >> 57) | (w1 << 7)) & mask;
  out[4] = (w1 >> 12) & mask;
  out[5] = (w1 >> 31) & mask;
  out[6] = ((w1 >> 50) | (w2 << 14)) & mask;
  out[7] = (w2 >> 5) & mask;
  out[8] = (w2 >> 24) & mask;
  out[9] = (w2 >> 43) & mask;
  out[10] = ((w2 >> 62) | (w3 << 2)) & mask;
  out[11] = (w3 >> 17) & mask;
  out[12] = (w3 >> 36) & mask;
  out[13] = ((w3 >> 55) | (w4 << 9)) & mask;
  out[14] = (w4 >> 10) & mask;
  out[15] = (w4 >> 29) & mask;
  out[16] = ((w4 >> 48) | (w5 << 16)) & mask;
  out[17] = (w5 >> 3) & mask;
  out[18] = (w5 >> 22) & mask;
  out[19] = (w5 >> 41) & mask;
  out[20] = ((w5 >> 60) | (w6 << 4)) & mask;
  out[21] = (w6 >> 15) & mask;
  out[22] = (w6 >> 34) & mask;
  out[23] = ((w6 >> 53) | (w7 << 11)) & mask;
  out[24] = (w7 >> 8) & mask;
  out[25] = (w7 >> 27) & mask;
  out[26] = ((w7 >> 46) | (w8 << 18)) & mask;
  out[27] = (w8 >> 1) & mask;
  out[28] = (w8 >> 20) & mask;
  out[29] = (w8 >> 39) & mask;
  out[30] = ((w8 >> 58) | (w9 << 6)) & mask;
  out[31] = (w9 >> 13) & mask;

  return in;
}

inline const uint8_t* unpack20_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 1048575ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 20) & mask;
  out[2] = (w0 >> 40) & mask;
  out[3] = ((w0 >> 60) | (w1 << 4)) & mask;
  out[4] = (w1 >> 16) & mask;
  out[5] = (w1 >> 36) & mask;
  out[6] = ((w1 >> 56) | (w2 << 8)) & mask;
  out[7] = (w2 >> 12) & mask;
  out[8] = (w2 >> 32) & mask;
  out[9] = ((w2 >> 52) | (w3 << 12)) & mask;
  out[10] = (w3 >> 8) & mask;
  out[11] = (w3 >> 28) & mask;
  out[12] = ((w3 >> 48) | (w4 << 16)) & mask;
  out[13] = (w4 >> 4) & mask;
  out[14] = (w4 >> 24) & mask;
  out[15] = w4 >> 44;
  out[16] = (w5)&mask;
  out[17] = (w5 >> 20) & mask;
  out[18] = (w5 >> 40) & mask;
  out[19] = ((w5 >> 60) | (w6 << 4)) & mask;
  out[20] = (w6 >> 16) & mask;
  out[21] = (w6 >> 36) & mask;
  out[22] = ((w6 >> 56) | (w7 << 8)) & mask;
  out[23] = (w7 >> 12) & mask;
  out[24] = (w7 >> 32) & mask;
  out[25] = ((w7 >> 52) | (w8 << 12)) & mask;
  out[26] = (w8 >> 8) & mask;
  out[27] = (w8 >> 28) & mask;
  out[28] = ((w8 >> 48) | (w9 << 16)) & mask;
  out[29] = (w9 >> 4) & mask;
  out[30] = (w9 >> 24) & mask;
  out[31] = w9 >> 44;

  return in;
}

inline const uint8_t* unpack21_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 2097151ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint32_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 21) & mask;
  out[2] = (w0 >> 42) & mask;
  out[3] = ((w0 >> 63) | (w1 << 1)) & mask;
  out[4] = (w1 >> 20) & mask;
  out[5] = (w1 >> 41) & mask;
  out[6] = ((w1 >> 62) | (w2 << 2)) & mask;
  out[7] = (w2 >> 19) & mask;
  out[8] = (w2 >> 40) & mask;
  out[9] = ((w2 >> 61) | (w3 << 3)) & mask;
  out[10] = (w3 >> 18) & mask;
  out[11] = (w3 >> 39) & mask;
  out[12] = ((w3 >> 60) | (w4 << 4)) & mask;
  out[13] = (w4 >> 17) & mask;
  out[14] = (w4 >> 38) & mask;
  out[15] = ((w4 >> 59) | (w5 << 5)) & mask;
  out[16] = (w5 >> 16) & mask;
  out[17] = (w5 >> 37) & mask;
  out[18] = ((w5 >> 58) | (w6 << 6)) & mask;
  out[19] = (w6 >> 15) & mask;
  out[20] = (w6 >> 36) & mask;
  out[21] = ((w6 >> 57) | (w7 << 7)) & mask;
  out[22] = (w7 >> 14) & mask;
  out[23] = (w7 >> 35) & mask;
  out[24] = ((w7 >> 56) | (w8 << 8)) & mask;
  out[25] = (w8 >> 13) & mask;
  out[26] = (w8 >> 34) & mask;
  out[27] = ((w8 >> 55) | (w9 << 9)) & mask;
  out[28] = (w9 >> 12) & mask;
  out[29] = (w9 >> 33) & mask;
  out[30] = ((w9 >> 54) | (w10 << 10)) & mask;
  out[31] = (w10 >> 11) & mask;

  return in;
}

inline const uint8_t* unpack22_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 4194303ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 22) & mask;
  out[2] = ((w0 >> 44) | (w1 << 20)) & mask;
  out[3] = (w1 >> 2) & mask;
  out[4] = (w1 >> 24) & mask;
  out[5] = ((w1 >> 46) | (w2 << 18)) & mask;
  out[6] = (w2 >> 4) & mask;
  out[7] = (w2 >> 26) & mask;
  out[8] = ((w2 >> 48) | (w3 << 16)) & mask;
  out[9] = (w3 >> 6) & mask;
  out[10] = (w3 >> 28) & mask;
  out[11] = ((w3 >> 50) | (w4 << 14)) & mask;
  out[12] = (w4 >> 8) & mask;
  out[13] = (w4 >> 30) & mask;
  out[14] = ((w4 >> 52) | (w5 << 12)) & mask;
  out[15] = (w5 >> 10) & mask;
  out[16] = (w5 >> 32) & mask;
  out[17] = ((w5 >> 54) | (w6 << 10)) & mask;
  out[18] = (w6 >> 12) & mask;
  out[19] = (w6 >> 34) & mask;
  out[20] = ((w6 >> 56) | (w7 << 8)) & mask;
  out[21] = (w7 >> 14) & mask;
  out[22] = (w7 >> 36) & mask;
  out[23] = ((w7 >> 58) | (w8 << 6)) & mask;
  out[24] = (w8 >> 16) & mask;
  out[25] = (w8 >> 38) & mask;
  out[26] = ((w8 >> 60) | (w9 << 4)) & mask;
  out[27] = (w9 >> 18) & mask;
  out[28] = (w9 >> 40) & mask;
  out[29] = ((w9 >> 62) | (w10 << 2)) & mask;
  out[30] = (w10 >> 20) & mask;
  out[31] = w10 >> 42;

  return in;
}

inline const uint8_t* unpack23_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 8388607ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint32_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 23) & mask;
  out[2] = ((w0 >> 46) | (w1 << 18)) & mask;
  out[3] = (w1 >> 5) & mask;
  out[4] = (w1 >> 28) & mask;
  out[5] = ((w1 >> 51) | (w2 << 13)) & mask;
  out[6] = (w2 >> 10) & mask;
  out[7] = (w2 >> 33) & mask;
  out[8] = ((w2 >> 56) | (w3 << 8)) & mask;
  out[9] = (w3 >> 15) & mask;
  out[10] = (w3 >> 38) & mask;
  out[11] = ((w3 >> 61) | (w4 << 3)) & mask;
  out[12] = (w4 >> 20) & mask;
  out[13] = ((w4 >> 43) | (w5 << 21)) & mask;
  out[14] = (w5 >> 2) & mask;
  out[15] = (w5 >> 25) & mask;
  out[16] = ((w5 >> 48) | (w6 << 16)) & mask;
  out[17] = (w6 >> 7) & mask;
  out[18] = (w6 >> 30) & mask;
  out[19] = ((w6 >> 53) | (w7 << 11)) & mask;
  out[20] = (w7 >> 12) & mask;
  out[21] = (w7 >> 35) & mask;
  out[22] = ((w7 >> 58) | (w8 << 6)) & mask;
  out[23] = (w8 >> 17) & mask;
  out[24] = (w8 >> 40) & mask;
  out[25] = ((w8 >> 63) | (w9 << 1)) & mask;
  out[26] = (w9 >> 22) & mask;
  out[27] = ((w9 >> 45) | (w10 << 19)) & mask;
  out[28] = (w10 >> 4) & mask;
  out[29] = (w10 >> 27) & mask;
  out[30] = ((w10 >> 50) | (w11 << 14)) & mask;
  out[31] = (w11 >> 9) & mask;

  return in;
}

inline const uint8_t* unpack24_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 16777215ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 24) & mask;
  out[2] = ((w0 >> 48) | (w1 << 16)) & mask;
  out[3] = (w1 >> 8) & mask;
  out[4] = (w1 >> 32) & mask;
  out[5] = ((w1 >> 56) | (w2 << 8)) & mask;
  out[6] = (w2 >> 16) & mask;
  out[7] = w2 >> 40;
  out[8] = (w3)&mask;
  out[9] = (w3 >> 24) & mask;
  out[10] = ((w3 >> 48) | (w4 << 16)) & mask;
  out[11] = (w4 >> 8) & mask;
  out[12] = (w4 >> 32) & mask;
  out[13] = ((w4 >> 56) | (w5 << 8)) & mask;
  out[14] = (w5 >> 16) & mask;
  out[15] = w5 >> 40;
  out[16] = (w6)&mask;
  out[17] = (w6 >> 24) & mask;
  out[18] = ((w6 >> 48) | (w7 << 16)) & mask;
  out[19] = (w7 >> 8) & mask;
  out[20] = (w7 >> 32) & mask;
  out[21] = ((w7 >> 56) | (w8 << 8)) & mask;
  out[22] = (w8 >> 16) & mask;
  out[23] = w8 >> 40;
  out[24] = (w9)&mask;
  out[25] = (w9 >> 24) & mask;
  out[26] = ((w9 >> 48) | (w10 << 16)) & mask;
  out[27] = (w10 >> 8) & mask;
  out[28] = (w10 >> 32) & mask;
  out[29] = ((w10 >> 56) | (w11 << 8)) & mask;
  out[30] = (w11 >> 16) & mask;
  out[31] = w11 >> 40;

  return in;
}

inline const uint8_t* unpack25_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 33554431ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint32_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 25) & mask;
  out[2] = ((w0 >> 50) | (w1 << 14)) & mask;
  out[3] = (w1 >> 11) & mask;
  out[4] = (w1 >> 36) & mask;
  out[5] = ((w1 >> 61) | (w2 << 3)) & mask;
  out[6] = (w2 >> 22) & mask;
  out[7] = ((w2 >> 47) | (w3 << 17)) & mask;
  out[8] = (w3 >> 8) & mask;
  out[9] = (w3 >> 33) & mask;
  out[10] = ((w3 >> 58) | (w4 << 6)) & mask;
  out[11] = (w4 >> 19) & mask;
  out[12] = ((w4 >> 44) | (w5 << 20)) & mask;
  out[13] = (w5 >> 5) & mask;
  out[14] = (w5 >> 30) & mask;
  out[15] = ((w5 >> 55) | (w6 << 9)) & mask;
  out[16] = (w6 >> 16) & mask;
  out[17] = ((w6 >> 41) | (w7 << 23)) & mask;
  out[18] = (w7 >> 2) & mask;
  out[19] = (w7 >> 27) & mask;
  out[20] = ((w7 >> 52) | (w8 << 12)) & mask;
  out[21] = (w8 >> 13) & mask;
  out[22] = (w8 >> 38) & mask;
  out[23] = ((w8 >> 63) | (w9 << 1)) & mask;
  out[24] = (w9 >> 24) & mask;
  out[25] = ((w9 >> 49) | (w10 << 15)) & mask;
  out[26] = (w10 >> 10) & mask;
  out[27] = (w10 >> 35) & mask;
  out[28] = ((w10 >> 60) | (w11 << 4)) & mask;
  out[29] = (w11 >> 21) & mask;
  out[30] = ((w11 >> 46) | (w12 << 18)) & mask;
  out[31] = (w12 >> 7) & mask;

  return in;
}

inline const uint8_t* unpack26_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 67108863ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 26) & mask;
  out[2] = ((w0 >> 52) | (w1 << 12)) & mask;
  out[3] = (w1 >> 14) & mask;
  out[4] = ((w1 >> 40) | (w2 << 24)) & mask;
  out[5] = (w2 >> 2) & mask;
  out[6] = (w2 >> 28) & mask;
  out[7] = ((w2 >> 54) | (w3 << 10)) & mask;
  out[8] = (w3 >> 16) & mask;
  out[9] = ((w3 >> 42) | (w4 << 22)) & mask;
  out[10] = (w4 >> 4) & mask;
  out[11] = (w4 >> 30) & mask;
  out[12] = ((w4 >> 56) | (w5 << 8)) & mask;
  out[13] = (w5 >> 18) & mask;
  out[14] = ((w5 >> 44) | (w6 << 20)) & mask;
  out[15] = (w6 >> 6) & mask;
  out[16] = (w6 >> 32) & mask;
  out[17] = ((w6 >> 58) | (w7 << 6)) & mask;
  out[18] = (w7 >> 20) & mask;
  out[19] = ((w7 >> 46) | (w8 << 18)) & mask;
  out[20] = (w8 >> 8) & mask;
  out[21] = (w8 >> 34) & mask;
  out[22] = ((w8 >> 60) | (w9 << 4)) & mask;
  out[23] = (w9 >> 22) & mask;
  out[24] = ((w9 >> 48) | (w10 << 16)) & mask;
  out[25] = (w10 >> 10) & mask;
  out[26] = (w10 >> 36) & mask;
  out[27] = ((w10 >> 62) | (w11 << 2)) & mask;
  out[28] = (w11 >> 24) & mask;
  out[29] = ((w11 >> 50) | (w12 << 14)) & mask;
  out[30] = (w12 >> 12) & mask;
  out[31] = w12 >> 38;

  return in;
}

inline const uint8_t* unpack27_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 134217727ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint32_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 27) & mask;
  out[2] = ((w0 >> 54) | (w1 << 10)) & mask;
  out[3] = (w1 >> 17) & mask;
  out[4] = ((w1 >> 44) | (w2 << 20)) & mask;
  out[5] = (w2 >> 7) & mask;
  out[6] = (w2 >> 34) & mask;
  out[7] = ((w2 >> 61) | (w3 << 3)) & mask;
  out[8] = (w3 >> 24) & mask;
  out[9] = ((w3 >> 51) | (w4 << 13)) & mask;
  out[10] = (w4 >> 14) & mask;
  out[11] = ((w4 >> 41) | (w5 << 23)) & mask;
  out[12] = (w5 >> 4) & mask;
  out[13] = (w5 >> 31) & mask;
  out[14] = ((w5 >> 58) | (w6 << 6)) & mask;
  out[15] = (w6 >> 21) & mask;
  out[16] = ((w6 >> 48) | (w7 << 16)) & mask;
  out[17] = (w7 >> 11) & mask;
  out[18] = ((w7 >> 38) | (w8 << 26)) & mask;
  out[19] = (w8 >> 1) & mask;
  out[20] = (w8 >> 28) & mask;
  out[21] = ((w8 >> 55) | (w9 << 9)) & mask;
  out[22] = (w9 >> 18) & mask;
  out[23] = ((w9 >> 45) | (w10 << 19)) & mask;
  out[24] = (w10 >> 8) & mask;
  out[25] = (w10 >> 35) & mask;
  out[26] = ((w10 >> 62) | (w11 << 2)) & mask;
  out[27] = (w11 >> 25) & mask;
  out[28] = ((w11 >> 52) | (w12 << 12)) & mask;
  out[29] = (w12 >> 15) & mask;
  out[30] = ((w12 >> 42) | (w13 << 22)) & mask;
  out[31] = (w13 >> 5) & mask;

  return in;
}

inline const uint8_t* unpack28_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 268435455ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 28) & mask;
  out[2] = ((w0 >> 56) | (w1 << 8)) & mask;
  out[3] = (w1 >> 20) & mask;
  out[4] = ((w1 >> 48) | (w2 << 16)) & mask;
  out[5] = (w2 >> 12) & mask;
  out[6] = ((w2 >> 40) | (w3 << 24)) & mask;
  out[7] = (w3 >> 4) & mask;
  out[8] = (w3 >> 32) & mask;
  out[9] = ((w3 >> 60) | (w4 << 4)) & mask;
  out[10] = (w4 >> 24) & mask;
  out[11] = ((w4 >> 52) | (w5 << 12)) & mask;
  out[12] = (w5 >> 16) & mask;
  out[13] = ((w5 >> 44) | (w6 << 20)) & mask;
  out[14] = (w6 >> 8) & mask;
  out[15] = w6 >> 36;
  out[16] = (w7)&mask;
  out[17] = (w7 >> 28) & mask;
  out[18] = ((w7 >> 56) | (w8 << 8)) & mask;
  out[19] = (w8 >> 20) & mask;
  out[20] = ((w8 >> 48) | (w9 << 16)) & mask;
  out[21] = (w9 >> 12) & mask;
  out[22] = ((w9 >> 40) | (w10 << 24)) & mask;
  out[23] = (w10 >> 4) & mask;
  out[24] = (w10 >> 32) & mask;
  out[25] = ((w10 >> 60) | (w11 << 4)) & mask;
  out[26] = (w11 >> 24) & mask;
  out[27] = ((w11 >> 52) | (w12 << 12)) & mask;
  out[28] = (w12 >> 16) & mask;
  out[29] = ((w12 >> 44) | (w13 << 20)) & mask;
  out[30] = (w13 >> 8) & mask;
  out[31] = w13 >> 36;

  return in;
}

inline const uint8_t* unpack29_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 536870911ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint32_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 29) & mask;
  out[2] = ((w0 >> 58) | (w1 << 6)) & mask;
  out[3] = (w1 >> 23) & mask;
  out[4] = ((w1 >> 52) | (w2 << 12)) & mask;
  out[5] = (w2 >> 17) & mask;
  out[6] = ((w2 >> 46) | (w3 << 18)) & mask;
  out[7] = (w3 >> 11) & mask;
  out[8] = ((w3 >> 40) | (w4 << 24)) & mask;
  out[9] = (w4 >> 5) & mask;
  out[10] = (w4 >> 34) & mask;
  out[11] = ((w4 >> 63) | (w5 << 1)) & mask;
  out[12] = (w5 >> 28) & mask;
  out[13] = ((w5 >> 57) | (w6 << 7)) & mask;
  out[14] = (w6 >> 22) & mask;
  out[15] = ((w6 >> 51) | (w7 << 13)) & mask;
  out[16] = (w7 >> 16) & mask;
  out[17] = ((w7 >> 45) | (w8 << 19)) & mask;
  out[18] = (w8 >> 10) & mask;
  out[19] = ((w8 >> 39) | (w9 << 25)) & mask;
  out[20] = (w9 >> 4) & mask;
  out[21] = (w9 >> 33) & mask;
  out[22] = ((w9 >> 62) | (w10 << 2)) & mask;
  out[23] = (w10 >> 27) & mask;
  out[24] = ((w10 >> 56) | (w11 << 8)) & mask;
  out[25] = (w11 >> 21) & mask;
  out[26] = ((w11 >> 50) | (w12 << 14)) & mask;
  out[27] = (w12 >> 15) & mask;
  out[28] = ((w12 >> 44) | (w13 << 20)) & mask;
  out[29] = (w13 >> 9) & mask;
  out[30] = ((w13 >> 38) | (w14 << 26)) & mask;
  out[31] = (w14 >> 3) & mask;

  return in;
}

inline const uint8_t* unpack30_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 1073741823ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 30) & mask;
  out[2] = ((w0 >> 60) | (w1 << 4)) & mask;
  out[3] = (w1 >> 26) & mask;
  out[4] = ((w1 >> 56) | (w2 << 8)) & mask;
  out[5] = (w2 >> 22) & mask;
  out[6] = ((w2 >> 52) | (w3 << 12)) & mask;
  out[7] = (w3 >> 18) & mask;
  out[8] = ((w3 >> 48) | (w4 << 16)) & mask;
  out[9] = (w4 >> 14) & mask;
  out[10] = ((w4 >> 44) | (w5 << 20)) & mask;
  out[11] = (w5 >> 10) & mask;
  out[12] = ((w5 >> 40) | (w6 << 24)) & mask;
  out[13] = (w6 >> 6) & mask;
  out[14] = ((w6 >> 36) | (w7 << 28)) & mask;
  out[15] = (w7 >> 2) & mask;
  out[16] = (w7 >> 32) & mask;
  out[17] = ((w7 >> 62) | (w8 << 2)) & mask;
  out[18] = (w8 >> 28) & mask;
  out[19] = ((w8 >> 58) | (w9 << 6)) & mask;
  out[20] = (w9 >> 24) & mask;
  out[21] = ((w9 >> 54) | (w10 << 10)) & mask;
  out[22] = (w10 >> 20) & mask;
  out[23] = ((w10 >> 50) | (w11 << 14)) & mask;
  out[24] = (w11 >> 16) & mask;
  out[25] = ((w11 >> 46) | (w12 << 18)) & mask;
  out[26] = (w12 >> 12) & mask;
  out[27] = ((w12 >> 42) | (w13 << 22)) & mask;
  out[28] = (w13 >> 8) & mask;
  out[29] = ((w13 >> 38) | (w14 << 26)) & mask;
  out[30] = (w14 >> 4) & mask;
  out[31] = w14 >> 34;

  return in;
}

inline const uint8_t* unpack31_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 2147483647ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint32_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = (w0 >> 31) & mask;
  out[2] = ((w0 >> 62) | (w1 << 2)) & mask;
  out[3] = (w1 >> 29) & mask;
  out[4] = ((w1 >> 60) | (w2 << 4)) & mask;
  out[5] = (w2 >> 27) & mask;
  out[6] = ((w2 >> 58) | (w3 << 6)) & mask;
  out[7] = (w3 >> 25) & mask;
  out[8] = ((w3 >> 56) | (w4 << 8)) & mask;
  out[9] = (w4 >> 23) & mask;
  out[10] = ((w4 >> 54) | (w5 << 10)) & mask;
  out[11] = (w5 >> 21) & mask;
  out[12] = ((w5 >> 52) | (w6 << 12)) & mask;
  out[13] = (w6 >> 19) & mask;
  out[14] = ((w6 >> 50) | (w7 << 14)) & mask;
  out[15] = (w7 >> 17) & mask;
  out[16] = ((w7 >> 48) | (w8 << 16)) & mask;
  out[17] = (w8 >> 15) & mask;
  out[18] = ((w8 >> 46) | (w9 << 18)) & mask;
  out[19] = (w9 >> 13) & mask;
  out[20] = ((w9 >> 44) | (w10 << 20)) & mask;
  out[21] = (w10 >> 11) & mask;
  out[22] = ((w10 >> 42) | (w11 << 22)) & mask;
  out[23] = (w11 >> 9) & mask;
  out[24] = ((w11 >> 40) | (w12 << 24)) & mask;
  out[25] = (w12 >> 7) & mask;
  out[26] = ((w12 >> 38) | (w13 << 26)) & mask;
  out[27] = (w13 >> 5) & mask;
  out[28] = ((w13 >> 36) | (w14 << 28)) & mask;
  out[29] = (w14 >> 3) & mask;
  out[30] = ((w14 >> 34) | (w15 << 30)) & mask;
  out[31] = (w15 >> 1) & mask;

  return in;
}

inline const uint8_t* unpack32_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 4294967295ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = w0 >> 32;
  out[2] = (w1)&mask;
  out[3] = w1 >> 32;
  out[4] = (w2)&mask;
  out[5] = w2 >> 32;
  out[6] = (w3)&mask;
  out[7] = w3 >> 32;
  out[8] = (w4)&mask;
  out[9] = w4 >> 32;
  out[10] = (w5)&mask;
  out[11] = w5 >> 32;
  out[12] = (w6)&mask;
  out[13] = w6 >> 32;
  out[14] = (w7)&mask;
  out[15] = w7 >> 32;
  out[16] = (w8)&mask;
  out[17] = w8 >> 32;
  out[18] = (w9)&mask;
  out[19] = w9 >> 32;
  out[20] = (w10)&mask;
  out[21] = w10 >> 32;
  out[22] = (w11)&mask;
  out[23] = w11 >> 32;
  out[24] = (w12)&mask;
  out[25] = w12 >> 32;
  out[26] = (w13)&mask;
  out[27] = w13 >> 32;
  out[28] = (w14)&mask;
  out[29] = w14 >> 32;
  out[30] = (w15)&mask;
  out[31] = w15 >> 32;

  return in;
}

inline const uint8_t* unpack33_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 8589934591ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint32_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 33) | (w1 << 31)) & mask;
  out[2] = (w1 >> 2) & mask;
  out[3] = ((w1 >> 35) | (w2 << 29)) & mask;
  out[4] = (w2 >> 4) & mask;
  out[5] = ((w2 >> 37) | (w3 << 27)) & mask;
  out[6] = (w3 >> 6) & mask;
  out[7] = ((w3 >> 39) | (w4 << 25)) & mask;
  out[8] = (w4 >> 8) & mask;
  out[9] = ((w4 >> 41) | (w5 << 23)) & mask;
  out[10] = (w5 >> 10) & mask;
  out[11] = ((w5 >> 43) | (w6 << 21)) & mask;
  out[12] = (w6 >> 12) & mask;
  out[13] = ((w6 >> 45) | (w7 << 19)) & mask;
  out[14] = (w7 >> 14) & mask;
  out[15] = ((w7 >> 47) | (w8 << 17)) & mask;
  out[16] = (w8 >> 16) & mask;
  out[17] = ((w8 >> 49) | (w9 << 15)) & mask;
  out[18] = (w9 >> 18) & mask;
  out[19] = ((w9 >> 51) | (w10 << 13)) & mask;
  out[20] = (w10 >> 20) & mask;
  out[21] = ((w10 >> 53) | (w11 << 11)) & mask;
  out[22] = (w11 >> 22) & mask;
  out[23] = ((w11 >> 55) | (w12 << 9)) & mask;
  out[24] = (w12 >> 24) & mask;
  out[25] = ((w12 >> 57) | (w13 << 7)) & mask;
  out[26] = (w13 >> 26) & mask;
  out[27] = ((w13 >> 59) | (w14 << 5)) & mask;
  out[28] = (w14 >> 28) & mask;
  out[29] = ((w14 >> 61) | (w15 << 3)) & mask;
  out[30] = (w15 >> 30) & mask;
  out[31] = ((w15 >> 63) | (w16 << 1)) & mask;

  return in;
}

inline const uint8_t* unpack34_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 17179869183ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 34) | (w1 << 30)) & mask;
  out[2] = (w1 >> 4) & mask;
  out[3] = ((w1 >> 38) | (w2 << 26)) & mask;
  out[4] = (w2 >> 8) & mask;
  out[5] = ((w2 >> 42) | (w3 << 22)) & mask;
  out[6] = (w3 >> 12) & mask;
  out[7] = ((w3 >> 46) | (w4 << 18)) & mask;
  out[8] = (w4 >> 16) & mask;
  out[9] = ((w4 >> 50) | (w5 << 14)) & mask;
  out[10] = (w5 >> 20) & mask;
  out[11] = ((w5 >> 54) | (w6 << 10)) & mask;
  out[12] = (w6 >> 24) & mask;
  out[13] = ((w6 >> 58) | (w7 << 6)) & mask;
  out[14] = (w7 >> 28) & mask;
  out[15] = ((w7 >> 62) | (w8 << 2)) & mask;
  out[16] = ((w8 >> 32) | (w9 << 32)) & mask;
  out[17] = (w9 >> 2) & mask;
  out[18] = ((w9 >> 36) | (w10 << 28)) & mask;
  out[19] = (w10 >> 6) & mask;
  out[20] = ((w10 >> 40) | (w11 << 24)) & mask;
  out[21] = (w11 >> 10) & mask;
  out[22] = ((w11 >> 44) | (w12 << 20)) & mask;
  out[23] = (w12 >> 14) & mask;
  out[24] = ((w12 >> 48) | (w13 << 16)) & mask;
  out[25] = (w13 >> 18) & mask;
  out[26] = ((w13 >> 52) | (w14 << 12)) & mask;
  out[27] = (w14 >> 22) & mask;
  out[28] = ((w14 >> 56) | (w15 << 8)) & mask;
  out[29] = (w15 >> 26) & mask;
  out[30] = ((w15 >> 60) | (w16 << 4)) & mask;
  out[31] = w16 >> 30;

  return in;
}

inline const uint8_t* unpack35_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 34359738367ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint32_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 35) | (w1 << 29)) & mask;
  out[2] = (w1 >> 6) & mask;
  out[3] = ((w1 >> 41) | (w2 << 23)) & mask;
  out[4] = (w2 >> 12) & mask;
  out[5] = ((w2 >> 47) | (w3 << 17)) & mask;
  out[6] = (w3 >> 18) & mask;
  out[7] = ((w3 >> 53) | (w4 << 11)) & mask;
  out[8] = (w4 >> 24) & mask;
  out[9] = ((w4 >> 59) | (w5 << 5)) & mask;
  out[10] = ((w5 >> 30) | (w6 << 34)) & mask;
  out[11] = (w6 >> 1) & mask;
  out[12] = ((w6 >> 36) | (w7 << 28)) & mask;
  out[13] = (w7 >> 7) & mask;
  out[14] = ((w7 >> 42) | (w8 << 22)) & mask;
  out[15] = (w8 >> 13) & mask;
  out[16] = ((w8 >> 48) | (w9 << 16)) & mask;
  out[17] = (w9 >> 19) & mask;
  out[18] = ((w9 >> 54) | (w10 << 10)) & mask;
  out[19] = (w10 >> 25) & mask;
  out[20] = ((w10 >> 60) | (w11 << 4)) & mask;
  out[21] = ((w11 >> 31) | (w12 << 33)) & mask;
  out[22] = (w12 >> 2) & mask;
  out[23] = ((w12 >> 37) | (w13 << 27)) & mask;
  out[24] = (w13 >> 8) & mask;
  out[25] = ((w13 >> 43) | (w14 << 21)) & mask;
  out[26] = (w14 >> 14) & mask;
  out[27] = ((w14 >> 49) | (w15 << 15)) & mask;
  out[28] = (w15 >> 20) & mask;
  out[29] = ((w15 >> 55) | (w16 << 9)) & mask;
  out[30] = (w16 >> 26) & mask;
  out[31] = ((w16 >> 61) | (w17 << 3)) & mask;

  return in;
}

inline const uint8_t* unpack36_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 68719476735ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 36) | (w1 << 28)) & mask;
  out[2] = (w1 >> 8) & mask;
  out[3] = ((w1 >> 44) | (w2 << 20)) & mask;
  out[4] = (w2 >> 16) & mask;
  out[5] = ((w2 >> 52) | (w3 << 12)) & mask;
  out[6] = (w3 >> 24) & mask;
  out[7] = ((w3 >> 60) | (w4 << 4)) & mask;
  out[8] = ((w4 >> 32) | (w5 << 32)) & mask;
  out[9] = (w5 >> 4) & mask;
  out[10] = ((w5 >> 40) | (w6 << 24)) & mask;
  out[11] = (w6 >> 12) & mask;
  out[12] = ((w6 >> 48) | (w7 << 16)) & mask;
  out[13] = (w7 >> 20) & mask;
  out[14] = ((w7 >> 56) | (w8 << 8)) & mask;
  out[15] = w8 >> 28;
  out[16] = (w9)&mask;
  out[17] = ((w9 >> 36) | (w10 << 28)) & mask;
  out[18] = (w10 >> 8) & mask;
  out[19] = ((w10 >> 44) | (w11 << 20)) & mask;
  out[20] = (w11 >> 16) & mask;
  out[21] = ((w11 >> 52) | (w12 << 12)) & mask;
  out[22] = (w12 >> 24) & mask;
  out[23] = ((w12 >> 60) | (w13 << 4)) & mask;
  out[24] = ((w13 >> 32) | (w14 << 32)) & mask;
  out[25] = (w14 >> 4) & mask;
  out[26] = ((w14 >> 40) | (w15 << 24)) & mask;
  out[27] = (w15 >> 12) & mask;
  out[28] = ((w15 >> 48) | (w16 << 16)) & mask;
  out[29] = (w16 >> 20) & mask;
  out[30] = ((w16 >> 56) | (w17 << 8)) & mask;
  out[31] = w17 >> 28;

  return in;
}

inline const uint8_t* unpack37_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 137438953471ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint32_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 37) | (w1 << 27)) & mask;
  out[2] = (w1 >> 10) & mask;
  out[3] = ((w1 >> 47) | (w2 << 17)) & mask;
  out[4] = (w2 >> 20) & mask;
  out[5] = ((w2 >> 57) | (w3 << 7)) & mask;
  out[6] = ((w3 >> 30) | (w4 << 34)) & mask;
  out[7] = (w4 >> 3) & mask;
  out[8] = ((w4 >> 40) | (w5 << 24)) & mask;
  out[9] = (w5 >> 13) & mask;
  out[10] = ((w5 >> 50) | (w6 << 14)) & mask;
  out[11] = (w6 >> 23) & mask;
  out[12] = ((w6 >> 60) | (w7 << 4)) & mask;
  out[13] = ((w7 >> 33) | (w8 << 31)) & mask;
  out[14] = (w8 >> 6) & mask;
  out[15] = ((w8 >> 43) | (w9 << 21)) & mask;
  out[16] = (w9 >> 16) & mask;
  out[17] = ((w9 >> 53) | (w10 << 11)) & mask;
  out[18] = (w10 >> 26) & mask;
  out[19] = ((w10 >> 63) | (w11 << 1)) & mask;
  out[20] = ((w11 >> 36) | (w12 << 28)) & mask;
  out[21] = (w12 >> 9) & mask;
  out[22] = ((w12 >> 46) | (w13 << 18)) & mask;
  out[23] = (w13 >> 19) & mask;
  out[24] = ((w13 >> 56) | (w14 << 8)) & mask;
  out[25] = ((w14 >> 29) | (w15 << 35)) & mask;
  out[26] = (w15 >> 2) & mask;
  out[27] = ((w15 >> 39) | (w16 << 25)) & mask;
  out[28] = (w16 >> 12) & mask;
  out[29] = ((w16 >> 49) | (w17 << 15)) & mask;
  out[30] = (w17 >> 22) & mask;
  out[31] = ((w17 >> 59) | (w18 << 5)) & mask;

  return in;
}

inline const uint8_t* unpack38_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 274877906943ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 38) | (w1 << 26)) & mask;
  out[2] = (w1 >> 12) & mask;
  out[3] = ((w1 >> 50) | (w2 << 14)) & mask;
  out[4] = (w2 >> 24) & mask;
  out[5] = ((w2 >> 62) | (w3 << 2)) & mask;
  out[6] = ((w3 >> 36) | (w4 << 28)) & mask;
  out[7] = (w4 >> 10) & mask;
  out[8] = ((w4 >> 48) | (w5 << 16)) & mask;
  out[9] = (w5 >> 22) & mask;
  out[10] = ((w5 >> 60) | (w6 << 4)) & mask;
  out[11] = ((w6 >> 34) | (w7 << 30)) & mask;
  out[12] = (w7 >> 8) & mask;
  out[13] = ((w7 >> 46) | (w8 << 18)) & mask;
  out[14] = (w8 >> 20) & mask;
  out[15] = ((w8 >> 58) | (w9 << 6)) & mask;
  out[16] = ((w9 >> 32) | (w10 << 32)) & mask;
  out[17] = (w10 >> 6) & mask;
  out[18] = ((w10 >> 44) | (w11 << 20)) & mask;
  out[19] = (w11 >> 18) & mask;
  out[20] = ((w11 >> 56) | (w12 << 8)) & mask;
  out[21] = ((w12 >> 30) | (w13 << 34)) & mask;
  out[22] = (w13 >> 4) & mask;
  out[23] = ((w13 >> 42) | (w14 << 22)) & mask;
  out[24] = (w14 >> 16) & mask;
  out[25] = ((w14 >> 54) | (w15 << 10)) & mask;
  out[26] = ((w15 >> 28) | (w16 << 36)) & mask;
  out[27] = (w16 >> 2) & mask;
  out[28] = ((w16 >> 40) | (w17 << 24)) & mask;
  out[29] = (w17 >> 14) & mask;
  out[30] = ((w17 >> 52) | (w18 << 12)) & mask;
  out[31] = w18 >> 26;

  return in;
}

inline const uint8_t* unpack39_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 549755813887ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint32_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 39) | (w1 << 25)) & mask;
  out[2] = (w1 >> 14) & mask;
  out[3] = ((w1 >> 53) | (w2 << 11)) & mask;
  out[4] = ((w2 >> 28) | (w3 << 36)) & mask;
  out[5] = (w3 >> 3) & mask;
  out[6] = ((w3 >> 42) | (w4 << 22)) & mask;
  out[7] = (w4 >> 17) & mask;
  out[8] = ((w4 >> 56) | (w5 << 8)) & mask;
  out[9] = ((w5 >> 31) | (w6 << 33)) & mask;
  out[10] = (w6 >> 6) & mask;
  out[11] = ((w6 >> 45) | (w7 << 19)) & mask;
  out[12] = (w7 >> 20) & mask;
  out[13] = ((w7 >> 59) | (w8 << 5)) & mask;
  out[14] = ((w8 >> 34) | (w9 << 30)) & mask;
  out[15] = (w9 >> 9) & mask;
  out[16] = ((w9 >> 48) | (w10 << 16)) & mask;
  out[17] = (w10 >> 23) & mask;
  out[18] = ((w10 >> 62) | (w11 << 2)) & mask;
  out[19] = ((w11 >> 37) | (w12 << 27)) & mask;
  out[20] = (w12 >> 12) & mask;
  out[21] = ((w12 >> 51) | (w13 << 13)) & mask;
  out[22] = ((w13 >> 26) | (w14 << 38)) & mask;
  out[23] = (w14 >> 1) & mask;
  out[24] = ((w14 >> 40) | (w15 << 24)) & mask;
  out[25] = (w15 >> 15) & mask;
  out[26] = ((w15 >> 54) | (w16 << 10)) & mask;
  out[27] = ((w16 >> 29) | (w17 << 35)) & mask;
  out[28] = (w17 >> 4) & mask;
  out[29] = ((w17 >> 43) | (w18 << 21)) & mask;
  out[30] = (w18 >> 18) & mask;
  out[31] = ((w18 >> 57) | (w19 << 7)) & mask;

  return in;
}

inline const uint8_t* unpack40_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 1099511627775ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 40) | (w1 << 24)) & mask;
  out[2] = (w1 >> 16) & mask;
  out[3] = ((w1 >> 56) | (w2 << 8)) & mask;
  out[4] = ((w2 >> 32) | (w3 << 32)) & mask;
  out[5] = (w3 >> 8) & mask;
  out[6] = ((w3 >> 48) | (w4 << 16)) & mask;
  out[7] = w4 >> 24;
  out[8] = (w5)&mask;
  out[9] = ((w5 >> 40) | (w6 << 24)) & mask;
  out[10] = (w6 >> 16) & mask;
  out[11] = ((w6 >> 56) | (w7 << 8)) & mask;
  out[12] = ((w7 >> 32) | (w8 << 32)) & mask;
  out[13] = (w8 >> 8) & mask;
  out[14] = ((w8 >> 48) | (w9 << 16)) & mask;
  out[15] = w9 >> 24;
  out[16] = (w10)&mask;
  out[17] = ((w10 >> 40) | (w11 << 24)) & mask;
  out[18] = (w11 >> 16) & mask;
  out[19] = ((w11 >> 56) | (w12 << 8)) & mask;
  out[20] = ((w12 >> 32) | (w13 << 32)) & mask;
  out[21] = (w13 >> 8) & mask;
  out[22] = ((w13 >> 48) | (w14 << 16)) & mask;
  out[23] = w14 >> 24;
  out[24] = (w15)&mask;
  out[25] = ((w15 >> 40) | (w16 << 24)) & mask;
  out[26] = (w16 >> 16) & mask;
  out[27] = ((w16 >> 56) | (w17 << 8)) & mask;
  out[28] = ((w17 >> 32) | (w18 << 32)) & mask;
  out[29] = (w18 >> 8) & mask;
  out[30] = ((w18 >> 48) | (w19 << 16)) & mask;
  out[31] = w19 >> 24;

  return in;
}

inline const uint8_t* unpack41_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 2199023255551ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint32_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 41) | (w1 << 23)) & mask;
  out[2] = (w1 >> 18) & mask;
  out[3] = ((w1 >> 59) | (w2 << 5)) & mask;
  out[4] = ((w2 >> 36) | (w3 << 28)) & mask;
  out[5] = (w3 >> 13) & mask;
  out[6] = ((w3 >> 54) | (w4 << 10)) & mask;
  out[7] = ((w4 >> 31) | (w5 << 33)) & mask;
  out[8] = (w5 >> 8) & mask;
  out[9] = ((w5 >> 49) | (w6 << 15)) & mask;
  out[10] = ((w6 >> 26) | (w7 << 38)) & mask;
  out[11] = (w7 >> 3) & mask;
  out[12] = ((w7 >> 44) | (w8 << 20)) & mask;
  out[13] = (w8 >> 21) & mask;
  out[14] = ((w8 >> 62) | (w9 << 2)) & mask;
  out[15] = ((w9 >> 39) | (w10 << 25)) & mask;
  out[16] = (w10 >> 16) & mask;
  out[17] = ((w10 >> 57) | (w11 << 7)) & mask;
  out[18] = ((w11 >> 34) | (w12 << 30)) & mask;
  out[19] = (w12 >> 11) & mask;
  out[20] = ((w12 >> 52) | (w13 << 12)) & mask;
  out[21] = ((w13 >> 29) | (w14 << 35)) & mask;
  out[22] = (w14 >> 6) & mask;
  out[23] = ((w14 >> 47) | (w15 << 17)) & mask;
  out[24] = ((w15 >> 24) | (w16 << 40)) & mask;
  out[25] = (w16 >> 1) & mask;
  out[26] = ((w16 >> 42) | (w17 << 22)) & mask;
  out[27] = (w17 >> 19) & mask;
  out[28] = ((w17 >> 60) | (w18 << 4)) & mask;
  out[29] = ((w18 >> 37) | (w19 << 27)) & mask;
  out[30] = (w19 >> 14) & mask;
  out[31] = ((w19 >> 55) | (w20 << 9)) & mask;

  return in;
}

inline const uint8_t* unpack42_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 4398046511103ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint64_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 42) | (w1 << 22)) & mask;
  out[2] = (w1 >> 20) & mask;
  out[3] = ((w1 >> 62) | (w2 << 2)) & mask;
  out[4] = ((w2 >> 40) | (w3 << 24)) & mask;
  out[5] = (w3 >> 18) & mask;
  out[6] = ((w3 >> 60) | (w4 << 4)) & mask;
  out[7] = ((w4 >> 38) | (w5 << 26)) & mask;
  out[8] = (w5 >> 16) & mask;
  out[9] = ((w5 >> 58) | (w6 << 6)) & mask;
  out[10] = ((w6 >> 36) | (w7 << 28)) & mask;
  out[11] = (w7 >> 14) & mask;
  out[12] = ((w7 >> 56) | (w8 << 8)) & mask;
  out[13] = ((w8 >> 34) | (w9 << 30)) & mask;
  out[14] = (w9 >> 12) & mask;
  out[15] = ((w9 >> 54) | (w10 << 10)) & mask;
  out[16] = ((w10 >> 32) | (w11 << 32)) & mask;
  out[17] = (w11 >> 10) & mask;
  out[18] = ((w11 >> 52) | (w12 << 12)) & mask;
  out[19] = ((w12 >> 30) | (w13 << 34)) & mask;
  out[20] = (w13 >> 8) & mask;
  out[21] = ((w13 >> 50) | (w14 << 14)) & mask;
  out[22] = ((w14 >> 28) | (w15 << 36)) & mask;
  out[23] = (w15 >> 6) & mask;
  out[24] = ((w15 >> 48) | (w16 << 16)) & mask;
  out[25] = ((w16 >> 26) | (w17 << 38)) & mask;
  out[26] = (w17 >> 4) & mask;
  out[27] = ((w17 >> 46) | (w18 << 18)) & mask;
  out[28] = ((w18 >> 24) | (w19 << 40)) & mask;
  out[29] = (w19 >> 2) & mask;
  out[30] = ((w19 >> 44) | (w20 << 20)) & mask;
  out[31] = w20 >> 22;

  return in;
}

inline const uint8_t* unpack43_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 8796093022207ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint64_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 8;
  uint64_t w21 = util::SafeLoadAs<uint32_t>(in);
  w21 = arrow::bit_util::FromLittleEndian(w21);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 43) | (w1 << 21)) & mask;
  out[2] = ((w1 >> 22) | (w2 << 42)) & mask;
  out[3] = (w2 >> 1) & mask;
  out[4] = ((w2 >> 44) | (w3 << 20)) & mask;
  out[5] = ((w3 >> 23) | (w4 << 41)) & mask;
  out[6] = (w4 >> 2) & mask;
  out[7] = ((w4 >> 45) | (w5 << 19)) & mask;
  out[8] = ((w5 >> 24) | (w6 << 40)) & mask;
  out[9] = (w6 >> 3) & mask;
  out[10] = ((w6 >> 46) | (w7 << 18)) & mask;
  out[11] = ((w7 >> 25) | (w8 << 39)) & mask;
  out[12] = (w8 >> 4) & mask;
  out[13] = ((w8 >> 47) | (w9 << 17)) & mask;
  out[14] = ((w9 >> 26) | (w10 << 38)) & mask;
  out[15] = (w10 >> 5) & mask;
  out[16] = ((w10 >> 48) | (w11 << 16)) & mask;
  out[17] = ((w11 >> 27) | (w12 << 37)) & mask;
  out[18] = (w12 >> 6) & mask;
  out[19] = ((w12 >> 49) | (w13 << 15)) & mask;
  out[20] = ((w13 >> 28) | (w14 << 36)) & mask;
  out[21] = (w14 >> 7) & mask;
  out[22] = ((w14 >> 50) | (w15 << 14)) & mask;
  out[23] = ((w15 >> 29) | (w16 << 35)) & mask;
  out[24] = (w16 >> 8) & mask;
  out[25] = ((w16 >> 51) | (w17 << 13)) & mask;
  out[26] = ((w17 >> 30) | (w18 << 34)) & mask;
  out[27] = (w18 >> 9) & mask;
  out[28] = ((w18 >> 52) | (w19 << 12)) & mask;
  out[29] = ((w19 >> 31) | (w20 << 33)) & mask;
  out[30] = (w20 >> 10) & mask;
  out[31] = ((w20 >> 53) | (w21 << 11)) & mask;

  return in;
}

inline const uint8_t* unpack44_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 17592186044415ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint64_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 8;
  uint64_t w21 = util::SafeLoadAs<uint64_t>(in);
  w21 = arrow::bit_util::FromLittleEndian(w21);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 44) | (w1 << 20)) & mask;
  out[2] = ((w1 >> 24) | (w2 << 40)) & mask;
  out[3] = (w2 >> 4) & mask;
  out[4] = ((w2 >> 48) | (w3 << 16)) & mask;
  out[5] = ((w3 >> 28) | (w4 << 36)) & mask;
  out[6] = (w4 >> 8) & mask;
  out[7] = ((w4 >> 52) | (w5 << 12)) & mask;
  out[8] = ((w5 >> 32) | (w6 << 32)) & mask;
  out[9] = (w6 >> 12) & mask;
  out[10] = ((w6 >> 56) | (w7 << 8)) & mask;
  out[11] = ((w7 >> 36) | (w8 << 28)) & mask;
  out[12] = (w8 >> 16) & mask;
  out[13] = ((w8 >> 60) | (w9 << 4)) & mask;
  out[14] = ((w9 >> 40) | (w10 << 24)) & mask;
  out[15] = w10 >> 20;
  out[16] = (w11)&mask;
  out[17] = ((w11 >> 44) | (w12 << 20)) & mask;
  out[18] = ((w12 >> 24) | (w13 << 40)) & mask;
  out[19] = (w13 >> 4) & mask;
  out[20] = ((w13 >> 48) | (w14 << 16)) & mask;
  out[21] = ((w14 >> 28) | (w15 << 36)) & mask;
  out[22] = (w15 >> 8) & mask;
  out[23] = ((w15 >> 52) | (w16 << 12)) & mask;
  out[24] = ((w16 >> 32) | (w17 << 32)) & mask;
  out[25] = (w17 >> 12) & mask;
  out[26] = ((w17 >> 56) | (w18 << 8)) & mask;
  out[27] = ((w18 >> 36) | (w19 << 28)) & mask;
  out[28] = (w19 >> 16) & mask;
  out[29] = ((w19 >> 60) | (w20 << 4)) & mask;
  out[30] = ((w20 >> 40) | (w21 << 24)) & mask;
  out[31] = w21 >> 20;

  return in;
}

inline const uint8_t* unpack45_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 35184372088831ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint64_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 8;
  uint64_t w21 = util::SafeLoadAs<uint64_t>(in);
  w21 = arrow::bit_util::FromLittleEndian(w21);
  in += 8;
  uint64_t w22 = util::SafeLoadAs<uint32_t>(in);
  w22 = arrow::bit_util::FromLittleEndian(w22);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 45) | (w1 << 19)) & mask;
  out[2] = ((w1 >> 26) | (w2 << 38)) & mask;
  out[3] = (w2 >> 7) & mask;
  out[4] = ((w2 >> 52) | (w3 << 12)) & mask;
  out[5] = ((w3 >> 33) | (w4 << 31)) & mask;
  out[6] = (w4 >> 14) & mask;
  out[7] = ((w4 >> 59) | (w5 << 5)) & mask;
  out[8] = ((w5 >> 40) | (w6 << 24)) & mask;
  out[9] = ((w6 >> 21) | (w7 << 43)) & mask;
  out[10] = (w7 >> 2) & mask;
  out[11] = ((w7 >> 47) | (w8 << 17)) & mask;
  out[12] = ((w8 >> 28) | (w9 << 36)) & mask;
  out[13] = (w9 >> 9) & mask;
  out[14] = ((w9 >> 54) | (w10 << 10)) & mask;
  out[15] = ((w10 >> 35) | (w11 << 29)) & mask;
  out[16] = (w11 >> 16) & mask;
  out[17] = ((w11 >> 61) | (w12 << 3)) & mask;
  out[18] = ((w12 >> 42) | (w13 << 22)) & mask;
  out[19] = ((w13 >> 23) | (w14 << 41)) & mask;
  out[20] = (w14 >> 4) & mask;
  out[21] = ((w14 >> 49) | (w15 << 15)) & mask;
  out[22] = ((w15 >> 30) | (w16 << 34)) & mask;
  out[23] = (w16 >> 11) & mask;
  out[24] = ((w16 >> 56) | (w17 << 8)) & mask;
  out[25] = ((w17 >> 37) | (w18 << 27)) & mask;
  out[26] = (w18 >> 18) & mask;
  out[27] = ((w18 >> 63) | (w19 << 1)) & mask;
  out[28] = ((w19 >> 44) | (w20 << 20)) & mask;
  out[29] = ((w20 >> 25) | (w21 << 39)) & mask;
  out[30] = (w21 >> 6) & mask;
  out[31] = ((w21 >> 51) | (w22 << 13)) & mask;

  return in;
}

inline const uint8_t* unpack46_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 70368744177663ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint64_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 8;
  uint64_t w21 = util::SafeLoadAs<uint64_t>(in);
  w21 = arrow::bit_util::FromLittleEndian(w21);
  in += 8;
  uint64_t w22 = util::SafeLoadAs<uint64_t>(in);
  w22 = arrow::bit_util::FromLittleEndian(w22);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 46) | (w1 << 18)) & mask;
  out[2] = ((w1 >> 28) | (w2 << 36)) & mask;
  out[3] = (w2 >> 10) & mask;
  out[4] = ((w2 >> 56) | (w3 << 8)) & mask;
  out[5] = ((w3 >> 38) | (w4 << 26)) & mask;
  out[6] = ((w4 >> 20) | (w5 << 44)) & mask;
  out[7] = (w5 >> 2) & mask;
  out[8] = ((w5 >> 48) | (w6 << 16)) & mask;
  out[9] = ((w6 >> 30) | (w7 << 34)) & mask;
  out[10] = (w7 >> 12) & mask;
  out[11] = ((w7 >> 58) | (w8 << 6)) & mask;
  out[12] = ((w8 >> 40) | (w9 << 24)) & mask;
  out[13] = ((w9 >> 22) | (w10 << 42)) & mask;
  out[14] = (w10 >> 4) & mask;
  out[15] = ((w10 >> 50) | (w11 << 14)) & mask;
  out[16] = ((w11 >> 32) | (w12 << 32)) & mask;
  out[17] = (w12 >> 14) & mask;
  out[18] = ((w12 >> 60) | (w13 << 4)) & mask;
  out[19] = ((w13 >> 42) | (w14 << 22)) & mask;
  out[20] = ((w14 >> 24) | (w15 << 40)) & mask;
  out[21] = (w15 >> 6) & mask;
  out[22] = ((w15 >> 52) | (w16 << 12)) & mask;
  out[23] = ((w16 >> 34) | (w17 << 30)) & mask;
  out[24] = (w17 >> 16) & mask;
  out[25] = ((w17 >> 62) | (w18 << 2)) & mask;
  out[26] = ((w18 >> 44) | (w19 << 20)) & mask;
  out[27] = ((w19 >> 26) | (w20 << 38)) & mask;
  out[28] = (w20 >> 8) & mask;
  out[29] = ((w20 >> 54) | (w21 << 10)) & mask;
  out[30] = ((w21 >> 36) | (w22 << 28)) & mask;
  out[31] = w22 >> 18;

  return in;
}

inline const uint8_t* unpack47_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 140737488355327ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint64_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 8;
  uint64_t w21 = util::SafeLoadAs<uint64_t>(in);
  w21 = arrow::bit_util::FromLittleEndian(w21);
  in += 8;
  uint64_t w22 = util::SafeLoadAs<uint64_t>(in);
  w22 = arrow::bit_util::FromLittleEndian(w22);
  in += 8;
  uint64_t w23 = util::SafeLoadAs<uint32_t>(in);
  w23 = arrow::bit_util::FromLittleEndian(w23);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 47) | (w1 << 17)) & mask;
  out[2] = ((w1 >> 30) | (w2 << 34)) & mask;
  out[3] = (w2 >> 13) & mask;
  out[4] = ((w2 >> 60) | (w3 << 4)) & mask;
  out[5] = ((w3 >> 43) | (w4 << 21)) & mask;
  out[6] = ((w4 >> 26) | (w5 << 38)) & mask;
  out[7] = (w5 >> 9) & mask;
  out[8] = ((w5 >> 56) | (w6 << 8)) & mask;
  out[9] = ((w6 >> 39) | (w7 << 25)) & mask;
  out[10] = ((w7 >> 22) | (w8 << 42)) & mask;
  out[11] = (w8 >> 5) & mask;
  out[12] = ((w8 >> 52) | (w9 << 12)) & mask;
  out[13] = ((w9 >> 35) | (w10 << 29)) & mask;
  out[14] = ((w10 >> 18) | (w11 << 46)) & mask;
  out[15] = (w11 >> 1) & mask;
  out[16] = ((w11 >> 48) | (w12 << 16)) & mask;
  out[17] = ((w12 >> 31) | (w13 << 33)) & mask;
  out[18] = (w13 >> 14) & mask;
  out[19] = ((w13 >> 61) | (w14 << 3)) & mask;
  out[20] = ((w14 >> 44) | (w15 << 20)) & mask;
  out[21] = ((w15 >> 27) | (w16 << 37)) & mask;
  out[22] = (w16 >> 10) & mask;
  out[23] = ((w16 >> 57) | (w17 << 7)) & mask;
  out[24] = ((w17 >> 40) | (w18 << 24)) & mask;
  out[25] = ((w18 >> 23) | (w19 << 41)) & mask;
  out[26] = (w19 >> 6) & mask;
  out[27] = ((w19 >> 53) | (w20 << 11)) & mask;
  out[28] = ((w20 >> 36) | (w21 << 28)) & mask;
  out[29] = ((w21 >> 19) | (w22 << 45)) & mask;
  out[30] = (w22 >> 2) & mask;
  out[31] = ((w22 >> 49) | (w23 << 15)) & mask;

  return in;
}

inline const uint8_t* unpack48_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 281474976710655ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint64_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 8;
  uint64_t w21 = util::SafeLoadAs<uint64_t>(in);
  w21 = arrow::bit_util::FromLittleEndian(w21);
  in += 8;
  uint64_t w22 = util::SafeLoadAs<uint64_t>(in);
  w22 = arrow::bit_util::FromLittleEndian(w22);
  in += 8;
  uint64_t w23 = util::SafeLoadAs<uint64_t>(in);
  w23 = arrow::bit_util::FromLittleEndian(w23);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 48) | (w1 << 16)) & mask;
  out[2] = ((w1 >> 32) | (w2 << 32)) & mask;
  out[3] = w2 >> 16;
  out[4] = (w3)&mask;
  out[5] = ((w3 >> 48) | (w4 << 16)) & mask;
  out[6] = ((w4 >> 32) | (w5 << 32)) & mask;
  out[7] = w5 >> 16;
  out[8] = (w6)&mask;
  out[9] = ((w6 >> 48) | (w7 << 16)) & mask;
  out[10] = ((w7 >> 32) | (w8 << 32)) & mask;
  out[11] = w8 >> 16;
  out[12] = (w9)&mask;
  out[13] = ((w9 >> 48) | (w10 << 16)) & mask;
  out[14] = ((w10 >> 32) | (w11 << 32)) & mask;
  out[15] = w11 >> 16;
  out[16] = (w12)&mask;
  out[17] = ((w12 >> 48) | (w13 << 16)) & mask;
  out[18] = ((w13 >> 32) | (w14 << 32)) & mask;
  out[19] = w14 >> 16;
  out[20] = (w15)&mask;
  out[21] = ((w15 >> 48) | (w16 << 16)) & mask;
  out[22] = ((w16 >> 32) | (w17 << 32)) & mask;
  out[23] = w17 >> 16;
  out[24] = (w18)&mask;
  out[25] = ((w18 >> 48) | (w19 << 16)) & mask;
  out[26] = ((w19 >> 32) | (w20 << 32)) & mask;
  out[27] = w20 >> 16;
  out[28] = (w21)&mask;
  out[29] = ((w21 >> 48) | (w22 << 16)) & mask;
  out[30] = ((w22 >> 32) | (w23 << 32)) & mask;
  out[31] = w23 >> 16;

  return in;
}

inline const uint8_t* unpack49_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 562949953421311ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint64_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 8;
  uint64_t w21 = util::SafeLoadAs<uint64_t>(in);
  w21 = arrow::bit_util::FromLittleEndian(w21);
  in += 8;
  uint64_t w22 = util::SafeLoadAs<uint64_t>(in);
  w22 = arrow::bit_util::FromLittleEndian(w22);
  in += 8;
  uint64_t w23 = util::SafeLoadAs<uint64_t>(in);
  w23 = arrow::bit_util::FromLittleEndian(w23);
  in += 8;
  uint64_t w24 = util::SafeLoadAs<uint32_t>(in);
  w24 = arrow::bit_util::FromLittleEndian(w24);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 49) | (w1 << 15)) & mask;
  out[2] = ((w1 >> 34) | (w2 << 30)) & mask;
  out[3] = ((w2 >> 19) | (w3 << 45)) & mask;
  out[4] = (w3 >> 4) & mask;
  out[5] = ((w3 >> 53) | (w4 << 11)) & mask;
  out[6] = ((w4 >> 38) | (w5 << 26)) & mask;
  out[7] = ((w5 >> 23) | (w6 << 41)) & mask;
  out[8] = (w6 >> 8) & mask;
  out[9] = ((w6 >> 57) | (w7 << 7)) & mask;
  out[10] = ((w7 >> 42) | (w8 << 22)) & mask;
  out[11] = ((w8 >> 27) | (w9 << 37)) & mask;
  out[12] = (w9 >> 12) & mask;
  out[13] = ((w9 >> 61) | (w10 << 3)) & mask;
  out[14] = ((w10 >> 46) | (w11 << 18)) & mask;
  out[15] = ((w11 >> 31) | (w12 << 33)) & mask;
  out[16] = ((w12 >> 16) | (w13 << 48)) & mask;
  out[17] = (w13 >> 1) & mask;
  out[18] = ((w13 >> 50) | (w14 << 14)) & mask;
  out[19] = ((w14 >> 35) | (w15 << 29)) & mask;
  out[20] = ((w15 >> 20) | (w16 << 44)) & mask;
  out[21] = (w16 >> 5) & mask;
  out[22] = ((w16 >> 54) | (w17 << 10)) & mask;
  out[23] = ((w17 >> 39) | (w18 << 25)) & mask;
  out[24] = ((w18 >> 24) | (w19 << 40)) & mask;
  out[25] = (w19 >> 9) & mask;
  out[26] = ((w19 >> 58) | (w20 << 6)) & mask;
  out[27] = ((w20 >> 43) | (w21 << 21)) & mask;
  out[28] = ((w21 >> 28) | (w22 << 36)) & mask;
  out[29] = (w22 >> 13) & mask;
  out[30] = ((w22 >> 62) | (w23 << 2)) & mask;
  out[31] = ((w23 >> 47) | (w24 << 17)) & mask;

  return in;
}

inline const uint8_t* unpack50_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 1125899906842623ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint64_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 8;
  uint64_t w21 = util::SafeLoadAs<uint64_t>(in);
  w21 = arrow::bit_util::FromLittleEndian(w21);
  in += 8;
  uint64_t w22 = util::SafeLoadAs<uint64_t>(in);
  w22 = arrow::bit_util::FromLittleEndian(w22);
  in += 8;
  uint64_t w23 = util::SafeLoadAs<uint64_t>(in);
  w23 = arrow::bit_util::FromLittleEndian(w23);
  in += 8;
  uint64_t w24 = util::SafeLoadAs<uint64_t>(in);
  w24 = arrow::bit_util::FromLittleEndian(w24);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 50) | (w1 << 14)) & mask;
  out[2] = ((w1 >> 36) | (w2 << 28)) & mask;
  out[3] = ((w2 >> 22) | (w3 << 42)) & mask;
  out[4] = (w3 >> 8) & mask;
  out[5] = ((w3 >> 58) | (w4 << 6)) & mask;
  out[6] = ((w4 >> 44) | (w5 << 20)) & mask;
  out[7] = ((w5 >> 30) | (w6 << 34)) & mask;
  out[8] = ((w6 >> 16) | (w7 << 48)) & mask;
  out[9] = (w7 >> 2) & mask;
  out[10] = ((w7 >> 52) | (w8 << 12)) & mask;
  out[11] = ((w8 >> 38) | (w9 << 26)) & mask;
  out[12] = ((w9 >> 24) | (w10 << 40)) & mask;
  out[13] = (w10 >> 10) & mask;
  out[14] = ((w10 >> 60) | (w11 << 4)) & mask;
  out[15] = ((w11 >> 46) | (w12 << 18)) & mask;
  out[16] = ((w12 >> 32) | (w13 << 32)) & mask;
  out[17] = ((w13 >> 18) | (w14 << 46)) & mask;
  out[18] = (w14 >> 4) & mask;
  out[19] = ((w14 >> 54) | (w15 << 10)) & mask;
  out[20] = ((w15 >> 40) | (w16 << 24)) & mask;
  out[21] = ((w16 >> 26) | (w17 << 38)) & mask;
  out[22] = (w17 >> 12) & mask;
  out[23] = ((w17 >> 62) | (w18 << 2)) & mask;
  out[24] = ((w18 >> 48) | (w19 << 16)) & mask;
  out[25] = ((w19 >> 34) | (w20 << 30)) & mask;
  out[26] = ((w20 >> 20) | (w21 << 44)) & mask;
  out[27] = (w21 >> 6) & mask;
  out[28] = ((w21 >> 56) | (w22 << 8)) & mask;
  out[29] = ((w22 >> 42) | (w23 << 22)) & mask;
  out[30] = ((w23 >> 28) | (w24 << 36)) & mask;
  out[31] = w24 >> 14;

  return in;
}

inline const uint8_t* unpack51_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 2251799813685247ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint64_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 8;
  uint64_t w21 = util::SafeLoadAs<uint64_t>(in);
  w21 = arrow::bit_util::FromLittleEndian(w21);
  in += 8;
  uint64_t w22 = util::SafeLoadAs<uint64_t>(in);
  w22 = arrow::bit_util::FromLittleEndian(w22);
  in += 8;
  uint64_t w23 = util::SafeLoadAs<uint64_t>(in);
  w23 = arrow::bit_util::FromLittleEndian(w23);
  in += 8;
  uint64_t w24 = util::SafeLoadAs<uint64_t>(in);
  w24 = arrow::bit_util::FromLittleEndian(w24);
  in += 8;
  uint64_t w25 = util::SafeLoadAs<uint32_t>(in);
  w25 = arrow::bit_util::FromLittleEndian(w25);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 51) | (w1 << 13)) & mask;
  out[2] = ((w1 >> 38) | (w2 << 26)) & mask;
  out[3] = ((w2 >> 25) | (w3 << 39)) & mask;
  out[4] = (w3 >> 12) & mask;
  out[5] = ((w3 >> 63) | (w4 << 1)) & mask;
  out[6] = ((w4 >> 50) | (w5 << 14)) & mask;
  out[7] = ((w5 >> 37) | (w6 << 27)) & mask;
  out[8] = ((w6 >> 24) | (w7 << 40)) & mask;
  out[9] = (w7 >> 11) & mask;
  out[10] = ((w7 >> 62) | (w8 << 2)) & mask;
  out[11] = ((w8 >> 49) | (w9 << 15)) & mask;
  out[12] = ((w9 >> 36) | (w10 << 28)) & mask;
  out[13] = ((w10 >> 23) | (w11 << 41)) & mask;
  out[14] = (w11 >> 10) & mask;
  out[15] = ((w11 >> 61) | (w12 << 3)) & mask;
  out[16] = ((w12 >> 48) | (w13 << 16)) & mask;
  out[17] = ((w13 >> 35) | (w14 << 29)) & mask;
  out[18] = ((w14 >> 22) | (w15 << 42)) & mask;
  out[19] = (w15 >> 9) & mask;
  out[20] = ((w15 >> 60) | (w16 << 4)) & mask;
  out[21] = ((w16 >> 47) | (w17 << 17)) & mask;
  out[22] = ((w17 >> 34) | (w18 << 30)) & mask;
  out[23] = ((w18 >> 21) | (w19 << 43)) & mask;
  out[24] = (w19 >> 8) & mask;
  out[25] = ((w19 >> 59) | (w20 << 5)) & mask;
  out[26] = ((w20 >> 46) | (w21 << 18)) & mask;
  out[27] = ((w21 >> 33) | (w22 << 31)) & mask;
  out[28] = ((w22 >> 20) | (w23 << 44)) & mask;
  out[29] = (w23 >> 7) & mask;
  out[30] = ((w23 >> 58) | (w24 << 6)) & mask;
  out[31] = ((w24 >> 45) | (w25 << 19)) & mask;

  return in;
}

inline const uint8_t* unpack52_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 4503599627370495ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint64_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 8;
  uint64_t w21 = util::SafeLoadAs<uint64_t>(in);
  w21 = arrow::bit_util::FromLittleEndian(w21);
  in += 8;
  uint64_t w22 = util::SafeLoadAs<uint64_t>(in);
  w22 = arrow::bit_util::FromLittleEndian(w22);
  in += 8;
  uint64_t w23 = util::SafeLoadAs<uint64_t>(in);
  w23 = arrow::bit_util::FromLittleEndian(w23);
  in += 8;
  uint64_t w24 = util::SafeLoadAs<uint64_t>(in);
  w24 = arrow::bit_util::FromLittleEndian(w24);
  in += 8;
  uint64_t w25 = util::SafeLoadAs<uint64_t>(in);
  w25 = arrow::bit_util::FromLittleEndian(w25);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 52) | (w1 << 12)) & mask;
  out[2] = ((w1 >> 40) | (w2 << 24)) & mask;
  out[3] = ((w2 >> 28) | (w3 << 36)) & mask;
  out[4] = ((w3 >> 16) | (w4 << 48)) & mask;
  out[5] = (w4 >> 4) & mask;
  out[6] = ((w4 >> 56) | (w5 << 8)) & mask;
  out[7] = ((w5 >> 44) | (w6 << 20)) & mask;
  out[8] = ((w6 >> 32) | (w7 << 32)) & mask;
  out[9] = ((w7 >> 20) | (w8 << 44)) & mask;
  out[10] = (w8 >> 8) & mask;
  out[11] = ((w8 >> 60) | (w9 << 4)) & mask;
  out[12] = ((w9 >> 48) | (w10 << 16)) & mask;
  out[13] = ((w10 >> 36) | (w11 << 28)) & mask;
  out[14] = ((w11 >> 24) | (w12 << 40)) & mask;
  out[15] = w12 >> 12;
  out[16] = (w13)&mask;
  out[17] = ((w13 >> 52) | (w14 << 12)) & mask;
  out[18] = ((w14 >> 40) | (w15 << 24)) & mask;
  out[19] = ((w15 >> 28) | (w16 << 36)) & mask;
  out[20] = ((w16 >> 16) | (w17 << 48)) & mask;
  out[21] = (w17 >> 4) & mask;
  out[22] = ((w17 >> 56) | (w18 << 8)) & mask;
  out[23] = ((w18 >> 44) | (w19 << 20)) & mask;
  out[24] = ((w19 >> 32) | (w20 << 32)) & mask;
  out[25] = ((w20 >> 20) | (w21 << 44)) & mask;
  out[26] = (w21 >> 8) & mask;
  out[27] = ((w21 >> 60) | (w22 << 4)) & mask;
  out[28] = ((w22 >> 48) | (w23 << 16)) & mask;
  out[29] = ((w23 >> 36) | (w24 << 28)) & mask;
  out[30] = ((w24 >> 24) | (w25 << 40)) & mask;
  out[31] = w25 >> 12;

  return in;
}

inline const uint8_t* unpack53_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 9007199254740991ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint64_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 8;
  uint64_t w21 = util::SafeLoadAs<uint64_t>(in);
  w21 = arrow::bit_util::FromLittleEndian(w21);
  in += 8;
  uint64_t w22 = util::SafeLoadAs<uint64_t>(in);
  w22 = arrow::bit_util::FromLittleEndian(w22);
  in += 8;
  uint64_t w23 = util::SafeLoadAs<uint64_t>(in);
  w23 = arrow::bit_util::FromLittleEndian(w23);
  in += 8;
  uint64_t w24 = util::SafeLoadAs<uint64_t>(in);
  w24 = arrow::bit_util::FromLittleEndian(w24);
  in += 8;
  uint64_t w25 = util::SafeLoadAs<uint64_t>(in);
  w25 = arrow::bit_util::FromLittleEndian(w25);
  in += 8;
  uint64_t w26 = util::SafeLoadAs<uint32_t>(in);
  w26 = arrow::bit_util::FromLittleEndian(w26);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 53) | (w1 << 11)) & mask;
  out[2] = ((w1 >> 42) | (w2 << 22)) & mask;
  out[3] = ((w2 >> 31) | (w3 << 33)) & mask;
  out[4] = ((w3 >> 20) | (w4 << 44)) & mask;
  out[5] = (w4 >> 9) & mask;
  out[6] = ((w4 >> 62) | (w5 << 2)) & mask;
  out[7] = ((w5 >> 51) | (w6 << 13)) & mask;
  out[8] = ((w6 >> 40) | (w7 << 24)) & mask;
  out[9] = ((w7 >> 29) | (w8 << 35)) & mask;
  out[10] = ((w8 >> 18) | (w9 << 46)) & mask;
  out[11] = (w9 >> 7) & mask;
  out[12] = ((w9 >> 60) | (w10 << 4)) & mask;
  out[13] = ((w10 >> 49) | (w11 << 15)) & mask;
  out[14] = ((w11 >> 38) | (w12 << 26)) & mask;
  out[15] = ((w12 >> 27) | (w13 << 37)) & mask;
  out[16] = ((w13 >> 16) | (w14 << 48)) & mask;
  out[17] = (w14 >> 5) & mask;
  out[18] = ((w14 >> 58) | (w15 << 6)) & mask;
  out[19] = ((w15 >> 47) | (w16 << 17)) & mask;
  out[20] = ((w16 >> 36) | (w17 << 28)) & mask;
  out[21] = ((w17 >> 25) | (w18 << 39)) & mask;
  out[22] = ((w18 >> 14) | (w19 << 50)) & mask;
  out[23] = (w19 >> 3) & mask;
  out[24] = ((w19 >> 56) | (w20 << 8)) & mask;
  out[25] = ((w20 >> 45) | (w21 << 19)) & mask;
  out[26] = ((w21 >> 34) | (w22 << 30)) & mask;
  out[27] = ((w22 >> 23) | (w23 << 41)) & mask;
  out[28] = ((w23 >> 12) | (w24 << 52)) & mask;
  out[29] = (w24 >> 1) & mask;
  out[30] = ((w24 >> 54) | (w25 << 10)) & mask;
  out[31] = ((w25 >> 43) | (w26 << 21)) & mask;

  return in;
}

inline const uint8_t* unpack54_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 18014398509481983ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint64_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 8;
  uint64_t w21 = util::SafeLoadAs<uint64_t>(in);
  w21 = arrow::bit_util::FromLittleEndian(w21);
  in += 8;
  uint64_t w22 = util::SafeLoadAs<uint64_t>(in);
  w22 = arrow::bit_util::FromLittleEndian(w22);
  in += 8;
  uint64_t w23 = util::SafeLoadAs<uint64_t>(in);
  w23 = arrow::bit_util::FromLittleEndian(w23);
  in += 8;
  uint64_t w24 = util::SafeLoadAs<uint64_t>(in);
  w24 = arrow::bit_util::FromLittleEndian(w24);
  in += 8;
  uint64_t w25 = util::SafeLoadAs<uint64_t>(in);
  w25 = arrow::bit_util::FromLittleEndian(w25);
  in += 8;
  uint64_t w26 = util::SafeLoadAs<uint64_t>(in);
  w26 = arrow::bit_util::FromLittleEndian(w26);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 54) | (w1 << 10)) & mask;
  out[2] = ((w1 >> 44) | (w2 << 20)) & mask;
  out[3] = ((w2 >> 34) | (w3 << 30)) & mask;
  out[4] = ((w3 >> 24) | (w4 << 40)) & mask;
  out[5] = ((w4 >> 14) | (w5 << 50)) & mask;
  out[6] = (w5 >> 4) & mask;
  out[7] = ((w5 >> 58) | (w6 << 6)) & mask;
  out[8] = ((w6 >> 48) | (w7 << 16)) & mask;
  out[9] = ((w7 >> 38) | (w8 << 26)) & mask;
  out[10] = ((w8 >> 28) | (w9 << 36)) & mask;
  out[11] = ((w9 >> 18) | (w10 << 46)) & mask;
  out[12] = (w10 >> 8) & mask;
  out[13] = ((w10 >> 62) | (w11 << 2)) & mask;
  out[14] = ((w11 >> 52) | (w12 << 12)) & mask;
  out[15] = ((w12 >> 42) | (w13 << 22)) & mask;
  out[16] = ((w13 >> 32) | (w14 << 32)) & mask;
  out[17] = ((w14 >> 22) | (w15 << 42)) & mask;
  out[18] = ((w15 >> 12) | (w16 << 52)) & mask;
  out[19] = (w16 >> 2) & mask;
  out[20] = ((w16 >> 56) | (w17 << 8)) & mask;
  out[21] = ((w17 >> 46) | (w18 << 18)) & mask;
  out[22] = ((w18 >> 36) | (w19 << 28)) & mask;
  out[23] = ((w19 >> 26) | (w20 << 38)) & mask;
  out[24] = ((w20 >> 16) | (w21 << 48)) & mask;
  out[25] = (w21 >> 6) & mask;
  out[26] = ((w21 >> 60) | (w22 << 4)) & mask;
  out[27] = ((w22 >> 50) | (w23 << 14)) & mask;
  out[28] = ((w23 >> 40) | (w24 << 24)) & mask;
  out[29] = ((w24 >> 30) | (w25 << 34)) & mask;
  out[30] = ((w25 >> 20) | (w26 << 44)) & mask;
  out[31] = w26 >> 10;

  return in;
}

inline const uint8_t* unpack55_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 36028797018963967ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint64_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 8;
  uint64_t w21 = util::SafeLoadAs<uint64_t>(in);
  w21 = arrow::bit_util::FromLittleEndian(w21);
  in += 8;
  uint64_t w22 = util::SafeLoadAs<uint64_t>(in);
  w22 = arrow::bit_util::FromLittleEndian(w22);
  in += 8;
  uint64_t w23 = util::SafeLoadAs<uint64_t>(in);
  w23 = arrow::bit_util::FromLittleEndian(w23);
  in += 8;
  uint64_t w24 = util::SafeLoadAs<uint64_t>(in);
  w24 = arrow::bit_util::FromLittleEndian(w24);
  in += 8;
  uint64_t w25 = util::SafeLoadAs<uint64_t>(in);
  w25 = arrow::bit_util::FromLittleEndian(w25);
  in += 8;
  uint64_t w26 = util::SafeLoadAs<uint64_t>(in);
  w26 = arrow::bit_util::FromLittleEndian(w26);
  in += 8;
  uint64_t w27 = util::SafeLoadAs<uint32_t>(in);
  w27 = arrow::bit_util::FromLittleEndian(w27);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 55) | (w1 << 9)) & mask;
  out[2] = ((w1 >> 46) | (w2 << 18)) & mask;
  out[3] = ((w2 >> 37) | (w3 << 27)) & mask;
  out[4] = ((w3 >> 28) | (w4 << 36)) & mask;
  out[5] = ((w4 >> 19) | (w5 << 45)) & mask;
  out[6] = ((w5 >> 10) | (w6 << 54)) & mask;
  out[7] = (w6 >> 1) & mask;
  out[8] = ((w6 >> 56) | (w7 << 8)) & mask;
  out[9] = ((w7 >> 47) | (w8 << 17)) & mask;
  out[10] = ((w8 >> 38) | (w9 << 26)) & mask;
  out[11] = ((w9 >> 29) | (w10 << 35)) & mask;
  out[12] = ((w10 >> 20) | (w11 << 44)) & mask;
  out[13] = ((w11 >> 11) | (w12 << 53)) & mask;
  out[14] = (w12 >> 2) & mask;
  out[15] = ((w12 >> 57) | (w13 << 7)) & mask;
  out[16] = ((w13 >> 48) | (w14 << 16)) & mask;
  out[17] = ((w14 >> 39) | (w15 << 25)) & mask;
  out[18] = ((w15 >> 30) | (w16 << 34)) & mask;
  out[19] = ((w16 >> 21) | (w17 << 43)) & mask;
  out[20] = ((w17 >> 12) | (w18 << 52)) & mask;
  out[21] = (w18 >> 3) & mask;
  out[22] = ((w18 >> 58) | (w19 << 6)) & mask;
  out[23] = ((w19 >> 49) | (w20 << 15)) & mask;
  out[24] = ((w20 >> 40) | (w21 << 24)) & mask;
  out[25] = ((w21 >> 31) | (w22 << 33)) & mask;
  out[26] = ((w22 >> 22) | (w23 << 42)) & mask;
  out[27] = ((w23 >> 13) | (w24 << 51)) & mask;
  out[28] = (w24 >> 4) & mask;
  out[29] = ((w24 >> 59) | (w25 << 5)) & mask;
  out[30] = ((w25 >> 50) | (w26 << 14)) & mask;
  out[31] = ((w26 >> 41) | (w27 << 23)) & mask;

  return in;
}

inline const uint8_t* unpack56_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 72057594037927935ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint64_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 8;
  uint64_t w21 = util::SafeLoadAs<uint64_t>(in);
  w21 = arrow::bit_util::FromLittleEndian(w21);
  in += 8;
  uint64_t w22 = util::SafeLoadAs<uint64_t>(in);
  w22 = arrow::bit_util::FromLittleEndian(w22);
  in += 8;
  uint64_t w23 = util::SafeLoadAs<uint64_t>(in);
  w23 = arrow::bit_util::FromLittleEndian(w23);
  in += 8;
  uint64_t w24 = util::SafeLoadAs<uint64_t>(in);
  w24 = arrow::bit_util::FromLittleEndian(w24);
  in += 8;
  uint64_t w25 = util::SafeLoadAs<uint64_t>(in);
  w25 = arrow::bit_util::FromLittleEndian(w25);
  in += 8;
  uint64_t w26 = util::SafeLoadAs<uint64_t>(in);
  w26 = arrow::bit_util::FromLittleEndian(w26);
  in += 8;
  uint64_t w27 = util::SafeLoadAs<uint64_t>(in);
  w27 = arrow::bit_util::FromLittleEndian(w27);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 56) | (w1 << 8)) & mask;
  out[2] = ((w1 >> 48) | (w2 << 16)) & mask;
  out[3] = ((w2 >> 40) | (w3 << 24)) & mask;
  out[4] = ((w3 >> 32) | (w4 << 32)) & mask;
  out[5] = ((w4 >> 24) | (w5 << 40)) & mask;
  out[6] = ((w5 >> 16) | (w6 << 48)) & mask;
  out[7] = w6 >> 8;
  out[8] = (w7)&mask;
  out[9] = ((w7 >> 56) | (w8 << 8)) & mask;
  out[10] = ((w8 >> 48) | (w9 << 16)) & mask;
  out[11] = ((w9 >> 40) | (w10 << 24)) & mask;
  out[12] = ((w10 >> 32) | (w11 << 32)) & mask;
  out[13] = ((w11 >> 24) | (w12 << 40)) & mask;
  out[14] = ((w12 >> 16) | (w13 << 48)) & mask;
  out[15] = w13 >> 8;
  out[16] = (w14)&mask;
  out[17] = ((w14 >> 56) | (w15 << 8)) & mask;
  out[18] = ((w15 >> 48) | (w16 << 16)) & mask;
  out[19] = ((w16 >> 40) | (w17 << 24)) & mask;
  out[20] = ((w17 >> 32) | (w18 << 32)) & mask;
  out[21] = ((w18 >> 24) | (w19 << 40)) & mask;
  out[22] = ((w19 >> 16) | (w20 << 48)) & mask;
  out[23] = w20 >> 8;
  out[24] = (w21)&mask;
  out[25] = ((w21 >> 56) | (w22 << 8)) & mask;
  out[26] = ((w22 >> 48) | (w23 << 16)) & mask;
  out[27] = ((w23 >> 40) | (w24 << 24)) & mask;
  out[28] = ((w24 >> 32) | (w25 << 32)) & mask;
  out[29] = ((w25 >> 24) | (w26 << 40)) & mask;
  out[30] = ((w26 >> 16) | (w27 << 48)) & mask;
  out[31] = w27 >> 8;

  return in;
}

inline const uint8_t* unpack57_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 144115188075855871ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint64_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 8;
  uint64_t w21 = util::SafeLoadAs<uint64_t>(in);
  w21 = arrow::bit_util::FromLittleEndian(w21);
  in += 8;
  uint64_t w22 = util::SafeLoadAs<uint64_t>(in);
  w22 = arrow::bit_util::FromLittleEndian(w22);
  in += 8;
  uint64_t w23 = util::SafeLoadAs<uint64_t>(in);
  w23 = arrow::bit_util::FromLittleEndian(w23);
  in += 8;
  uint64_t w24 = util::SafeLoadAs<uint64_t>(in);
  w24 = arrow::bit_util::FromLittleEndian(w24);
  in += 8;
  uint64_t w25 = util::SafeLoadAs<uint64_t>(in);
  w25 = arrow::bit_util::FromLittleEndian(w25);
  in += 8;
  uint64_t w26 = util::SafeLoadAs<uint64_t>(in);
  w26 = arrow::bit_util::FromLittleEndian(w26);
  in += 8;
  uint64_t w27 = util::SafeLoadAs<uint64_t>(in);
  w27 = arrow::bit_util::FromLittleEndian(w27);
  in += 8;
  uint64_t w28 = util::SafeLoadAs<uint32_t>(in);
  w28 = arrow::bit_util::FromLittleEndian(w28);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 57) | (w1 << 7)) & mask;
  out[2] = ((w1 >> 50) | (w2 << 14)) & mask;
  out[3] = ((w2 >> 43) | (w3 << 21)) & mask;
  out[4] = ((w3 >> 36) | (w4 << 28)) & mask;
  out[5] = ((w4 >> 29) | (w5 << 35)) & mask;
  out[6] = ((w5 >> 22) | (w6 << 42)) & mask;
  out[7] = ((w6 >> 15) | (w7 << 49)) & mask;
  out[8] = ((w7 >> 8) | (w8 << 56)) & mask;
  out[9] = (w8 >> 1) & mask;
  out[10] = ((w8 >> 58) | (w9 << 6)) & mask;
  out[11] = ((w9 >> 51) | (w10 << 13)) & mask;
  out[12] = ((w10 >> 44) | (w11 << 20)) & mask;
  out[13] = ((w11 >> 37) | (w12 << 27)) & mask;
  out[14] = ((w12 >> 30) | (w13 << 34)) & mask;
  out[15] = ((w13 >> 23) | (w14 << 41)) & mask;
  out[16] = ((w14 >> 16) | (w15 << 48)) & mask;
  out[17] = ((w15 >> 9) | (w16 << 55)) & mask;
  out[18] = (w16 >> 2) & mask;
  out[19] = ((w16 >> 59) | (w17 << 5)) & mask;
  out[20] = ((w17 >> 52) | (w18 << 12)) & mask;
  out[21] = ((w18 >> 45) | (w19 << 19)) & mask;
  out[22] = ((w19 >> 38) | (w20 << 26)) & mask;
  out[23] = ((w20 >> 31) | (w21 << 33)) & mask;
  out[24] = ((w21 >> 24) | (w22 << 40)) & mask;
  out[25] = ((w22 >> 17) | (w23 << 47)) & mask;
  out[26] = ((w23 >> 10) | (w24 << 54)) & mask;
  out[27] = (w24 >> 3) & mask;
  out[28] = ((w24 >> 60) | (w25 << 4)) & mask;
  out[29] = ((w25 >> 53) | (w26 << 11)) & mask;
  out[30] = ((w26 >> 46) | (w27 << 18)) & mask;
  out[31] = ((w27 >> 39) | (w28 << 25)) & mask;

  return in;
}

inline const uint8_t* unpack58_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 288230376151711743ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint64_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 8;
  uint64_t w21 = util::SafeLoadAs<uint64_t>(in);
  w21 = arrow::bit_util::FromLittleEndian(w21);
  in += 8;
  uint64_t w22 = util::SafeLoadAs<uint64_t>(in);
  w22 = arrow::bit_util::FromLittleEndian(w22);
  in += 8;
  uint64_t w23 = util::SafeLoadAs<uint64_t>(in);
  w23 = arrow::bit_util::FromLittleEndian(w23);
  in += 8;
  uint64_t w24 = util::SafeLoadAs<uint64_t>(in);
  w24 = arrow::bit_util::FromLittleEndian(w24);
  in += 8;
  uint64_t w25 = util::SafeLoadAs<uint64_t>(in);
  w25 = arrow::bit_util::FromLittleEndian(w25);
  in += 8;
  uint64_t w26 = util::SafeLoadAs<uint64_t>(in);
  w26 = arrow::bit_util::FromLittleEndian(w26);
  in += 8;
  uint64_t w27 = util::SafeLoadAs<uint64_t>(in);
  w27 = arrow::bit_util::FromLittleEndian(w27);
  in += 8;
  uint64_t w28 = util::SafeLoadAs<uint64_t>(in);
  w28 = arrow::bit_util::FromLittleEndian(w28);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 58) | (w1 << 6)) & mask;
  out[2] = ((w1 >> 52) | (w2 << 12)) & mask;
  out[3] = ((w2 >> 46) | (w3 << 18)) & mask;
  out[4] = ((w3 >> 40) | (w4 << 24)) & mask;
  out[5] = ((w4 >> 34) | (w5 << 30)) & mask;
  out[6] = ((w5 >> 28) | (w6 << 36)) & mask;
  out[7] = ((w6 >> 22) | (w7 << 42)) & mask;
  out[8] = ((w7 >> 16) | (w8 << 48)) & mask;
  out[9] = ((w8 >> 10) | (w9 << 54)) & mask;
  out[10] = (w9 >> 4) & mask;
  out[11] = ((w9 >> 62) | (w10 << 2)) & mask;
  out[12] = ((w10 >> 56) | (w11 << 8)) & mask;
  out[13] = ((w11 >> 50) | (w12 << 14)) & mask;
  out[14] = ((w12 >> 44) | (w13 << 20)) & mask;
  out[15] = ((w13 >> 38) | (w14 << 26)) & mask;
  out[16] = ((w14 >> 32) | (w15 << 32)) & mask;
  out[17] = ((w15 >> 26) | (w16 << 38)) & mask;
  out[18] = ((w16 >> 20) | (w17 << 44)) & mask;
  out[19] = ((w17 >> 14) | (w18 << 50)) & mask;
  out[20] = ((w18 >> 8) | (w19 << 56)) & mask;
  out[21] = (w19 >> 2) & mask;
  out[22] = ((w19 >> 60) | (w20 << 4)) & mask;
  out[23] = ((w20 >> 54) | (w21 << 10)) & mask;
  out[24] = ((w21 >> 48) | (w22 << 16)) & mask;
  out[25] = ((w22 >> 42) | (w23 << 22)) & mask;
  out[26] = ((w23 >> 36) | (w24 << 28)) & mask;
  out[27] = ((w24 >> 30) | (w25 << 34)) & mask;
  out[28] = ((w25 >> 24) | (w26 << 40)) & mask;
  out[29] = ((w26 >> 18) | (w27 << 46)) & mask;
  out[30] = ((w27 >> 12) | (w28 << 52)) & mask;
  out[31] = w28 >> 6;

  return in;
}

inline const uint8_t* unpack59_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 576460752303423487ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint64_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 8;
  uint64_t w21 = util::SafeLoadAs<uint64_t>(in);
  w21 = arrow::bit_util::FromLittleEndian(w21);
  in += 8;
  uint64_t w22 = util::SafeLoadAs<uint64_t>(in);
  w22 = arrow::bit_util::FromLittleEndian(w22);
  in += 8;
  uint64_t w23 = util::SafeLoadAs<uint64_t>(in);
  w23 = arrow::bit_util::FromLittleEndian(w23);
  in += 8;
  uint64_t w24 = util::SafeLoadAs<uint64_t>(in);
  w24 = arrow::bit_util::FromLittleEndian(w24);
  in += 8;
  uint64_t w25 = util::SafeLoadAs<uint64_t>(in);
  w25 = arrow::bit_util::FromLittleEndian(w25);
  in += 8;
  uint64_t w26 = util::SafeLoadAs<uint64_t>(in);
  w26 = arrow::bit_util::FromLittleEndian(w26);
  in += 8;
  uint64_t w27 = util::SafeLoadAs<uint64_t>(in);
  w27 = arrow::bit_util::FromLittleEndian(w27);
  in += 8;
  uint64_t w28 = util::SafeLoadAs<uint64_t>(in);
  w28 = arrow::bit_util::FromLittleEndian(w28);
  in += 8;
  uint64_t w29 = util::SafeLoadAs<uint32_t>(in);
  w29 = arrow::bit_util::FromLittleEndian(w29);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 59) | (w1 << 5)) & mask;
  out[2] = ((w1 >> 54) | (w2 << 10)) & mask;
  out[3] = ((w2 >> 49) | (w3 << 15)) & mask;
  out[4] = ((w3 >> 44) | (w4 << 20)) & mask;
  out[5] = ((w4 >> 39) | (w5 << 25)) & mask;
  out[6] = ((w5 >> 34) | (w6 << 30)) & mask;
  out[7] = ((w6 >> 29) | (w7 << 35)) & mask;
  out[8] = ((w7 >> 24) | (w8 << 40)) & mask;
  out[9] = ((w8 >> 19) | (w9 << 45)) & mask;
  out[10] = ((w9 >> 14) | (w10 << 50)) & mask;
  out[11] = ((w10 >> 9) | (w11 << 55)) & mask;
  out[12] = (w11 >> 4) & mask;
  out[13] = ((w11 >> 63) | (w12 << 1)) & mask;
  out[14] = ((w12 >> 58) | (w13 << 6)) & mask;
  out[15] = ((w13 >> 53) | (w14 << 11)) & mask;
  out[16] = ((w14 >> 48) | (w15 << 16)) & mask;
  out[17] = ((w15 >> 43) | (w16 << 21)) & mask;
  out[18] = ((w16 >> 38) | (w17 << 26)) & mask;
  out[19] = ((w17 >> 33) | (w18 << 31)) & mask;
  out[20] = ((w18 >> 28) | (w19 << 36)) & mask;
  out[21] = ((w19 >> 23) | (w20 << 41)) & mask;
  out[22] = ((w20 >> 18) | (w21 << 46)) & mask;
  out[23] = ((w21 >> 13) | (w22 << 51)) & mask;
  out[24] = ((w22 >> 8) | (w23 << 56)) & mask;
  out[25] = (w23 >> 3) & mask;
  out[26] = ((w23 >> 62) | (w24 << 2)) & mask;
  out[27] = ((w24 >> 57) | (w25 << 7)) & mask;
  out[28] = ((w25 >> 52) | (w26 << 12)) & mask;
  out[29] = ((w26 >> 47) | (w27 << 17)) & mask;
  out[30] = ((w27 >> 42) | (w28 << 22)) & mask;
  out[31] = ((w28 >> 37) | (w29 << 27)) & mask;

  return in;
}

inline const uint8_t* unpack60_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 1152921504606846975ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint64_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 8;
  uint64_t w21 = util::SafeLoadAs<uint64_t>(in);
  w21 = arrow::bit_util::FromLittleEndian(w21);
  in += 8;
  uint64_t w22 = util::SafeLoadAs<uint64_t>(in);
  w22 = arrow::bit_util::FromLittleEndian(w22);
  in += 8;
  uint64_t w23 = util::SafeLoadAs<uint64_t>(in);
  w23 = arrow::bit_util::FromLittleEndian(w23);
  in += 8;
  uint64_t w24 = util::SafeLoadAs<uint64_t>(in);
  w24 = arrow::bit_util::FromLittleEndian(w24);
  in += 8;
  uint64_t w25 = util::SafeLoadAs<uint64_t>(in);
  w25 = arrow::bit_util::FromLittleEndian(w25);
  in += 8;
  uint64_t w26 = util::SafeLoadAs<uint64_t>(in);
  w26 = arrow::bit_util::FromLittleEndian(w26);
  in += 8;
  uint64_t w27 = util::SafeLoadAs<uint64_t>(in);
  w27 = arrow::bit_util::FromLittleEndian(w27);
  in += 8;
  uint64_t w28 = util::SafeLoadAs<uint64_t>(in);
  w28 = arrow::bit_util::FromLittleEndian(w28);
  in += 8;
  uint64_t w29 = util::SafeLoadAs<uint64_t>(in);
  w29 = arrow::bit_util::FromLittleEndian(w29);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 60) | (w1 << 4)) & mask;
  out[2] = ((w1 >> 56) | (w2 << 8)) & mask;
  out[3] = ((w2 >> 52) | (w3 << 12)) & mask;
  out[4] = ((w3 >> 48) | (w4 << 16)) & mask;
  out[5] = ((w4 >> 44) | (w5 << 20)) & mask;
  out[6] = ((w5 >> 40) | (w6 << 24)) & mask;
  out[7] = ((w6 >> 36) | (w7 << 28)) & mask;
  out[8] = ((w7 >> 32) | (w8 << 32)) & mask;
  out[9] = ((w8 >> 28) | (w9 << 36)) & mask;
  out[10] = ((w9 >> 24) | (w10 << 40)) & mask;
  out[11] = ((w10 >> 20) | (w11 << 44)) & mask;
  out[12] = ((w11 >> 16) | (w12 << 48)) & mask;
  out[13] = ((w12 >> 12) | (w13 << 52)) & mask;
  out[14] = ((w13 >> 8) | (w14 << 56)) & mask;
  out[15] = w14 >> 4;
  out[16] = (w15)&mask;
  out[17] = ((w15 >> 60) | (w16 << 4)) & mask;
  out[18] = ((w16 >> 56) | (w17 << 8)) & mask;
  out[19] = ((w17 >> 52) | (w18 << 12)) & mask;
  out[20] = ((w18 >> 48) | (w19 << 16)) & mask;
  out[21] = ((w19 >> 44) | (w20 << 20)) & mask;
  out[22] = ((w20 >> 40) | (w21 << 24)) & mask;
  out[23] = ((w21 >> 36) | (w22 << 28)) & mask;
  out[24] = ((w22 >> 32) | (w23 << 32)) & mask;
  out[25] = ((w23 >> 28) | (w24 << 36)) & mask;
  out[26] = ((w24 >> 24) | (w25 << 40)) & mask;
  out[27] = ((w25 >> 20) | (w26 << 44)) & mask;
  out[28] = ((w26 >> 16) | (w27 << 48)) & mask;
  out[29] = ((w27 >> 12) | (w28 << 52)) & mask;
  out[30] = ((w28 >> 8) | (w29 << 56)) & mask;
  out[31] = w29 >> 4;

  return in;
}

inline const uint8_t* unpack61_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 2305843009213693951ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint64_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 8;
  uint64_t w21 = util::SafeLoadAs<uint64_t>(in);
  w21 = arrow::bit_util::FromLittleEndian(w21);
  in += 8;
  uint64_t w22 = util::SafeLoadAs<uint64_t>(in);
  w22 = arrow::bit_util::FromLittleEndian(w22);
  in += 8;
  uint64_t w23 = util::SafeLoadAs<uint64_t>(in);
  w23 = arrow::bit_util::FromLittleEndian(w23);
  in += 8;
  uint64_t w24 = util::SafeLoadAs<uint64_t>(in);
  w24 = arrow::bit_util::FromLittleEndian(w24);
  in += 8;
  uint64_t w25 = util::SafeLoadAs<uint64_t>(in);
  w25 = arrow::bit_util::FromLittleEndian(w25);
  in += 8;
  uint64_t w26 = util::SafeLoadAs<uint64_t>(in);
  w26 = arrow::bit_util::FromLittleEndian(w26);
  in += 8;
  uint64_t w27 = util::SafeLoadAs<uint64_t>(in);
  w27 = arrow::bit_util::FromLittleEndian(w27);
  in += 8;
  uint64_t w28 = util::SafeLoadAs<uint64_t>(in);
  w28 = arrow::bit_util::FromLittleEndian(w28);
  in += 8;
  uint64_t w29 = util::SafeLoadAs<uint64_t>(in);
  w29 = arrow::bit_util::FromLittleEndian(w29);
  in += 8;
  uint64_t w30 = util::SafeLoadAs<uint32_t>(in);
  w30 = arrow::bit_util::FromLittleEndian(w30);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 61) | (w1 << 3)) & mask;
  out[2] = ((w1 >> 58) | (w2 << 6)) & mask;
  out[3] = ((w2 >> 55) | (w3 << 9)) & mask;
  out[4] = ((w3 >> 52) | (w4 << 12)) & mask;
  out[5] = ((w4 >> 49) | (w5 << 15)) & mask;
  out[6] = ((w5 >> 46) | (w6 << 18)) & mask;
  out[7] = ((w6 >> 43) | (w7 << 21)) & mask;
  out[8] = ((w7 >> 40) | (w8 << 24)) & mask;
  out[9] = ((w8 >> 37) | (w9 << 27)) & mask;
  out[10] = ((w9 >> 34) | (w10 << 30)) & mask;
  out[11] = ((w10 >> 31) | (w11 << 33)) & mask;
  out[12] = ((w11 >> 28) | (w12 << 36)) & mask;
  out[13] = ((w12 >> 25) | (w13 << 39)) & mask;
  out[14] = ((w13 >> 22) | (w14 << 42)) & mask;
  out[15] = ((w14 >> 19) | (w15 << 45)) & mask;
  out[16] = ((w15 >> 16) | (w16 << 48)) & mask;
  out[17] = ((w16 >> 13) | (w17 << 51)) & mask;
  out[18] = ((w17 >> 10) | (w18 << 54)) & mask;
  out[19] = ((w18 >> 7) | (w19 << 57)) & mask;
  out[20] = ((w19 >> 4) | (w20 << 60)) & mask;
  out[21] = (w20 >> 1) & mask;
  out[22] = ((w20 >> 62) | (w21 << 2)) & mask;
  out[23] = ((w21 >> 59) | (w22 << 5)) & mask;
  out[24] = ((w22 >> 56) | (w23 << 8)) & mask;
  out[25] = ((w23 >> 53) | (w24 << 11)) & mask;
  out[26] = ((w24 >> 50) | (w25 << 14)) & mask;
  out[27] = ((w25 >> 47) | (w26 << 17)) & mask;
  out[28] = ((w26 >> 44) | (w27 << 20)) & mask;
  out[29] = ((w27 >> 41) | (w28 << 23)) & mask;
  out[30] = ((w28 >> 38) | (w29 << 26)) & mask;
  out[31] = ((w29 >> 35) | (w30 << 29)) & mask;

  return in;
}

inline const uint8_t* unpack62_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 4611686018427387903ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint64_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 8;
  uint64_t w21 = util::SafeLoadAs<uint64_t>(in);
  w21 = arrow::bit_util::FromLittleEndian(w21);
  in += 8;
  uint64_t w22 = util::SafeLoadAs<uint64_t>(in);
  w22 = arrow::bit_util::FromLittleEndian(w22);
  in += 8;
  uint64_t w23 = util::SafeLoadAs<uint64_t>(in);
  w23 = arrow::bit_util::FromLittleEndian(w23);
  in += 8;
  uint64_t w24 = util::SafeLoadAs<uint64_t>(in);
  w24 = arrow::bit_util::FromLittleEndian(w24);
  in += 8;
  uint64_t w25 = util::SafeLoadAs<uint64_t>(in);
  w25 = arrow::bit_util::FromLittleEndian(w25);
  in += 8;
  uint64_t w26 = util::SafeLoadAs<uint64_t>(in);
  w26 = arrow::bit_util::FromLittleEndian(w26);
  in += 8;
  uint64_t w27 = util::SafeLoadAs<uint64_t>(in);
  w27 = arrow::bit_util::FromLittleEndian(w27);
  in += 8;
  uint64_t w28 = util::SafeLoadAs<uint64_t>(in);
  w28 = arrow::bit_util::FromLittleEndian(w28);
  in += 8;
  uint64_t w29 = util::SafeLoadAs<uint64_t>(in);
  w29 = arrow::bit_util::FromLittleEndian(w29);
  in += 8;
  uint64_t w30 = util::SafeLoadAs<uint64_t>(in);
  w30 = arrow::bit_util::FromLittleEndian(w30);
  in += 8;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 62) | (w1 << 2)) & mask;
  out[2] = ((w1 >> 60) | (w2 << 4)) & mask;
  out[3] = ((w2 >> 58) | (w3 << 6)) & mask;
  out[4] = ((w3 >> 56) | (w4 << 8)) & mask;
  out[5] = ((w4 >> 54) | (w5 << 10)) & mask;
  out[6] = ((w5 >> 52) | (w6 << 12)) & mask;
  out[7] = ((w6 >> 50) | (w7 << 14)) & mask;
  out[8] = ((w7 >> 48) | (w8 << 16)) & mask;
  out[9] = ((w8 >> 46) | (w9 << 18)) & mask;
  out[10] = ((w9 >> 44) | (w10 << 20)) & mask;
  out[11] = ((w10 >> 42) | (w11 << 22)) & mask;
  out[12] = ((w11 >> 40) | (w12 << 24)) & mask;
  out[13] = ((w12 >> 38) | (w13 << 26)) & mask;
  out[14] = ((w13 >> 36) | (w14 << 28)) & mask;
  out[15] = ((w14 >> 34) | (w15 << 30)) & mask;
  out[16] = ((w15 >> 32) | (w16 << 32)) & mask;
  out[17] = ((w16 >> 30) | (w17 << 34)) & mask;
  out[18] = ((w17 >> 28) | (w18 << 36)) & mask;
  out[19] = ((w18 >> 26) | (w19 << 38)) & mask;
  out[20] = ((w19 >> 24) | (w20 << 40)) & mask;
  out[21] = ((w20 >> 22) | (w21 << 42)) & mask;
  out[22] = ((w21 >> 20) | (w22 << 44)) & mask;
  out[23] = ((w22 >> 18) | (w23 << 46)) & mask;
  out[24] = ((w23 >> 16) | (w24 << 48)) & mask;
  out[25] = ((w24 >> 14) | (w25 << 50)) & mask;
  out[26] = ((w25 >> 12) | (w26 << 52)) & mask;
  out[27] = ((w26 >> 10) | (w27 << 54)) & mask;
  out[28] = ((w27 >> 8) | (w28 << 56)) & mask;
  out[29] = ((w28 >> 6) | (w29 << 58)) & mask;
  out[30] = ((w29 >> 4) | (w30 << 60)) & mask;
  out[31] = w30 >> 2;

  return in;
}

inline const uint8_t* unpack63_64(const uint8_t* in, uint64_t* out) {
  const uint64_t mask = 9223372036854775807ULL;
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint64_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 8;
  uint64_t w21 = util::SafeLoadAs<uint64_t>(in);
  w21 = arrow::bit_util::FromLittleEndian(w21);
  in += 8;
  uint64_t w22 = util::SafeLoadAs<uint64_t>(in);
  w22 = arrow::bit_util::FromLittleEndian(w22);
  in += 8;
  uint64_t w23 = util::SafeLoadAs<uint64_t>(in);
  w23 = arrow::bit_util::FromLittleEndian(w23);
  in += 8;
  uint64_t w24 = util::SafeLoadAs<uint64_t>(in);
  w24 = arrow::bit_util::FromLittleEndian(w24);
  in += 8;
  uint64_t w25 = util::SafeLoadAs<uint64_t>(in);
  w25 = arrow::bit_util::FromLittleEndian(w25);
  in += 8;
  uint64_t w26 = util::SafeLoadAs<uint64_t>(in);
  w26 = arrow::bit_util::FromLittleEndian(w26);
  in += 8;
  uint64_t w27 = util::SafeLoadAs<uint64_t>(in);
  w27 = arrow::bit_util::FromLittleEndian(w27);
  in += 8;
  uint64_t w28 = util::SafeLoadAs<uint64_t>(in);
  w28 = arrow::bit_util::FromLittleEndian(w28);
  in += 8;
  uint64_t w29 = util::SafeLoadAs<uint64_t>(in);
  w29 = arrow::bit_util::FromLittleEndian(w29);
  in += 8;
  uint64_t w30 = util::SafeLoadAs<uint64_t>(in);
  w30 = arrow::bit_util::FromLittleEndian(w30);
  in += 8;
  uint64_t w31 = util::SafeLoadAs<uint32_t>(in);
  w31 = arrow::bit_util::FromLittleEndian(w31);
  in += 4;
  out[0] = (w0)&mask;
  out[1] = ((w0 >> 63) | (w1 << 1)) & mask;
  out[2] = ((w1 >> 62) | (w2 << 2)) & mask;
  out[3] = ((w2 >> 61) | (w3 << 3)) & mask;
  out[4] = ((w3 >> 60) | (w4 << 4)) & mask;
  out[5] = ((w4 >> 59) | (w5 << 5)) & mask;
  out[6] = ((w5 >> 58) | (w6 << 6)) & mask;
  out[7] = ((w6 >> 57) | (w7 << 7)) & mask;
  out[8] = ((w7 >> 56) | (w8 << 8)) & mask;
  out[9] = ((w8 >> 55) | (w9 << 9)) & mask;
  out[10] = ((w9 >> 54) | (w10 << 10)) & mask;
  out[11] = ((w10 >> 53) | (w11 << 11)) & mask;
  out[12] = ((w11 >> 52) | (w12 << 12)) & mask;
  out[13] = ((w12 >> 51) | (w13 << 13)) & mask;
  out[14] = ((w13 >> 50) | (w14 << 14)) & mask;
  out[15] = ((w14 >> 49) | (w15 << 15)) & mask;
  out[16] = ((w15 >> 48) | (w16 << 16)) & mask;
  out[17] = ((w16 >> 47) | (w17 << 17)) & mask;
  out[18] = ((w17 >> 46) | (w18 << 18)) & mask;
  out[19] = ((w18 >> 45) | (w19 << 19)) & mask;
  out[20] = ((w19 >> 44) | (w20 << 20)) & mask;
  out[21] = ((w20 >> 43) | (w21 << 21)) & mask;
  out[22] = ((w21 >> 42) | (w22 << 22)) & mask;
  out[23] = ((w22 >> 41) | (w23 << 23)) & mask;
  out[24] = ((w23 >> 40) | (w24 << 24)) & mask;
  out[25] = ((w24 >> 39) | (w25 << 25)) & mask;
  out[26] = ((w25 >> 38) | (w26 << 26)) & mask;
  out[27] = ((w26 >> 37) | (w27 << 27)) & mask;
  out[28] = ((w27 >> 36) | (w28 << 28)) & mask;
  out[29] = ((w28 >> 35) | (w29 << 29)) & mask;
  out[30] = ((w29 >> 34) | (w30 << 30)) & mask;
  out[31] = ((w30 >> 33) | (w31 << 31)) & mask;

  return in;
}

inline const uint8_t* unpack64_64(const uint8_t* in, uint64_t* out) {
  uint64_t w0 = util::SafeLoadAs<uint64_t>(in);
  w0 = arrow::bit_util::FromLittleEndian(w0);
  in += 8;
  uint64_t w1 = util::SafeLoadAs<uint64_t>(in);
  w1 = arrow::bit_util::FromLittleEndian(w1);
  in += 8;
  uint64_t w2 = util::SafeLoadAs<uint64_t>(in);
  w2 = arrow::bit_util::FromLittleEndian(w2);
  in += 8;
  uint64_t w3 = util::SafeLoadAs<uint64_t>(in);
  w3 = arrow::bit_util::FromLittleEndian(w3);
  in += 8;
  uint64_t w4 = util::SafeLoadAs<uint64_t>(in);
  w4 = arrow::bit_util::FromLittleEndian(w4);
  in += 8;
  uint64_t w5 = util::SafeLoadAs<uint64_t>(in);
  w5 = arrow::bit_util::FromLittleEndian(w5);
  in += 8;
  uint64_t w6 = util::SafeLoadAs<uint64_t>(in);
  w6 = arrow::bit_util::FromLittleEndian(w6);
  in += 8;
  uint64_t w7 = util::SafeLoadAs<uint64_t>(in);
  w7 = arrow::bit_util::FromLittleEndian(w7);
  in += 8;
  uint64_t w8 = util::SafeLoadAs<uint64_t>(in);
  w8 = arrow::bit_util::FromLittleEndian(w8);
  in += 8;
  uint64_t w9 = util::SafeLoadAs<uint64_t>(in);
  w9 = arrow::bit_util::FromLittleEndian(w9);
  in += 8;
  uint64_t w10 = util::SafeLoadAs<uint64_t>(in);
  w10 = arrow::bit_util::FromLittleEndian(w10);
  in += 8;
  uint64_t w11 = util::SafeLoadAs<uint64_t>(in);
  w11 = arrow::bit_util::FromLittleEndian(w11);
  in += 8;
  uint64_t w12 = util::SafeLoadAs<uint64_t>(in);
  w12 = arrow::bit_util::FromLittleEndian(w12);
  in += 8;
  uint64_t w13 = util::SafeLoadAs<uint64_t>(in);
  w13 = arrow::bit_util::FromLittleEndian(w13);
  in += 8;
  uint64_t w14 = util::SafeLoadAs<uint64_t>(in);
  w14 = arrow::bit_util::FromLittleEndian(w14);
  in += 8;
  uint64_t w15 = util::SafeLoadAs<uint64_t>(in);
  w15 = arrow::bit_util::FromLittleEndian(w15);
  in += 8;
  uint64_t w16 = util::SafeLoadAs<uint64_t>(in);
  w16 = arrow::bit_util::FromLittleEndian(w16);
  in += 8;
  uint64_t w17 = util::SafeLoadAs<uint64_t>(in);
  w17 = arrow::bit_util::FromLittleEndian(w17);
  in += 8;
  uint64_t w18 = util::SafeLoadAs<uint64_t>(in);
  w18 = arrow::bit_util::FromLittleEndian(w18);
  in += 8;
  uint64_t w19 = util::SafeLoadAs<uint64_t>(in);
  w19 = arrow::bit_util::FromLittleEndian(w19);
  in += 8;
  uint64_t w20 = util::SafeLoadAs<uint64_t>(in);
  w20 = arrow::bit_util::FromLittleEndian(w20);
  in += 8;
  uint64_t w21 = util::SafeLoadAs<uint64_t>(in);
  w21 = arrow::bit_util::FromLittleEndian(w21);
  in += 8;
  uint64_t w22 = util::SafeLoadAs<uint64_t>(in);
  w22 = arrow::bit_util::FromLittleEndian(w22);
  in += 8;
  uint64_t w23 = util::SafeLoadAs<uint64_t>(in);
  w23 = arrow::bit_util::FromLittleEndian(w23);
  in += 8;
  uint64_t w24 = util::SafeLoadAs<uint64_t>(in);
  w24 = arrow::bit_util::FromLittleEndian(w24);
  in += 8;
  uint64_t w25 = util::SafeLoadAs<uint64_t>(in);
  w25 = arrow::bit_util::FromLittleEndian(w25);
  in += 8;
  uint64_t w26 = util::SafeLoadAs<uint64_t>(in);
  w26 = arrow::bit_util::FromLittleEndian(w26);
  in += 8;
  uint64_t w27 = util::SafeLoadAs<uint64_t>(in);
  w27 = arrow::bit_util::FromLittleEndian(w27);
  in += 8;
  uint64_t w28 = util::SafeLoadAs<uint64_t>(in);
  w28 = arrow::bit_util::FromLittleEndian(w28);
  in += 8;
  uint64_t w29 = util::SafeLoadAs<uint64_t>(in);
  w29 = arrow::bit_util::FromLittleEndian(w29);
  in += 8;
  uint64_t w30 = util::SafeLoadAs<uint64_t>(in);
  w30 = arrow::bit_util::FromLittleEndian(w30);
  in += 8;
  uint64_t w31 = util::SafeLoadAs<uint64_t>(in);
  w31 = arrow::bit_util::FromLittleEndian(w31);
  in += 8;
  out[0] = w0;
  out[1] = w1;
  out[2] = w2;
  out[3] = w3;
  out[4] = w4;
  out[5] = w5;
  out[6] = w6;
  out[7] = w7;
  out[8] = w8;
  out[9] = w9;
  out[10] = w10;
  out[11] = w11;
  out[12] = w12;
  out[13] = w13;
  out[14] = w14;
  out[15] = w15;
  out[16] = w16;
  out[17] = w17;
  out[18] = w18;
  out[19] = w19;
  out[20] = w20;
  out[21] = w21;
  out[22] = w22;
  out[23] = w23;
  out[24] = w24;
  out[25] = w25;
  out[26] = w26;
  out[27] = w27;
  out[28] = w28;
  out[29] = w29;
  out[30] = w30;
  out[31] = w31;

  return in;
}

}  // namespace internal
}  // namespace arrow
