// Licensed to the Apache Software Foundation (ASF) under one
// or more contributor license agreements.  See the NOTICE file
// distributed with this work for additional information
// regarding copyright ownership.  The ASF licenses this file
// to you under the Apache License, Version 2.0 (the
// "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.

#pragma once

#include <string>

#include "parquet/platform.h"

namespace arrow {
namespace json {
namespace internal {
class ObjectParser;
}  // namespace internal
}  // namespace json
}  // namespace arrow

namespace parquet::encryption {

// KeyMaterial class represents the "key material", keeping the information that allows
// readers to recover an encryption key (see description of the KeyMetadata class). The
// keytools package (PARQUET-1373) implements the "envelope encryption" pattern, in a
// "single wrapping" or "double wrapping" mode. In the single wrapping mode, the key
// material is generated by encrypting the "data encryption key" (DEK) by a "master key".
// In the double wrapping mode, the key material is generated by encrypting the DEK by a
// "key encryption key" (KEK), that in turn is encrypted by a "master key".
//
// Key material is kept in a flat json object, with the following fields:
// 1. "keyMaterialType" - a String, with the type of  key material. In the current
// version, only one value is allowed - "PKMT1" (stands
//     for "parquet key management tools, version 1"). For external key material storage,
//     this field is written in both "key metadata" and "key material" jsons. For internal
//     key material storage, this field is written only once in the common json.
// 2. "isFooterKey" - a boolean. If true, means that the material belongs to a file footer
// key, and keeps additional information (such as
//     KMS instance ID and URL). If false, means that the material belongs to a column
//     key.
// 3. "kmsInstanceID" - a String, with the KMS Instance ID. Written only in footer key
// material.
// 4. "kmsInstanceURL" - a String, with the KMS Instance URL. Written only in footer key
// material.
// 5. "masterKeyID" - a String, with the ID of the master key used to generate the
// material.
// 6. "wrappedDEK" - a String, with the wrapped DEK (base64 encoding).
// 7. "doubleWrapping" - a boolean. If true, means that the material was generated in
// double wrapping mode.
//     If false - in single wrapping mode.
// 8. "keyEncryptionKeyID" - a String, with the ID of the KEK used to generate the
// material. Written only in double wrapping mode.
// 9. "wrappedKEK" - a String, with the wrapped KEK (base64 encoding). Written only in
// double wrapping mode.
class PARQUET_EXPORT KeyMaterial {
 public:
  // these fields are defined in a specification and should never be changed
  static constexpr const char kKeyMaterialTypeField[] = "keyMaterialType";
  static constexpr const char kKeyMaterialType1[] = "PKMT1";

  static constexpr const char kFooterKeyIdInFile[] = "footerKey";
  static constexpr const char kColumnKeyIdInFilePrefix[] = "columnKey";

  static constexpr const char kIsFooterKeyField[] = "isFooterKey";
  static constexpr const char kDoubleWrappingField[] = "doubleWrapping";
  static constexpr const char kKmsInstanceIdField[] = "kmsInstanceID";
  static constexpr const char kKmsInstanceUrlField[] = "kmsInstanceURL";
  static constexpr const char kMasterKeyIdField[] = "masterKeyID";
  static constexpr const char kWrappedDataEncryptionKeyField[] = "wrappedDEK";
  static constexpr const char kKeyEncryptionKeyIdField[] = "keyEncryptionKeyID";
  static constexpr const char kWrappedKeyEncryptionKeyField[] = "wrappedKEK";

 public:
  KeyMaterial() = default;

  static KeyMaterial Parse(const std::string& key_material_string);

  static KeyMaterial Parse(
      const ::arrow::json::internal::ObjectParser* key_material_json);

  /// This method returns a json string that will be stored either inside a parquet file
  /// or in a key material store outside the parquet file.
  static std::string SerializeToJson(bool is_footer_key,
                                     const std::string& kms_instance_id,
                                     const std::string& kms_instance_url,
                                     const std::string& master_key_id,
                                     bool is_double_wrapped, const std::string& kek_id,
                                     const std::string& encoded_wrapped_kek,
                                     const std::string& encoded_wrapped_dek,
                                     bool is_internal_storage);

  bool is_footer_key() const { return is_footer_key_; }
  bool is_double_wrapped() const { return is_double_wrapped_; }
  const std::string& master_key_id() const { return master_key_id_; }
  const std::string& wrapped_dek() const { return encoded_wrapped_dek_; }
  const std::string& kek_id() const { return kek_id_; }
  const std::string& wrapped_kek() const { return encoded_wrapped_kek_; }
  const std::string& kms_instance_id() const { return kms_instance_id_; }
  const std::string& kms_instance_url() const { return kms_instance_url_; }

 private:
  KeyMaterial(bool is_footer_key, const std::string& kms_instance_id,
              const std::string& kms_instance_url, const std::string& master_key_id,
              bool is_double_wrapped, const std::string& kek_id,
              const std::string& encoded_wrapped_kek,
              const std::string& encoded_wrapped_dek);

  bool is_footer_key_;
  std::string kms_instance_id_;
  std::string kms_instance_url_;
  std::string master_key_id_;
  bool is_double_wrapped_;
  std::string kek_id_;
  std::string encoded_wrapped_kek_;
  std::string encoded_wrapped_dek_;
};

}  // namespace parquet::encryption
