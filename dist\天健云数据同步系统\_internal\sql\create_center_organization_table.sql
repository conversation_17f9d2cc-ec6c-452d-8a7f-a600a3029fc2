-- 在中心库(Examdb)创建机构配置表
-- 服务器: ***********
-- 数据库: Examdb
-- 用户名: tj
-- 密码: jiare<PERSON><PERSON>an

USE Examdb;

-- 1. 创建机构配置主表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='T_Center_Organization_Config' AND xtype='U')
BEGIN
    CREATE TABLE T_Center_Organization_Config (
        id INT IDENTITY(1,1) PRIMARY KEY,                   -- 自增主键
        cOrgCode VARCHAR(20) NOT NULL UNIQUE,               -- 机构编码（唯一）
        cOrgName NVARCHAR(100) NOT NULL,                    -- 机构名称
        cOrgType VARCHAR(20) DEFAULT 'HOSPITAL',            -- 机构类型（HOSPITAL/CLINIC/CENTER/BRANCH）
        cStatus VARCHAR(1) DEFAULT '1',                     -- 状态（1=启用，0=停用）
        
        -- 天健云API配置
        cTianjianMicCode VARCHAR(50),                       -- 天健云机构代码（mic-code）
        cTianjianMiscId VARCHAR(50),                        -- 天健云系统ID（misc-id）
        cTianjianApiKey VARCHAR(200),                       -- 天健云API密钥
        cTianjianBaseUrl VARCHAR(200) DEFAULT 'http://**************:9300', -- 天健云API基础URL
        
        -- 数据库配置
        cDbHost VARCHAR(100),                               -- 数据库服务器
        cDbPort INT DEFAULT 1433,                           -- 数据库端口
        cDbName VARCHAR(50),                                -- 数据库名称
        cDbUser VARCHAR(50),                                -- 数据库用户名
        cDbPassword VARCHAR(200),                           -- 数据库密码（建议加密存储）
        cDbDriver VARCHAR(100) DEFAULT 'ODBC Driver 17 for SQL Server', -- 数据库驱动
        
        -- 业务配置
        cShopCode VARCHAR(20),                              -- 关联的门店编码
        cDefaultDeptCode VARCHAR(20),                       -- 默认科室编码
        cSyncEnabled VARCHAR(1) DEFAULT '1',                -- 是否启用同步（1=是，0=否）
        cSyncIntervals INT DEFAULT 60,                      -- 同步间隔（分钟）
        

        
        -- 系统字段
        dCreateTime DATETIME DEFAULT GETDATE(),             -- 创建时间
        dUpdateTime DATETIME DEFAULT GETDATE(),             -- 更新时间
        cCreateUser VARCHAR(20) DEFAULT 'SYSTEM',           -- 创建用户
        cUpdateUser VARCHAR(20) DEFAULT 'SYSTEM'            -- 更新用户
    );
    
    PRINT '中心库机构配置表 T_Center_Organization_Config 创建成功';

    -- 创建索引
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_T_Center_Organization_Config_OrgCode')
    BEGIN
        CREATE UNIQUE INDEX IX_T_Center_Organization_Config_OrgCode ON T_Center_Organization_Config (cOrgCode);
        PRINT '索引 IX_T_Center_Organization_Config_OrgCode 创建成功';
    END

    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_T_Center_Organization_Config_Status')
    BEGIN
        CREATE INDEX IX_T_Center_Organization_Config_Status ON T_Center_Organization_Config (cStatus);
        PRINT '索引 IX_T_Center_Organization_Config_Status 创建成功';
    END

    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_T_Center_Organization_Config_CreateTime')
    BEGIN
        CREATE INDEX IX_T_Center_Organization_Config_CreateTime ON T_Center_Organization_Config (dCreateTime);
        PRINT '索引 IX_T_Center_Organization_Config_CreateTime 创建成功';
    END
END
ELSE
BEGIN
    PRINT '中心库机构配置表 T_Center_Organization_Config 已存在';
END

-- 2. 创建机构接口配置表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='T_Center_Organization_Interface' AND xtype='U')
BEGIN
    CREATE TABLE T_Center_Organization_Interface (
        id INT IDENTITY(1,1) PRIMARY KEY,                  -- 自增主键
        cOrgCode VARCHAR(20) NOT NULL,                     -- 机构编码
        cInterfaceCode VARCHAR(20) NOT NULL,               -- 接口编码（01-21）
        cInterfaceName NVARCHAR(100),                      -- 接口名称
        cEnabled VARCHAR(1) DEFAULT '1',                   -- 是否启用（1=是，0=否）
        cSyncMode VARCHAR(10) DEFAULT 'AUTO',              -- 同步模式（AUTO=自动，MANUAL=手动）
        cBatchSize INT DEFAULT 50,                         -- 批量大小
        cRetryTimes INT DEFAULT 3,                         -- 重试次数
        cTimeout INT DEFAULT 30,                           -- 超时时间（秒）
        cCustomParams NVARCHAR(1000),                      -- 自定义参数（JSON格式）
        cLastSyncTime DATETIME,                            -- 最后同步时间
        cSyncStatus VARCHAR(20),                           -- 同步状态
        
        -- 系统字段
        dCreateTime DATETIME DEFAULT GETDATE(),
        dUpdateTime DATETIME DEFAULT GETDATE(),
        cCreateUser VARCHAR(20) DEFAULT 'SYSTEM',
        cUpdateUser VARCHAR(20) DEFAULT 'SYSTEM',
        
        -- 外键约束
        FOREIGN KEY (cOrgCode) REFERENCES T_Center_Organization_Config(cOrgCode),
        -- 唯一约束
        UNIQUE (cOrgCode, cInterfaceCode),
        
        -- 索引
        INDEX IX_T_Center_Organization_Interface_OrgCode (cOrgCode),
        INDEX IX_T_Center_Organization_Interface_Status (cSyncStatus)
    );
    
    PRINT '中心库机构接口配置表 T_Center_Organization_Interface 创建成功';
END
ELSE
BEGIN
    PRINT '中心库机构接口配置表 T_Center_Organization_Interface 已存在';
END

-- 3. 创建机构同步日志表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='T_Center_Organization_Sync_Log' AND xtype='U')
BEGIN
    CREATE TABLE T_Center_Organization_Sync_Log (
        id BIGINT IDENTITY(1,1) PRIMARY KEY,               -- 自增主键
        cOrgCode VARCHAR(20) NOT NULL,                     -- 机构编码
        cInterfaceCode VARCHAR(20),                        -- 接口编码
        cSyncType VARCHAR(20),                             -- 同步类型（FULL=全量，INCR=增量）
        cSyncStatus VARCHAR(10),                           -- 同步状态（SUCCESS=成功，FAILED=失败，RUNNING=运行中）
        
        -- 同步统计
        iTotalCount INT DEFAULT 0,                         -- 总记录数
        iSuccessCount INT DEFAULT 0,                       -- 成功记录数
        iFailedCount INT DEFAULT 0,                        -- 失败记录数
        
        -- 时间信息
        dStartTime DATETIME,                               -- 开始时间
        dEndTime DATETIME,                                 -- 结束时间
        iDuration INT,                                     -- 耗时（秒）
        
        -- 详细信息
        cErrorMessage NVARCHAR(2000),                      -- 错误信息
        cRequestData NVARCHAR(MAX),                        -- 请求数据（可选）
        cResponseData NVARCHAR(MAX),                       -- 响应数据（可选）
        cLogLevel VARCHAR(10) DEFAULT 'INFO',              -- 日志级别
        cOperator VARCHAR(50),                             -- 操作员
        
        -- 系统字段
        dCreateTime DATETIME DEFAULT GETDATE(),
        cCreateUser VARCHAR(20) DEFAULT 'SYSTEM',
        
        -- 外键约束
        FOREIGN KEY (cOrgCode) REFERENCES T_Center_Organization_Config(cOrgCode),
        
        -- 索引
        INDEX IX_T_Center_Organization_Sync_Log_OrgCode_Time (cOrgCode, dCreateTime),
        INDEX IX_T_Center_Organization_Sync_Log_Status (cSyncStatus),
        INDEX IX_T_Center_Organization_Sync_Log_Interface (cInterfaceCode)
    );
    
    PRINT '中心库机构同步日志表 T_Center_Organization_Sync_Log 创建成功';
END
ELSE
BEGIN
    PRINT '中心库机构同步日志表 T_Center_Organization_Sync_Log 已存在';
END

-- 4. 插入默认机构配置数据
IF NOT EXISTS (SELECT * FROM T_Center_Organization_Config)
BEGIN
    INSERT INTO T_Center_Organization_Config (
        cOrgCode, cOrgName, cOrgType, cStatus,
        cTianjianMicCode, cTianjianMiscId, cTianjianApiKey, cTianjianBaseUrl,
        cDbHost, cDbPort, cDbName, cDbUser, cDbPassword,
        cShopCode, cCreateUser
    ) VALUES
    -- 默认机构（嘉仁体检中心总部）
    ('JR001', '嘉仁体检中心', 'CENTER', '1',
     'MIC1.001E', 'MISC1.00001A', '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM', 'http://**************:9300',
     '***********', 1433, 'examdb_center', 'tj', 'jiarentijian',
     '01', 'SYSTEM'),

    -- 福田分院
    ('JR002', '嘉仁体检中心福田分院', 'BRANCH', '1',
     'MIC1.002E', 'MISC1.00002A', 'API_KEY_FOR_BRANCH_002', 'http://**************:9300',
     '***********', 1433, 'examdb_center_ft', 'tj', 'jiarentijian_ft',
     '02', 'SYSTEM'),

    -- 罗湖分院
    ('JR003', '嘉仁体检中心罗湖分院', 'BRANCH', '1',
     'MIC1.003E', 'MISC1.00003A', 'API_KEY_FOR_BRANCH_003', 'http://**************:9300',
     '***********', 1433, 'examdb_center_lh', 'tj', 'jiarentijian_lh',
     '03', 'SYSTEM');
    
    PRINT '默认机构配置数据插入成功';
END

-- 5. 插入默认接口配置
IF NOT EXISTS (SELECT * FROM T_Center_Organization_Interface)
BEGIN
    -- 为每个机构插入常用接口配置
    DECLARE @OrgCode VARCHAR(20);
    DECLARE org_cursor CURSOR FOR 
        SELECT cOrgCode FROM T_Center_Organization_Config WHERE cStatus = '1';
    
    OPEN org_cursor;
    FETCH NEXT FROM org_cursor INTO @OrgCode;
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        INSERT INTO T_Center_Organization_Interface (
            cOrgCode, cInterfaceCode, cInterfaceName, cEnabled, cSyncMode, cBatchSize, cRetryTimes, cTimeout
        ) VALUES 
        (@OrgCode, '01', '体检信息传输', '1', 'AUTO', 50, 3, 30),
        (@OrgCode, '02', '申请项目字典', '1', 'AUTO', 100, 3, 60),
        (@OrgCode, '03', '科室结果传输', '1', 'AUTO', 30, 3, 45),
        (@OrgCode, '04', '医生信息传输', '1', 'AUTO', 50, 3, 30),
        (@OrgCode, '05', '科室信息传输', '1', 'AUTO', 50, 3, 30),
        (@OrgCode, '06', '字典信息传输', '1', 'AUTO', 100, 3, 30);
        
        FETCH NEXT FROM org_cursor INTO @OrgCode;
    END
    
    CLOSE org_cursor;
    DEALLOCATE org_cursor;
    
    PRINT '默认接口配置数据插入成功';
END

-- 6. 创建视图用于查询机构统计信息
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='V_Center_Organization_Summary' AND xtype='V')
BEGIN
    EXEC('
    CREATE VIEW V_Center_Organization_Summary AS
    SELECT
        o.cOrgCode,
        o.cOrgName,
        o.cOrgType,
        o.cStatus,
        o.dCreateTime,
        COUNT(i.id) as InterfaceCount,
        SUM(CASE WHEN i.cEnabled = ''1'' THEN 1 ELSE 0 END) as EnabledInterfaceCount,
        MAX(l.dCreateTime) as LastSyncTime,
        COUNT(l.id) as TotalSyncCount,
        SUM(CASE WHEN l.cSyncStatus = ''SUCCESS'' THEN 1 ELSE 0 END) as SuccessSyncCount
    FROM T_Center_Organization_Config o
    LEFT JOIN T_Center_Organization_Interface i ON o.cOrgCode = i.cOrgCode
    LEFT JOIN T_Center_Organization_Sync_Log l ON o.cOrgCode = l.cOrgCode
    GROUP BY o.cOrgCode, o.cOrgName, o.cOrgType, o.cStatus, o.dCreateTime
    ');
    
    PRINT '机构统计视图 V_Center_Organization_Summary 创建成功';
END

PRINT '';
PRINT '中心库机构配置表创建完成！';
PRINT '可以通过以下SQL查询机构配置：';
PRINT 'SELECT * FROM T_Center_Organization_Config;';
PRINT 'SELECT * FROM T_Center_Organization_Interface;';
PRINT 'SELECT * FROM V_Center_Organization_Summary;';
