-- 多机构支持数据库表设计
-- 创建机构配置表和相关支持表

-- 1. 机构配置主表
CREATE TABLE T_Organization_Config (
    cOrgCode VARCHAR(20) PRIMARY KEY,                    -- 机构编码（主键）
    cOrgName NVARCHAR(100) NOT NULL,                     -- 机构名称
    cOrgType VARCHAR(10) DEFAULT 'HOSPITAL',             -- 机构类型（HOSPITAL/CLINIC/CENTER）
    cStatus VARCHAR(1) DEFAULT '1',                      -- 状态（1=启用，0=停用）
    
    -- 天健云API配置
    cTianjianMicCode VARCHAR(50),                        -- 天健云机构代码（mic-code）
    cTianjianMiscId VARCHAR(50),                         -- 天健云系统ID（misc-id）
    cTianjianApiKey VARCHAR(100),                        -- 天健云API密钥
    cTianjianBaseUrl VARCHAR(200),                       -- 天健云API基础URL
    
    -- 数据库配置
    cDbHost VARCHAR(100),                                -- 数据库服务器
    cDbPort INT DEFAULT 1433,                            -- 数据库端口
    cDbName VARCHAR(50),                                 -- 数据库名称
    cDbUser VARCHAR(50),                                 -- 数据库用户名
    cDbPassword VARCHAR(100),                            -- 数据库密码（加密存储）
    cDbDriver VARCHAR(100) DEFAULT 'ODBC Driver 17 for SQL Server', -- 数据库驱动
    
    -- 业务配置
    cShopCode VARCHAR(20),                               -- 关联的门店编码
    cDefaultDeptCode VARCHAR(20),                        -- 默认科室编码
    cSyncEnabled VARCHAR(1) DEFAULT '1',                 -- 是否启用同步（1=是，0=否）
    cSyncIntervals INT DEFAULT 60,                       -- 同步间隔（分钟）
    
    -- 联系信息
    cContactPerson NVARCHAR(50),                         -- 联系人
    cContactPhone VARCHAR(20),                           -- 联系电话
    cContactEmail VARCHAR(100),                          -- 联系邮箱
    cAddress NVARCHAR(200),                              -- 机构地址
    
    -- 系统字段
    dCreateTime DATETIME DEFAULT GETDATE(),              -- 创建时间
    dUpdateTime DATETIME DEFAULT GETDATE(),              -- 更新时间
    cCreateUser VARCHAR(20),                             -- 创建用户
    cUpdateUser VARCHAR(20),                             -- 更新用户
    cRemark NVARCHAR(500)                                -- 备注
);

-- 2. 机构接口配置表（支持不同接口的个性化配置）
CREATE TABLE T_Organization_Interface_Config (
    id INT IDENTITY(1,1) PRIMARY KEY,                    -- 自增主键
    cOrgCode VARCHAR(20) NOT NULL,                       -- 机构编码
    cInterfaceCode VARCHAR(20) NOT NULL,                 -- 接口编码（01-21）
    cInterfaceName NVARCHAR(100),                        -- 接口名称
    cEnabled VARCHAR(1) DEFAULT '1',                     -- 是否启用（1=是，0=否）
    cSyncMode VARCHAR(10) DEFAULT 'AUTO',                -- 同步模式（AUTO=自动，MANUAL=手动）
    cBatchSize INT DEFAULT 50,                           -- 批量大小
    cRetryTimes INT DEFAULT 3,                           -- 重试次数
    cTimeout INT DEFAULT 30,                             -- 超时时间（秒）
    cCustomParams NVARCHAR(1000),                        -- 自定义参数（JSON格式）
    
    -- 系统字段
    dCreateTime DATETIME DEFAULT GETDATE(),
    dUpdateTime DATETIME DEFAULT GETDATE(),
    cCreateUser VARCHAR(20),
    cUpdateUser VARCHAR(20),
    
    -- 外键约束
    FOREIGN KEY (cOrgCode) REFERENCES T_Organization_Config(cOrgCode),
    -- 唯一约束
    UNIQUE (cOrgCode, cInterfaceCode)
);

-- 3. 机构同步日志表
CREATE TABLE T_Organization_Sync_Log (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,                 -- 自增主键
    cOrgCode VARCHAR(20) NOT NULL,                       -- 机构编码
    cInterfaceCode VARCHAR(20),                          -- 接口编码
    cSyncType VARCHAR(20),                               -- 同步类型（FULL=全量，INCR=增量）
    cSyncStatus VARCHAR(10),                             -- 同步状态（SUCCESS=成功，FAILED=失败，RUNNING=运行中）
    
    -- 同步统计
    iTotalCount INT DEFAULT 0,                           -- 总记录数
    iSuccessCount INT DEFAULT 0,                         -- 成功记录数
    iFailedCount INT DEFAULT 0,                          -- 失败记录数
    
    -- 时间信息
    dStartTime DATETIME,                                 -- 开始时间
    dEndTime DATETIME,                                   -- 结束时间
    iDuration INT,                                       -- 耗时（秒）
    
    -- 详细信息
    cErrorMessage NVARCHAR(2000),                        -- 错误信息
    cRequestData NVARCHAR(MAX),                          -- 请求数据
    cResponseData NVARCHAR(MAX),                         -- 响应数据
    cLogLevel VARCHAR(10) DEFAULT 'INFO',                -- 日志级别
    
    -- 系统字段
    dCreateTime DATETIME DEFAULT GETDATE(),
    cCreateUser VARCHAR(20),
    
    -- 外键约束
    FOREIGN KEY (cOrgCode) REFERENCES T_Organization_Config(cOrgCode)
);

-- 4. 机构数据映射表（用于不同机构间的数据映射）
CREATE TABLE T_Organization_Data_Mapping (
    id INT IDENTITY(1,1) PRIMARY KEY,                    -- 自增主键
    cOrgCode VARCHAR(20) NOT NULL,                       -- 机构编码
    cMappingType VARCHAR(20) NOT NULL,                   -- 映射类型（DEPT=科室，ITEM=项目，USER=用户等）
    cSourceCode VARCHAR(50) NOT NULL,                    -- 源编码
    cSourceName NVARCHAR(100),                           -- 源名称
    cTargetCode VARCHAR(50) NOT NULL,                    -- 目标编码
    cTargetName NVARCHAR(100),                           -- 目标名称
    cStatus VARCHAR(1) DEFAULT '1',                      -- 状态（1=启用，0=停用）
    
    -- 系统字段
    dCreateTime DATETIME DEFAULT GETDATE(),
    dUpdateTime DATETIME DEFAULT GETDATE(),
    cCreateUser VARCHAR(20),
    cUpdateUser VARCHAR(20),
    cRemark NVARCHAR(500),                               -- 备注
    
    -- 外键约束
    FOREIGN KEY (cOrgCode) REFERENCES T_Organization_Config(cOrgCode),
    -- 唯一约束
    UNIQUE (cOrgCode, cMappingType, cSourceCode)
);

-- 5. 创建索引
CREATE INDEX IX_T_Organization_Sync_Log_OrgCode_Time ON T_Organization_Sync_Log(cOrgCode, dCreateTime);
CREATE INDEX IX_T_Organization_Interface_Config_OrgCode ON T_Organization_Interface_Config(cOrgCode);
CREATE INDEX IX_T_Organization_Data_Mapping_OrgCode_Type ON T_Organization_Data_Mapping(cOrgCode, cMappingType);

-- 6. 插入示例数据
INSERT INTO T_Organization_Config (
    cOrgCode, cOrgName, cOrgType, cStatus,
    cTianjianMicCode, cTianjianMiscId, cTianjianApiKey, cTianjianBaseUrl,
    cDbHost, cDbPort, cDbName, cDbUser, cDbPassword,
    cShopCode, cContactPerson, cContactPhone, cAddress,
    cCreateUser, cRemark
) VALUES 
-- 嘉仁体检中心（当前机构）
('JR001', '嘉仁体检中心', 'CENTER', '1',
 'MIC1.001E', 'MISC1.00001A', '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM', 'http://**************:9300',
 '***********', 1433, 'examdb_center', 'tj', 'jiarentijian',
 '01', '张主任', '0755-12345678', '深圳市南山区嘉仁体检中心',
 'SYSTEM', '主要体检中心'),

-- 分院示例
('JR002', '嘉仁体检中心福田分院', 'BRANCH', '1',
 'MIC1.002E', 'MISC1.00002A', 'API_KEY_FOR_BRANCH_002', 'http://**************:9300',
 '***********', 1433, 'examdb_center_ft', 'tj', 'jiarentijian_ft',
 '02', '李主任', '0755-87654321', '深圳市福田区嘉仁体检分院',
 'SYSTEM', '福田分院'),

('JR003', '嘉仁体检中心罗湖分院', 'BRANCH', '1',
 'MIC1.003E', 'MISC1.00003A', 'API_KEY_FOR_BRANCH_003', 'http://**************:9300',
 '***********', 1433, 'examdb_center_lh', 'tj', 'jiarentijian_lh',
 '03', '王主任', '0755-11223344', '深圳市罗湖区嘉仁体检分院',
 'SYSTEM', '罗湖分院');

-- 7. 插入接口配置示例
INSERT INTO T_Organization_Interface_Config (
    cOrgCode, cInterfaceCode, cInterfaceName, cEnabled, cSyncMode, cBatchSize, cRetryTimes, cTimeout
) VALUES 
-- 主院配置
('JR001', '01', '体检信息传输', '1', 'AUTO', 50, 3, 30),
('JR001', '02', '申请项目字典', '1', 'AUTO', 100, 3, 60),
('JR001', '03', '科室结果传输', '1', 'AUTO', 30, 3, 45),
-- 分院配置
('JR002', '01', '体检信息传输', '1', 'AUTO', 30, 3, 30),
('JR002', '02', '申请项目字典', '1', 'MANUAL', 50, 3, 60),
('JR003', '01', '体检信息传输', '1', 'AUTO', 20, 3, 30);

PRINT '多机构支持数据库表创建完成！';
