-- 创建机构配置表
-- 用于存储多个体检机构的配置信息

-- 1. 机构配置主表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='T_Organization_Config' AND xtype='U')
BEGIN
    CREATE TABLE T_Organization_Config (
        id INT IDENTITY(1,1) PRIMARY KEY,                   -- 自增主键
        cOrgCode VARCHAR(20) NOT NULL UNIQUE,               -- 机构编码（唯一）
        cOrgName NVARCHAR(100) NOT NULL,                    -- 机构名称
        cOrgType VARCHAR(20) DEFAULT 'HOSPITAL',            -- 机构类型（HOSPITAL/CLINIC/CENTER）
        cStatus VARCHAR(1) DEFAULT '1',                     -- 状态（1=启用，0=停用）
        
        -- 天健云API配置
        cTianjianMicCode VARCHAR(50),                       -- 天健云机构代码（mic-code）
        cTianjianMiscId VARCHAR(50),                        -- 天健云系统ID（misc-id）
        cTianjianApiKey VARCHAR(200),                       -- 天健云API密钥
        cTianjianBaseUrl VARCHAR(200) DEFAULT 'http://**************:9300', -- 天健云API基础URL
        
        -- 数据库配置
        cDbHost VARCHAR(100),                               -- 数据库服务器
        cDbPort INT DEFAULT 1433,                           -- 数据库端口
        cDbName VARCHAR(50),                                -- 数据库名称
        cDbUser VARCHAR(50),                                -- 数据库用户名
        cDbPassword VARCHAR(200),                           -- 数据库密码
        cDbDriver VARCHAR(100) DEFAULT 'ODBC Driver 17 for SQL Server', -- 数据库驱动
        
        -- 业务配置
        cShopCode VARCHAR(20),                              -- 关联的门店编码
        cDefaultDeptCode VARCHAR(20),                       -- 默认科室编码
        cSyncEnabled VARCHAR(1) DEFAULT '1',                -- 是否启用同步（1=是，0=否）
        cSyncIntervals INT DEFAULT 60,                      -- 同步间隔（分钟）
        
        -- 联系信息
        cContactPerson NVARCHAR(50),                        -- 联系人
        cContactPhone VARCHAR(20),                          -- 联系电话
        cContactEmail VARCHAR(100),                         -- 联系邮箱
        cAddress NVARCHAR(200),                             -- 机构地址
        
        -- 系统字段
        dCreateTime DATETIME DEFAULT GETDATE(),             -- 创建时间
        dUpdateTime DATETIME DEFAULT GETDATE(),             -- 更新时间
        cCreateUser VARCHAR(20) DEFAULT 'SYSTEM',           -- 创建用户
        cUpdateUser VARCHAR(20) DEFAULT 'SYSTEM',           -- 更新用户
        cRemark NVARCHAR(500),                              -- 备注
        
        -- 索引
        INDEX IX_T_Organization_Config_OrgCode (cOrgCode),
        INDEX IX_T_Organization_Config_Status (cStatus)
    );
    
    PRINT '机构配置表 T_Organization_Config 创建成功';
END
ELSE
BEGIN
    PRINT '机构配置表 T_Organization_Config 已存在';
END

-- 2. 插入默认机构配置（如果表为空）
IF NOT EXISTS (SELECT * FROM T_Organization_Config)
BEGIN
    INSERT INTO T_Organization_Config (
        cOrgCode, cOrgName, cOrgType, cStatus,
        cTianjianMicCode, cTianjianMiscId, cTianjianApiKey, cTianjianBaseUrl,
        cDbHost, cDbPort, cDbName, cDbUser, cDbPassword,
        cShopCode, cContactPerson, cContactPhone, cAddress,
        cCreateUser, cRemark
    ) VALUES 
    -- 默认机构（当前系统）
    ('DEFAULT', '默认体检机构', 'CENTER', '1',
     'MIC1.001E', 'MISC1.00001A', '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM', 'http://**************:9300',
     '***********', 1433, 'examdb_center', 'tj', 'jiarentijian',
     '01', '系统管理员', '0755-12345678', '默认体检机构地址',
     'SYSTEM', '系统默认机构配置'),
    
    -- 示例机构1
    ('JR001', '嘉仁体检中心', 'CENTER', '1',
     'MIC1.001E', 'MISC1.00001A', '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM', 'http://**************:9300',
     '***********', 1433, 'examdb_center', 'tj', 'jiarentijian',
     '01', '张主任', '0755-12345678', '深圳市南山区嘉仁体检中心',
     'SYSTEM', '主要体检中心'),
    
    -- 示例机构2
    ('JR002', '嘉仁体检中心福田分院', 'BRANCH', '1',
     'MIC1.002E', 'MISC1.00002A', 'API_KEY_FOR_BRANCH_002', 'http://**************:9300',
     '***********', 1433, 'examdb_center_ft', 'tj', 'jiarentijian_ft',
     '02', '李主任', '0755-87654321', '深圳市福田区嘉仁体检分院',
     'SYSTEM', '福田分院');
    
    PRINT '默认机构配置数据插入成功';
END

-- 3. 创建机构接口配置表（可选，用于更细粒度的接口控制）
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='T_Organization_Interface_Config' AND xtype='U')
BEGIN
    CREATE TABLE T_Organization_Interface_Config (
        id INT IDENTITY(1,1) PRIMARY KEY,                  -- 自增主键
        cOrgCode VARCHAR(20) NOT NULL,                     -- 机构编码
        cInterfaceCode VARCHAR(20) NOT NULL,               -- 接口编码（01-21）
        cInterfaceName NVARCHAR(100),                      -- 接口名称
        cEnabled VARCHAR(1) DEFAULT '1',                   -- 是否启用（1=是，0=否）
        cSyncMode VARCHAR(10) DEFAULT 'AUTO',              -- 同步模式（AUTO=自动，MANUAL=手动）
        cBatchSize INT DEFAULT 50,                         -- 批量大小
        cRetryTimes INT DEFAULT 3,                         -- 重试次数
        cTimeout INT DEFAULT 30,                           -- 超时时间（秒）
        cCustomParams NVARCHAR(1000),                      -- 自定义参数（JSON格式）
        
        -- 系统字段
        dCreateTime DATETIME DEFAULT GETDATE(),
        dUpdateTime DATETIME DEFAULT GETDATE(),
        cCreateUser VARCHAR(20) DEFAULT 'SYSTEM',
        cUpdateUser VARCHAR(20) DEFAULT 'SYSTEM',
        
        -- 外键约束
        FOREIGN KEY (cOrgCode) REFERENCES T_Organization_Config(cOrgCode),
        -- 唯一约束
        UNIQUE (cOrgCode, cInterfaceCode),
        
        -- 索引
        INDEX IX_T_Organization_Interface_Config_OrgCode (cOrgCode)
    );
    
    PRINT '机构接口配置表 T_Organization_Interface_Config 创建成功';
END
ELSE
BEGIN
    PRINT '机构接口配置表 T_Organization_Interface_Config 已存在';
END

-- 4. 插入默认接口配置
IF NOT EXISTS (SELECT * FROM T_Organization_Interface_Config)
BEGIN
    -- 为默认机构插入接口配置
    INSERT INTO T_Organization_Interface_Config (
        cOrgCode, cInterfaceCode, cInterfaceName, cEnabled, cSyncMode, cBatchSize, cRetryTimes, cTimeout
    ) VALUES 
    ('DEFAULT', '01', '体检信息传输', '1', 'AUTO', 50, 3, 30),
    ('DEFAULT', '02', '申请项目字典', '1', 'AUTO', 100, 3, 60),
    ('DEFAULT', '03', '科室结果传输', '1', 'AUTO', 30, 3, 45),
    ('DEFAULT', '04', '医生信息传输', '1', 'AUTO', 50, 3, 30),
    ('DEFAULT', '05', '科室信息传输', '1', 'AUTO', 50, 3, 30),
    ('DEFAULT', '06', '字典信息传输', '1', 'AUTO', 100, 3, 30),
    
    -- 为JR001机构插入接口配置
    ('JR001', '01', '体检信息传输', '1', 'AUTO', 50, 3, 30),
    ('JR001', '02', '申请项目字典', '1', 'AUTO', 100, 3, 60),
    ('JR001', '03', '科室结果传输', '1', 'AUTO', 30, 3, 45),
    
    -- 为JR002机构插入接口配置
    ('JR002', '01', '体检信息传输', '1', 'AUTO', 30, 3, 30),
    ('JR002', '02', '申请项目字典', '1', 'MANUAL', 50, 3, 60);
    
    PRINT '默认接口配置数据插入成功';
END

-- 5. 创建机构同步日志表（用于记录同步历史）
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='T_Organization_Sync_Log' AND xtype='U')
BEGIN
    CREATE TABLE T_Organization_Sync_Log (
        id BIGINT IDENTITY(1,1) PRIMARY KEY,               -- 自增主键
        cOrgCode VARCHAR(20) NOT NULL,                     -- 机构编码
        cInterfaceCode VARCHAR(20),                        -- 接口编码
        cSyncType VARCHAR(20),                             -- 同步类型（FULL=全量，INCR=增量）
        cSyncStatus VARCHAR(10),                           -- 同步状态（SUCCESS=成功，FAILED=失败，RUNNING=运行中）
        
        -- 同步统计
        iTotalCount INT DEFAULT 0,                         -- 总记录数
        iSuccessCount INT DEFAULT 0,                       -- 成功记录数
        iFailedCount INT DEFAULT 0,                        -- 失败记录数
        
        -- 时间信息
        dStartTime DATETIME,                               -- 开始时间
        dEndTime DATETIME,                                 -- 结束时间
        iDuration INT,                                     -- 耗时（秒）
        
        -- 详细信息
        cErrorMessage NVARCHAR(2000),                      -- 错误信息
        cRequestData NVARCHAR(MAX),                        -- 请求数据
        cResponseData NVARCHAR(MAX),                       -- 响应数据
        cLogLevel VARCHAR(10) DEFAULT 'INFO',              -- 日志级别
        
        -- 系统字段
        dCreateTime DATETIME DEFAULT GETDATE(),
        cCreateUser VARCHAR(20) DEFAULT 'SYSTEM',
        
        -- 外键约束
        FOREIGN KEY (cOrgCode) REFERENCES T_Organization_Config(cOrgCode),
        
        -- 索引
        INDEX IX_T_Organization_Sync_Log_OrgCode_Time (cOrgCode, dCreateTime),
        INDEX IX_T_Organization_Sync_Log_Status (cSyncStatus)
    );
    
    PRINT '机构同步日志表 T_Organization_Sync_Log 创建成功';
END
ELSE
BEGIN
    PRINT '机构同步日志表 T_Organization_Sync_Log 已存在';
END

PRINT '机构配置相关表创建完成！';
PRINT '可以通过以下SQL查询机构配置：';
PRINT 'SELECT * FROM T_Organization_Config;';
PRINT 'SELECT * FROM T_Organization_Interface_Config;';
