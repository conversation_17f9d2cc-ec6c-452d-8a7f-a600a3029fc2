-- ========================================
-- 体检系统日常报表查询脚本
-- 文件：daily_report_queries.sql
-- 用途：包含常用的日常统计查询语句
-- 创建时间：2025-06-15
-- ========================================

-- 1. 今天体检人数统计
PRINT '1. 今天体检人数统计'
PRINT '===================='

-- 根据登记时间查询今天体检人数
SELECT COUNT(*) as 今天体检人数
FROM T_Register_Main 
WHERE CONVERT(date, dOperdate) = CONVERT(date, GETDATE());

-- 查询不同状态的体检人数 (cStatus: 0=已登记, 1=已确认, 2=已总检, 3=已打印)
SELECT 
    CASE cStatus 
        WHEN '0' THEN '已登记'
        WHEN '1' THEN '已确认'
        WHEN '2' THEN '已总检'
        WHEN '3' THEN '已打印'
        ELSE '未知状态'
    END as 体检状态,
    COUNT(*) as 人数,
    CONVERT(decimal(5,2), COUNT(*) * 100.0 / SUM(COUNT(*)) OVER()) as 占比百分比
FROM T_Register_Main 
WHERE CONVERT(date, dOperdate) = CONVERT(date, GETDATE())
GROUP BY cStatus
ORDER BY cStatus;

-- 查询今天体检流程进度汇总
SELECT 
    COUNT(CASE WHEN cStatus = '0' THEN 1 END) as 已登记人数,
    COUNT(CASE WHEN cStatus = '1' THEN 1 END) as 已确认人数,
    COUNT(CASE WHEN cStatus = '2' THEN 1 END) as 已总检人数,
    COUNT(CASE WHEN cStatus = '3' THEN 1 END) as 已打印人数,
    COUNT(*) as 总登记人数
FROM T_Register_Main 
WHERE CONVERT(date, dOperdate) = CONVERT(date, GETDATE());

PRINT ''
PRINT '2. 今天体检详细名单'
PRINT '=================='

-- 查看今天体检详细信息
SELECT 
    cClientCode as 客户编号,
    cName as 姓名,
    cSex as 性别,
    iAges as 年龄,
    dOperdate as 登记时间,
    dAffirmdate as 确认时间,
    cStatus as 状态,
    cOperName as 操作员
FROM T_Register_Main 
WHERE CONVERT(date, dOperdate) = CONVERT(date, GETDATE())
ORDER BY dOperdate DESC;

PRINT ''
PRINT '3. 体检完成情况统计'
PRINT '=================='

-- 查询今天已完成体检的人数（有检查结果的）
SELECT COUNT(DISTINCT r.cClientCode) as 今天完成体检人数
FROM T_Register_Main r
INNER JOIN T_Check_result_Main cr ON r.cClientCode = cr.cClientCode
WHERE CONVERT(date, r.dOperdate) = CONVERT(date, GETDATE())
  AND cr.dOperDate IS NOT NULL;

-- 查询今天体检进度
SELECT 
    '总登记人数' as 类型,
    COUNT(*) as 人数
FROM T_Register_Main 
WHERE CONVERT(date, dOperdate) = CONVERT(date, GETDATE())
  AND cStatus IS NOT NULL  -- 有效登记记录

UNION ALL

SELECT 
    '已完成体检' as 类型,
    COUNT(DISTINCT r.cClientCode) as 人数
FROM T_Register_Main r
INNER JOIN T_Check_result_Main cr ON r.cClientCode = cr.cClientCode
WHERE CONVERT(date, r.dOperdate) = CONVERT(date, GETDATE())
  AND cr.dOperDate IS NOT NULL;

PRINT ''
PRINT '4. 各科室检查情况'
PRINT '=================='

-- 查询今天各科室检查人数
SELECT 
    d.cName as 科室名称,
    COUNT(DISTINCT cr.cClientCode) as 检查人数,
    COUNT(cr.cMainCode) as 检查项目数
FROM T_Check_result_Main cr
INNER JOIN Code_Dept_Main d ON cr.cDeptCode = d.cCode
WHERE CONVERT(date, cr.dOperDate) = CONVERT(date, GETDATE())
GROUP BY d.cCode, d.cName
ORDER BY 检查人数 DESC;

PRINT ''
PRINT '5. 套餐使用情况'
PRINT '================'

-- 查询今天各套餐体检人数 (排除已取消的，即cStatus不为空)
SELECT 
    ISNULL(sm.cName, '无套餐') as 套餐名称,
    COUNT(*) as 使用人数,
    CONVERT(decimal(5,2), COUNT(*) * 100.0 / SUM(COUNT(*)) OVER()) as 占比百分比
FROM T_Register_Main r
LEFT JOIN Code_Suit_Master sm ON r.cSuitCode = sm.cCode
WHERE CONVERT(date, r.dOperdate) = CONVERT(date, GETDATE())
  AND r.cStatus IS NOT NULL  -- 有效登记记录
GROUP BY sm.cCode, sm.cName
ORDER BY 使用人数 DESC;

PRINT ''
PRINT '6. 异常结果统计'
PRINT '================'

-- 查询今天有异常结果的体检者
SELECT 
    r.cClientCode as 客户编号,
    r.cName as 姓名,
    r.cSex as 性别,
    COUNT(cr.cDetailCode) as 异常项目数
FROM T_Register_Main r
INNER JOIN T_Check_result cr ON r.cClientCode = cr.cClientCode
WHERE CONVERT(date, r.dOperdate) = CONVERT(date, GETDATE())
  AND cr.cAbnor = '1'  -- 异常标记
GROUP BY r.cClientCode, r.cName, r.cSex
HAVING COUNT(cr.cDetailCode) > 0
ORDER BY 异常项目数 DESC;

-- 异常率统计
SELECT 
    COUNT(DISTINCT CASE WHEN cr.cAbnor = '1' THEN r.cClientCode END) as 有异常人数,
    COUNT(DISTINCT r.cClientCode) as 总体检人数,
    CONVERT(decimal(5,2), 
        COUNT(DISTINCT CASE WHEN cr.cAbnor = '1' THEN r.cClientCode END) * 100.0 / 
        COUNT(DISTINCT r.cClientCode)) as 异常率百分比
FROM T_Register_Main r
LEFT JOIN T_Check_result cr ON r.cClientCode = cr.cClientCode
WHERE CONVERT(date, r.dOperdate) = CONVERT(date, GETDATE())
  AND r.cStatus IS NOT NULL;  -- 有效登记记录

PRINT ''
PRINT '7. 操作员工作量统计'
PRINT '=================='

-- 查询今天各操作员登记数量
SELECT 
    cOperName as 操作员,
    COUNT(*) as 登记数量,
    MIN(dOperdate) as 最早登记时间,
    MAX(dOperdate) as 最晚登记时间
FROM T_Register_Main 
WHERE CONVERT(date, dOperdate) = CONVERT(date, GETDATE())
  AND cStatus IS NOT NULL  -- 有效登记记录
GROUP BY cOperCode, cOperName
ORDER BY 登记数量 DESC;

PRINT ''
PRINT '8. 今天收入统计'
PRINT '================'

-- 总收入统计
SELECT 
    SUM(fFactTotal) as 今天实际收入,
    SUM(fTotal) as 今天应收金额,
    COUNT(*) as 收费人数,
    AVG(fFactTotal) as 平均收费,
    SUM(fTotal - fFactTotal) as 优惠减免金额
FROM T_Register_Main 
WHERE CONVERT(date, dOperdate) = CONVERT(date, GETDATE())
  AND fFactTotal IS NOT NULL
  AND fFactTotal > 0;

-- 按支付方式统计收入
SELECT 
    CASE pd.cPayMode
        WHEN '1' THEN '现金'
        WHEN '2' THEN '银行卡'
        WHEN '3' THEN '支付宝'
        WHEN '4' THEN '微信'
        ELSE '其他方式'
    END as 支付方式,
    SUM(pd.fPayMoney) as 收入金额,
    COUNT(*) as 笔数,
    CONVERT(decimal(5,2), SUM(pd.fPayMoney) * 100.0 / SUM(SUM(pd.fPayMoney)) OVER()) as 占比百分比
FROM T_Charge_Main cm
INNER JOIN T_Charge_PayDetail pd ON cm.cChargeNo = pd.cChargeNo
WHERE CONVERT(date, cm.dOperDate) = CONVERT(date, GETDATE())
GROUP BY pd.cPayMode
ORDER BY 收入金额 DESC;

-- 按时段统计收入
SELECT 
    CASE 
        WHEN DATEPART(HOUR, dOperdate) BETWEEN 8 AND 11 THEN '上午(8-11点)'
        WHEN DATEPART(HOUR, dOperdate) BETWEEN 12 AND 17 THEN '下午(12-17点)'
        WHEN DATEPART(HOUR, dOperdate) BETWEEN 18 AND 21 THEN '晚上(18-21点)'
        ELSE '其他时段'
    END as 时段,
    SUM(fFactTotal) as 收入金额,
    COUNT(*) as 人数
FROM T_Register_Main 
WHERE CONVERT(date, dOperdate) = CONVERT(date, GETDATE())
  AND fFactTotal IS NOT NULL
  AND fFactTotal > 0
GROUP BY CASE 
    WHEN DATEPART(HOUR, dOperdate) BETWEEN 8 AND 11 THEN '上午(8-11点)'
    WHEN DATEPART(HOUR, dOperdate) BETWEEN 12 AND 17 THEN '下午(12-17点)'
    WHEN DATEPART(HOUR, dOperdate) BETWEEN 18 AND 21 THEN '晚上(18-21点)'
    ELSE '其他时段'
END
ORDER BY 收入金额 DESC;

-- 收入明细前10名
SELECT TOP 10
    r.cClientCode as 客户编号,
    r.cName as 姓名,
    r.fTotal as 应收金额,
    r.fFactTotal as 实际收费,
    r.dOperdate as 登记时间,
    r.cOperName as 操作员
FROM T_Register_Main r
WHERE CONVERT(date, r.dOperdate) = CONVERT(date, GETDATE())
  AND r.fFactTotal IS NOT NULL
  AND r.fFactTotal > 0
ORDER BY r.fFactTotal DESC;

PRINT ''
PRINT '查询完成！' 