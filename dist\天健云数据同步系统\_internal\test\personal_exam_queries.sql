-- ============================================================================
-- 个人体检项目查询示例（修正版）
-- 文件：test/personal_exam_queries.sql
-- 修正日期：2025年1月
-- 修正内容：
-- 1. 套餐查询使用T_UnitsSuit_Master表，不是Code_Suit_Master
-- 2. 增加团检散客区分逻辑
-- 3. 根据dashboard_api.php的业务规则修正查询逻辑
-- ============================================================================

-- ==========================================================================
-- 1. 基础查询：根据客户编号查询体检项目（推荐方法）
-- ==========================================================================
-- 包含团检散客区分和正确的套餐信息
SELECT 
    r.cClientCode as 客户编号,
    r.cName as 姓名,
    r.cSex as 性别,
    r.cIdCard as 身份证号,
    r.dOperdate as 登记时间,
    -- 团检散客区分
    CASE 
        WHEN (r.cContractCode IS NULL OR r.cContractCode = '') THEN '散客体检'
        WHEN tc.cCheckType = '散客体检' THEN '散客体检'
        WHEN r.cContractCode IS NOT NULL AND r.cContractCode != '' AND tc.cCheckType != '散客体检' THEN '团检体检'
        ELSE '未知类型'
    END as 体检类型,
    -- 套餐信息（仅团检有套餐）
    CASE 
        WHEN (r.cContractCode IS NULL OR r.cContractCode = '' OR tc.cCheckType = '散客体检') THEN '无套餐（散客）'
        ELSE ISNULL(us.cName, '未设置套餐')
    END as 套餐名称,
    r.cSuitCode as 套餐编码,
    -- 体检项目信息
    im.cName as 检查项目,
    ip.cName as 价格项目名称,
    ip.fPrice as 项目价格,
    dd.cName as 科室名称,
    rd.cPriceCode as 价格编码,
    CASE r.cStatus 
        WHEN '0' THEN '已登记'
        WHEN '1' THEN '已确认'
        WHEN '2' THEN '已总检'
        WHEN '3' THEN '已打印'
        ELSE '未知状态'
    END as 体检状态
FROM T_Register_Main r
INNER JOIN T_Register_Detail rd ON r.cClientCode = rd.cClientCode
INNER JOIN code_Item_Price ip ON rd.cPriceCode = ip.cCode
INNER JOIN Code_Item_Main im ON ip.cMainCode = im.cCode
LEFT JOIN Code_Dept_Main dm ON im.cCode = dm.cMainCode
LEFT JOIN Code_Dept_dict dd ON dm.cDeptCode = dd.cCode
LEFT JOIN T_Contract tc ON r.cContractCode = tc.cCode
LEFT JOIN T_UnitsSuit_Master us ON r.cSuitCode = us.cSuitCode  -- 修正：使用正确的套餐表
WHERE r.cClientCode = '0000000001'  -- 替换为具体的客户编号
ORDER BY dd.cName, im.cName, ip.cName;

-- ==========================================================================
-- 2. 根据姓名和身份证查询（当不知道客户编号时）
-- ==========================================================================
SELECT 
    r.cClientCode as 客户编号,
    r.cName as 姓名,
    r.cIdCard as 身份证号,
    r.dOperdate as 登记时间,
    -- 团检散客区分
    CASE 
        WHEN (r.cContractCode IS NULL OR r.cContractCode = '') THEN '散客体检'
        WHEN tc.cCheckType = '散客体检' THEN '散客体检'
        WHEN r.cContractCode IS NOT NULL AND r.cContractCode != '' AND tc.cCheckType != '散客体检' THEN '团检体检'
        ELSE '未知类型'
    END as 体检类型,
    im.cName as 检查项目,
    ip.cName as 价格项目名称,
    ip.fPrice as 项目价格,
    dd.cName as 科室名称,
    rd.cPriceCode as 价格编码
FROM T_Register_Main r
INNER JOIN T_Register_Detail rd ON r.cClientCode = rd.cClientCode
INNER JOIN code_Item_Price ip ON rd.cPriceCode = ip.cCode
INNER JOIN Code_Item_Main im ON ip.cMainCode = im.cCode
LEFT JOIN Code_Dept_Main dm ON im.cCode = dm.cMainCode
LEFT JOIN Code_Dept_dict dd ON dm.cDeptCode = dd.cCode
LEFT JOIN T_Contract tc ON r.cContractCode = tc.cCode
WHERE r.cName = '张三'  -- 替换为具体姓名
  AND r.cIdCard = '123456789012345678'  -- 替换为具体身份证号
ORDER BY dd.cName, im.cName, ip.cName;

-- ==========================================================================
-- 3. 团检套餐内容 vs 实际安排对比查询（仅适用于团检用户）
-- ==========================================================================
-- 注意：散客没有套餐概念，此查询仅适用于团检用户
-- 使用T_UnitsSuit_Detail表而不是Code_Suit_Detail
SELECT 
    r.cClientCode as 客户编号,
    r.cName as 姓名,
    us.cName as 套餐名称,
    im.cName as 检查项目,
    dd.cName as 科室名称,
    usd.cMainCode as 套餐项目编码,
    CASE 
        WHEN rd.cClientCode IS NOT NULL THEN '已安排'
        ELSE '未安排'
    END as 安排状态
FROM T_Register_Main r
INNER JOIN T_Contract tc ON r.cContractCode = tc.cCode
INNER JOIN T_UnitsSuit_Master us ON r.cSuitCode = us.cSuitCode  -- 修正：使用正确的套餐表
INNER JOIN T_UnitsSuit_Detail usd ON us.cSuitCode = usd.cSuitCode  -- 修正：使用团检套餐明细表
INNER JOIN Code_Item_Main im ON usd.cMainCode = im.cCode
LEFT JOIN Code_Dept_Main dm ON im.cCode = dm.cMainCode
LEFT JOIN Code_Dept_dict dd ON dm.cDeptCode = dd.cCode
LEFT JOIN (
    T_Register_Detail rd 
    INNER JOIN code_Item_Price ip ON rd.cPriceCode = ip.cCode
) ON (r.cClientCode = rd.cClientCode AND im.cCode = ip.cMainCode)
WHERE r.cClientCode = '0000000001'  -- 替换为具体的客户编号
  AND r.cContractCode IS NOT NULL 
  AND r.cContractCode != ''
  AND tc.cCheckType != '散客体检'  -- 确保是团检用户
ORDER BY dd.cName, im.cName;

-- ==========================================================================
-- 4. 查询体检项目执行情况（最详细）
-- ==========================================================================
SELECT 
    r.cClientCode as 客户编号,
    r.cName as 姓名,
    -- 团检散客区分
    CASE 
        WHEN (r.cContractCode IS NULL OR r.cContractCode = '') THEN '散客体检'
        WHEN tc.cCheckType = '散客体检' THEN '散客体检'
        WHEN r.cContractCode IS NOT NULL AND r.cContractCode != '' AND tc.cCheckType != '散客体检' THEN '团检体检'
        ELSE '未知类型'
    END as 体检类型,
    im.cName as 检查项目,
    ip.cName as 价格项目名称,
    dd.cName as 科室名称,
    rd.cPriceCode as 价格编码,
    CASE 
        WHEN cr.cClientCode IS NOT NULL THEN '已检查'
        WHEN rd.cClientCode IS NOT NULL THEN '已安排未检'
        ELSE '未安排'
    END as 检查状态,
    cr.cResult as 检查结果,
    CASE cr.cAbnor 
        WHEN '1' THEN '异常'
        WHEN '0' THEN '正常'
        ELSE '未检'
    END as 是否异常,
    cr.dOperDate as 检查时间,
    cr.cDoctName as 检查医生,
    cr.cRemark as 医生备注
FROM T_Register_Main r
INNER JOIN T_Register_Detail rd ON r.cClientCode = rd.cClientCode
INNER JOIN code_Item_Price ip ON rd.cPriceCode = ip.cCode
INNER JOIN Code_Item_Main im ON ip.cMainCode = im.cCode
LEFT JOIN Code_Dept_Main dm ON im.cCode = dm.cMainCode
LEFT JOIN Code_Dept_dict dd ON dm.cDeptCode = dd.cCode
LEFT JOIN T_Contract tc ON r.cContractCode = tc.cCode
LEFT JOIN T_Check_result cr ON (r.cClientCode = cr.cClientCode AND im.cCode = cr.cMainCode)
WHERE r.cClientCode = '0000000001'  -- 替换为具体的客户编号
ORDER BY dd.cName, im.cName, ip.cName;

-- ==========================================================================
-- 5. 统计个人体检项目完成情况（汇总统计）
-- ==========================================================================
SELECT 
    r.cClientCode as 客户编号,
    r.cName as 姓名,
    r.dOperdate as 登记时间,
    -- 团检散客区分
    CASE 
        WHEN (r.cContractCode IS NULL OR r.cContractCode = '') THEN '散客体检'
        WHEN tc.cCheckType = '散客体检' THEN '散客体检'
        WHEN r.cContractCode IS NOT NULL AND r.cContractCode != '' AND tc.cCheckType != '散客体检' THEN '团检体检'
        ELSE '未知类型'
    END as 体检类型,
    COUNT(ip.cCode) as 总项目数,
    COUNT(cr.cMainCode) as 已完成项目数,
    COUNT(ip.cCode) - COUNT(cr.cMainCode) as 未完成项目数,
    CASE 
        WHEN COUNT(ip.cCode) > 0 
        THEN CONVERT(decimal(5,2), COUNT(cr.cMainCode) * 100.0 / COUNT(ip.cCode))
        ELSE 0 
    END as 完成百分比,
    COUNT(CASE WHEN cr.cAbnor = '1' THEN 1 END) as 异常项目数
FROM T_Register_Main r
LEFT JOIN T_Register_Detail rd ON r.cClientCode = rd.cClientCode
LEFT JOIN code_Item_Price ip ON rd.cPriceCode = ip.cCode
LEFT JOIN T_Contract tc ON r.cContractCode = tc.cCode
LEFT JOIN T_Check_result cr ON (r.cClientCode = cr.cClientCode AND ip.cMainCode = cr.cMainCode)
WHERE r.cClientCode = '0000000001'  -- 替换为具体的客户编号
GROUP BY r.cClientCode, r.cName, r.dOperdate, r.cContractCode, tc.cCheckType;

-- ==========================================================================
-- 6. 查询个人体检费用明细（区分团检散客）
-- ==========================================================================
SELECT 
    r.cClientCode as 客户编号,
    r.cName as 姓名,
    -- 团检散客区分
    CASE 
        WHEN (r.cContractCode IS NULL OR r.cContractCode = '') THEN '散客体检'
        WHEN tc.cCheckType = '散客体检' THEN '散客体检'
        WHEN r.cContractCode IS NOT NULL AND r.cContractCode != '' AND tc.cCheckType != '散客体检' THEN '团检体检'
        ELSE '未知类型'
    END as 体检类型,
    -- 套餐信息（仅团检有套餐）
    CASE 
        WHEN (r.cContractCode IS NULL OR r.cContractCode = '' OR tc.cCheckType = '散客体检') THEN '无套餐（散客）'
        ELSE ISNULL(us.cName, '未设置套餐')
    END as 套餐名称,
    im.cName as 检查项目,
    ip.cName as 价格项目名称,
    rd.cPriceCode as 价格编码,
    ip.fPrice as 项目价格,
    SUM(ip.fPrice) OVER() as 项目总价,
    r.fTotal as 应收金额,
    r.fFactTotal as 实际收费金额,
    r.fDiscount as 折扣金额,
    CASE 
        WHEN r.fFactTotal > 0 THEN '已缴费'
        ELSE '未缴费'
    END as 缴费状态
FROM T_Register_Main r
INNER JOIN T_Register_Detail rd ON r.cClientCode = rd.cClientCode
INNER JOIN code_Item_Price ip ON rd.cPriceCode = ip.cCode
INNER JOIN Code_Item_Main im ON ip.cMainCode = im.cCode
LEFT JOIN T_Contract tc ON r.cContractCode = tc.cCode
LEFT JOIN T_UnitsSuit_Master us ON r.cSuitCode = us.cSuitCode  -- 修正：使用正确的套餐表
WHERE r.cClientCode = '0000000001'  -- 替换为具体的客户编号
ORDER BY im.cName, ip.cName;

-- ==========================================================================
-- 7. 按科室分组查询个人体检项目
-- ==========================================================================
SELECT 
    r.cClientCode as 客户编号,
    r.cName as 姓名,
    dd.cName as 科室名称,
    COUNT(ip.cCode) as 科室项目数,
    COUNT(cr.cMainCode) as 已完成数,
    COUNT(ip.cCode) - COUNT(cr.cMainCode) as 未完成数,
    SUM(ip.fPrice) as 科室费用,
    STRING_AGG(ip.cName, ', ') as 检查项目列表
FROM T_Register_Main r
INNER JOIN T_Register_Detail rd ON r.cClientCode = rd.cClientCode
INNER JOIN code_Item_Price ip ON rd.cPriceCode = ip.cCode
INNER JOIN Code_Item_Main im ON ip.cMainCode = im.cCode
LEFT JOIN Code_Dept_Main dm ON im.cCode = dm.cMainCode
LEFT JOIN Code_Dept_dict dd ON dm.cDeptCode = dd.cCode
LEFT JOIN T_Check_result cr ON (r.cClientCode = cr.cClientCode AND im.cCode = cr.cMainCode)
WHERE r.cClientCode = '0000000001'  -- 替换为具体的客户编号
GROUP BY r.cClientCode, r.cName, dd.cCode, dd.cName
ORDER BY dd.cName;

-- ==========================================================================
-- 8. 查询个人异常检查结果
-- ==========================================================================
SELECT 
    r.cClientCode as 客户编号,
    r.cName as 姓名,
    im.cName as 检查项目,
    id.cName as 检查明细,
    cr.cResult as 检查结果,
    cr.cReference as 参考值,
    dd.cName as 科室名称,
    cr.dOperDate as 检查时间,
    cr.cDoctName as 检查医生,
    cr.cRemark as 医生备注
FROM T_Register_Main r
INNER JOIN T_Check_result cr ON r.cClientCode = cr.cClientCode
INNER JOIN Code_Item_Detail id ON cr.cDetailCode = id.cCode
INNER JOIN Code_Item_Main im ON id.cMainCode = im.cCode
LEFT JOIN Code_Dept_Main dm ON im.cCode = dm.cMainCode
LEFT JOIN Code_Dept_dict dd ON dm.cDeptCode = dd.cCode
WHERE r.cClientCode = '0000000001'  -- 替换为具体的客户编号
  AND cr.cAbnor = '1'  -- 只查询异常结果
ORDER BY dd.cName, im.cName, id.nIndex;

-- ==========================================================================
-- 9. 查询个人历史体检记录（多次体检对比）
-- ==========================================================================
SELECT 
    r.cClientCode as 客户编号,
    r.cName as 姓名,
    r.dOperdate as 登记时间,
    -- 团检散客区分
    CASE 
        WHEN (r.cContractCode IS NULL OR r.cContractCode = '') THEN '散客体检'
        WHEN tc.cCheckType = '散客体检' THEN '散客体检'
        WHEN r.cContractCode IS NOT NULL AND r.cContractCode != '' AND tc.cCheckType != '散客体检' THEN '团检体检'
        ELSE '未知类型'
    END as 体检类型,
    -- 套餐信息（仅团检有套餐）
    CASE 
        WHEN (r.cContractCode IS NULL OR r.cContractCode = '' OR tc.cCheckType = '散客体检') THEN '无套餐（散客）'
        ELSE ISNULL(us.cName, '未设置套餐')
    END as 套餐名称,
    COUNT(rd.cPriceCode) as 检查项目数,
    SUM(ip.fPrice) as 项目费用,
    r.fFactTotal as 实际收费,
    CASE r.cStatus 
        WHEN '0' THEN '已登记'
        WHEN '1' THEN '已确认'
        WHEN '2' THEN '已总检'
        WHEN '3' THEN '已打印'
        ELSE '未知状态'
    END as 体检状态
FROM T_Register_Main r
LEFT JOIN T_Register_Detail rd ON r.cClientCode = rd.cClientCode
LEFT JOIN code_Item_Price ip ON rd.cPriceCode = ip.cCode
LEFT JOIN T_Contract tc ON r.cContractCode = tc.cCode
LEFT JOIN T_UnitsSuit_Master us ON r.cSuitCode = us.cSuitCode  -- 修正：使用正确的套餐表
WHERE r.cClientCode = '0000000001'  -- 替换为具体的客户编号
GROUP BY r.cClientCode, r.cName, r.dOperdate, r.cContractCode, tc.cCheckType, us.cName, r.fFactTotal, r.cStatus
ORDER BY r.dOperdate DESC;

-- ==========================================================================
-- 10. 查询具体检查明细结果（明细级别）
-- ==========================================================================
SELECT 
    r.cClientCode as 客户编号,
    r.cName as 姓名,
    im.cName as 检查项目,
    id.cName as 检查明细,
    id.cUnit as 单位,
    cr.cResult as 检查结果,
    cr.cReference as 参考值,
    CASE cr.cAbnor 
        WHEN '1' THEN '异常'
        WHEN '0' THEN '正常'
        ELSE '未检'
    END as 是否异常,
    dd.cName as 科室名称,
    cr.dOperDate as 检查时间,
    cr.cDoctName as 检查医生,
    cr.cRemark as 医生备注
FROM T_Register_Main r
INNER JOIN T_Check_result cr ON r.cClientCode = cr.cClientCode
INNER JOIN Code_Item_Detail id ON cr.cDetailCode = id.cCode
INNER JOIN Code_Item_Main im ON id.cMainCode = im.cCode
LEFT JOIN Code_Dept_Main dm ON im.cCode = dm.cMainCode
LEFT JOIN Code_Dept_dict dd ON dm.cDeptCode = dd.cCode
WHERE r.cClientCode = '0000000001'  -- 替换为具体的客户编号
ORDER BY dd.cName, im.cName, id.nIndex;

-- ============================================================================
-- 说明：正确的表关联关系（修正版）
-- ============================================================================
-- 核心关联路径：
-- T_Register_Detail.cPriceCode → code_Item_Price.cCode
-- code_Item_Price.cMainCode → Code_Item_Main.cCode
-- Code_Item_Main.cCode → Code_Dept_Main.cMainCode (科室关联)
-- Code_Dept_Main.cDeptCode → Code_Dept_dict.cCode (科室名称)
-- Code_Item_Main.cCode → Code_Item_Detail.cMainCode (一对多，用于检查明细)
-- 
-- 套餐关联（仅团检）：
-- T_Register_Main.cSuitCode → T_UnitsSuit_Master.cSuitCode (套餐信息)
-- T_UnitsSuit_Master.cSuitCode → T_UnitsSuit_Detail.cSuitCode (套餐明细)
-- 
-- 团检散客区分：
-- T_Register_Main.cContractCode → T_Contract.cCode (合同信息)
-- 通过T_Contract.cCheckType字段区分团检和散客
-- ============================================================================ 