-- 查询张三的体检结果明细
-- 根据README.md中的核心关联关系编写

-- 方法1：基础体检结果明细查询
SELECT 
    rm.cClientCode as 客户编号,
    rm.cName as 姓名,
    rm.cSex as 性别,
    rm.dOperdate as 登记时间,
    cr.cMainCode as 检查项目编码,
    im.cName as 检查项目名称,
    cr.cDetailCode as 检查明细编码,
    id.cName as 检查明细名称,
    cr.cResult as 检查结果,
    cr.cResultDesc as 结果描述,
    cr.c<PERSON><PERSON><PERSON> as 是否异常,
    dd.cName as 检查科室,
    cr.dOperDate as 检查时间,
    cr.cDoctName as 检查医生
FROM T_Register_Main rm
INNER JOIN T_Check_result cr ON rm.cClientCode = cr.cClientCode
INNER JOIN Code_Item_Main im ON cr.cMainCode = im.cCode
LEFT JOIN Code_Item_Detail id ON cr.cDetailCode = id.cCode
LEFT JOIN Code_Dept_Main dm ON cr.cDeptCode = dm.cDeptCode
LEFT JOIN Code_Dept_dict dd ON dm.cDeptCode = dd.cCode
WHERE rm.cName = '张三'  -- 替换为具体姓名
ORDER BY cr.cMainCode, cr.cDetailCode;

-- 方法2：包含正常值范围的详细结果
SELECT 
    rm.cClientCode as 客户编号,
    rm.cName as 姓名,
    cr.cMainCode as 项目编码,
    im.cName as 项目名称,
    cr.cDetailCode as 明细编码,
    id.cName as 明细名称,
    cr.cResult as 检查结果,
    cr.cResultDesc as 结果描述,
    id.cConsult as 参考值范围,
    id.cUnit as 单位,
    CASE cr.cAbnor 
        WHEN '1' THEN '异常'
        WHEN '0' THEN '正常'
        ELSE '未知'
    END as 异常状态,
    dd.cName as 科室,
    cr.dOperDate as 检查时间
FROM T_Register_Main rm
INNER JOIN T_Check_result cr ON rm.cClientCode = cr.cClientCode
INNER JOIN Code_Item_Main im ON cr.cMainCode = im.cCode
LEFT JOIN Code_Item_Detail id ON cr.cDetailCode = id.cCode
LEFT JOIN Code_Dept_Main dm ON cr.cDeptCode = dm.cDeptCode
LEFT JOIN Code_Dept_dict dd ON dm.cDeptCode = dd.cCode
WHERE rm.cName = '张三'
ORDER BY dd.cName, im.cName, id.cName;

-- 方法3：按科室分组的体检结果汇总
SELECT 
    rm.cName as 姓名,
    dd.cName as 科室名称,
    COUNT(*) as 检查项目数,
    COUNT(CASE WHEN cr.cAbnor = '1' THEN 1 END) as 异常项目数,
    COUNT(CASE WHEN cr.cAbnor = '0' THEN 1 END) as 正常项目数,
    MAX(cr.dOperDate) as 最后检查时间
FROM T_Register_Main rm
INNER JOIN T_Check_result cr ON rm.cClientCode = cr.cClientCode
LEFT JOIN Code_Dept_Main dm ON cr.cDeptCode = dm.cDeptCode
LEFT JOIN Code_Dept_dict dd ON dm.cDeptCode = dd.cCode
WHERE rm.cName = '张三'
GROUP BY rm.cClientCode, rm.cName, dd.cCode, dd.cName
ORDER BY dd.cName;

-- 方法4：仅查询异常结果
SELECT 
    rm.cClientCode as 客户编号,
    rm.cName as 姓名,
    im.cName as 检查项目,
    id.cName as 检查明细,
    cr.cResult as 检查结果,
    cr.cResultDesc as 结果描述,
    id.cConsult as 参考值范围,
    dd.cName as 科室,
    cr.dOperDate as 检查时间,
    cr.cDoctName as 检查医生
FROM T_Register_Main rm
INNER JOIN T_Check_result cr ON rm.cClientCode = cr.cClientCode
INNER JOIN Code_Item_Main im ON cr.cMainCode = im.cCode
LEFT JOIN Code_Item_Detail id ON cr.cDetailCode = id.cCode
LEFT JOIN Code_Dept_Main dm ON cr.cDeptCode = dm.cDeptCode
LEFT JOIN Code_Dept_dict dd ON dm.cDeptCode = dd.cCode
WHERE rm.cName = '张三'
  AND cr.cAbnor = '1'  -- 仅显示异常结果
ORDER BY cr.dOperDate DESC;

-- 方法5：完整体检报告（包含总检结论）
SELECT 
    rm.cClientCode as 客户编号,
    rm.cName as 姓名,
    rm.cSex as 性别,
    rm.iAges as 年龄,
    rm.dOperdate as 登记日期,
    -- 检查结果
    im.cName as 检查项目,
    id.cName as 检查明细,
    cr.cResult as 检查结果,
    cr.cResultDesc as 结果描述,
    CASE cr.cAbnor WHEN '1' THEN '异常' ELSE '正常' END as 异常状态,
    dd.cName as 检查科室,
    -- 总检结论信息
    dr.cDiag as 总检结论,
    dr.cDiagDesc as 总检描述,
    dr.dOperDate as 总检时间,
    dr.cDoctName as 总检医生
FROM T_Register_Main rm
INNER JOIN T_Check_result cr ON rm.cClientCode = cr.cClientCode
INNER JOIN Code_Item_Main im ON cr.cMainCode = im.cCode
LEFT JOIN Code_Item_Detail id ON cr.cDetailCode = id.cCode
LEFT JOIN Code_Dept_Main dm ON cr.cDeptCode = dm.cDeptCode
LEFT JOIN Code_Dept_dict dd ON dm.cDeptCode = dd.cCode
LEFT JOIN T_Diag_result dr ON rm.cClientCode = dr.cClientCode
WHERE rm.cName = '张三'
ORDER BY cr.cMainCode, cr.cDetailCode;

-- 方法6：通过客户编号查询（如果知道具体编号）
SELECT 
    rm.cClientCode as 客户编号,
    rm.cName as 姓名,
    cr.cMainCode as 项目编码,
    im.cName as 项目名称,
    cr.cDetailCode as 明细编码,
    id.cName as 明细名称,
    cr.cResult as 检查结果,
    cr.cResultDesc as 结果描述,
    cr.cAbnor as 是否异常,
    dd.cName as 科室,
    cr.dOperDate as 检查时间
FROM T_Register_Main rm
INNER JOIN T_Check_result cr ON rm.cClientCode = cr.cClientCode
INNER JOIN Code_Item_Main im ON cr.cMainCode = im.cCode
LEFT JOIN Code_Item_Detail id ON cr.cDetailCode = id.cCode
LEFT JOIN Code_Dept_Main dm ON cr.cDeptCode = dm.cDeptCode
LEFT JOIN Code_Dept_dict dd ON dm.cDeptCode = dd.cCode
WHERE rm.cClientCode = '0000000001'  -- 替换为具体的客户编号
ORDER BY cr.cMainCode, cr.cDetailCode;

-- 方法7：查询客户异常疾病汇总
SELECT 
    rm.cClientCode as 客户编号,
    rm.cName as 姓名,
    cri.cIllnessCode as 疾病编码,
    cri.cIllnessName as 疾病名称,
    cri.cGrade as 疾病等级,
    cri.dOperDate as 诊断时间,
    cri.cDoctName as 诊断医生
FROM T_Register_Main rm
INNER JOIN T_Check_Result_Illness cri ON rm.cClientCode = cri.cClientCode
WHERE rm.cName = '张三'
ORDER BY cri.cGrade DESC, cri.dOperDate DESC;

-- 方法8：统计客户异常疾病数量
SELECT 
    rm.cClientCode as 客户编号,
    rm.cName as 姓名,
    COUNT(*) as 异常疾病总数,
    COUNT(CASE WHEN cri.cGrade = '1' THEN 1 END) as 严重疾病数,
    COUNT(CASE WHEN cri.cGrade = '2' THEN 1 END) as 中等疾病数,
    COUNT(CASE WHEN cri.cGrade = '3' THEN 1 END) as 轻微疾病数
FROM T_Register_Main rm
INNER JOIN T_Check_Result_Illness cri ON rm.cClientCode = cri.cClientCode
WHERE rm.cName = '张三'
GROUP BY rm.cClientCode, rm.cName; 