-- 查询08机构今天体检人数
-- 根据不同的机构字段含义提供多种查询方式

-- 方法1：按登记机构统计（在08机构登记的人数）
SELECT COUNT(*) as 今天在08机构登记人数
FROM T_Register_Main rm
WHERE CONVERT(date, rm.dOperdate) = CONVERT(date, GETDATE())
  AND rm.cShopCode = '08'
  AND rm.cStatus > 0;  -- 有效状态

-- 方法2：按确认到检机构统计（确认到08机构体检的人数）
SELECT COUNT(*) as 今天确认到08机构体检人数
FROM T_Register_Main rm
WHERE CONVERT(date, rm.dAffirmdate) = CONVERT(date, GETDATE())
  AND rm.cAffirmShopCode = '08'
  AND rm.cStatus > 0;

-- 方法3：按收费机构统计（在08机构收费的人数）
SELECT COUNT(DISTINCT cm.cClientCode) as 今天在08机构收费人数
FROM T_Charge_Main cm
WHERE CONVERT(date, cm.dOperDate) = CONVERT(date, GETDATE())
  AND cm.cShopCode = '08'
  AND cm.fFactTotal > 0;

-- 方法4：详细统计08机构今天体检情况
SELECT 
    '08' as 机构编码,
    si.cShopName as 机构名称,
    COUNT(*) as 登记总人数,
    COUNT(CASE WHEN rm.cSex = '1' THEN 1 END) as 男性人数,
    COUNT(CASE WHEN rm.cSex = '2' THEN 1 END) as 女性人数,
    COUNT(CASE WHEN rm.cStatus = '0' THEN 1 END) as 已登记,
    COUNT(CASE WHEN rm.cStatus = '1' THEN 1 END) as 已确认,
    COUNT(CASE WHEN rm.cStatus = '2' THEN 1 END) as 已总检,
    COUNT(CASE WHEN rm.cStatus = '3' THEN 1 END) as 已打印
FROM T_Register_Main rm
LEFT JOIN T_SysLinkShopInfo si ON rm.cShopCode = si.cShopCode
WHERE CONVERT(date, rm.dOperdate) = CONVERT(date, GETDATE())
  AND rm.cShopCode = '08'
  AND rm.cStatus > 0
GROUP BY si.cShopCode, si.cShopName;

-- 方法5：08机构团检散客分类统计
SELECT 
    CASE 
        WHEN (rm.cContractCode IS NULL OR rm.cContractCode = '' OR tc.cCheckType = '散客体检') 
        THEN '散客体检'
        ELSE '团检体检'
    END as 体检类型,
    COUNT(*) as 人数,
    CONVERT(decimal(5,2), COUNT(*) * 100.0 / SUM(COUNT(*)) OVER()) as 占比百分比
FROM T_Register_Main rm
LEFT JOIN T_Contract tc ON rm.cContractCode = tc.cCode
WHERE CONVERT(date, rm.dOperdate) = CONVERT(date, GETDATE())
  AND rm.cShopCode = '08'
  AND rm.cStatus > 0
GROUP BY 
    CASE 
        WHEN (rm.cContractCode IS NULL OR rm.cContractCode = '' OR tc.cCheckType = '散客体检') 
        THEN '散客体检'
        ELSE '团检体检'
    END
ORDER BY 人数 DESC;

-- 方法6：08机构收费统计
SELECT 
    '08' as 收费机构,
    COUNT(DISTINCT cm.cClientCode) as 收费人数,
    SUM(cm.fFactTotal) as 总收入,
    AVG(cm.fFactTotal) as 平均收费,
    COUNT(cm.cChargeNo) as 收费笔数
FROM T_Charge_Main cm
WHERE CONVERT(date, cm.dOperDate) = CONVERT(date, GETDATE())
  AND cm.cShopCode = '08'
  AND cm.fFactTotal > 0;

-- 方法7：跨机构流动统计（登记在其他机构但到08机构体检的人数）
SELECT 
    rm.cShopCode as 登记机构,
    rm.cAffirmShopCode as 到检机构,
    COUNT(*) as 人数
FROM T_Register_Main rm
WHERE CONVERT(date, rm.dAffirmdate) = CONVERT(date, GETDATE())
  AND rm.cAffirmShopCode = '08'  -- 到08机构体检
  AND rm.cStatus > 0
GROUP BY rm.cShopCode, rm.cAffirmShopCode
ORDER BY 人数 DESC; 