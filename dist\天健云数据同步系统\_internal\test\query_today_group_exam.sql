-- 查询今天团检人数
-- 根据README.md中的团检散客区分规则编写

-- 方法1：基础团检人数查询
SELECT COUNT(*) as 今天团检人数
FROM T_Register_Main rm
LEFT JOIN T_Contract tc ON rm.cContractCode = tc.cCode
WHERE CONVERT(date, rm.dOperdate) = CONVERT(date, GETDATE())
  AND rm.cContractCode IS NOT NULL 
  AND rm.cContractCode != ''
  AND (tc.cCheckType IS NULL OR tc.cCheckType != '散客体检')
  AND rm.cStatus > 0;  -- 有效状态

-- 方法2：详细团检信息查询
SELECT 
    COUNT(*) as 团检总人数,
    COUNT(CASE WHEN rm.cSex = '1' THEN 1 END) as 男性人数,
    COUNT(CASE WHEN rm.cSex = '2' THEN 1 END) as 女性人数,
    COUNT(CASE WHEN rm.cStatus = '0' THEN 1 END) as 已登记,
    COUNT(CASE WHEN rm.cStatus = '1' THEN 1 END) as 已确认,
    COUNT(CASE WHEN rm.cStatus = '2' THEN 1 END) as 已总检,
    COUNT(CASE WHEN rm.cStatus = '3' THEN 1 END) as 已打印
FROM T_Register_Main rm
LEFT JOIN T_Contract tc ON rm.cContractCode = tc.cCode
WHERE CONVERT(date, rm.dOperdate) = CONVERT(date, GETDATE())
  AND rm.cContractCode IS NOT NULL 
  AND rm.cContractCode != ''
  AND (tc.cCheckType IS NULL OR tc.cCheckType != '散客体检')
  AND rm.cStatus > 0;

-- 方法3：按单位分组的团检人数
SELECT 
    tc.cName as 合同名称,
    tc.cUnitsCode as 单位编码,
    COUNT(*) as 体检人数,
    us.cName as 套餐名称
FROM T_Register_Main rm
INNER JOIN T_Contract tc ON rm.cContractCode = tc.cCode
LEFT JOIN T_UnitsSuit_Master us ON rm.cSuitCode = us.cSuitCode
WHERE CONVERT(date, rm.dOperdate) = CONVERT(date, GETDATE())
  AND tc.cCheckType != '散客体检'
  AND rm.cStatus > 0
GROUP BY tc.cCode, tc.cName, tc.cUnitsCode, us.cName
ORDER BY 体检人数 DESC;

-- 方法4：团检与散客对比统计
SELECT 
    CASE 
        WHEN (rm.cContractCode IS NULL OR rm.cContractCode = '' OR tc.cCheckType = '散客体检') 
        THEN '散客体检'
        ELSE '团检体检'
    END as 体检类型,
    COUNT(*) as 人数,
    CONVERT(decimal(5,2), COUNT(*) * 100.0 / SUM(COUNT(*)) OVER()) as 占比百分比
FROM T_Register_Main rm
LEFT JOIN T_Contract tc ON rm.cContractCode = tc.cCode
WHERE CONVERT(date, rm.dOperdate) = CONVERT(date, GETDATE())
  AND rm.cStatus > 0
GROUP BY 
    CASE 
        WHEN (rm.cContractCode IS NULL OR rm.cContractCode = '' OR tc.cCheckType = '散客体检') 
        THEN '散客体检'
        ELSE '团检体检'
    END
ORDER BY 人数 DESC;

-- 方法5：团检收费统计（关联收费信息）
SELECT 
    COUNT(DISTINCT rm.cClientCode) as 团检人数,
    SUM(cm.fFactTotal) as 团检总收入,
    AVG(cm.fFactTotal) as 平均收费,
    COUNT(cm.cChargeNo) as 收费笔数
FROM T_Register_Main rm
INNER JOIN T_Contract tc ON rm.cContractCode = tc.cCode
LEFT JOIN T_Charge_Main cm ON rm.cClientCode = cm.cClientCode
WHERE CONVERT(date, rm.dOperdate) = CONVERT(date, GETDATE())
  AND tc.cCheckType != '散客体检'
  AND rm.cStatus > 0; 