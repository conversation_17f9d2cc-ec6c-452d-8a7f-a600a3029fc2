-- ============================================================================
-- 查询指定单位指定年份后的体检客户（修正版）
-- 文件：test/unit_query_by_year.sql
-- 说明：查询单位编号02000003在2020年以后体检的客户有哪些
-- 修正：通过T_Charge_Main表获取正确的收费信息
-- ============================================================================

-- 查询单位编号02000003在2020年以后体检的客户列表（含收费信息）
SELECT 
    rm.cClientCode as 客户编号,
    rm.cName as 姓名,
    rm.cSex as 性别,
    rm.cIdCard as 身份证号,
    rm.dOperdate as 登记时间,
    rm.dAffirmdate as 确认时间,
    CASE rm.cStatus 
        WHEN '0' THEN '已登记'
        WHEN '1' THEN '已确认'
        WHEN '2' THEN '已总检'
        WHEN '3' THEN '已打印'
        ELSE '未知状态'
    END as 体检状态,
    tc.cUnitsCode as 单位编码,
    tc.cName as 单位名称,
    us.cName as 套餐名称,
    YEAR(rm.dOperdate) as 体检年份,
    -- 收费信息（从T_Charge_Main表获取）
    cm.fTotal as 应收金额,
    cm.fFactTotal as 实际收费,
    cm.cPayMode as 支付方式编码,
    cd.cName as 支付方式名称,
    cm.dOperDate as 收费时间,
    cm.cOpername as 收费员,
    CASE 
        WHEN cm.fFactTotal > 0 THEN '已缴费'
        WHEN cm.cClientCode IS NULL THEN '未收费'
        ELSE '未缴费'
    END as 缴费状态
FROM T_Register_Main rm
INNER JOIN T_Contract tc ON rm.cContractCode = tc.cCode
LEFT JOIN T_UnitsSuit_Master us ON rm.cSuitCode = us.cSuitCode
LEFT JOIN T_Charge_Main cm ON rm.cClientCode = cm.cClientCode
LEFT JOIN Code_Comm_Dict cd ON (cm.cPayMode = cd.cCode AND cd.iNameCode = 8)
WHERE tc.cUnitsCode = '02000003'  -- 单位编号
  AND YEAR(rm.dOperdate) >= 2020  -- 2020年以后（包含2020年）
ORDER BY rm.dOperdate DESC;

-- 统计数量和收费汇总
SELECT 
    COUNT(*) as 客户总数,
    COUNT(DISTINCT rm.cClientCode) as 去重客户数,
    MIN(YEAR(rm.dOperdate)) as 最早年份,
    MAX(YEAR(rm.dOperdate)) as 最晚年份,
    -- 收费统计（从T_Charge_Main表）
    SUM(cm.fTotal) as 应收总额,
    SUM(cm.fFactTotal) as 实收总额,
    SUM(cm.fTotal - cm.fFactTotal) as 折扣总额,
    COUNT(CASE WHEN cm.fFactTotal > 0 THEN 1 END) as 已缴费人数,
    COUNT(CASE WHEN cm.cClientCode IS NULL OR cm.fFactTotal = 0 THEN 1 END) as 未缴费人数,
    AVG(cm.fFactTotal) as 平均收费
FROM T_Register_Main rm
INNER JOIN T_Contract tc ON rm.cContractCode = tc.cCode
LEFT JOIN T_Charge_Main cm ON rm.cClientCode = cm.cClientCode
WHERE tc.cUnitsCode = '02000003'
  AND YEAR(rm.dOperdate) >= 2020;

-- 按年份分组统计（含收费）
SELECT 
    YEAR(rm.dOperdate) as 体检年份,
    COUNT(*) as 体检人次,
    COUNT(DISTINCT rm.cClientCode) as 去重客户数,
    -- 年度收费统计（从T_Charge_Main表）
    SUM(cm.fTotal) as 年度应收,
    SUM(cm.fFactTotal) as 年度实收,
    SUM(cm.fTotal - cm.fFactTotal) as 年度折扣,
    COUNT(CASE WHEN cm.fFactTotal > 0 THEN 1 END) as 已缴费人数,
    AVG(cm.fFactTotal) as 年度平均收费
FROM T_Register_Main rm
INNER JOIN T_Contract tc ON rm.cContractCode = tc.cCode
LEFT JOIN T_Charge_Main cm ON rm.cClientCode = cm.cClientCode
WHERE tc.cUnitsCode = '02000003'
  AND YEAR(rm.dOperdate) >= 2020
GROUP BY YEAR(rm.dOperdate)
ORDER BY 体检年份 DESC;

-- 按支付方式分组统计
SELECT 
    cd.cName as 支付方式,
    cm.cPayMode as 支付方式编码,
    COUNT(*) as 使用人次,
    SUM(cm.fFactTotal) as 收费金额,
    AVG(cm.fFactTotal) as 平均金额
FROM T_Register_Main rm
INNER JOIN T_Contract tc ON rm.cContractCode = tc.cCode
INNER JOIN T_Charge_Main cm ON rm.cClientCode = cm.cClientCode
LEFT JOIN Code_Comm_Dict cd ON (cm.cPayMode = cd.cCode AND cd.iNameCode = 8)
WHERE tc.cUnitsCode = '02000003'
  AND YEAR(rm.dOperdate) >= 2020
  AND cm.fFactTotal > 0  -- 只统计已缴费的
GROUP BY cm.cPayMode, cd.cName
ORDER BY 收费金额 DESC; 