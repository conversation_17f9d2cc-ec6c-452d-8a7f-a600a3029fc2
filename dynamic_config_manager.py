#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态配置管理器
从数据库配置表中获取连接信息，替代硬编码配置
"""

import os
import logging
from typing import Dict, Optional, List
from dataclasses import dataclass
from center_organization_service import get_center_organization_service, CenterOrganizationConfig


@dataclass
class DatabaseConnectionConfig:
    """数据库连接配置"""
    host: str
    port: int
    database: str
    username: str
    password: str
    driver: str = "ODBC Driver 17 for SQL Server"
    
    def get_connection_string(self) -> str:
        """获取连接字符串"""
        return (
            f"DRIVER={{{self.driver}}};"
            f"SERVER={self.host},{self.port};"
            f"DATABASE={self.database};"
            f"UID={self.username};"
            f"PWD={self.password};"
            f"TrustServerCertificate=yes"
        )


class DynamicConfigManager:
    """动态配置管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._org_service = None
        self._config_cache = {}
        self._cache_timeout = 300  # 5分钟缓存
        
    @property
    def org_service(self):
        """获取机构服务（延迟初始化）"""
        if self._org_service is None:
            self._org_service = get_center_organization_service()
        return self._org_service
    
    def get_organization_config(self, org_code: str) -> Optional[CenterOrganizationConfig]:
        """获取机构配置"""
        try:
            # 检查缓存
            cache_key = f"org_{org_code}"
            if cache_key in self._config_cache:
                return self._config_cache[cache_key]
            
            # 从数据库获取
            org_config = self.org_service.get_organization_by_code(org_code)
            
            if org_config:
                # 缓存配置
                self._config_cache[cache_key] = org_config
                self.logger.info(f"已加载机构配置: {org_code} - {org_config.org_name}")
                return org_config
            else:
                self.logger.warning(f"未找到机构配置: {org_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"获取机构配置失败 {org_code}: {e}")
            return None
    
    def get_database_config(self, org_code: str) -> Optional[DatabaseConnectionConfig]:
        """获取数据库连接配置"""
        org_config = self.get_organization_config(org_code)
        
        if not org_config:
            return None
        
        return DatabaseConnectionConfig(
            host=org_config.db_host,
            port=org_config.db_port,
            database=org_config.db_name,
            username=org_config.db_user,
            password=org_config.db_password,
            driver=org_config.db_driver or "ODBC Driver 17 for SQL Server"
        )
    
    def get_database_connection_string(self, org_code: str) -> Optional[str]:
        """获取数据库连接字符串"""
        db_config = self.get_database_config(org_code)
        
        if db_config:
            return db_config.get_connection_string()
        else:
            return None
    
    def get_all_organizations(self) -> List[CenterOrganizationConfig]:
        """获取所有机构配置"""
        try:
            return self.org_service.get_all_organizations()
        except Exception as e:
            self.logger.error(f"获取所有机构配置失败: {e}")
            return []
    
    def get_default_organization(self) -> Optional[CenterOrganizationConfig]:
        """获取默认机构配置（第一个启用的机构）"""
        try:
            organizations = self.get_all_organizations()
            
            # 查找第一个启用的机构
            for org in organizations:
                if org.status == '1':  # 启用状态
                    self.logger.info(f"使用默认机构: {org.org_code} - {org.org_name}")
                    return org
            
            # 如果没有启用的机构，返回第一个
            if organizations:
                org = organizations[0]
                self.logger.warning(f"没有启用的机构，使用第一个: {org.org_code} - {org.org_name}")
                return org
            
            self.logger.error("没有找到任何机构配置")
            return None
            
        except Exception as e:
            self.logger.error(f"获取默认机构配置失败: {e}")
            return None
    
    def get_fallback_config(self) -> DatabaseConnectionConfig:
        """获取回退配置（原来的硬编码配置）"""
        return DatabaseConnectionConfig(
            host=os.environ.get('INTERFACE_DB_HOST', '************'),
            port=int(os.environ.get('INTERFACE_DB_PORT', 1433)),
            database=os.environ.get('INTERFACE_DB_NAME', 'Examdb'),
            username=os.environ.get('INTERFACE_DB_USER', 'znzj'),
            password=os.environ.get('INTERFACE_DB_PASSWORD', '2025znzj/888'),
            driver=os.environ.get('INTERFACE_DB_DRIVER', 'ODBC Driver 17 for SQL Server')
        )
    
    def clear_cache(self):
        """清理缓存"""
        self._config_cache.clear()
        self.logger.info("配置缓存已清理")


# 全局实例 - 延迟初始化
_dynamic_config_manager = None


def get_dynamic_config_manager() -> DynamicConfigManager:
    """获取动态配置管理器实例（延迟初始化）"""
    global _dynamic_config_manager
    if _dynamic_config_manager is None:
        _dynamic_config_manager = DynamicConfigManager()
    return _dynamic_config_manager


def get_database_connection_string_by_org(org_code: str) -> str:
    """根据机构编码获取数据库连接字符串"""
    manager = get_dynamic_config_manager()
    
    # 尝试从配置表获取
    connection_string = manager.get_database_connection_string(org_code)
    
    if connection_string:
        return connection_string
    else:
        # 回退到默认配置
        logging.warning(f"机构 {org_code} 配置不存在，使用回退配置")
        fallback_config = manager.get_fallback_config()
        return fallback_config.get_connection_string()


def get_default_database_connection_string() -> str:
    """获取默认数据库连接字符串"""
    manager = get_dynamic_config_manager()
    
    # 尝试获取默认机构配置
    default_org = manager.get_default_organization()
    
    if default_org:
        return manager.get_database_connection_string(default_org.org_code)
    else:
        # 回退到硬编码配置
        logging.warning("没有找到默认机构配置，使用回退配置")
        fallback_config = manager.get_fallback_config()
        return fallback_config.get_connection_string()
