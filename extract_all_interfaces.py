#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从接口文档中提取所有接口URL和方法
"""

import shutil
import os
import re
import json

def extract_all_interfaces():
    try:
        # 源文件路径
        src_path = r"D:\python\福能AI对接\接口文档.html"
        # 目标路径（项目内临时文件）
        dst_path = r"D:\python\福能AI对接\temp_api_doc.html"
        
        # 复制文件
        shutil.copy2(src_path, dst_path)
        print(f"文件已复制到: {dst_path}")
        
        # 读取整个文件内容
        with open(dst_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 查找所有接口的名称、URL和方法
        # 匹配接口对象
        interface_pattern = r'\{[^{}]*"type"\s*:\s*"api"[^{}]*"name"\s*:\s*"[^"]*"[^{}]*"url"\s*:\s*"[^"]*"[^{}]*"method"\s*:\s*"[^"]*"[^{}]*\}'
        interfaces = re.findall(interface_pattern, content)
        
        print(f"总共找到 {len(interfaces)} 个接口")
        
        # 查找18号接口
        interface_18 = None
        for interface_str in interfaces:
            name_match = re.search(r'"name"\s*:\s*"([^"]*)"', interface_str)
            if name_match and ("18" in name_match.group(1) or "医生信息" in name_match.group(1)):
                interface_18 = interface_str
                break
        
        if interface_18:
            print("\n找到18号接口:")
            # 提取详细信息
            name_match = re.search(r'"name"\s*:\s*"([^"]*)"', interface_18)
            url_match = re.search(r'"url"\s*:\s*"([^"]*)"', interface_18)
            method_match = re.search(r'"method"\s*:\s*"([^"]*)"', interface_18)
            
            if name_match:
                print(f"  名称: {name_match.group(1)}")
            if url_match:
                print(f"  URL: {url_match.group(1)}")
            if method_match:
                print(f"  方法: {method_match.group(1)}")
        else:
            print("\n未找到明确的18号接口信息")
            
        # 显示所有接口的简要信息
        print("\n所有接口列表:")
        for i, interface_str in enumerate(interfaces[:20]):  # 只显示前20个
            name_match = re.search(r'"name"\s*:\s*"([^"]*)"', interface_str)
            url_match = re.search(r'"url"\s*:\s*"([^"]*)"', interface_str)
            if name_match and url_match:
                name = name_match.group(1)
                url = url_match.group(1)
                print(f"  {i+1}. {name} -> {url}")
                
    except Exception as e:
        print(f"处理文件时出错: {e}")
    finally:
        # 清理临时文件
        if os.path.exists(dst_path):
            try:
                os.remove(dst_path)
            except:
                pass

if __name__ == "__main__":
    extract_all_interfaces()