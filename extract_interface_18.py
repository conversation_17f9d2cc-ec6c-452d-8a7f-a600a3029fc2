#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取接口文档中的文本内容并查找18号接口信息
"""

from bs4 import BeautifulSoup
import re

def extract_interface_18_info():
    try:
        with open(r"D:\python\福能AI对接\接口文档.html", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(content, 'html.parser')
        
        # 查找所有包含"18"的文本
        text_elements = soup.find_all(string=re.compile(r'18'))
        
        print("在接口文档中找到包含'18'的文本:")
        print("=" * 50)
        for element in text_elements:
            # 只打印包含接口相关信息的文本
            if any(keyword in element for keyword in ['接口', 'getDoctor', '医生']):
                print(element.strip())
                print("-" * 30)
                
    except Exception as e:
        print(f"处理文件时出错: {e}")

if __name__ == "__main__":
    extract_interface_18_info()