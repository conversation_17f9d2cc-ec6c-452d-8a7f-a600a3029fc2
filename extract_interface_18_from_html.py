#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用正则表达式从HTML中提取18号接口的完整信息
"""

import re
import json

def extract_interface_18_from_html():
    try:
        with open(r"D:\python\福能AI对接\接口文档.html", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 查找所有的API接口定义（JSON格式）
        # 在Apipost导出的HTML中，接口信息通常存储在apipostData变量中
        apipost_data_match = re.search(r'apipostData\s*=\s*({.*?});', content, re.DOTALL)
        if apipost_data_match:
            json_str = apipost_data_match.group(1)
            # 尝试修复可能的转义问题
            json_str = re.sub(r'\\([^"\\/bfnrt])', r'\\\\\1', json_str)
            
            try:
                data = json.loads(json_str)
                
                # 递归搜索所有接口，找到名称包含"18"或"医生信息"的接口
                def find_interface_18(obj):
                    if isinstance(obj, dict):
                        # 检查是否是接口对象
                        if obj.get("type") == "api":
                            name = obj.get("name", "")
                            if "18" in name or "医生信息" in name:
                                print("=" * 60)
                                print(f"接口名称: {name}")
                                print(f"URL: {obj.get('url', '')}")
                                print(f"方法: {obj.get('method', '')}")
                                
                                # 请求参数
                                if "request" in obj:
                                    request = obj["request"]
                                    if "params" in request and request["params"]:
                                        print("\n请求参数:")
                                        for param in request["params"]:
                                            print(f"  {param.get('name', '')}: {param.get('type', '')} "
                                                  f"- {param.get('required', False) and '必填' or '选填'} "
                                                  f"- {param.get('description', '')}")
                                    
                                    # 请求头
                                    if "headers" in request and request["headers"]:
                                        print("\n请求头:")
                                        for header in request["headers"]:
                                            print(f"  {header.get('name', '')}: {header.get('value', '')}")
                                
                                # 响应示例
                                if "response" in obj and obj["response"]:
                                    response = obj["response"][0] if isinstance(obj["response"], list) else obj["response"]
                                    if "body" in response:
                                        print("\n响应示例:")
                                        print(response["body"])
                                
                                print("=" * 60)
                        
                        # 递归搜索
                        for value in obj.values():
                            find_interface_18(value)
                    elif isinstance(obj, list):
                        for item in obj:
                            find_interface_18(item)
                
                find_interface_18(data)
            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {e}")
        else:
            print("未找到apipostData数据")
                
    except Exception as e:
        print(f"处理文件时出错: {e}")

if __name__ == "__main__":
    extract_interface_18_from_html()