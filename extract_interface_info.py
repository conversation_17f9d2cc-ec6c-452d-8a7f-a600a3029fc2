#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接处理接口文档，提取接口信息
"""

import re

def extract_interface_info():
    try:
        # 直接读取源文件
        with open(r"D:\python\福能AI对接\接口文档.html", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 查找所有接口的名称、URL和方法
        # 匹配接口对象
        interface_pattern = r'\{[^{}]*"type"\s*:\s*"api"[^{}]*"name"\s*:\s*"[^"]*"[^{}]*"url"\s*:\s*"[^"]*"[^{}]*"method"\s*:\s*"[^"]*"[^{}]*\}'
        interfaces = re.findall(interface_pattern, content)
        
        print(f"总共找到 {len(interfaces)} 个接口")
        
        # 查找18号接口
        interface_18 = None
        for interface_str in interfaces:
            name_match = re.search(r'"name"\s*:\s*"([^"]*)"', interface_str)
            if name_match and ("18" in name_match.group(1) or "医生信息" in name_match.group(1)):
                interface_18 = interface_str
                break
        
        if interface_18:
            print("\n找到18号接口:")
            # 提取详细信息
            name_match = re.search(r'"name"\s*:\s*"([^"]*)"', interface_18)
            url_match = re.search(r'"url"\s*:\s*"([^"]*)"', interface_18)
            method_match = re.search(r'"method"\s*:\s*"([^"]*)"', interface_18)
            
            if name_match:
                print(f"  名称: {name_match.group(1)}")
            if url_match:
                print(f"  URL: {url_match.group(1)}")
            if method_match:
                print(f"  方法: {method_match.group(1)}")
        else:
            print("\n未找到明确的18号接口信息")
            
        # 显示包含"医生"或编号18的接口
        print("\n相关接口列表:")
        for i, interface_str in enumerate(interfaces):
            name_match = re.search(r'"name"\s*:\s*"([^"]*)"', interface_str)
            url_match = re.search(r'"url"\s*:\s*"([^"]*)"', interface_str)
            method_match = re.search(r'"method"\s*:\s*"([^"]*)"', interface_str)
            
            if name_match:
                name = name_match.group(1)
                url = url_match.group(1) if url_match else ""
                method = method_match.group(1) if method_match else ""
                
                # 筛选相关接口
                if "18" in name or "医生信息" in name or "getDoctor" in name or "getDoctor" in url:
                    print(f"  {name} -> {url} ({method})")
                
    except Exception as e:
        print(f"处理文件时出错: {e}")

if __name__ == "__main__":
    extract_interface_info()