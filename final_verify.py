#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证接口修改完成状态
"""

def verify_modifications():
    """验证修改完成"""
    print("验证19、20、21号接口修改完成")
    print("=" * 50)
    
    interfaces = [
        'interface_19_getDeptInfo.py',
        'interface_20_getPersonalOrders.py', 
        'interface_21_getAbnormalNotice.py'
    ]
    
    results = []
    
    for filename in interfaces:
        print(f"\n[检查] {filename}")
        print("-" * 30)
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            checks = []
            
            # 检查1：路由方法
            if 'def get_' in content and 'def get_' in content:
                print("+ 添加了路由接口方法")
                checks.append(True)
            else:
                print("- 缺少路由接口方法")
                checks.append(False)
            
            # 检查2：TOP限制移除
            if '# if limit:' in content or 'SELECT TOP' not in content:
                print("+ TOP限制已移除")
                checks.append(True)
            else:
                print("- 仍有TOP限制")
                checks.append(False)
            
            # 检查3：数据库查询改进
            if '从数据库查询' in content:
                print("+ 改为数据库查询")
                checks.append(True)
            else:
                print("- 查询逻辑未改")
                checks.append(False)
            
            # 检查4：错误处理
            if 'except Exception as e:' in content:
                print("+ 有异常处理")
                checks.append(True)
            else:
                print("- 缺少异常处理") 
                checks.append(False)
            
            success = all(checks)
            results.append((filename, success))
            print(f"结果: {'通过' if success else '部分完成'}")
            
        except Exception as e:
            print(f"检查失败: {str(e)}")
            results.append((filename, False))
    
    # 检查GUI
    print(f"\n[检查] gui_main.py")
    print("-" * 30)
    
    try:
        with open('gui_main.py', 'r', encoding='utf-8') as f:
            gui_content = f.read()
        
        gui_checks = []
        
        # GUI日志改进
        if '收到查询科室信息请求 -' in gui_content:
            print("+ 19号接口日志已改进")
            gui_checks.append(True)
        else:
            print("- 19号接口日志未改进")
            gui_checks.append(False)
            
        if '收到查询个人开单请求 -' in gui_content:
            print("+ 20号接口日志已改进")
            gui_checks.append(True)
        else:
            print("- 20号接口日志未改进")
            gui_checks.append(False)
            
        if '收到查询异常通知请求 -' in gui_content:
            print("+ 21号接口日志已改进")
            gui_checks.append(True)
        else:
            print("- 21号接口日志未改进")
            gui_checks.append(False)
        
        gui_success = all(gui_checks)
        results.append(('gui_main.py', gui_success))
        print(f"结果: {'通过' if gui_success else '部分完成'}")
        
    except Exception as e:
        print(f"GUI检查失败: {str(e)}")
        results.append(('gui_main.py', False))
    
    # 总结
    print(f"\n" + "=" * 50)
    print("[总结]")
    
    passed = 0
    total = len(results)
    
    for filename, success in results:
        status = "OK" if success else "PARTIAL"
        print(f"{filename}: {status}")
        if success:
            passed += 1
    
    print(f"\n总体进度: {passed}/{total}")
    
    if passed == total:
        print("\n[完成] 所有接口修改已完成!")
        print("- 修复了NoneType错误")
        print("- 移除了数量限制")
        print("- 改进了GUI日志显示")
        print("- 统一了接口实现模式")
    else:
        print(f"\n[进行中] {passed}/{total} 个文件修改完成")
    
    return passed == total

if __name__ == "__main__":
    verify_modifications()