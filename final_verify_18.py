#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证18号接口限制移除
"""

import json
import requests

def final_verification():
    """最终验证"""
    print("最终验证18号接口限制移除")
    print("=" * 50)
    
    base_url = "http://localhost:5007"
    
    # 测试不同的查询场景
    test_cases = [
        {"name": "查询所有医生", "data": {"id": "", "shopcode": "08"}},
        {"name": "查询特定医生", "data": {"id": "DOCTOR001", "shopcode": "08"}},
    ]
    
    try:
        # 检查服务器状态
        try:
            health_response = requests.get(f"{base_url}/health", timeout=5)
            if health_response.status_code != 200:
                raise Exception()
        except:
            print("[错误] GUI服务器未运行，无法进行完整测试")
            print("[建议] 请运行: python gui_main.py")
            print("[建议] 然后重新运行此测试")
            return False
        
        print("[信息] GUI服务器正在运行")
        print("[重要] 如果GUI服务器在修改代码前就已启动，请重启以加载新代码")
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{'-' * 30}")
            print(f"[测试 {i}] {test_case['name']}")
            print(f"请求: {json.dumps(test_case['data'], ensure_ascii=False)}")
            
            try:
                response = requests.post(
                    f"{base_url}/dx/inter/getDoctorInfo",
                    json=test_case['data'],
                    headers={'Content-Type': 'application/json'},
                    timeout=15
                )
                
                if response.status_code == 200:
                    result = response.json()
                    code = result.get('code', -1)
                    msg = result.get('msg', '')
                    data_count = len(result.get('data', []))
                    
                    print(f"返回码: {code}")
                    print(f"消息: {msg}")
                    print(f"数据条数: {data_count}")
                    
                    if code == 0:
                        if data_count > 0:
                            # 分析返回数量
                            if data_count == 100:
                                print(f"[分析] 返回了正好100条记录")
                                print(f"[可能] 1. 数据库中确实只有100条医生记录")
                                print(f"[可能] 2. 仍有其他限制或缓存")
                                print(f"[可能] 3. GUI服务器需要重启")
                            else:
                                print(f"[成功] 返回了 {data_count} 条记录，限制已移除")
                        else:
                            print(f"[信息] 查询成功但无数据")
                    else:
                        print(f"[失败] 查询失败: {msg}")
                        
                else:
                    print(f"[错误] HTTP状态码: {response.status_code}")
                    
            except Exception as e:
                print(f"[异常] 请求异常: {str(e)}")
        
        print(f"\n{'-' * 30}")
        print("[验证总结]")
        print("代码修改已完成:")
        print("  ✓ 移除了 query_doctor_info 中的 limit=100")
        print("  ✓ 移除了 get_doctor_info_from_db 中的 TOP 限制")
        print("  ✓ 注释了 SQL 中的 TOP 限制逻辑")
        print("  ✓ 移除了测试函数中的 limit 参数")
        
        print(f"\n如果仍显示100条记录，请尝试:")
        print("  1. 重启GUI服务器: 关闭 gui_main.py 然后重新运行")
        print("  2. 检查数据库中的实际医生记录数量")
        print("  3. 确认没有其他地方的限制")
        
        return True
        
    except Exception as e:
        print(f"[异常] 验证过程异常: {str(e)}")
        return False

if __name__ == "__main__":
    final_verification()