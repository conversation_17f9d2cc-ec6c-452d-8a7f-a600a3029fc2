#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
读取整个HTML文件并查找所有接口信息
"""

def search_all_interfaces():
    try:
        with open(r"D:\python\福能AI对接\接口文档.html", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 查找所有接口的定义（根据HTML结构推测）
        import re
        
        # 查找可能的接口标题
        titles = re.findall(r'<h[1-4][^>]*>([^<]+接口[^<]*)</h[1-4]>', content)
        if titles:
            print("找到以下接口标题:")
            for title in titles:
                print(f"  - {title}")
            print()
        
        # 查找可能的接口名称和URL
        interfaces = re.findall(r'"name"\s*:\s*"([^"]*接口[^"]*)"', content)
        if interfaces:
            print("找到以下接口名称:")
            for interface in interfaces:
                print(f"  - {interface}")
            print()
            
        # 查找包含数字的接口名称
        numbered_interfaces = re.findall(r'"name"\s*:\s*"(\d+[^"]*)"', content)
        if numbered_interfaces:
            print("找到以下编号接口:")
            for interface in numbered_interfaces:
                print(f"  - {interface}")
            print()
            
        # 特别查找18号接口
        interface_18 = re.findall(r'"name"\s*:\s*"([^"]*18[^"]*)"', content)
        if interface_18:
            print("找到18号接口:")
            for interface in interface_18:
                print(f"  - {interface}")
            print()
            
        # 查找getDoctor相关接口
        get_doctor = re.findall(r'"name"\s*:\s*"([^"]*getDoctor[^"]*)"', content)
        if get_doctor:
            print("找到getDoctor接口:")
            for interface in get_doctor:
                print(f"  - {interface}")
            print()
                
    except Exception as e:
        print(f"处理文件时出错: {e}")

if __name__ == "__main__":
    search_all_interfaces()