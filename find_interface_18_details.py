#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门查找18号接口的详细信息
"""

def search_interface_18_details():
    try:
        with open(r"D:\python\福能AI对接\接口文档.html", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 查找18号接口的完整信息
        import re
        
        # 找到18号接口的起始位置
        start_match = re.search(r'18\.ѯҽϢӿ', content)
        if start_match:
            start_pos = start_match.start()
            # 查找接口信息的结束位置（下一个接口的开始或者其他明显的分隔符）
            end_pos = len(content)
            next_interface_match = re.search(r'\d+\.[^\.]+接口', content[start_pos+10:])
            if next_interface_match:
                end_pos = start_pos + 10 + next_interface_match.start()
            
            # 提取18号接口的相关内容
            interface_content = content[start_pos:end_pos]
            
            # 查找URL
            url_match = re.search(r'"url"\s*:\s*"([^"]*)"', interface_content)
            if url_match:
                print(f"18号接口URL: {url_match.group(1)}")
            
            # 查找请求方法
            method_match = re.search(r'"method"\s*:\s*"([^"]*)"', interface_content)
            if method_match:
                print(f"请求方法: {method_match.group(1)}")
            
            # 查找请求参数
            params_section = re.search(r'"params"\s*:\s*$(.*?)$', interface_content, re.DOTALL)
            if params_section:
                params_content = params_section.group(1)
                # 简单提取参数名
                param_names = re.findall(r'"name"\s*:\s*"([^"]*)"', params_content)
                if param_names:
                    print("请求参数:")
                    for param in param_names:
                        print(f"  - {param}")
            
            # 查找请求头
            headers_section = re.search(r'"headers"\s*:\s*$(.*?)$', interface_content, re.DOTALL)
            if headers_section:
                headers_content = headers_section.group(1)
                # 简单提取头信息
                header_names = re.findall(r'"name"\s*:\s*"([^"]*)"', headers_content)
                if header_names:
                    print("请求头:")
                    for header in header_names:
                        print(f"  - {header}")
            
            # 查找响应示例
            response_match = re.search(r'"body"\s*:\s*"({.*?})"', interface_content)
            if response_match:
                print("响应示例:")
                print(response_match.group(1))
        else:
            print("未找到18号接口信息")
                
    except Exception as e:
        print(f"处理文件时出错: {e}")

if __name__ == "__main__":
    search_interface_18_details()