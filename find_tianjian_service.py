#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找TianjianInterfaceService类的定义位置
"""

def find_tianjian_interface_service_class():
    try:
        with open(r"D:\python\福能AI对接\gui_main.py", "r", encoding="utf-8") as f:
            lines = f.readlines()
        
        # 查找TianjianInterfaceService类定义
        for i, line in enumerate(lines):
            if "class TianjianInterfaceService" in line:
                print(f"在第 {i+1} 行找到类定义:")
                print(line.strip())
                
                # 查找__init__方法
                for j in range(i+1, min(i+50, len(lines))):
                    if "def __init__" in lines[j]:
                        print(f"在第 {j+1} 行找到__init__方法:")
                        print(lines[j].strip())
                        break
                        
                # 查找start_service方法
                for j in range(i+1, min(i+100, len(lines))):
                    if "def start_service" in lines[j]:
                        print(f"在第 {j+1} 行找到start_service方法:")
                        print(lines[j].strip())
                        break
                        
                break
                
    except Exception as e:
        print(f"读取文件时出错: {e}")

if __name__ == "__main__":
    find_tianjian_interface_service_class()