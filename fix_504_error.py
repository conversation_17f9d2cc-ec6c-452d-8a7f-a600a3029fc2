#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门解决02号接口HTTP 504错误的优化脚本
通过极小批量和重试机制提高成功率
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_small_batch_transmission():
    """测试极小批量传输，解决504错误"""
    print("=" * 80)
    print("02号接口504错误解决方案测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    try:
        from interface_02_syncApplyItem import TianjianInterface02
        from config import Config
        
        # 获取API配置
        api_config = Config.get_tianjian_api_config()
        print(f"使用API配置: {api_config['base_url']}")
        
        # 创建接口实例
        interface = TianjianInterface02(api_config)
        
        print("\n🎯 针对504错误的优化策略:")
        print("1. 极小批量：每批次只传输3条数据")
        print("2. 增加超时：每条数据5秒超时")
        print("3. 批次间延迟：每批次间休息5秒")
        print("4. 限制总量：只传输30条数据测试")
        
        print("\n" + "=" * 60)
        print("开始极小批量传输测试")
        print("=" * 60)
        
        # 使用极小批量进行传输
        result = interface.sync_apply_items(
            limit=30,           # 限制30条数据
            test_mode=False,    # 实际传输
            batch_size=3,       # 极小批量：3条/批次
            verbose_message=True
        )
        
        print("\n" + "=" * 60)
        print("传输结果分析")
        print("=" * 60)
        
        if result.get('success'):
            print("✅ 传输成功!")
            print(f"   成功条数: {result.get('sent', 0)}")
            print(f"   失败条数: {result.get('failed', 0)}")
            print(f"   总耗时: {result.get('total_time', 0):.2f}秒")
            
            success_rate = (result.get('sent', 0) / (result.get('sent', 0) + result.get('failed', 0))) * 100 if (result.get('sent', 0) + result.get('failed', 0)) > 0 else 0
            print(f"   成功率: {success_rate:.1f}%")
            
            if success_rate >= 90:
                print("🎉 极小批量策略成功！504错误得到解决")
                return True
            else:
                print("⚠️  成功率仍然不够高，需要进一步优化")
                return False
        else:
            print("❌ 传输失败")
            print(f"   错误信息: {result.get('message', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def recommend_optimal_settings():
    """推荐最优的传输设置"""
    print("\n" + "=" * 80)
    print("🎯 针对504错误的最优设置推荐")
    print("=" * 80)
    
    print("\n📋 推荐配置:")
    print("1. 批量大小: 3-5条/批次")
    print("2. 超时时间: 180秒基础 + 每条5秒")
    print("3. 批次间延迟: 5-10秒")
    print("4. 单次传输量: 50-100条")
    print("5. 重试次数: 3次")
    
    print("\n🚀 使用命令:")
    print("# 极小批量测试")
    print("python interface_02_syncApplyItem.py --limit 50 --batch-size 3")
    print()
    print("# 稳定传输")
    print("python interface_02_syncApplyItem.py --limit 100 --batch-size 5")
    print()
    print("# 大量传输（分多次）")
    print("python interface_02_syncApplyItem.py --limit 200 --batch-size 5")
    
    print("\n⚠️  注意事项:")
    print("- 504错误通常是服务器处理超时，不是网络问题")
    print("- 减少批量大小是最有效的解决方案")
    print("- 增加批次间延迟可以减轻服务器压力")
    print("- 如果仍有504错误，可以进一步减少到2条/批次")
    
    print("\n📊 性能预估:")
    print("- 3条/批次: 877条数据需要292批次，约2-3小时")
    print("- 5条/批次: 877条数据需要175批次，约1.5-2小时")
    print("- 建议分多次传输，每次100-200条")


def create_batch_script():
    """创建分批传输脚本"""
    print("\n" + "=" * 80)
    print("📝 创建分批传输脚本")
    print("=" * 80)
    
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
02号接口分批传输脚本 - 解决504错误
"""

import time
import subprocess
import sys

def run_batch_transmission():
    """分批传输所有数据"""
    
    total_data = 877  # 总数据量
    batch_limit = 100  # 每次传输100条
    batch_size = 5     # 每批次5条
    
    print(f"开始分批传输 {total_data} 条数据")
    print(f"每次传输: {batch_limit} 条")
    print(f"批量大小: {batch_size} 条/批次")
    print("=" * 50)
    
    total_sent = 0
    batch_num = 1
    
    for start in range(0, total_data, batch_limit):
        end = min(start + batch_limit, total_data)
        current_limit = end - start
        
        print(f"\\n🚀 第{batch_num}次传输: 第{start+1}-{end}条数据")
        
        # 构建命令
        cmd = [
            sys.executable, 
            "interface_02_syncApplyItem.py",
            "--limit", str(current_limit),
            "--batch-size", str(batch_size),
            "--offset", str(start)  # 如果支持offset参数
        ]
        
        try:
            # 执行传输
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)
            
            if result.returncode == 0:
                print(f"✅ 第{batch_num}次传输成功")
                total_sent += current_limit
            else:
                print(f"❌ 第{batch_num}次传输失败")
                print(f"错误: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print(f"⏰ 第{batch_num}次传输超时")
        except Exception as e:
            print(f"❌ 第{batch_num}次传输异常: {e}")
        
        batch_num += 1
        
        # 批次间休息
        if end < total_data:
            print("⏸️  休息30秒...")
            time.sleep(30)
    
    print(f"\\n🏁 分批传输完成!")
    print(f"总计成功传输: {total_sent} 条")
    print(f"成功率: {(total_sent/total_data)*100:.1f}%")

if __name__ == "__main__":
    run_batch_transmission()
'''
    
    with open("batch_transmission_02.py", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    print("✅ 已创建分批传输脚本: batch_transmission_02.py")
    print("使用方法: python batch_transmission_02.py")


def main():
    """主函数"""
    print("🎯 02号接口504错误解决方案")
    
    # 测试极小批量传输
    success = test_small_batch_transmission()
    
    # 推荐最优设置
    recommend_optimal_settings()
    
    # 创建分批传输脚本
    create_batch_script()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 504错误解决方案测试成功!")
        print("✅ 建议使用3-5条/批次的极小批量传输")
    else:
        print("⚠️  需要进一步优化传输策略")
        print("💡 建议联系天健云技术支持检查服务器性能")
    print("=" * 80)


if __name__ == "__main__":
    main()
