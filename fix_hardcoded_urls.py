#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复硬编码的天健云API地址
将硬编码地址替换为配置管理器调用
"""

import os
import re
import glob
from typing import List, <PERSON><PERSON>


def get_files_to_fix() -> List[str]:
    """获取需要修复的文件列表"""
    files_to_fix = []
    
    # Python接口文件
    files_to_fix.extend(glob.glob("interface_*.py"))
    
    # 服务文件
    service_files = [
        "center_organization_service.py",
        "organization_config_service.py", 
        "multi_org_config.py",
        "organization_config_window.py",
        "setup_wizard.py"
    ]
    files_to_fix.extend([f for f in service_files if os.path.exists(f)])
    
    return files_to_fix


def fix_hardcoded_urls_in_file(file_path: str) -> Tuple[bool, int]:
    """修复单个文件中的硬编码URL"""
    if not os.path.exists(file_path):
        return False, 0
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = 0
        
        # 模式1: 'base_url': 'http://203.83.237.114:9300'
        pattern1 = r"'base_url'\s*:\s*'http://203\.83\.237\.114:9300'"
        replacement1 = "'base_url': get_tianjian_base_url()"
        
        if re.search(pattern1, content):
            content = re.sub(pattern1, replacement1, content)
            changes_made += 1
            
            # 添加import语句（如果不存在）
            if 'from api_config_manager import get_tianjian_base_url' not in content:
                # 在其他import语句之后添加
                import_pattern = r'(from [^\n]+ import [^\n]+\n)'
                if re.search(import_pattern, content):
                    content = re.sub(
                        r'((?:from [^\n]+ import [^\n]+\n)+)',
                        r'\1from api_config_manager import get_tianjian_base_url\n',
                        content,
                        count=1
                    )
                else:
                    # 如果没有其他import，在文件开头添加
                    content = 'from api_config_manager import get_tianjian_base_url\n' + content
        
        # 模式2: "base_url": "http://203.83.237.114:9300"
        pattern2 = r'"base_url"\s*:\s*"http://203\.83\.237\.114:9300"'
        replacement2 = '"base_url": get_tianjian_base_url()'
        
        if re.search(pattern2, content):
            content = re.sub(pattern2, replacement2, content)
            changes_made += 1
            
            if 'from api_config_manager import get_tianjian_base_url' not in content:
                content = 'from api_config_manager import get_tianjian_base_url\n' + content
        
        # 模式3: self.org_config.get('tianjian_base_url', 'http://203.83.237.114:9300')
        pattern3 = r"self\.org_config\.get\('tianjian_base_url',\s*'http://203\.83\.237\.114:9300'\)"
        replacement3 = "self.org_config.get('tianjian_base_url', get_tianjian_base_url())"
        
        if re.search(pattern3, content):
            content = re.sub(pattern3, replacement3, content)
            changes_made += 1
            
            if 'from api_config_manager import get_tianjian_base_url' not in content:
                content = 'from api_config_manager import get_tianjian_base_url\n' + content
        
        # 模式4: 直接的URL字符串
        pattern4 = r'"http://203\.83\.237\.114:9300"'
        if re.search(pattern4, content):
            # 只替换在配置或API相关上下文中的URL
            if any(keyword in content for keyword in ['base_url', 'api', 'tianjian', 'config']):
                content = re.sub(pattern4, 'get_tianjian_base_url()', content)
                changes_made += 1
                
                if 'from api_config_manager import get_tianjian_base_url' not in content:
                    content = 'from api_config_manager import get_tianjian_base_url\n' + content
        
        # 保存文件（如果有变化）
        if changes_made > 0:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True, changes_made
        
        return False, 0
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return False, 0


def create_backup_files(files: List[str]):
    """创建文件备份"""
    backup_dir = "backup_before_url_fix"
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    for file_path in files:
        if os.path.exists(file_path):
            backup_path = os.path.join(backup_dir, os.path.basename(file_path))
            try:
                with open(file_path, 'r', encoding='utf-8') as src:
                    with open(backup_path, 'w', encoding='utf-8') as dst:
                        dst.write(src.read())
                print(f"[OK] 已备份: {file_path} -> {backup_path}")
            except Exception as e:
                print(f"[ERROR] 备份失败: {file_path} - {e}")


def main():
    """主函数"""
    print("=== 修复硬编码天健云API地址 ===\n")
    
    # 获取需要修复的文件
    files_to_fix = get_files_to_fix()
    print(f"找到 {len(files_to_fix)} 个需要检查的文件\n")
    
    # 创建备份
    print("创建文件备份...")
    create_backup_files(files_to_fix)
    print()
    
    # 修复每个文件
    total_changes = 0
    fixed_files = 0
    
    for file_path in files_to_fix:
        print(f"检查文件: {file_path}")
        success, changes = fix_hardcoded_urls_in_file(file_path)
        
        if success:
            print(f"  [OK] 修复完成，共 {changes} 处更改")
            fixed_files += 1
            total_changes += changes
        else:
            print(f"  [-] 无需修复")
    
    print(f"\n=== 修复完成 ===")
    print(f"修复文件数: {fixed_files}")
    print(f"总更改数: {total_changes}")
    
    if total_changes > 0:
        print(f"\n[SUCCESS] 硬编码URL已替换为配置管理器调用")
        print(f"[SUCCESS] 文件备份已保存在 backup_before_url_fix/ 目录")
        print(f"[SUCCESS] 现在API地址可以通过环境变量或配置文件统一管理")
    else:
        print(f"\n[-] 没有发现需要修复的硬编码URL")


if __name__ == '__main__':
    main()