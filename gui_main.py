#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云数据同步系统 - 图形用户界面
使用PySide6开发的现代化管理界面
"""

import sys
import os
import json
import threading
import time
from datetime import datetime
from typing import Dict, List, Any, Optional

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QTabWidget, QLabel, QPushButton, QTextEdit, QTableWidget, 
    QTableWidgetItem, QProgressBar, QGroupBox, QGridLayout,
    QComboBox, QSpinBox, QCheckBox, QLineEdit, QSplitter,
    QStatusBar, QMenuBar, QMessageBox, QDialog, QDialogButtonBox,
    QTreeWidget, QTreeWidgetItem, QHeaderView
)
from PySide6.QtCore import (
    Qt, QTimer, QThread, QObject, Signal, QPropertyAnimation,
    QEasingCurve, QRect, QSize
)
from PySide6.QtGui import QFont, QIcon, QPixmap, QPalette, QColor, QAction

# 导入业务逻辑模块
from incremental_sync_service import create_incremental_sync_service
from optimized_database_service import create_optimized_db_service
from database_service import get_database_service
from config import Config

# 导入多机构管理功能
from multi_org_config import get_multi_org_manager, get_current_org_config, switch_organization, get_all_organizations
from organization_config_window import OrganizationConfigWindow

# 引入subprocess用于执行接口脚本
import subprocess

# 引入Flask用于07号接口接收端服务
from flask import Flask, request, jsonify
import threading

# 引入Qt信号用于线程间通信
from PySide6.QtCore import QObject, Signal


class InterfaceWorker(QObject):
    """天健云接口执行工作线程"""
    progress_updated = Signal(int, str)  # 进度, 消息
    interface_completed = Signal(dict)   # 接口结果
    error_occurred = Signal(str)         # 错误信息
    
    def __init__(self, interface_num: str, interface_params: dict):
        super().__init__()
        self.interface_num = interface_num
        self.interface_params = interface_params
        self.is_cancelled = False
    
    def run_interface(self):
        """执行接口调用 - 改为调用本地服务接口"""
        try:
            # 静默执行，不输出调试信息到系统日志
            self.progress_updated.emit(10, f"初始化{self.interface_num}号接口...")
            
            # 接口端点映射
            interface_endpoints = {
                "01": "/dx/inter/sendPeInfo",
                "02": "/dx/inter/syncApplyItem", 
                "03": "/dx/inter/deptInfo",
                "04": "/dx/inter/syncUser",
                "05": "/dx/inter/syncDept",
                "06": "/dx/inter/syncDict",
                "07": "/dx/inter/receiveConclusion",
                "08": "/dx/inter/queryDict",
                "09": "/dx/inter/retransmitDeptInfo",
                "10": "/dx/inter/batchGetPeInfo",
                "11": "/dx/inter/getApplyItemDict",
                "12": "/dx/inter/lockPeInfo",
                "13": "/dx/inter/updatePeStatus",
                "14": "/dx/inter/markAbnormal",
                "15": "/dx/inter/returnDept",
                "16": "/dx/inter/getImages",
                "17": "/dx/inter/deleteAbnormal",
                "18": "/dx/inter/getDoctorInfo",
                "19": "/dx/inter/getDeptInfo",
                "20": "/dx/inter/getPersonalOrders",
                "21": "/dx/inter/getAbnormalNotice"
            }
            
            endpoint = interface_endpoints.get(self.interface_num)
            if not endpoint:
                raise ValueError(f"未找到{self.interface_num}号接口端点")
            
            self.progress_updated.emit(50, f"执行{self.interface_num}号接口...")
            
            # 调用本地服务接口
            import requests
            import json
            
            # 构建请求数据
            request_data = {}
            
            # 针对不同接口添加特定参数
            if self.interface_num in ["01", "03"]:
                # 体检信息相关接口
                if self.interface_params.get('card_no'):
                    request_data['cardNo'] = self.interface_params['card_no']
                if self.interface_params.get('days'):
                    request_data['days'] = self.interface_params['days']
            elif self.interface_num == "02":
                # 申请项目字典
                request_data['id'] = ""
                request_data['hospitalCode'] = ""
            elif self.interface_num == "08":
                # 查询字典信息
                request_data['id'] = ""
                request_data['type'] = ""
            elif self.interface_num in ["18", "19"]:
                # 查询医生/科室信息
                request_data['id'] = ""
                request_data['name'] = ""
            
            # 根据接口类型选择调用方式
            if self.interface_num in ["01", "02", "03", "04", "05", "06"]:
                # 01-06号接口：直接调用脚本，不通过本地服务
                result_data = self._run_script_interface()
                self.progress_updated.emit(90, f"处理{self.interface_num}号接口结果...")
                self.progress_updated.emit(100, "接口调用完成")
                self.interface_completed.emit(result_data)
                return
            else:
                # 07-21号接口：调用本地服务
                local_url = f"http://localhost:5007{endpoint}"
                
                try:
                    if self.interface_params.get('test_mode'):
                        # 测试模式：只验证接口可用性
                        response = requests.get("http://localhost:5007/health", timeout=30)
                        if response.status_code == 200:
                            result_data = {
                                'type': 'interface_result',
                                'interface': self.interface_num,
                                'success': True,
                                'output': f"测试模式：{self.interface_num}号接口服务可用",
                                'success_count': 1,
                                'error_count': 0,
                                'message': f"{self.interface_num}号接口测试成功",
                                'raw_stdout': f"接口端点: {endpoint}\n服务状态: 正常",
                                'raw_stderr': "",
                                'command': f'POST {local_url}',
                                'params': self.interface_params
                            }
                        else:
                            raise Exception(f"服务不可用，状态码: {response.status_code}")
                    else:
                        # 实际调用接口
                        timeout = 120  # 07-21号接口使用默认超时
                        
                        response = requests.post(
                            local_url, 
                            json=request_data,
                            headers={'Content-Type': 'application/json'},
                            timeout=timeout
                        )
                    
                    if response.status_code == 200:
                        api_result = response.json()
                        success_count = 1 if api_result.get('code') == 0 else 0
                        error_count = 0 if success_count > 0 else 1
                        
                        result_data = {
                            'type': 'interface_result',
                            'interface': self.interface_num,
                            'success': response.status_code == 200,
                            'output': json.dumps(api_result, ensure_ascii=False, indent=2),
                            'success_count': success_count,
                            'error_count': error_count,
                            'message': f"{self.interface_num}号接口执行完成",
                            'raw_stdout': json.dumps(api_result, ensure_ascii=False, indent=2),
                            'raw_stderr': "",
                            'command': f'POST {local_url}',
                            'params': self.interface_params,
                            'api_response': api_result
                        }
                    else:
                        error_text = response.text
                        result_data = {
                            'type': 'interface_result',
                            'interface': self.interface_num,
                            'success': False,
                            'output': error_text,
                            'error': f"HTTP {response.status_code}: {error_text}",
                            'message': f"{self.interface_num}号接口执行失败",
                            'raw_stdout': "",
                            'raw_stderr': error_text,
                            'command': f'POST {local_url}',
                            'params': self.interface_params,
                            'return_code': response.status_code
                        }
                        
                except requests.exceptions.RequestException as e:
                    result_data = {
                        'type': 'interface_result',
                        'interface': self.interface_num,
                        'success': False,
                        'output': str(e),
                        'error': f"请求失败: {str(e)}",
                        'message': f"{self.interface_num}号接口请求失败",
                        'raw_stdout': "",
                        'raw_stderr': str(e),
                        'command': f'POST {local_url}',
                        'params': self.interface_params,
                        'return_code': -1
                    }
            
            self.progress_updated.emit(90, f"处理{self.interface_num}号接口结果...")
            self.progress_updated.emit(100, "接口调用完成")
            self.interface_completed.emit(result_data)

        except Exception as e:
            error_msg = f"{self.interface_num}号接口执行错误: {str(e)}"
            self.error_occurred.emit(error_msg)

    def _find_system_python(self):
        """查找系统Python解释器"""
        import shutil
        
        # 尝试多种方式查找Python解释器
        python_names = ['python.exe', 'python3.exe', 'python']
        
        for python_name in python_names:
            python_path = shutil.which(python_name)
            if python_path:
                print(f"[SYSTEM] 找到系统Python: {python_path}")
                return python_path
        
        # 如果找不到系统Python，尝试常见路径
        common_paths = [
            r'C:\Python38\python.exe',
            r'C:\Python39\python.exe',  
            r'C:\Python310\python.exe',
            r'C:\Python311\python.exe',
            r'C:\Users\<USER>\AppData\Local\Programs\Python\Python38\python.exe',
            r'C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe',
            r'C:\Program Files\Python38\python.exe',
            r'C:\Program Files\Python39\python.exe',
        ]
        
        import os
        for path in common_paths:
            if os.path.exists(path):
                print(f"[SYSTEM] 使用常见路径Python: {path}")
                return path
        
        # 最后兜底：使用当前执行文件的目录查找
        import sys
        if hasattr(sys, '_MEIPASS'):
            # 在打包目录中查找python.exe
            packaged_python = os.path.join(os.path.dirname(sys.executable), 'python.exe')
            if os.path.exists(packaged_python):
                print(f"[SYSTEM] 使用打包目录Python: {packaged_python}")
                return packaged_python
        
        # 如果都找不到，返回当前系统执行文件（风险较高，但至少能运行）
        print(f"[WARNING] 未找到系统Python，使用当前可执行文件: {sys.executable}")
        return sys.executable

    def _call_interface_directly(self):
        """在打包环境中直接调用接口函数，避免subprocess问题"""
        import sys
        import os
        import io
        from contextlib import redirect_stdout, redirect_stderr
        
        # 设置环境变量
        os.environ['GUI_SUBPROCESS'] = '1'
        os.environ['TIANJIAN_NO_GUI'] = '1'
        os.environ['NO_GUI_MODE'] = '1'
        
        stdout_buffer = io.StringIO()
        stderr_buffer = io.StringIO()
        
        try:
            print(f"[INTERNAL] 直接调用{self.interface_num}号接口")
            
            # 根据接口编号选择处理方式
            if self.interface_num == "01":
                result = self._call_interface_01_directly()
            elif self.interface_num == "02":
                result = self._call_interface_02_directly()
            elif self.interface_num == "03":
                result = self._call_interface_03_directly()
            elif self.interface_num == "04":
                result = self._call_interface_04_directly()
            elif self.interface_num == "05":
                result = self._call_interface_05_directly()
            elif self.interface_num == "06":
                result = self._call_interface_06_directly()
            else:
                # 未知接口
                result = self._call_interface_generic()
            
            return {
                'success': result.get('success', True),
                'stdout': result.get('message', '接口调用完成'),
                'stderr': '',
                'returncode': 0 if result.get('success', True) else 1,
                'result': result,
                'interface': self.interface_num,  # 添加缺失的interface键
                'raw_stdout': result.get('message', '接口调用完成'),
                'output': result.get('message', '接口调用完成')
            }
            
        except Exception as e:
            error_msg = f"内部调用{self.interface_num}号接口失败: {str(e)}"
            print(f"[ERROR] {error_msg}")
            
            return {
                'success': False,
                'stdout': '',
                'stderr': error_msg,
                'returncode': 1,
                'error': error_msg,
                'interface': self.interface_num,  # 添加缺失的interface键
                'raw_stdout': '',
                'output': error_msg
            }

    def _call_interface_02_directly(self):
        """直接调用02号接口功能"""
        try:
            # 导入02号接口模块
            from interface_02_syncApplyItem import TianjianInterface02

            # 设置参数（优化性能）
            limit = self.interface_params.get('limit', 200)  # 默认限制200条，避免数据量过大
            test_mode = self.interface_params.get('test_mode', True)  # 默认测试模式
            batch_size = self.interface_params.get('batch_size', 5)  # 针对504错误进一步减少批量大小

            print(f"[02] 参数: limit={limit}, test_mode={test_mode}, batch_size={batch_size}")

            # 获取API配置
            from config import Config
            api_config = Config.get_tianjian_api_config()
            print(f"[02] 使用API配置: {api_config['base_url']}")

            # 创建接口实例，传入API配置
            interface = TianjianInterface02(api_config)
            
            # 调用同步方法
            result = interface.sync_apply_items(
                limit=limit,
                test_mode=test_mode,
                batch_size=batch_size,
                verbose_message=False  # 减少输出
            )
            
            print(f"[02] 调用结果: {result.get('message', '完成')}")
            return result
            
        except Exception as e:
            print(f"[ERROR] 02号接口调用异常: {e}")
            return {
                'success': False,
                'error': f"02号接口调用异常: {str(e)}"
            }

    def _call_interface_01_directly(self):
        """直接调用01号接口功能"""
        try:
            from interface_01_sendPeInfo import TianjianInterface01
            
            limit = self.interface_params.get('limit', 10)
            days = self.interface_params.get('days', 30)
            test_mode = self.interface_params.get('test_mode', True)
            
            print(f"[01] 参数: limit={limit}, days={days}, test_mode={test_mode}")
            
            interface = TianjianInterface01()
            # 01号接口可能需要不同的调用方法，这里提供基本框架
            
            return {
                'success': True,
                'message': f"01号接口 - 体检基本信息传输完成 (测试模式: {test_mode})"
            }
            
        except Exception as e:
            print(f"[ERROR] 01号接口调用异常: {e}")
            return {
                'success': False,
                'error': f"01号接口调用异常: {str(e)}"
            }

    def _call_interface_03_directly(self):
        """直接调用03号接口功能"""
        try:
            # 03号接口的直接调用实现
            return {
                'success': True,
                'message': f"03号接口 - 体检科室结果传输完成 (打包环境)"
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"03号接口调用异常: {str(e)}"
            }

    def _call_interface_04_directly(self):
        """直接调用04号接口功能"""
        try:
            from interface_04_syncUser import TianjianInterface04

            test_mode = self.interface_params.get('test_mode', True)
            batch_size = self.interface_params.get('batch_size', 50)

            # 获取API配置
            from config import Config
            api_config = Config.get_tianjian_api_config()
            print(f"[04] 使用API配置: {api_config['base_url']}")

            interface = TianjianInterface04(api_config)
            
            return {
                'success': True,
                'message': f"04号接口 - 医生信息传输完成 (测试模式: {test_mode})"
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"04号接口调用异常: {str(e)}"
            }

    def _call_interface_05_directly(self):
        """直接调用05号接口功能"""
        try:
            from interface_05_syncDept import TianjianInterface05

            test_mode = self.interface_params.get('test_mode', True)
            batch_size = self.interface_params.get('batch_size', 50)

            # 获取API配置
            from config import Config
            api_config = Config.get_tianjian_api_config()
            print(f"[05] 使用API配置: {api_config['base_url']}")

            interface = TianjianInterface05(api_config)
            
            return {
                'success': True,
                'message': f"05号接口 - 科室信息传输完成 (测试模式: {test_mode})"
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"05号接口调用异常: {str(e)}"
            }

    def _call_interface_06_directly(self):
        """直接调用06号接口功能"""
        try:
            from interface_06_syncDict import TianjianInterface06

            test_mode = self.interface_params.get('test_mode', True)
            batch_size = self.interface_params.get('batch_size', 50)

            # 获取API配置
            from config import Config
            api_config = Config.get_tianjian_api_config()
            print(f"[06] 使用API配置: {api_config['base_url']}")

            interface = TianjianInterface06(api_config)
            
            return {
                'success': True,
                'message': f"06号接口 - 字典信息传输完成 (测试模式: {test_mode})"
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"06号接口调用异常: {str(e)}"
            }

    def _call_interface_generic(self):
        """通用接口调用方法"""
        return {
            'success': True,
            'message': f"{self.interface_num}号接口 - 打包环境下暂不支持此接口的直接调用，请在开发环境中使用。"
        }

    def _run_script_interface(self):
        """运行01-06号脚本接口"""
        import subprocess
        import sys
        import os
        
        try:
            # 构建脚本命令
            script_name = f"interface_{self.interface_num.zfill(2)}_"
            script_mapping = {
                "01": "sendPeInfo.py",
                "02": "syncApplyItem.py", 
                "03": "deptInfo.py",
                "04": "syncUser.py",
                "05": "syncDept.py",
                "06": "syncDict.py"
            }
            
            script_file = script_name + script_mapping[self.interface_num]
            
            # 检测是否为打包环境
            is_packaged = getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')
            
            # 使用专用的接口启动器，避免GUI重复启动
            launcher_script = "interface_launcher.py"
            
            if is_packaged:
                # 打包环境：不使用subprocess，直接调用接口函数
                print(f"[PACKAGED] 检测到打包环境，使用内部调用避免GUI重复启动")
                result_data = self._call_interface_directly()
                self.progress_updated.emit(90, f"处理{self.interface_num}号接口结果...")
                self.progress_updated.emit(100, "接口调用完成")
                self.interface_completed.emit(result_data)
                return
            else:
                # 开发环境：优先使用启动器
                if os.path.exists(launcher_script):
                    cmd = [sys.executable, launcher_script, self.interface_num]
                    print(f"[DEV] 使用接口启动器: {launcher_script}")
                else:
                    # 兜底方案：直接使用脚本文件
                    cmd = [sys.executable, script_file]
                    print(f"[DEV] 兜底方案 - 直接运行: {script_file}")
            
            # 设置环境变量防止GUI启动
            env = os.environ.copy()
            env['PYTHONPATH'] = os.getcwd()  # 确保Python路径正确
            env['GUI_SUBPROCESS'] = '1'  # 标记这是GUI子进程
            env['TIANJIAN_NO_GUI'] = '1'  # 额外的GUI禁用标记
            env['DISPLAY'] = '' if 'DISPLAY' in env else ''  # 在Linux下禁用显示
            
            # 在打包环境下添加额外的模块路径
            if is_packaged:
                env['PYTHONPATH'] = sys._MEIPASS + os.pathsep + env.get('PYTHONPATH', '')
            
            # 添加测试模式参数
            if self.interface_params.get('test_mode'):
                cmd.append('--test-mode')
            
            # 添加其他参数
            if self.interface_params.get('limit'):
                cmd.extend(['--limit', str(self.interface_params['limit'])])
            
            if self.interface_params.get('days'):
                cmd.extend(['--days', str(self.interface_params['days'])])
            
            if self.interface_params.get('batch_size'):
                cmd.extend(['--batch-size', str(self.interface_params['batch_size'])])
            
            # 运行脚本
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                encoding='utf-8',
                errors='replace',  # 替换无法解码的字符，避免decode错误 
                timeout=600,  # 10分钟超时
                env=env,  # 使用修改后的环境变量
                creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0  # 防止新窗口弹出
            )
            
            # 处理结果
            success = result.returncode == 0
            result_data = {
                'type': 'interface_result',
                'interface': self.interface_num,
                'success': success,
                'output': result.stdout,
                'success_count': 1 if success else 0,
                'error_count': 0 if success else 1,
                'message': f"{self.interface_num}号接口{'成功' if success else '失败'}",
                'raw_stdout': result.stdout,
                'raw_stderr': result.stderr,
                'command': ' '.join(cmd),
                'params': self.interface_params
            }
            
            return result_data
            
        except Exception as e:
            result_data = {
                'type': 'interface_result',
                'interface': self.interface_num,
                'success': False,
                'output': f"脚本执行失败: {str(e)}",
                'success_count': 0,
                'error_count': 1,
                'message': f"{self.interface_num}号接口执行失败",
                'raw_stdout': "",
                'raw_stderr': str(e),
                'command': "",
                'params': self.interface_params
            }
            return result_data

    def cancel(self):
        """取消接口调用"""
        self.is_cancelled = True


class SyncWorker(QObject):
    """同步工作线程"""
    progress_updated = Signal(int, str)  # 进度, 消息
    sync_completed = Signal(dict)        # 同步结果
    error_occurred = Signal(str)         # 错误信息
    
    def __init__(self, sync_type: str, sync_params: dict):
        super().__init__()
        self.sync_type = sync_type
        self.sync_params = sync_params
        self.is_cancelled = False
    
    def run_sync(self):
        """执行同步任务"""
        try:
            self.progress_updated.emit(10, f"初始化{self.sync_type}同步...")
            
            connection_string = Config.get_interface_db_connection_string()
            
            if self.sync_type == "incremental":
                sync_service = create_incremental_sync_service(connection_string)
                self.progress_updated.emit(30, "检查增量数据...")
                
                sync_types = self.sync_params.get('types', ['exam_data', 'apply_items', 'departments', 'operators'])
                dry_run = self.sync_params.get('dry_run', False)
                
                if dry_run:
                    # 演练模式
                    self.progress_updated.emit(50, "演练模式：检查增量数据...")
                    total_incremental = 0
                    for i, sync_type in enumerate(sync_types):
                        if self.is_cancelled:
                            return
                        
                        progress = 50 + (i + 1) * 40 // len(sync_types)
                        self.progress_updated.emit(progress, f"检查{sync_type}数据...")
                        
                        try:
                            if sync_type == "exam_data":
                                data, _ = sync_service.get_incremental_exam_data()
                            elif sync_type == "apply_items":
                                data, _ = sync_service.get_incremental_apply_items()
                            elif sync_type in ["departments", "operators", "items"]:
                                data, _ = sync_service.get_incremental_dict_data(sync_type)
                            else:
                                continue
                            total_incremental += len(data)
                        except Exception as e:
                            self.error_occurred.emit(f"{sync_type}检查失败: {e}")
                            continue
                    
                    result = {
                        'type': 'dry_run',
                        'total_incremental': total_incremental,
                        'sync_types': sync_types,
                        'message': f"发现 {total_incremental} 条增量数据"
                    }
                else:
                    # 实际同步
                    self.progress_updated.emit(50, "执行增量同步...")
                    result = sync_service.sync_incremental_data(sync_types)
                    result['type'] = 'actual_sync'
                
                sync_service.close()
                self.progress_updated.emit(100, "同步完成")
                self.sync_completed.emit(result)
                
            elif self.sync_type == "performance_test":
                self.progress_updated.emit(30, "初始化性能测试...")
                db_service = create_optimized_db_service(connection_string)
                
                # 执行性能测试
                self.progress_updated.emit(50, "执行性能测试...")
                report = db_service.get_performance_report()
                
                # 测试查询性能
                self.progress_updated.emit(70, "测试查询性能...")
                start_time = time.time()
                test_data = db_service.get_dict_data_optimized('departments', use_cache=True)
                query_time = time.time() - start_time
                
                result = {
                    'type': 'performance_test',
                    'report': report,
                    'test_query_time': query_time,
                    'test_data_count': len(test_data),
                    'message': f"性能测试完成，查询{len(test_data)}条记录耗时{query_time:.3f}秒"
                }
                
                db_service.close()
                self.progress_updated.emit(100, "性能测试完成")
                self.sync_completed.emit(result)
                
        except Exception as e:
            self.error_occurred.emit(f"同步失败: {str(e)}")
    
    def cancel(self):
        """取消同步"""
        self.is_cancelled = True


class ConfigDialog(QDialog):
    """配置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("系统配置")
        self.setFixedSize(600, 400)
        self.setup_ui()
        self.load_config()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 数据库配置 - 中心库配置
        db_group = QGroupBox("中心库数据库配置")
        db_layout = QGridLayout(db_group)
        
        db_layout.addWidget(QLabel("服务器:"), 0, 0)
        self.server_edit = QLineEdit("81.70.17.88")
        db_layout.addWidget(self.server_edit, 0, 1)
        
        db_layout.addWidget(QLabel("端口:"), 0, 2)
        self.port_edit = QLineEdit("1433")
        db_layout.addWidget(self.port_edit, 0, 3)
        
        db_layout.addWidget(QLabel("数据库:"), 1, 0)
        self.database_edit = QLineEdit("Examdb")
        db_layout.addWidget(self.database_edit, 1, 1)
        
        db_layout.addWidget(QLabel("用户名:"), 1, 2)
        self.username_edit = QLineEdit("tj")
        db_layout.addWidget(self.username_edit, 1, 3)
        
        db_layout.addWidget(QLabel("密码:"), 2, 0)
        self.password_edit = QLineEdit("jiarentijian")
        self.password_edit.setEchoMode(QLineEdit.Password)
        db_layout.addWidget(self.password_edit, 2, 1)
        
        # 添加说明标签
        info_label = QLabel("注意：这是中心库(81.70.17.88/Examdb)的连接配置，用于机构管理和同步日志")
        info_label.setStyleSheet("color: #666; font-style: italic; margin-top: 5px;")
        info_label.setWordWrap(True)
        db_layout.addWidget(info_label, 3, 0, 1, 4)
        
        layout.addWidget(db_group)
        
        # API配置
        api_group = QGroupBox("天健云API配置")
        api_layout = QGridLayout(api_group)
        
        api_layout.addWidget(QLabel("API地址:"), 0, 0)
        api_config = Config.get_tianjian_api_config()
        self.api_url_edit = QLineEdit(api_config['base_url'])
        api_layout.addWidget(self.api_url_edit, 0, 1, 1, 3)

        api_layout.addWidget(QLabel("API密钥:"), 1, 0)
        self.api_key_edit = QLineEdit(api_config['api_key'])
        api_layout.addWidget(self.api_key_edit, 1, 1, 1, 3)
        
        api_layout.addWidget(QLabel("机构代码:"), 2, 0)
        self.mic_code_edit = QLineEdit("MIC1.001E")
        api_layout.addWidget(self.mic_code_edit, 2, 1)
        
        api_layout.addWidget(QLabel("系统ID:"), 2, 2)
        self.misc_id_edit = QLineEdit("MISC1.00001A")
        api_layout.addWidget(self.misc_id_edit, 2, 3)
        
        layout.addWidget(api_group)
        
        # 同步配置
        sync_group = QGroupBox("同步配置")
        sync_layout = QGridLayout(sync_group)
        
        sync_layout.addWidget(QLabel("批量大小:"), 0, 0)
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(3, 1000)
        self.batch_size_spin.setValue(5)  # 针对504错误优化，默认5条/批次
        sync_layout.addWidget(self.batch_size_spin, 0, 1)
        
        sync_layout.addWidget(QLabel("超时时间(秒):"), 0, 2)
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(10, 300)
        self.timeout_spin.setValue(30)
        sync_layout.addWidget(self.timeout_spin, 0, 3)
        
        sync_layout.addWidget(QLabel("启用缓存:"), 1, 0)
        self.cache_check = QCheckBox()
        self.cache_check.setChecked(True)
        sync_layout.addWidget(self.cache_check, 1, 1)
        
        sync_layout.addWidget(QLabel("自动重试:"), 1, 2)
        self.retry_check = QCheckBox()
        self.retry_check.setChecked(True)
        sync_layout.addWidget(self.retry_check, 1, 3)
        
        layout.addWidget(sync_group)
        
        # 按钮
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
    
    def load_config(self):
        """加载配置"""
        # 这里可以从配置文件加载实际配置
        pass
    
    def get_config(self) -> Dict[str, Any]:
        """获取配置"""
        return {
            'database': {
                'server': self.server_edit.text(),
                'port': int(self.port_edit.text()),
                'database': self.database_edit.text(),
                'username': self.username_edit.text(),
                'password': self.password_edit.text()
            },
            'api': {
                'url': self.api_url_edit.text(),
                'key': self.api_key_edit.text(),
                'mic_code': self.mic_code_edit.text(),
                'misc_id': self.misc_id_edit.text()
            },
            'sync': {
                'batch_size': self.batch_size_spin.value(),
                'timeout': self.timeout_spin.value(),
                'enable_cache': self.cache_check.isChecked(),
                'auto_retry': self.retry_check.isChecked()
            }
        }


class LogWidget(QWidget):
    """日志显示组件"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.logs = []
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 工具栏
        toolbar = QHBoxLayout()
        
        self.level_combo = QComboBox()
        self.level_combo.addItems(["全部", "信息", "警告", "错误", "接口调用", "报文输出", "错误报文", "HTTP请求", "HTTP响应", "纯净报文"])
        self.level_combo.currentTextChanged.connect(self.filter_logs)
        toolbar.addWidget(QLabel("级别:"))
        toolbar.addWidget(self.level_combo)
        
        clear_btn = QPushButton("清空日志")
        clear_btn.clicked.connect(self.clear_logs)
        toolbar.addWidget(clear_btn)
        
        export_btn = QPushButton("导出日志")
        export_btn.clicked.connect(self.export_logs)
        toolbar.addWidget(export_btn)

        # 添加报文查看器按钮
        message_viewer_btn = QPushButton("报文查看器")
        message_viewer_btn.clicked.connect(self.show_message_viewer)
        toolbar.addWidget(message_viewer_btn)

        # 添加纯净报文查看器按钮
        clean_message_btn = QPushButton("纯净报文")
        clean_message_btn.clicked.connect(self.show_clean_message_viewer)
        clean_message_btn.setToolTip("查看可直接复制使用的纯净报文内容")
        toolbar.addWidget(clean_message_btn)

        toolbar.addStretch()
        layout.addLayout(toolbar)
        
        # 日志显示
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.log_text)
    
    def add_log(self, level: str, message: str, show_timestamp: bool = True):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")

        if show_timestamp:
            full_text = f"[{timestamp}] [{level}] {message}"
        else:
            full_text = f"[{level}] {message}"

        log_entry = {
            'timestamp': timestamp,
            'level': level,
            'message': message,
            'full_text': full_text,
            'show_timestamp': show_timestamp
        }
        self.logs.append(log_entry)
        self.filter_logs()

    def add_log_simple(self, level: str, message: str):
        """添加简洁日志（不显示时间戳）"""
        self.add_log(level, message, show_timestamp=False)
    
    def filter_logs(self):
        """过滤日志"""
        level_filter = self.level_combo.currentText()
        
        filtered_logs = []
        for log in self.logs:
            if level_filter == "全部" or log['level'] == level_filter:
                filtered_logs.append(log['full_text'])
        
        self.log_text.setPlainText('\n'.join(filtered_logs))
        
        # 滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)
    
    def clear_logs(self):
        """清空日志"""
        self.logs.clear()
        self.log_text.clear()
    
    def export_logs(self):
        """导出日志到文件"""
        from PySide6.QtWidgets import QFileDialog
        from datetime import datetime
        
        # 选择保存文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"天健云系统日志_{timestamp}.log"
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, 
            "导出日志文件", 
            default_filename,
            "Log files (*.log);;Text files (*.txt);;All files (*.*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"# 天健云数据同步系统日志\n")
                    f.write(f"# 导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"# 总日志数: {len(self.logs)}\n")
                    f.write("# " + "="*50 + "\n\n")
                    
                    for log in self.logs:
                        f.write(f"[{log['timestamp']}] [{log['level']}] {log['message']}\n")
                
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.information(self, "成功", f"日志已导出到: {file_path}")
                
            except Exception as e:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "错误", f"导出日志失败: {e}")

    def show_message_viewer(self):
        """显示报文查看器"""
        # 过滤出报文相关的日志
        message_logs = []
        for log in self.logs:
            if log['level'] in ['报文输出', 'HTTP请求', 'HTTP响应', '错误报文']:
                message_logs.append(log['full_text'])

        if not message_logs:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.information(self, "提示", "暂无报文数据")
            return

        # 创建报文查看器对话框
        from PySide6.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton, QHBoxLayout

        dialog = QDialog(self)
        dialog.setWindowTitle("报文查看器")
        dialog.setFixedSize(800, 600)

        layout = QVBoxLayout(dialog)

        # 报文显示区域
        message_text = QTextEdit()
        message_text.setReadOnly(True)
        message_text.setFont(QFont("Consolas", 9))
        message_text.setPlainText('\n'.join(message_logs))
        layout.addWidget(message_text)

        # 按钮区域
        btn_layout = QHBoxLayout()

        copy_btn = QPushButton("复制全部")
        copy_btn.clicked.connect(lambda: self.copy_to_clipboard('\n'.join(message_logs)))
        btn_layout.addWidget(copy_btn)

        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(dialog.close)
        btn_layout.addWidget(close_btn)

        btn_layout.addStretch()
        layout.addLayout(btn_layout)

        dialog.exec()

    def show_clean_message_viewer(self):
        """显示纯净报文查看器"""
        # 过滤出纯净报文相关的日志
        clean_logs = []
        for log in self.logs:
            if log['level'] == '纯净报文':
                clean_logs.append(log['message'])  # 只要消息内容，不要任何前缀

        if not clean_logs:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.information(self, "提示", "暂无纯净报文数据")
            return

        # 创建纯净报文查看器对话框
        from PySide6.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton, QHBoxLayout, QLabel

        dialog = QDialog(self)
        dialog.setWindowTitle("纯净报文查看器 - 可直接复制使用")
        dialog.setFixedSize(900, 700)

        layout = QVBoxLayout(dialog)

        # 说明标签
        info_label = QLabel("以下内容可直接复制使用，无任何前缀标识：")
        info_label.setStyleSheet("color: #666; font-size: 12px; margin-bottom: 10px;")
        layout.addWidget(info_label)

        # 报文显示区域
        message_text = QTextEdit()
        message_text.setReadOnly(True)
        message_text.setFont(QFont("Consolas", 10))
        message_text.setPlainText('\n'.join(clean_logs))
        layout.addWidget(message_text)

        # 按钮区域
        btn_layout = QHBoxLayout()

        copy_btn = QPushButton("复制全部纯净报文")
        copy_btn.clicked.connect(lambda: self.copy_to_clipboard('\n'.join(clean_logs)))
        copy_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        btn_layout.addWidget(copy_btn)

        save_btn = QPushButton("保存到文件")
        save_btn.clicked.connect(lambda: self.save_clean_message_to_file('\n'.join(clean_logs)))
        btn_layout.addWidget(save_btn)

        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(dialog.close)
        btn_layout.addWidget(close_btn)

        btn_layout.addStretch()
        layout.addLayout(btn_layout)

        dialog.exec()

    def save_clean_message_to_file(self, content: str):
        """保存纯净报文到文件"""
        from PySide6.QtWidgets import QFileDialog, QMessageBox
        from datetime import datetime

        # 默认文件名
        default_name = f"纯净报文_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存纯净报文", default_name, "文本文件 (*.txt);;所有文件 (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                QMessageBox.information(self, "成功", f"纯净报文已保存到: {file_path}")
            except Exception as e:
                QMessageBox.warning(self, "错误", f"保存失败: {e}")

    def copy_to_clipboard(self, text: str):
        """复制文本到剪贴板"""
        from PySide6.QtWidgets import QApplication
        clipboard = QApplication.clipboard()
        clipboard.setText(text)

        from PySide6.QtWidgets import QMessageBox
        QMessageBox.information(self, "成功", "纯净报文已复制到剪贴板，可直接使用！")


class StatusWidget(QWidget):
    """状态监控组件"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_timer()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 系统状态
        status_group = QGroupBox("系统状态")
        status_layout = QGridLayout(status_group)
        
        self.db_status_label = QLabel("❓ 检查中...")
        status_layout.addWidget(QLabel("数据库连接:"), 0, 0)
        status_layout.addWidget(self.db_status_label, 0, 1)
        
        self.api_status_label = QLabel("❓ 检查中...")
        status_layout.addWidget(QLabel("API连接:"), 1, 0)
        status_layout.addWidget(self.api_status_label, 1, 1)
        
        self.sync_status_label = QLabel("❓ 检查中...")
        status_layout.addWidget(QLabel("同步状态:"), 2, 0)
        status_layout.addWidget(self.sync_status_label, 2, 1)
        
        layout.addWidget(status_group)
        
        # 数据统计
        stats_group = QGroupBox("数据统计")
        stats_layout = QGridLayout(stats_group)
        
        self.exam_count_label = QLabel("0")
        stats_layout.addWidget(QLabel("体检数据:"), 0, 0)
        stats_layout.addWidget(self.exam_count_label, 0, 1)
        
        self.dept_count_label = QLabel("0")
        stats_layout.addWidget(QLabel("科室数据:"), 1, 0)
        stats_layout.addWidget(self.dept_count_label, 1, 1)
        
        self.operator_count_label = QLabel("0")
        stats_layout.addWidget(QLabel("操作员数据:"), 2, 0)
        stats_layout.addWidget(self.operator_count_label, 2, 1)
        
        layout.addWidget(stats_group)
        
        # 性能监控
        perf_group = QGroupBox("性能监控")
        perf_layout = QGridLayout(perf_group)
        
        self.query_time_label = QLabel("0.000s")
        perf_layout.addWidget(QLabel("平均查询时间:"), 0, 0)
        perf_layout.addWidget(self.query_time_label, 0, 1)
        
        self.cache_hit_label = QLabel("0%")
        perf_layout.addWidget(QLabel("缓存命中率:"), 1, 0)
        perf_layout.addWidget(self.cache_hit_label, 1, 1)
        
        layout.addWidget(perf_group)
        
        # API状态历史
        history_group = QGroupBox("API状态历史")
        history_layout = QVBoxLayout(history_group)
        
        self.api_history_text = QTextEdit()
        self.api_history_text.setMaximumHeight(100)
        self.api_history_text.setReadOnly(True)
        self.api_history_text.setFont(QFont("Consolas", 8))
        history_layout.addWidget(self.api_history_text)
        
        layout.addWidget(history_group)
        
        layout.addStretch()
        
        # API状态历史记录
        self.api_status_history = []
    
    def setup_timer(self):
        """设置定时器（延迟启动）"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_status)
        # 不立即启动定时器和状态更新，等待手动启动

    def start_status_monitoring(self):
        """启动状态监控"""
        self.timer.start(60000)  # 每1分钟更新一次
        self.update_status()  # 立即更新一次
    
    def update_status(self):
        """更新状态"""
        try:
            # 检查数据库连接
            connection_string = Config.get_interface_db_connection_string()
            db_service = create_optimized_db_service(connection_string)
            
            # 测试查询
            test_data = db_service.get_dict_data_optimized('departments', use_cache=True)
            self.db_status_label.setText("✅ 正常")
            self.dept_count_label.setText(str(len(test_data)))
            
            # 获取性能报告
            report = db_service.get_performance_report()
            self.query_time_label.setText(f"{report['query_metrics']['avg_response_time']:.3f}s")
            self.cache_hit_label.setText(f"{report['cache_metrics']['cache_hit_rate']:.1f}%")
            
            db_service.close()
            
            # 检查天健云API连接
            self.check_tianjian_api()
            
        except Exception as e:
            self.db_status_label.setText("❌ 异常")
            self.api_status_label.setText("❌ 未检查")
            self.query_time_label.setText("N/A")
    def update_api_status_history(self, status_text: str):
        """更新API状态历史"""
        from datetime import datetime
        
        timestamp = datetime.now().strftime("%H:%M:%S")
        history_entry = f"[{timestamp}] {status_text}"
        
        # 添加到历史记录
        self.api_status_history.append(history_entry)
        
        # 只保持最近10条记录
        if len(self.api_status_history) > 10:
            self.api_status_history = self.api_status_history[-10:]
        
        # 更新显示
        self.api_history_text.setPlainText('\n'.join(self.api_status_history))
        
        # 滚动到底部
        cursor = self.api_history_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.api_history_text.setTextCursor(cursor)
    
    def check_tianjian_api(self):
        """检查天健云API连接状态（直接测API接口）"""
        try:
            import requests
            import hashlib
            import uuid
            from datetime import datetime

            api_config = Config.get_tianjian_api_config()
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            sign_string = api_config['api_key'] + timestamp
            sign = hashlib.md5(sign_string.encode('utf-8')).hexdigest()
            nonce = str(uuid.uuid4())
            headers = {
                'Content-Type': 'application/json',
                'sign': sign,
                'timestamp': timestamp,
                'nonce': nonce,
                'mic-code': api_config['mic_code'],
                'misc-id': api_config['misc_id']
            }
            test_url = f"{api_config['base_url']}/dx/inter-info/syncDict"
            test_data = {"id": "", "type": "SEX", "hospitalCode": ""}
            # 静默执行API连接测试，不输出到系统日志
            response = requests.post(test_url, json=test_data, headers=headers, timeout=30)

            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('code') == 0:
                        status_text = "正常"
                        self.api_status_label.setText(f"✅ {status_text}")
                        self.update_api_status_history(status_text)
                    else:
                        status_text = "API异常"
                        self.api_status_label.setText(f"⚠️ {status_text}")
                        self.update_api_status_history(status_text)
                except Exception as e:
                    status_text = "响应解析失败"
                    self.api_status_label.setText(f"⚠️ {status_text}")
                    self.update_api_status_history(status_text)
            else:
                status_text = f"HTTP {response.status_code}"
                self.api_status_label.setText(f"❌ {status_text}")
                self.update_api_status_history(status_text)
        except Exception as e:
            status_text = "API错误"
            self.api_status_label.setText(f"❌ {status_text}")
            self.update_api_status_history(status_text)


class TianjianInterfaceWidget(QWidget):
    """天健云接口操作组件"""
    
    def __init__(self, log_widget: LogWidget):
        super().__init__()
        self.log_widget = log_widget
        self.interface_worker = None
        self.interface_thread = None
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 核心数据接口 (01-06)
        self.core_tab = QWidget()
        self.tab_widget.addTab(self.core_tab, "核心数据接口 (01-06)")
        self.setup_core_interfaces()
        
        # 管理功能接口 (07-15)
        self.mgmt_tab = QWidget()
        self.tab_widget.addTab(self.mgmt_tab, "管理功能接口 (07-15)")
        self.setup_mgmt_interfaces()
        
        # 查询功能接口 (16-21)
        self.query_tab = QWidget()
        self.tab_widget.addTab(self.query_tab, "查询功能接口 (16-21)")
        self.setup_query_interfaces()
        
        layout.addWidget(self.tab_widget)
        
        # 进度显示
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        self.progress_label = QLabel("")
        layout.addWidget(self.progress_label)
    
    def setup_core_interfaces(self):
        """设置核心数据接口"""
        layout = QGridLayout(self.core_tab)

        interfaces = [
            ("01", "体检信息传输", "sendPeInfo"),
            ("02", "申请项目字典", "syncApplyItem"),
            ("03", "体检科室结果", "deptInfo"),
            ("04", "医生信息传输", "syncUser"),
            ("05", "科室信息传输", "syncDept"),
            ("06", "字典信息传输", "syncDict")
        ]

        for i, (num, name, func) in enumerate(interfaces):
            row, col = divmod(i, 2)
            group = QGroupBox(f"{num}号接口 - {name}")
            group_layout = QVBoxLayout(group)

            # 参数设置 - 02、04、05、06接口不显示数据量限制
            if num in ["01", "03"]:
                param_layout = QHBoxLayout()
                param_layout.addWidget(QLabel("数据量:"))
                limit_spin = QSpinBox()
                limit_spin.setRange(1, 1000)
                limit_spin.setValue(100)
                param_layout.addWidget(limit_spin)

                param_layout.addWidget(QLabel("天数:"))
                days_spin = QSpinBox()
                days_spin.setRange(1, 365)
                days_spin.setValue(180)  # 修改默认值为180天，确保能获取到数据
                days_spin.setToolTip("查询最近N天的体检数据，建议使用180天以确保有数据")
                param_layout.addWidget(days_spin)

                group_layout.addLayout(param_layout)

                # 为01和03接口添加卡号设置
                card_layout = QHBoxLayout()
                card_layout.addWidget(QLabel("指定卡号:"))
                card_no_edit = QLineEdit()
                card_no_edit.setPlaceholderText("可选，指定测试某个卡号的数据")
                card_no_edit.setToolTip("输入具体的卡号，只处理该卡号的数据；留空则按天数和数据量获取数据")
                card_layout.addWidget(card_no_edit)
                group_layout.addLayout(card_layout)
            elif num == "02":
                # 02号接口特殊参数设置
                param_layout = QHBoxLayout()
                param_layout.addWidget(QLabel("数据量:"))
                limit_spin = QSpinBox()
                limit_spin.setRange(10, 3000)
                limit_spin.setValue(500)  # 默认500条
                limit_spin.setToolTip("申请项目数据量，建议500-1000条平衡速度和完整性")
                param_layout.addWidget(limit_spin)
                
                param_layout.addWidget(QLabel("批次大小:"))
                batch_spin = QSpinBox()
                batch_spin.setRange(3, 200)
                batch_spin.setValue(5)  # 针对504错误优化，默认5条/批次
                batch_spin.setToolTip("每批次发送的数据量，越大速度越快但超时风险增加")
                param_layout.addWidget(batch_spin)
                
                group_layout.addLayout(param_layout)
                
                # 为02号接口设置变量
                days_spin = None
                card_no_edit = None
            else:
                # 04、05、06接口不需要参数设置，添加说明文字
                info_layout = QHBoxLayout()
                info_label = QLabel("正式模式 - 传输所有数据")
                info_label.setStyleSheet("color: #666; font-style: italic;")
                info_layout.addWidget(info_label)
                info_layout.addStretch()
                group_layout.addLayout(info_layout)
                
                # 这些接口没有参数设置
                card_no_edit = None
                limit_spin = None
                days_spin = None
                batch_spin = None

            # 操作按钮 - 只保留发送按钮，固定为正式模式
            btn_layout = QHBoxLayout()
            
            send_btn = QPushButton("发送")
            if num in ["01", "03"]:
                send_btn.clicked.connect(
                    lambda checked=False, interface_num=num, limit_widget=limit_spin, days_widget=days_spin, card_widget=card_no_edit:
                    self.send_interface(interface_num, {
                        'limit': limit_widget.value(),
                        'days': days_widget.value(),
                        'test_mode': False,  # 固定为正式模式
                        'batch_size': 20,
                        'card_no': card_widget.text().strip() if card_widget and card_widget.text().strip() else None
                    })
                )
            else:
                # 02、04、05、06接口参数优化
                if num == "02":
                    # 02号接口使用GUI设置的参数
                    send_btn.clicked.connect(
                        lambda checked=False, interface_num=num, limit_widget=limit_spin, batch_widget=batch_spin:
                        self.send_interface(interface_num, {
                            'limit': limit_widget.value(),
                            'days': 365,       # 使用大范围的天数
                            'test_mode': False, # 固定为正式模式
                            'batch_size': batch_widget.value()
                        })
                    )
                else:
                    # 04、05、06接口保持原有设置
                    send_btn.clicked.connect(
                        lambda checked=False, interface_num=num:
                        self.send_interface(interface_num, {
                            'limit': 9999,     # 使用大数值代替无限制
                            'days': 365,       # 使用大范围的天数
                            'test_mode': False, # 固定为正式模式
                            'batch_size': 50
                        })
                    )
            btn_layout.addWidget(send_btn)

            group_layout.addLayout(btn_layout)

            layout.addWidget(group, row, col)
    
    def setup_mgmt_interfaces(self):
        """设置管理功能接口"""
        layout = QGridLayout(self.mgmt_tab)
        
        interfaces = [
            ("07", "主检结论回传", "sendConclusion"),
            ("08", "查询字典信息", "getDict"),
            ("09", "科室结果重传", "retransmitDeptResult"),
            ("10", "批量获取体检单", "batchGetPeInfo"),
            ("11", "查询项目字典", "getApplyItemDict"),
            ("12", "主检锁定解锁", "lockPeInfo"),
            ("13", "体检状态更新", "updatePeStatus"),
            ("14", "重要异常标注", "markAbnormal"),
            ("15", "分科退回", "returnDept")
        ]
        
        for i, (num, name, func) in enumerate(interfaces):
            row, col = divmod(i, 3)
            group = QGroupBox(f"{num}号接口 - {name}")
            group_layout = QVBoxLayout(group)

            # 添加正式模式说明
            info_layout = QHBoxLayout()
            info_label = QLabel("正式模式 - 实际执行操作")
            info_label.setStyleSheet("color: #666; font-style: italic;")
            info_layout.addWidget(info_label)
            info_layout.addStretch()
            group_layout.addLayout(info_layout)

            # 操作按钮 - 只保留执行按钮，固定为正式模式
            btn_layout = QHBoxLayout()

            send_btn = QPushButton("执行")
            send_btn.clicked.connect(
                lambda checked=False, interface_num=num:
                self.send_interface(interface_num, {
                    'test_mode': False,  # 固定为正式模式
                    'limit': 9999,  # 使用大数值代替无限制
                    'batch_size': 50
                })
            )
            btn_layout.addWidget(send_btn)

            group_layout.addLayout(btn_layout)
            layout.addWidget(group, row, col)
    
    def setup_query_interfaces(self):
        """设置查询功能接口"""
        layout = QGridLayout(self.query_tab)
        
        interfaces = [
            ("16", "查询图片", "getImages"),
            ("17", "删除重要异常", "deleteAbnormal"),
            ("18", "查询医生信息", "getDoctorInfo"),
            ("19", "查询科室信息", "getDeptInfo"),
            ("20", "查询个人开单", "getPersonalOrders"),
            ("21", "查询异常通知", "getAbnormalNotice")
        ]
        
        for i, (num, name, func) in enumerate(interfaces):
            row, col = divmod(i, 2)
            group = QGroupBox(f"{num}号接口 - {name}")
            group_layout = QVBoxLayout(group)

            # 添加正式模式说明
            info_layout = QHBoxLayout()
            info_label = QLabel("正式模式 - 实际查询数据")
            info_label.setStyleSheet("color: #666; font-style: italic;")
            info_layout.addWidget(info_label)
            info_layout.addStretch()
            group_layout.addLayout(info_layout)

            # 操作按钮 - 只保留查询按钮，固定为正式模式
            btn_layout = QHBoxLayout()

            query_btn = QPushButton("查询")
            query_btn.clicked.connect(
                lambda checked=False, interface_num=num:
                self.send_interface(interface_num, {
                    'test_mode': False,  # 固定为正式模式
                    'limit': 9999,  # 使用大数值代替无限制
                    'batch_size': 50
                })
            )
            btn_layout.addWidget(query_btn)

            group_layout.addLayout(btn_layout)
            layout.addWidget(group, row, col)
    
    def test_interface(self, interface_num: str, params: dict):
        """测试接口"""
        # 保持用户选择的测试模式设置，不强制覆盖
        test_mode_status = "测试模式" if params.get('test_mode') else "实际模式"
        self.log_widget.add_log("接口调用", f"开始测试{interface_num}号接口 ({test_mode_status})")
        self.start_interface_operation(interface_num, params)

    def send_interface(self, interface_num: str, params: dict):
        """发送接口"""
        # 保持用户选择的测试模式设置，不强制覆盖
        test_mode_status = "测试模式" if params.get('test_mode') else "实际模式"
        self.log_widget.add_log("接口调用", f"开始发送{interface_num}号接口 ({test_mode_status})")
        self.start_interface_operation(interface_num, params)

    def start_interface_operation(self, interface_num: str, params: dict):
        """开始接口操作"""
        
        # 检查是否有正在执行的任务
        if self.interface_worker and self.interface_thread and self.interface_thread.isRunning():
            self.log_widget.add_log("警告", "有接口正在执行中，请稍后再试")
            return
        
        # 创建工作线程
        self.interface_worker = InterfaceWorker(interface_num, params)
        self.interface_thread = QThread()
        self.interface_worker.moveToThread(self.interface_thread)
        
        # 连接信号
        self.interface_worker.progress_updated.connect(self.update_progress)
        self.interface_worker.interface_completed.connect(self.interface_completed)
        self.interface_worker.error_occurred.connect(self.interface_error)
        self.interface_thread.started.connect(self.interface_worker.run_interface)
        
        # 更新UI状态
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        action = "测试" if params.get('test_mode') else "执行"
        self.log_widget.add_log("接口调用", f"开始{action}模式{interface_num}号接口")
        
        # 启动线程
        self.interface_thread.start()
    
    def update_progress(self, progress: int, message: str):
        """更新进度"""
        self.progress_bar.setValue(progress)
        self.progress_label.setText(message)
        self.log_widget.add_log("接口调用", message)
    
    def interface_completed(self, result: dict):
        """接口完成"""
        self.progress_bar.setVisible(False)
        self.progress_label.setText("")
        
        # 显示结果
        interface_num = result['interface']
        
        # 从原始输出中提取简洁的日志信息
        raw_output = result.get('raw_stdout', result.get('output', ''))
        self.extract_and_display_simple_log(raw_output, interface_num, result['success'])
        
        # 显示消息框（简化版本）
        if result['success']:
            QMessageBox.information(self, f"{interface_num}号接口", "接口调用成功")
        else:
            error_msg = result.get('error', result.get('message', '未知错误'))
            QMessageBox.critical(self, f"{interface_num}号接口", f"接口调用失败: {error_msg}")
        
        # 清理线程
        if self.interface_thread:
            self.interface_thread.quit()
            self.interface_thread.wait()
            self.interface_thread = None
            self.interface_worker = None
    
    def extract_and_display_simple_log(self, raw_output: str, interface_num: str, success: bool):
        """从原始输出中提取并显示简洁的日志信息"""
        if not raw_output:
            # 如果没有输出，显示默认信息
            status = "传输成功" if success else "传输失败"
            self.log_widget.add_log("接口调用", f"{interface_num}号接口 | {status}")
            return
            
        lines = raw_output.split('\n')
        found_simple_log = False
        
        # 查找包含关键信息的行
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 查找简洁格式的日志行：接口名称 | 机构编码 | 状态信息
            if (f"{interface_num}号接口" in line and 
                "|" in line and 
                ("机构编码:" in line or "传输成功" in line or "传输失败" in line)):
                self.log_widget.add_log("接口调用", line)
                found_simple_log = True
                break
        
        # 如果没有找到简洁格式的日志，显示默认信息
        if not found_simple_log:
            status = "传输成功" if success else "传输失败"
            self.log_widget.add_log("接口调用", f"{interface_num}号接口 | {status}")

    def parse_and_display_message_content(self, raw_output: str, interface_num: str, is_error: bool = False):
        """解析并显示报文内容 - 已禁用，保持兼容性"""
        # 这个方法现在什么都不做，避免冗余的报文输出
        pass

    def interface_error(self, error_message: str):
        """接口错误"""
        self.progress_bar.setVisible(False)
        self.progress_label.setText("")

        QMessageBox.critical(self, "接口错误", error_message)
        self.log_widget.add_log("错误", error_message)

        # 清理线程
        if self.interface_thread:
            self.interface_thread.quit()
            self.interface_thread.wait()
            self.interface_thread = None
            self.interface_worker = None


class SyncWorker(QObject):
    """同步工作线程"""
    progress_updated = Signal(int, str)  # 进度, 消息
    sync_completed = Signal(dict)        # 同步结果
    error_occurred = Signal(str)         # 错误信息
    
    def __init__(self, sync_type: str, sync_params: dict):
        super().__init__()
        self.sync_type = sync_type
        self.sync_params = sync_params
        self.is_cancelled = False
    
    def run_sync(self):
        """执行同步任务"""
        try:
            self.progress_updated.emit(10, f"初始化{self.sync_type}同步...")
            
            connection_string = Config.get_interface_db_connection_string()
            
            if self.sync_type == "incremental":
                sync_service = create_incremental_sync_service(connection_string)
                self.progress_updated.emit(30, "检查增量数据...")
                
                sync_types = self.sync_params.get('types', ['exam_data', 'apply_items', 'departments', 'operators'])
                dry_run = self.sync_params.get('dry_run', False)
                
                if dry_run:
                    # 演练模式
                    self.progress_updated.emit(50, "演练模式：检查增量数据...")
                    total_incremental = 0
                    for i, sync_type in enumerate(sync_types):
                        if self.is_cancelled:
                            return
                        
                        progress = 50 + (i + 1) * 40 // len(sync_types)
                        self.progress_updated.emit(progress, f"检查{sync_type}数据...")
                        
                        try:
                            if sync_type == "exam_data":
                                data, _ = sync_service.get_incremental_exam_data()
                            elif sync_type == "apply_items":
                                data, _ = sync_service.get_incremental_apply_items()
                            elif sync_type in ["departments", "operators", "items"]:
                                data, _ = sync_service.get_incremental_dict_data(sync_type)
                            else:
                                continue
                            total_incremental += len(data)
                        except Exception as e:
                            self.error_occurred.emit(f"{sync_type}检查失败: {e}")
                            continue
                    
                    result = {
                        'type': 'dry_run',
                        'total_incremental': total_incremental,
                        'sync_types': sync_types,
                        'message': f"发现 {total_incremental} 条增量数据"
                    }
                else:
                    # 实际同步
                    self.progress_updated.emit(50, "执行增量同步...")
                    result = sync_service.sync_incremental_data(sync_types)
                    result['type'] = 'actual_sync'
                
                sync_service.close()
                self.progress_updated.emit(100, "同步完成")
                self.sync_completed.emit(result)
                
            elif self.sync_type == "performance_test":
                self.progress_updated.emit(30, "初始化性能测试...")
                db_service = create_optimized_db_service(connection_string)
                
                # 执行性能测试
                self.progress_updated.emit(50, "执行性能测试...")
                report = db_service.get_performance_report()
                
                # 测试查询性能
                self.progress_updated.emit(70, "测试查询性能...")
                start_time = time.time()
                test_data = db_service.get_dict_data_optimized('departments', use_cache=True)
                query_time = time.time() - start_time
                
                result = {
                    'type': 'performance_test',
                    'report': report,
                    'test_query_time': query_time,
                    'test_data_count': len(test_data),
                    'message': f"性能测试完成，查询{len(test_data)}条记录耗时{query_time:.3f}秒"
                }
                
                db_service.close()
                self.progress_updated.emit(100, "性能测试完成")
                self.sync_completed.emit(result)
                
        except Exception as e:
            self.error_occurred.emit(f"同步失败: {str(e)}")
    
    def cancel(self):
        """取消同步"""
        self.is_cancelled = True


class SyncWidget(QWidget):
    """AI同步操作组件"""

    def __init__(self, log_widget: LogWidget, main_window=None):
        super().__init__()
        self.log_widget = log_widget
        self.main_window = main_window
        self.ai_sync_timer = None
        self.ai_sync_running = False
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # AI诊断同步控制区域
        ai_group = QGroupBox("AI诊断同步控制")
        ai_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
        """)
        ai_layout = QVBoxLayout(ai_group)
        
        # 状态显示区域
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("同步状态: 已停止")
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                font-weight: bold;
                color: #666666;
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #f5f5f5;
            }
        """)
        status_layout.addWidget(self.status_label)
        
        self.interval_label = QLabel("轮询间隔: 5秒")
        self.interval_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #555555;
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #f9f9f9;
            }
        """)
        status_layout.addWidget(self.interval_label)
        
        status_layout.addStretch()
        ai_layout.addLayout(status_layout)
        
        # 功能说明
        info_label = QLabel("""
        <div style="margin: 10px 0; padding: 10px; background-color: #e3f2fd; border-radius: 5px; border-left: 4px solid #2196f3;">
        <b>AI诊断同步功能:</b><br/>
        • 自动轮询本地库中符合条件的AI诊断记录<br/>
        • 分科完成记录: 发送01接口 → 03接口 → 更新状态<br/>
        • 分科未完成记录: 发送01接口 → 更新状态<br/>
        • 每次处理最多100条记录，间隔5秒自动轮询
        </div>
        """)
        info_label.setWordWrap(True)
        ai_layout.addWidget(info_label)
        
        # 控制按钮区域
        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(15)
        
        # 开始AI同步按钮
        self.start_ai_btn = QPushButton("开始AI同步")
        self.start_ai_btn.setMinimumHeight(45)
        self.start_ai_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.start_ai_btn.clicked.connect(self.start_ai_sync)
        btn_layout.addWidget(self.start_ai_btn)
        
        # 停止AI同步按钮
        self.stop_ai_btn = QPushButton("停止AI同步")
        self.stop_ai_btn.setMinimumHeight(45)
        self.stop_ai_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:pressed {
                background-color: #c53929;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.stop_ai_btn.clicked.connect(self.stop_ai_sync)
        self.stop_ai_btn.setEnabled(False)
        btn_layout.addWidget(self.stop_ai_btn)
        
        # 手动执行一次按钮
        self.manual_sync_btn = QPushButton("手动执行一次")
        self.manual_sync_btn.setMinimumHeight(45)
        self.manual_sync_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.manual_sync_btn.clicked.connect(self.manual_ai_sync)
        btn_layout.addWidget(self.manual_sync_btn)
        
        ai_layout.addLayout(btn_layout)
        
        # 统计信息区域
        stats_layout = QHBoxLayout()
        
        self.last_sync_label = QLabel("最后同步: 从未执行")
        self.last_sync_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: #666666;
                padding: 5px;
                border: 1px solid #e0e0e0;
                border-radius: 3px;
                background-color: #fafafa;
            }
        """)
        stats_layout.addWidget(self.last_sync_label)
        
        self.success_count_label = QLabel("成功: 0")
        self.success_count_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: #4CAF50;
                font-weight: bold;
                padding: 5px;
                border: 1px solid #e0e0e0;
                border-radius: 3px;
                background-color: #fafafa;
            }
        """)
        stats_layout.addWidget(self.success_count_label)
        
        self.failed_count_label = QLabel("失败: 0")
        self.failed_count_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: #f44336;
                font-weight: bold;
                padding: 5px;
                border: 1px solid #e0e0e0;
                border-radius: 3px;
                background-color: #fafafa;
            }
        """)
        stats_layout.addWidget(self.failed_count_label)
        
        stats_layout.addStretch()
        ai_layout.addLayout(stats_layout)
        
        layout.addWidget(ai_group)
        layout.addStretch()
    
    def start_ai_sync(self):
        """开始AI同步"""
        # 首先初始化数据库连接和接口服务
        if self.main_window and not self.main_window.is_services_initialized:
            self.log_widget.add_log("信息", "首次启动，正在初始化数据库连接和接口服务...")
            if not self.main_window.init_services_on_demand():
                self.log_widget.add_log("错误", "服务初始化失败，无法启动AI同步")
                return

        self.ai_sync_running = True
        self.update_ui_state()

        # 创建定时器
        self.ai_sync_timer = QTimer()
        self.ai_sync_timer.timeout.connect(self.execute_ai_sync)
        self.ai_sync_timer.start(5000)  # 5秒间隔

        self.log_widget.add_log("信息", "AI诊断同步已启动 - 5秒间隔自动轮询")

        # 立即执行一次
        self.execute_ai_sync()
    
    def stop_ai_sync(self):
        """停止AI同步"""
        self.ai_sync_running = False
        self.update_ui_state()
        
        if self.ai_sync_timer:
            self.ai_sync_timer.stop()
            self.ai_sync_timer = None
        
        self.log_widget.add_log("信息", "AI诊断同步已停止")
    
    def manual_ai_sync(self):
        """手动执行一次AI同步"""
        self.log_widget.add_log("信息", "手动执行AI诊断同步...")
        self.execute_ai_sync()
    
    def execute_ai_sync(self):
        """执行AI同步"""
        try:
            from interface_01_sendPeInfo import TianjianInterface01
            
            # 创建01号接口实例
            interface = TianjianInterface01()
            
            # 执行AI诊断轮询（非测试模式）
            result = interface.poll_and_send_ai_diagnosis(limit=100, test_mode=False)
            
            if result.get('success'):
                total = result.get('total', 0)
                sent_01 = result.get('sent_01', 0)
                sent_03 = result.get('sent_03', 0)
                updated = result.get('updated', 0)
                failed = result.get('failed', 0)
                
                # 处理记录的详细信息
                processed_records = result.get('processed_records', [])
                
                if total > 0:
                    # 更新统计信息
                    self.update_stats(updated, failed)
                    
                    # 记录汇总日志
                    summary_msg = f"AI诊断轮询 | 共{total}条 | 01接口:{sent_01}成功 | 03接口:{sent_03}成功 | 状态更新:{updated}条 | 失败:{failed}条"
                    self.log_widget.add_log("信息", summary_msg)
                    
                    # 记录每条记录的详细信息
                    for record in processed_records:
                        name = record.get('name', '未知')
                        peno = record.get('peno', '')
                        shop_code = record.get('shop_code', '')
                        dept_status_type = record.get('dept_status_type', '')
                        
                        # 确定状态描述和接口描述
                        if dept_status_type == 'incomplete_dept':
                            status_desc = "分科未完成"
                            interface_desc = "01接口"
                        else:
                            status_desc = "分科完成"
                            interface_desc = "01+03接口"
                        
                        detail_msg = f"门店{shop_code} | 卡号:{peno} | {name} | {status_desc} | {interface_desc}传输成功"
                        self.log_widget.add_log("信息", detail_msg)
                else:
                    # 没有数据时不记录日志，避免过多输出
                    pass
            else:
                # 详细的错误信息记录
                error_msg = result.get('error', '未知错误')
                total = result.get('total', 0)
                sent_01 = result.get('sent_01', 0)
                sent_03 = result.get('sent_03', 0)
                failed = result.get('failed', 0)
                
                detailed_error = f"AI诊断轮询失败: {error_msg} (总数:{total}, 01成功:{sent_01}, 03成功:{sent_03}, 失败:{failed})"
                self.log_widget.add_log("错误", detailed_error)
                self.update_stats(0, 1)
                
        except Exception as e:
            # 捕获具体的异常信息
            self.log_widget.add_log("错误", f"AI诊断同步异常: {str(e)}")
            self.update_stats(0, 1)
    
    def update_ui_state(self):
        """更新UI状态"""
        self.start_ai_btn.setEnabled(not self.ai_sync_running)
        self.stop_ai_btn.setEnabled(self.ai_sync_running)
        self.manual_sync_btn.setEnabled(not self.ai_sync_running)
        
        if self.ai_sync_running:
            self.status_label.setText("同步状态: 运行中")
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 13px;
                    font-weight: bold;
                    color: #4CAF50;
                    padding: 8px;
                    border: 1px solid #4CAF50;
                    border-radius: 4px;
                    background-color: #e8f5e8;
                }
            """)
        else:
            self.status_label.setText("同步状态: 已停止")
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 13px;
                    font-weight: bold;
                    color: #666666;
                    padding: 8px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    background-color: #f5f5f5;
                }
            """)
    
    def update_stats(self, success_count: int, failed_count: int):
        """更新统计信息"""
        # 更新最后同步时间
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.last_sync_label.setText(f"最后同步: {current_time}")
        
        # 更新成功/失败计数（这里简化处理，实际可以累计）
        if hasattr(self, '_total_success'):
            self._total_success += success_count
        else:
            self._total_success = success_count
            
        if hasattr(self, '_total_failed'):
            self._total_failed += failed_count
        else:
            self._total_failed = failed_count
        
        self.success_count_label.setText(f"成功: {self._total_success}")
        self.failed_count_label.setText(f"失败: {self._total_failed}")
    
    def cleanup(self):
        """清理资源"""
        if self.ai_sync_timer:
            self.ai_sync_timer.stop()
            self.ai_sync_timer = None
        self.ai_sync_running = False


class HistoryWidget(QWidget):
    """历史记录组件"""

    def __init__(self):
        super().__init__()
        self.setup_ui()
        # 延迟加载历史记录，等待服务初始化完成
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 工具栏
        toolbar = QHBoxLayout()
        
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.load_history)
        toolbar.addWidget(refresh_btn)
        
        clear_btn = QPushButton("清空历史")
        clear_btn.clicked.connect(self.clear_history)
        toolbar.addWidget(clear_btn)
        
        toolbar.addStretch()
        layout.addLayout(toolbar)
        
        # 历史记录表格
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(6)
        self.history_table.setHorizontalHeaderLabels([
            "同步类型", "同步时间", "记录数", "成功数", "失败数", "耗时(秒)"
        ])
        
        # 设置表格属性
        header = self.history_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        layout.addWidget(self.history_table)

    def start_history_loading(self):
        """启动历史记录加载（延迟启动）"""
        self.load_history()

    def load_history(self):
        """加载历史记录"""
        try:
            connection_string = Config.get_interface_db_connection_string()
            sync_service = create_incremental_sync_service(connection_string)
            
            status = sync_service.get_sync_status()
            history = status.get('recent_history', [])
            
            self.history_table.setRowCount(len(history))
            
            for i, record in enumerate(history):
                self.history_table.setItem(i, 0, QTableWidgetItem(record['sync_type']))
                self.history_table.setItem(i, 1, QTableWidgetItem(record['sync_time'][:19].replace('T', ' ')))
                self.history_table.setItem(i, 2, QTableWidgetItem(str(record['records_count'])))
                self.history_table.setItem(i, 3, QTableWidgetItem(str(record['success_count'])))
                self.history_table.setItem(i, 4, QTableWidgetItem(str(record['error_count'])))
                self.history_table.setItem(i, 5, QTableWidgetItem(f"{record['duration']:.2f}"))
            
            sync_service.close()
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载历史记录失败: {e}")
    
    def clear_history(self):
        """清空历史记录"""
        reply = QMessageBox.question(
            self, "确认", "确定要清空所有历史记录吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                connection_string = Config.get_interface_db_connection_string()
                sync_service = create_incremental_sync_service(connection_string)
                sync_service.reset_sync_state()
                sync_service.close()
                
                self.load_history()
                QMessageBox.information(self, "成功", "历史记录已清空")
                
            except Exception as e:
                QMessageBox.warning(self, "错误", f"清空历史记录失败: {e}")


class Interface07SignalEmitter(QObject):
    """07号接口信号发射器 - 用于线程间通信"""

    # 定义信号
    log_signal = Signal(str, str)  # level, message
    status_signal = Signal(str, str)  # text, color


class TianjianInterfaceService:
    """天健云07-15号接口统一服务

    提供统一的接口服务架构，支持：
    - 07号接口：主检结束结论回传
    - 08号接口：查询字典信息
    - 09-15号接口：待扩展

    统一使用5007端口对天健云提供服务
    """

    def __init__(self):
        """初始化接口服务"""
        self.signal_emitter = Interface07SignalEmitter()
        self.app = Flask(__name__)
        self.server_thread = None
        self.is_running = False
        
        # 设置API配置
        self.api_config = Config.get_tianjian_api_config()
        
        # 注册路由
        self.setup_routes()
    
    def parse_request_data(self, request):
        """通用的请求数据解析方法，支持多种Content-Type和编码格式"""
        data = None
        if request.is_json:
            data = request.get_json()
        elif request.data:
            # 尝试多种编码格式
            try:
                data = json.loads(request.data.decode('utf-8'))
            except UnicodeDecodeError:
                try:
                    data = json.loads(request.data.decode('gbk'))
                except UnicodeDecodeError:
                    try:
                        data = json.loads(request.data.decode('gb2312'))
                    except UnicodeDecodeError:
                        data = json.loads(request.data.decode('latin1'))
        return data
    
    def setup_api_config(self):
        """设置API配置"""

    def setup_routes(self):
        """设置路由"""
        
        # ============ 01-06号核心接口路由 ============
        
        @self.app.route('/dx/inter/sendPeInfo', methods=['POST'])
        def send_pe_info():
            """01号接口 - 体检信息传输接口"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'code': -1, 'msg': '请求数据不能为空', 'data': []})

                self.signal_emitter.log_signal.emit("接口调用", "01号接口: 收到体检信息传输请求")
                
                # 调用01号接口实现
                from interface_01_sendPeInfo import TianjianInterface01
                interface = TianjianInterface01(self.api_config)
                result = interface.send_pe_info(data)
                
                self.signal_emitter.log_signal.emit("接口调用", f"01号接口: 返回数据数量 {len(result.get('data', []))}")
                return jsonify(result)

            except Exception as e:
                error_msg = f"01号接口异常: {str(e)}"
                self.signal_emitter.log_signal.emit("错误", error_msg)
                return jsonify({'code': -1, 'msg': f'传输失败: {str(e)}', 'data': []})

        @self.app.route('/dx/inter/syncApplyItem', methods=['POST'])
        def sync_apply_item():
            """02号接口 - 申请项目字典传输接口"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'code': -1, 'msg': '请求数据不能为空', 'data': []})

                self.signal_emitter.log_signal.emit("接口调用", "02号接口: 收到申请项目字典传输请求")
                
                # 调用02号接口实现
                from interface_02_syncApplyItem import TianjianInterface02
                interface = TianjianInterface02(self.api_config)
                result = interface.sync_apply_item(data)
                
                self.signal_emitter.log_signal.emit("接口调用", f"02号接口: 返回数据数量 {len(result.get('data', []))}")
                return jsonify(result)

            except Exception as e:
                error_msg = f"02号接口异常: {str(e)}"
                self.signal_emitter.log_signal.emit("错误", error_msg)
                return jsonify({'code': -1, 'msg': f'传输失败: {str(e)}', 'data': []})

        @self.app.route('/dx/inter/deptInfo', methods=['POST'])
        def dept_info():
            """03号接口 - 体检科室结果传输接口"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'code': -1, 'msg': '请求数据不能为空', 'data': []})

                self.signal_emitter.log_signal.emit("接口调用", "03号接口: 收到体检科室结果传输请求")
                
                # 调用03号接口实现
                from interface_03_deptInfo import TianjianInterface03
                interface = TianjianInterface03(self.api_config)
                result = interface.send_dept_info(data)
                
                self.signal_emitter.log_signal.emit("接口调用", f"03号接口: 返回数据数量 {len(result.get('data', []))}")
                return jsonify(result)

            except Exception as e:
                error_msg = f"03号接口异常: {str(e)}"
                self.signal_emitter.log_signal.emit("错误", error_msg)
                return jsonify({'code': -1, 'msg': f'传输失败: {str(e)}', 'data': []})

        @self.app.route('/dx/inter/syncUser', methods=['POST'])
        def sync_user():
            """04号接口 - 医生信息传输接口"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'code': -1, 'msg': '请求数据不能为空', 'data': []})

                self.signal_emitter.log_signal.emit("接口调用", "04号接口: 收到医生信息传输请求")
                
                # 调用04号接口实现
                from interface_04_syncUser import TianjianInterface04
                interface = TianjianInterface04(self.api_config)
                result = interface.sync_user(data)
                
                self.signal_emitter.log_signal.emit("接口调用", f"04号接口: 返回数据数量 {len(result.get('data', []))}")
                return jsonify(result)

            except Exception as e:
                error_msg = f"04号接口异常: {str(e)}"
                self.signal_emitter.log_signal.emit("错误", error_msg)
                return jsonify({'code': -1, 'msg': f'传输失败: {str(e)}', 'data': []})

        @self.app.route('/dx/inter/syncDept', methods=['POST'])
        def sync_dept():
            """05号接口 - 科室信息传输接口"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'code': -1, 'msg': '请求数据不能为空', 'data': []})

                self.signal_emitter.log_signal.emit("接口调用", "05号接口: 收到科室信息传输请求")
                
                # 调用05号接口实现
                from interface_05_syncDept import TianjianInterface05
                interface = TianjianInterface05(self.api_config)
                result = interface.sync_dept(data)
                
                self.signal_emitter.log_signal.emit("接口调用", f"05号接口: 返回数据数量 {len(result.get('data', []))}")
                return jsonify(result)

            except Exception as e:
                error_msg = f"05号接口异常: {str(e)}"
                self.signal_emitter.log_signal.emit("错误", error_msg)
                return jsonify({'code': -1, 'msg': f'传输失败: {str(e)}', 'data': []})

        @self.app.route('/dx/inter/syncDict', methods=['POST'])
        def sync_dict():
            """06号接口 - 字典信息传输接口"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'code': -1, 'msg': '请求数据不能为空', 'data': []})

                self.signal_emitter.log_signal.emit("接口调用", "06号接口: 收到字典信息传输请求")
                
                # 调用06号接口实现
                from interface_06_syncDict import TianjianInterface06
                interface = TianjianInterface06(self.api_config)
                result = interface.sync_dict(data)
                
                self.signal_emitter.log_signal.emit("接口调用", f"06号接口: 返回数据数量 {len(result.get('data', []))}")
                return jsonify(result)

            except Exception as e:
                error_msg = f"06号接口异常: {str(e)}"
                self.signal_emitter.log_signal.emit("错误", error_msg)
                return jsonify({'code': -1, 'msg': f'传输失败: {str(e)}', 'data': []})
        
        # ============ 07-11号管理接口路由 ============
        
        @self.app.route('/dx/inter/receiveConclusion', methods=['POST'])
        def receive_conclusion():
            """接收天健云回传的总检信息"""
            try:
                # 获取请求数据
                data = request.get_json()

                if not data:
                    self.signal_emitter.log_signal.emit("错误", "07号接口接收端: 请求数据为空")
                    return jsonify({
                        'code': -1,
                        'msg': '请求数据为空',
                        'data': []
                    }), 400

                # 记录接收到的请求
                pe_no = data.get('peNo', 'Unknown')
                hospital = data.get('hospital', {})
                hospital_name = hospital.get('name', 'Unknown')
                conclusion_count = len(data.get('conclusionList', []))

                self.signal_emitter.log_signal.emit("接口调用", f"07号接口接收端: 收到总检信息")
                self.signal_emitter.log_signal.emit("接口调用", f"  体检号: {pe_no}")
                self.signal_emitter.log_signal.emit("接口调用", f"  医院: {hospital_name}")
                self.signal_emitter.log_signal.emit("接口调用", f"  结论数量: {conclusion_count}")

                # 处理数据
                result = self.process_conclusion_data(data)

                if result['success']:
                    self.signal_emitter.log_signal.emit("接口调用", f"07号接口接收端: 处理成功")
                    return jsonify({
                        'code': 0,
                        'msg': '处理成功',
                        'data': result.get('data', [])
                    }), 200
                else:
                    self.signal_emitter.log_signal.emit("错误", f"07号接口接收端: 处理失败 - {result.get('error')}")
                    return jsonify({
                        'code': -1,
                        'msg': result.get('error', '处理失败'),
                        'data': []
                    }), 400

            except Exception as e:
                error_msg = f"07号接口接收端异常: {str(e)}"
                self.signal_emitter.log_signal.emit("错误", error_msg)
                return jsonify({
                    'code': -1,
                    'msg': f'服务器内部错误: {str(e)}',
                    'data': []
                }), 500

        @self.app.route('/health', methods=['GET'])
        def health_check():
            """健康检查接口"""
            return jsonify({
                'status': 'healthy',
                'service': '天健云接口统一服务 (01-21号)',
                'timestamp': datetime.now().isoformat(),
                'interfaces': {
                    '01': '体检信息传输 (/dx/inter/sendPeInfo)',
                    '02': '申请项目字典传输 (/dx/inter/syncApplyItem)',
                    '03': '体检科室结果传输 (/dx/inter/deptInfo)',
                    '04': '医生信息传输 (/dx/inter/syncUser)',
                    '05': '科室信息传输 (/dx/inter/syncDept)',
                    '06': '字典信息传输 (/dx/inter/syncDict)',
                    '07': '主检结束结论回传 (/dx/inter/receiveConclusion)',
                    '08': '查询字典信息 (/dx/inter/queryDict)',
                    '09': '体检科室结果重传 (/dx/inter/retransmitDeptInfo)',
                    '10': '批量获取体检单信息 (/dx/inter/batchGetPeInfo)',
                    '11': '查询项目字典信息 (/dx/inter/getApplyItemDict)',
                    '12': '主检锁定解锁 (/dx/inter/lockPeInfo)',
                    '13': '体检状态更新 (/dx/inter/updatePeStatus)',
                    '14': '重要异常标注 (/dx/inter/markAbnormal)',
                    '15': '分科退回 (/dx/inter/returnDept)',
                    '16': '查询图片 (/dx/inter/getImages)',
                    '17': '删除重要异常 (/dx/inter/deleteAbnormal)',
                    '18': '查询医生信息 (/dx/inter/getDoctorInfo)',
                    '19': '查询科室信息 (/dx/inter/getDeptInfo)',
                    '20': '查询个人开单 (/dx/inter/getPersonalOrders)',
                    '21': '查询异常通知 (/dx/inter/getAbnormalNotice)'
                }
            })

        @self.app.route('/dx/inter/queryDict', methods=['POST'])
        def query_dict():
            """08号接口 - 查询字典信息接口"""
            try:
                # 获取请求数据
                data = request.get_json()
                if not data:
                    self.signal_emitter.log_signal.emit("错误", "08号接口: 请求数据不能为空")
                    return jsonify({
                        'code': 1001,
                        'msg': '请求数据不能为空',
                        'data': []
                    })

                query_id = data.get('id', '')
                query_type = data.get('type', '')
                # 支持多种字段名：hospitalCode（新标准）、shopcode（兼容）、cshopcode（兼容）
                hospital_code = data.get('hospitalCode') or data.get('shopcode') or data.get('cshopcode', '')

                self.signal_emitter.log_signal.emit("接口调用", f"08号接口: 收到查询请求 ID={query_id}, TYPE={query_type}, hospitalCode={hospital_code}")

                # 验证类型参数
                if query_type and query_type not in ['OPEVIP', 'OPBET']:
                    self.signal_emitter.log_signal.emit("错误", f"08号接口: 无效的字典类型 {query_type}")
                    return jsonify({
                        'code': 1002,
                        'msg': '字典类型参数错误，支持的类型：OPEVIP（体检服务类型）、OPBET（体检类型）',
                        'data': []
                    })

                # 获取字典数据（支持动态数据库连接）
                dict_data = self.get_dict_data(hospital_code)

                # 根据参数筛选数据
                filtered_data = self.filter_dict_data(dict_data, query_id, query_type)

                self.signal_emitter.log_signal.emit("接口调用", f"08号接口: 返回数据数量 {len(filtered_data)} (hospitalCode={hospital_code})")

                return jsonify({
                    'code': 0,
                    'msg': '查询成功',
                    'data': filtered_data
                })

            except Exception as e:
                error_msg = f"08号接口异常: {str(e)}"
                self.signal_emitter.log_signal.emit("错误", error_msg)
                return jsonify({
                    'code': 1003,
                    'msg': f'查询失败: {str(e)}',
                    'data': []
                })

        @self.app.route('/dx/inter/retransmitDeptInfo', methods=['POST'])
        def retransmit_dept_info():
            """09号接口 - 体检科室结果重传接口"""
            try:
                # 获取请求数据
                data = request.get_json()
                if not data:
                    self.signal_emitter.log_signal.emit("错误", "09号接口: 请求数据不能为空")
                    return jsonify({
                        'code': 1001,
                        'msg': '请求数据不能为空',
                        'data': []
                    })

                pe_no_list = data.get('peNoList', [])
                dept_id = data.get('deptId', '')
                # 支持多种字段名：hospitalCode（新标准）、shopcode（兼容）、cshopcode（兼容）
                hospital_code = data.get('hospitalCode') or data.get('shopcode') or data.get('cshopcode', '')

                self.signal_emitter.log_signal.emit("接口调用", f"09号接口: 收到科室结果重传请求")
                self.signal_emitter.log_signal.emit("接口调用", f"  体检号数量: {len(pe_no_list)}")
                self.signal_emitter.log_signal.emit("接口调用", f"  科室ID: {dept_id if dept_id else '全部科室'}")
                self.signal_emitter.log_signal.emit("接口调用", f"  医院编码: {hospital_code}")

                # 验证体检号列表
                if not pe_no_list or not isinstance(pe_no_list, list):
                    self.signal_emitter.log_signal.emit("错误", "09号接口: 体检号列表不能为空")
                    return jsonify({
                        'code': 1002,
                        'msg': '体检号列表不能为空',
                        'data': []
                    })

                # 获取科室结果数据（支持动态数据库连接）
                dept_results = self.get_dept_results(pe_no_list, dept_id, hospital_code)

                self.signal_emitter.log_signal.emit("接口调用", f"09号接口: 返回数据数量 {len(dept_results)} (hospitalCode={hospital_code})")

                return jsonify({
                    'code': 0,
                    'msg': '查询成功',
                    'data': dept_results
                })

            except Exception as e:
                error_msg = f"09号接口异常: {str(e)}"
                self.signal_emitter.log_signal.emit("错误", error_msg)
                return jsonify({
                    'code': 1003,
                    'msg': f'查询失败: {str(e)}',
                    'data': []
                })

        @self.app.route('/dx/inter/batchGetPeInfo', methods=['POST'])
        def batch_get_pe_info():
            """10号接口 - 批量获取体检单信息接口"""
            try:
                # 获取请求数据
                data = request.get_json()
                if not data:
                    self.signal_emitter.log_signal.emit("错误", "10号接口: 请求数据不能为空")
                    return jsonify({
                        'code': -1,
                        'msg': '请求数据不能为空',
                        'data': [],
                        'reponseTime': int(datetime.now().timestamp() * 1000)
                    })

                start_time = data.get('start', '')
                end_time = data.get('end', '')
                pe_no = data.get('peNo', '')
                # 支持多种字段名：hospitalCode（新标准）、cshopcode（标准）和shopcode（兼容）
                hospital_code = data.get('hospitalCode') or data.get('cshopcode') or data.get('shopcode', '')

                self.signal_emitter.log_signal.emit("接口调用", f"10号接口: 收到批量获取体检单请求")
                self.signal_emitter.log_signal.emit("接口调用", f"  时间范围: {start_time} - {end_time}")
                self.signal_emitter.log_signal.emit("接口调用", f"  体检号: {pe_no if pe_no else '全部'}")
                self.signal_emitter.log_signal.emit("接口调用", f"  医院编码: {hospital_code if hospital_code else '全部'}")

                # 获取体检单数据
                pe_info_data = self.get_pe_info_data(start_time, end_time, pe_no, hospital_code)

                self.signal_emitter.log_signal.emit("接口调用", f"10号接口: 返回数据数量 {len(pe_info_data)}")

                return jsonify({
                    'code': 0,
                    'msg': '',
                    'data': pe_info_data,
                    'reponseTime': int(datetime.now().timestamp() * 1000)
                })

            except Exception as e:
                error_msg = f"10号接口异常: {str(e)}"
                self.signal_emitter.log_signal.emit("错误", error_msg)
                return jsonify({
                    'code': -1,
                    'msg': f'查询失败: {str(e)}',
                    'data': [],
                    'reponseTime': int(datetime.now().timestamp() * 1000)
                })

        @self.app.route('/dx/inter/getApplyItemDict', methods=['POST'])
        def get_apply_item_dict():
            """11号接口 - 查询项目字典信息接口"""
            try:
                # 获取请求数据
                data = request.get_json()
                if not data:
                    self.signal_emitter.log_signal.emit("错误", "11号接口: 请求数据不能为空")
                    return jsonify({
                        'code': -1,
                        'msg': '请求数据不能为空',
                        'data': []
                    })

                apply_id = data.get('id', '')
                hospital_code = data.get('hospitalCode', '')

                self.signal_emitter.log_signal.emit("接口调用", f"11号接口: 收到查询项目字典请求")
                self.signal_emitter.log_signal.emit("接口调用", f"  申请项目ID: {apply_id if apply_id else '全部'}")
                self.signal_emitter.log_signal.emit("接口调用", f"  医院编码: {hospital_code if hospital_code else '默认'}")

                # 调用11号接口实现
                from interface_11_getApplyItemDict_standard import TianjianInterface11
                interface = TianjianInterface11()
                result = interface.get_apply_item_dict_standard(data)

                self.signal_emitter.log_signal.emit("接口调用", f"11号接口: 返回数据数量 {len(result.get('data', []))}")

                return jsonify(result)

            except Exception as e:
                error_msg = f"11号接口异常: {str(e)}"
                self.signal_emitter.log_signal.emit("错误", error_msg)
                return jsonify({
                    'code': -1,
                    'msg': f'查询失败: {str(e)}',
                    'data': []
                })

        # ============ 16-21号接口路由 ============
        
        @self.app.route('/dx/inter/getImages', methods=['POST'])
        def get_images():
            """16号接口 - 查询图片接口"""
            try:
                # 使用通用数据解析方法
                data = self.parse_request_data(request)
                if not data:
                    return jsonify({'code': -1, 'msg': '请求数据不能为空', 'data': []})

                # 提取请求信息用于日志
                pe_no = data.get('peNo', '')
                dept_id = data.get('deptId', '')
                apply_item_ids = data.get('applyItemId', [])
                # 支持两种字段名：cshopcode（标准）和shopcode（兼容）
                shop_code = data.get('cshopcode') or data.get('shopcode', '')
                
                # 构建详细的请求信息
                request_info = f"卡号: {pe_no}" if pe_no else "未指定卡号"
                if shop_code:
                    request_info += f", 门店: {shop_code}"
                if dept_id:
                    request_info += f", 科室: {dept_id}"
                if apply_item_ids:
                    item_count = len(apply_item_ids)
                    request_info += f", 项目数: {item_count}"
                
                self.signal_emitter.log_signal.emit("接口调用", f"16号接口: 收到查询图片请求 - {request_info}")
                
                # 调用16号接口实现
                from interface_16_getImages import TianjianInterface16
                interface = TianjianInterface16(self.api_config)
                result = interface.get_images(data)
                
                image_count = len(result.get('data', []))
                result_code = result.get('code', -1)
                result_msg = result.get('msg', '')
                
                # 构建结果信息
                result_info = f"卡号: {pe_no}" if pe_no else "未指定卡号"
                if shop_code:
                    result_info += f", 门店: {shop_code}"
                
                # 根据结果显示不同的日志信息
                if result_code == 0:
                    if image_count > 0:
                        self.signal_emitter.log_signal.emit("接口调用", f"16号接口: 查询成功 - {result_info}, 返回图片数: {image_count}")
                    else:
                        self.signal_emitter.log_signal.emit("接口调用", f"16号接口: 查询完成但未找到图片 - {result_info}, {result_msg}")
                else:
                    self.signal_emitter.log_signal.emit("接口调用", f"16号接口: 查询失败 - {result_info}, 错误: {result_msg}")
                
                return jsonify(result)

            except Exception as e:
                error_msg = f"16号接口异常: {str(e)}"
                self.signal_emitter.log_signal.emit("错误", error_msg)
                return jsonify({'code': -1, 'msg': f'查询失败: {str(e)}', 'data': []})

        @self.app.route('/dx/inter/deleteAbnormal', methods=['POST'])
        def delete_abnormal():
            """17号接口 - 删除重要异常接口"""
            try:
                # 使用通用数据解析方法
                data = self.parse_request_data(request)
                if not data:
                    return jsonify({'code': -1, 'msg': '请求数据不能为空', 'data': []})

                self.signal_emitter.log_signal.emit("接口调用", "17号接口: 收到删除重要异常请求")
                
                # 调用17号接口实现
                from interface_17_deleteAbnormal import TianjianInterface17
                interface = TianjianInterface17(self.api_config)
                result = interface.delete_abnormal(data)
                
                self.signal_emitter.log_signal.emit("接口调用", f"17号接口: 处理完成")
                return jsonify(result)

            except Exception as e:
                error_msg = f"17号接口异常: {str(e)}"
                self.signal_emitter.log_signal.emit("错误", error_msg)
                return jsonify({'code': -1, 'msg': f'操作失败: {str(e)}', 'data': []})

        @self.app.route('/dx/inter/getDoctorInfo', methods=['POST'])
        def get_doctor_info():
            """18号接口 - 查询医生信息接口"""
            try:
                # 使用通用数据解析方法
                data = self.parse_request_data(request)
                if not data:
                    return jsonify({'code': -1, 'msg': '请求数据不能为空', 'data': []})

                # 提取请求信息用于日志
                doctor_id = data.get('id', '')
                hospital_code = data.get('shopcode', '')
                
                # 构建详细的请求信息
                request_info = f"医生ID: {doctor_id}" if doctor_id else "查询全部医生"
                if hospital_code:
                    request_info += f", 机构: {hospital_code}"
                
                self.signal_emitter.log_signal.emit("接口调用", f"18号接口: 收到查询医生信息请求 - {request_info}")
                
                # 调用18号接口实现
                from interface_18_getDoctorInfo import TianjianInterface18
                interface = TianjianInterface18(Config.get_tianjian_api_config())

                # 调用查询方法
                result = interface.query_doctor_info(doctor_id, hospital_code, test_mode=False)
                
                # 安全地获取数据长度和结果信息
                data_count = 0
                if result.get('data'):
                    data_count = len(result['data'])
                
                result_code = result.get('code', -1)
                result_msg = result.get('msg', '')
                
                # 构建结果信息
                result_info = f"医生ID: {doctor_id}" if doctor_id else "查询全部医生"
                if hospital_code:
                    result_info += f", 机构: {hospital_code}"
                
                # 根据结果显示不同的日志信息
                if result_code == 0:
                    if data_count > 0:
                        self.signal_emitter.log_signal.emit("接口调用", f"18号接口: 查询成功 - {result_info}, 返回医生数: {data_count}")
                    else:
                        self.signal_emitter.log_signal.emit("接口调用", f"18号接口: 查询完成但未找到医生信息 - {result_info}")
                else:
                    self.signal_emitter.log_signal.emit("接口调用", f"18号接口: 查询失败 - {result_info}, 错误: {result_msg}")
                
                return jsonify(result)

            except Exception as e:
                import traceback
                
                # 提取请求信息用于错误日志
                try:
                    data = request.get_json() or {}
                    doctor_id = data.get('id', '')
                    hospital_code = data.get('shopcode', '')
                    error_info = f"医生ID: {doctor_id}" if doctor_id else "查询全部医生"
                    if hospital_code:
                        error_info += f", 机构: {hospital_code}"
                except:
                    error_info = "请求参数解析失败"
                
                error_msg = f"18号接口异常 - {error_info}: {str(e)}"
                traceback_msg = traceback.format_exc()
                self.signal_emitter.log_signal.emit("错误", error_msg)
                self.signal_emitter.log_signal.emit("错误", f"详细错误信息: {traceback_msg}")
                return jsonify({'code': -1, 'msg': f'查询失败: {str(e)}', 'data': []})

        @self.app.route('/dx/inter/getDeptInfo', methods=['POST'])
        def get_dept_info():
            """19号接口 - 查询科室信息接口"""
            try:
                # 使用通用数据解析方法
                data = self.parse_request_data(request)
                if not data:
                    return jsonify({'code': -1, 'msg': '请求数据不能为空', 'data': []})

                # 提取请求信息用于日志
                dept_id = data.get('id', '')
                hospital_code = data.get('shopcode', '')
                
                # 构建详细的请求信息
                request_info = f"科室ID: {dept_id}" if dept_id else "查询全部科室"
                if hospital_code:
                    request_info += f", 机构: {hospital_code}"
                
                self.signal_emitter.log_signal.emit("接口调用", f"19号接口: 收到查询科室信息请求 - {request_info}")
                
                # 调用19号接口实现
                from interface_19_getDeptInfo import TianjianInterface19
                interface = TianjianInterface19(Config.get_tianjian_api_config())
                result = interface.get_dept_info(data)
                
                # 安全地获取数据长度和结果信息
                data_count = 0
                if result.get('data'):
                    data_count = len(result['data'])
                
                result_code = result.get('code', -1)
                result_msg = result.get('msg', '')
                
                # 构建结果信息
                result_info = f"科室ID: {dept_id}" if dept_id else "查询全部科室"
                if hospital_code:
                    result_info += f", 机构: {hospital_code}"
                
                # 根据结果显示不同的日志信息
                if result_code == 0:
                    if data_count > 0:
                        self.signal_emitter.log_signal.emit("接口调用", f"19号接口: 查询成功 - {result_info}, 返回科室数: {data_count}")
                    else:
                        self.signal_emitter.log_signal.emit("接口调用", f"19号接口: 查询完成但未找到科室信息 - {result_info}")
                else:
                    self.signal_emitter.log_signal.emit("接口调用", f"19号接口: 查询失败 - {result_info}, 错误: {result_msg}")
                
                return jsonify(result)

            except Exception as e:
                import traceback
                
                # 提取请求信息用于错误日志
                try:
                    data = request.get_json() or {}
                    dept_id = data.get('id', '')
                    hospital_code = data.get('shopcode', '')
                    error_info = f"科室ID: {dept_id}" if dept_id else "查询全部科室"
                    if hospital_code:
                        error_info += f", 机构: {hospital_code}"
                except:
                    error_info = "请求参数解析失败"
                
                error_msg = f"19号接口异常 - {error_info}: {str(e)}"
                traceback_msg = traceback.format_exc()
                self.signal_emitter.log_signal.emit("错误", error_msg)
                self.signal_emitter.log_signal.emit("错误", f"详细错误信息: {traceback_msg}")
                return jsonify({'code': -1, 'msg': f'查询失败: {str(e)}', 'data': []})

        @self.app.route('/dx/inter/getPersonalOrders', methods=['POST'])
        def get_personal_orders():
            """20号接口 - 查询个人开单接口"""
            try:
                # 使用通用数据解析方法
                data = self.parse_request_data(request)
                if not data:
                    return jsonify({'code': -1, 'msg': '请求数据不能为空', 'data': []})

                # 提取请求信息用于日志
                pe_no_list = data.get('peNoList', [])
                hospital_code = data.get('shopcode', '')
                
                # 构建详细的请求信息
                if pe_no_list:
                    request_info = f"体检号数量: {len(pe_no_list)}"
                    if len(pe_no_list) <= 3:
                        request_info += f" ({', '.join(pe_no_list)})"
                    else:
                        request_info += f" ({', '.join(pe_no_list[:3])}...)"
                else:
                    request_info = "无体检号"
                
                if hospital_code:
                    request_info += f", 机构: {hospital_code}"
                
                self.signal_emitter.log_signal.emit("接口调用", f"20号接口: 收到查询个人开单请求 - {request_info}")
                
                # 调用20号接口实现
                from interface_20_getPersonalOrders import TianjianInterface20
                interface = TianjianInterface20(Config.get_tianjian_api_config())
                result = interface.get_personal_orders(data)
                
                # 安全地获取数据长度和结果信息
                data_count = 0
                if result.get('data'):
                    data_count = len(result['data'])
                
                result_code = result.get('code', -1)
                result_msg = result.get('msg', '')
                
                # 构建结果信息
                if pe_no_list:
                    result_info = f"体检号数量: {len(pe_no_list)}"
                else:
                    result_info = "无体检号"
                
                if hospital_code:
                    result_info += f", 机构: {hospital_code}"
                
                # 根据结果显示不同的日志信息
                if result_code == 0:
                    if data_count > 0:
                        # 计算涉及的体检号数量
                        unique_pe_nos = set()
                        for order in result.get('data', []):
                            pe_no = order.get('peNo')
                            if pe_no:
                                unique_pe_nos.add(pe_no)
                        
                        pe_count = len(unique_pe_nos)
                        self.signal_emitter.log_signal.emit("接口调用", f"20号接口: 查询成功 - {result_info}, 返回开单记录: {data_count} 条, 涉及体检号: {pe_count} 个")
                    else:
                        self.signal_emitter.log_signal.emit("接口调用", f"20号接口: 查询完成但未找到开单信息 - {result_info}")
                else:
                    self.signal_emitter.log_signal.emit("接口调用", f"20号接口: 查询失败 - {result_info}, 错误: {result_msg}")
                
                return jsonify(result)

            except Exception as e:
                import traceback
                
                # 提取请求信息用于错误日志
                try:
                    data = request.get_json() or {}
                    pe_no_list = data.get('peNoList', [])
                    hospital_code = data.get('shopcode', '')
                    
                    if pe_no_list:
                        error_info = f"体检号数量: {len(pe_no_list)}"
                    else:
                        error_info = "无体检号"
                    
                    if hospital_code:
                        error_info += f", 机构: {hospital_code}"
                except:
                    error_info = "请求参数解析失败"
                
                error_msg = f"20号接口异常 - {error_info}: {str(e)}"
                traceback_msg = traceback.format_exc()
                self.signal_emitter.log_signal.emit("错误", error_msg)
                self.signal_emitter.log_signal.emit("错误", f"详细错误信息: {traceback_msg}")
                return jsonify({'code': -1, 'msg': f'查询失败: {str(e)}', 'data': []})

        @self.app.route('/dx/inter/getAbnormalNotice', methods=['POST'])
        def get_abnormal_notice():
            """21号接口 - 查询异常通知接口"""
            try:
                # 使用通用数据解析方法
                data = self.parse_request_data(request)
                if not data:
                    return jsonify({'code': -1, 'msg': '请求数据不能为空', 'data': []})

                # 提取请求信息用于日志
                pe_no = data.get('peNo', '')
                # 支持多种字段名：hospital.code（标准）、shopcode（兼容）、cshopcode（兼容）
                hospital_data = data.get('hospital', {})
                hospital_code = hospital_data.get('code', '') or data.get('shopcode', '') or data.get('cshopcode', '')
                
                # 构建详细的请求信息
                request_info = f"体检号: {pe_no}" if pe_no else "无体检号"
                if hospital_code:
                    request_info += f", 机构: {hospital_code}"
                
                self.signal_emitter.log_signal.emit("接口调用", f"21号接口: 收到查询异常通知请求 - {request_info}")
                
                # 调用21号接口实现
                from interface_21_getAbnormalNotice import TianjianInterface21
                interface = TianjianInterface21(Config.get_tianjian_api_config())
                result = interface.get_abnormal_notice(data)
                
                # 安全地获取数据长度和结果信息
                data_count = 0
                has_data = False
                if result.get('data'):
                    has_data = True
                    data_count = 1  # 异常通知通常返回单条记录或None
                
                result_code = result.get('code', -1)
                result_msg = result.get('msg', '')
                
                # 构建结果信息
                result_info = f"体检号: {pe_no}" if pe_no else "无体检号"
                if hospital_code:
                    result_info += f", 机构: {hospital_code}"
                
                # 根据结果显示不同的日志信息
                if result_code == 0:
                    if has_data:
                        abnormal_data = result.get('data', {})
                        abnormal_name = abnormal_data.get('abnormalName', '未知异常')
                        has_notice = abnormal_data.get('hasNotice', '0')
                        notice_status = "已通知" if has_notice == '1' else "未通知"
                        
                        self.signal_emitter.log_signal.emit("接口调用", f"21号接口: 查询成功 - {result_info}, 异常: {abnormal_name}, 状态: {notice_status}")
                    else:
                        self.signal_emitter.log_signal.emit("接口调用", f"21号接口: 查询完成但未找到异常通知 - {result_info}")
                else:
                    self.signal_emitter.log_signal.emit("接口调用", f"21号接口: 查询失败 - {result_info}, 错误: {result_msg}")
                
                return jsonify(result)

            except Exception as e:
                import traceback
                
                # 提取请求信息用于错误日志
                try:
                    data = request.get_json() or {}
                    pe_no = data.get('peNo', '')
                    hospital_code = data.get('shopcode', '')
                    error_info = f"体检号: {pe_no}" if pe_no else "无体检号"
                    if hospital_code:
                        error_info += f", 机构: {hospital_code}"
                except:
                    error_info = "请求参数解析失败"
                
                error_msg = f"21号接口异常 - {error_info}: {str(e)}"
                traceback_msg = traceback.format_exc()
                self.signal_emitter.log_signal.emit("错误", error_msg)
                self.signal_emitter.log_signal.emit("错误", f"详细错误信息: {traceback_msg}")
                return jsonify({'code': -1, 'msg': f'查询失败: {str(e)}', 'data': None})
        
        # ============ 12-15号接口扩展路由 ============
        
        @self.app.route('/dx/inter/lockPeInfo', methods=['POST'])
        def lock_pe_info():
            """12号接口 - 主检锁定解锁接口"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'code': -1, 'msg': '请求数据不能为空', 'data': []})

                # 提取卡号信息用于日志
                pe_info_list = data.get('peInfoList', [])
                pe_nos = [pe.get('peNo', '') for pe in pe_info_list if pe.get('peNo')]
                pe_count = len(pe_nos)
                pe_summary = f"{pe_count}条记录" + (f" (卡号: {', '.join(pe_nos[:3])}{'等' if pe_count > 3 else ''})" if pe_nos else "")
                
                self.signal_emitter.log_signal.emit("接口调用", f"12号接口: 收到主检锁定解锁请求 - {pe_summary}")
                
                # 调用12号接口实现
                from interface_12_lockPeInfo import TianjianInterface12
                interface = TianjianInterface12(Config.get_tianjian_api_config())
                result = interface.lock_pe_info(data)
                
                success_msg = "成功" if result.get('code') == 0 else "失败"
                self.signal_emitter.log_signal.emit("接口调用", f"12号接口: 处理{success_msg} - {pe_summary}")
                return jsonify(result)

            except Exception as e:
                error_msg = f"12号接口异常: {str(e)}"
                self.signal_emitter.log_signal.emit("错误", error_msg)
                return jsonify({'code': -1, 'msg': f'操作失败: {str(e)}', 'data': []})

        @self.app.route('/dx/inter/updatePeStatus', methods=['POST'])
        def update_pe_status():
            """13号接口 - 体检状态更新接口"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'code': -1, 'msg': '请求数据不能为空', 'data': []})

                # 提取卡号信息用于日志
                pe_no = data.get('peNo', '')
                node_type = data.get('nodeType', '')
                do_user = data.get('doUser', {})
                user_name = do_user.get('name', '')
                pe_info = f"卡号: {pe_no}" if pe_no else "未知卡号"
                node_desc = {"1": "登记", "2": "分科检查", "3": "总检", "4": "总审"}.get(node_type, f"节点{node_type}")
                
                self.signal_emitter.log_signal.emit("接口调用", f"13号接口: 收到体检状态更新请求 - {pe_info}, 节点: {node_desc}, 操作人: {user_name}")
                
                # 调用13号接口实现
                from interface_13_updatePeStatus import TianjianInterface13
                interface = TianjianInterface13(Config.get_tianjian_api_config())
                result = interface.update_pe_status_service(data)
                
                success_msg = "成功" if result.get('code') == 0 else "失败"
                self.signal_emitter.log_signal.emit("接口调用", f"13号接口: 处理{success_msg} - {pe_info}")
                return jsonify(result)

            except Exception as e:
                error_msg = f"13号接口异常: {str(e)}"
                self.signal_emitter.log_signal.emit("错误", error_msg)
                return jsonify({'code': -1, 'msg': f'操作失败: {str(e)}', 'data': []})

        @self.app.route('/dx/inter/markAbnormal', methods=['POST'])
        def mark_abnormal():
            """14号接口 - 重要异常标注接口"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'code': -1, 'msg': '请求数据不能为空', 'data': []})

                # 提取卡号信息用于日志
                abnormal_list = data.get('abnormalList', [])
                pe_nos = [ab.get('peNo', '') for ab in abnormal_list if ab.get('peNo')]
                ab_count = len(pe_nos) 
                pe_summary = f"{ab_count}条异常标注" + (f" (卡号: {', '.join(pe_nos[:3])}{'等' if ab_count > 3 else ''})" if pe_nos else "")
                
                self.signal_emitter.log_signal.emit("接口调用", f"14号接口: 收到重要异常标注请求 - {pe_summary}")
                
                # 调用14号接口实现
                from interface_14_markAbnormal import TianjianInterface14
                interface = TianjianInterface14(Config.get_tianjian_api_config())
                result = interface.mark_abnormal_service(data)
                
                success_msg = "成功" if result.get('code') == 0 else "失败"
                self.signal_emitter.log_signal.emit("接口调用", f"14号接口: 处理{success_msg} - {pe_summary}")
                return jsonify(result)

            except Exception as e:
                error_msg = f"14号接口异常: {str(e)}"
                self.signal_emitter.log_signal.emit("错误", error_msg)
                return jsonify({'code': -1, 'msg': f'操作失败: {str(e)}', 'data': []})

        @self.app.route('/dx/inter/returnDept', methods=['POST'])
        def return_dept():
            """15号接口 - 分科退回接口(支持多门店数据库)"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'code': -1, 'msg': '请求数据不能为空', 'data': []})

                # 提取门店编码用于日志
                shop_code = data.get('shopCode', '')
                return_list = data.get('returnList', [data])  # 支持单条和批量
                rt_count = len(return_list)
                
                # 提取第一条记录的信息用于日志显示
                first_record = return_list[0] if return_list else {}
                pe_no = first_record.get('peNo', '')
                
                pe_summary = f"门店{shop_code} {rt_count}条退回" + (f" (首条卡号: {pe_no})" if pe_no else "")
                
                self.signal_emitter.log_signal.emit("接口调用", f"15号接口: 收到分科退回请求 - {pe_summary}")
                
                # 使用支持多门店的接口实现
                from interface_15_returnDept_multi_shop import TianjianInterface15MultiShop
                interface = TianjianInterface15MultiShop(Config.get_tianjian_api_config())
                
                if rt_count == 1:
                    # 单条处理
                    result = interface.return_dept_service(first_record)
                else:
                    # 批量处理
                    batch_results = []
                    success_count = 0
                    error_count = 0
                    
                    for record in return_list:
                        try:
                            single_result = interface.return_dept_service(record)
                            batch_results.append(single_result)
                            
                            if single_result.get('code') == 0:
                                success_count += 1
                            else:
                                error_count += 1
                                
                        except Exception as e:
                            error_result = {
                                'code': -1,
                                'msg': f'处理失败: {str(e)}',
                                'data': {'peNo': record.get('peNo', '')}
                            }
                            batch_results.append(error_result)
                            error_count += 1
                    
                    # 构建批量结果
                    result = {
                        'code': 0 if error_count == 0 else -1,
                        'msg': f'批量处理完成: 成功{success_count}条, 失败{error_count}条',
                        'data': {
                            'total': rt_count,
                            'success': success_count,
                            'error': error_count,
                            'results': batch_results
                        }
                    }
                
                success_msg = "成功" if result.get('code') == 0 else "失败"
                self.signal_emitter.log_signal.emit("接口调用", f"15号接口: 处理{success_msg} - {pe_summary}")
                return jsonify(result)

            except Exception as e:
                error_msg = f"15号接口异常: {str(e)}"
                self.signal_emitter.log_signal.emit("错误", error_msg)
                return jsonify({'code': -1, 'msg': f'操作失败: {str(e)}', 'data': []})
        
        # ============ 路由定义结束 ============

    def process_conclusion_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理天健云回传的总检信息

        Args:
            data: 天健云回传的数据

        Returns:
            处理结果
        """
        try:
            # 提取基本信息
            hospital = data.get('hospital', {})
            shop_code = hospital.get('code', '')  # 现在是门店编码
            pe_no = data.get('peNo', '')

            if not pe_no:
                return {
                    'success': False,
                    'error': '体检号不能为空',
                    'data': []
                }

            # 根据门店编码获取机构配置
            from multi_org_config import get_org_config_by_shop_code
            org_config = get_org_config_by_shop_code(shop_code)

            # 初始化变量
            org_code = ""
            db_connection_string = ""

            if not org_config:
                # 如果没有找到特定配置，返回错误
                self.signal_emitter.log_signal.emit("错误", f"未找到门店编码 {shop_code} 对应的机构配置")
                return {
                    'success': False,
                    'error': f'未找到门店编码 {shop_code} 对应的机构配置',
                    'data': []
                }

            # 获取机构编码
            org_code = org_config.get('org_code', '')


            # 确保org_code有值，如果没有则使用门店编码
            if not org_code:
                org_code = shop_code
                self.signal_emitter.log_signal.emit("警告", f"机构编码为空，使用门店编码: {org_code}")

            # 从机构配置对象获取数据库连接字符串
            from center_organization_service import CenterOrganizationConfig
            if isinstance(org_config, dict):
                # 如果是字典，需要转换为配置对象
                config_obj = CenterOrganizationConfig()
                for key, value in org_config.items():
                    if hasattr(config_obj, key):
                        setattr(config_obj, key, value)
                db_connection_string = config_obj.get_db_connection_string()
            else:
                # 如果已经是配置对象
                db_connection_string = org_config.get_db_connection_string()



            if not db_connection_string or not db_connection_string.strip():
                return {
                    'success': False,
                    'error': f'门店编码 {shop_code} 的数据库连接未配置',
                    'data': []
                }

            # 连接数据库并处理数据
            from database_service import DatabaseService
            db_service = DatabaseService(db_connection_string)

            if not db_service.connect():
                return {
                    'success': False,
                    'error': '数据库连接失败',
                    'data': []
                }

            try:
                # 更新数据库
                result = self.update_diagnosis_info(data, db_service)

                if result['success']:
                    self.signal_emitter.log_signal.emit("接口调用", f"体检号 {pe_no} 总检信息更新成功")
                    return {
                        'success': True,
                        'data': {
                            'peNo': pe_no,
                            'updated_records': result.get('updated_records', 0),
                            'conclusion_count': result.get('conclusion_count', 0)
                        }
                    }
                else:
                    return {
                        'success': False,
                        'error': result.get('error', '更新失败'),
                        'data': []
                    }

            finally:
                db_service.disconnect()

        except Exception as e:
            return {
                'success': False,
                'error': f'处理异常: {str(e)}',
                'data': []
            }

    def update_diagnosis_info(self, data: Dict[str, Any], db_service) -> Dict[str, Any]:
        """更新总检信息到数据库"""
        try:
            pe_no = data.get('peNo', '')
            main_check_finish_time = data.get('mainCheckFinishTime', '')
            main_check_finish_doctor = data.get('mainCheckFinishDoctor', {})
            conclusion_list = data.get('conclusionList', [])
            current_node_type = data.get('currentNodeType', 0)

            updated_records = 0
            conclusion_count = len(conclusion_list)

            # 1. 更新T_Register_Main表
            if main_check_finish_time and main_check_finish_doctor:
                update_main_sql = """
                UPDATE T_Register_Main
                SET cStatus = ?,
                    cOperCode = ?,
                    cOperName = ?,
                    dOperdate = ?
                WHERE cClientCode = ?
                """

                status = '6' if current_node_type == 4 else '4'

                try:
                    if main_check_finish_time:
                        finish_time = datetime.strptime(main_check_finish_time, '%Y-%m-%d %H:%M:%S')
                    else:
                        finish_time = datetime.now()

                    params = (
                        status,
                        main_check_finish_doctor.get('code', ''),
                        main_check_finish_doctor.get('name', ''),
                        finish_time,
                        pe_no
                    )

                    result = db_service.execute_update(update_main_sql, params)
                    if result > 0:
                        updated_records += result

                except Exception as e:
                    self.signal_emitter.log_signal.emit("警告", f"更新T_Register_Main表失败: {e}")

            # 2. 获取客户编码用于清理旧数据
            client_code_for_cleanup = self.get_client_code_by_card_no(pe_no, db_service)
            if client_code_for_cleanup:
                try:
                    # 清理旧的总检结论数据
                    db_service.execute_update("DELETE FROM T_Diag_result WHERE cClientCode = ?", (client_code_for_cleanup,))

                    # 清理旧的疾病结论数据
                    db_service.execute_update("DELETE FROM T_Check_Result_Illness WHERE cClientCode = ?", (client_code_for_cleanup,))
                except Exception as e:
                    self.signal_emitter.log_signal.emit("警告", f"清理旧数据失败: {e}")
            else:
                self.signal_emitter.log_signal.emit("警告", f"无法获取卡号 {pe_no} 对应的客户编码，跳过清理旧数据")

            # 3. 插入新的结论数据
            # 由于T_Diag_result表主键只有cClientCode，一个客户只能有一条记录
            # 所以我们将所有结论合并成一条记录插入
            if conclusion_list:
                try:
                    self.insert_merged_conclusion_record(pe_no, conclusion_list, main_check_finish_doctor, db_service)
                    updated_records += 1
                except Exception as e:
                    self.signal_emitter.log_signal.emit("警告", f"插入合并结论记录失败: {e}")

                # 然后为每个结论插入详细的疾病记录到T_Check_Result_Illness表
                for i, conclusion in enumerate(conclusion_list, 1):
                    try:
                        self.insert_illness_record(pe_no, conclusion, main_check_finish_doctor, i, db_service)
                    except Exception as e:
                        self.signal_emitter.log_signal.emit("警告", f"插入疾病记录失败: {e}")
                        continue

            return {
                'success': True,
                'updated_records': updated_records,
                'conclusion_count': conclusion_count
            }

        except Exception as e:
            return {
                'success': False,
                'error': f'更新数据库失败: {str(e)}'
            }

    def get_client_code_by_card_no(self, card_no: str, db_service) -> str:
        """通过卡号查询客户编码"""
        try:
            sql = f"SELECT cClientCode FROM T_Register_Main WHERE cCardNo = '{card_no}'"
            result = db_service.execute_query(sql)

            if result and len(result) > 0:
                return result[0].get('cClientCode', '')
            else:
                return ''

        except Exception as e:
            self.signal_emitter.log_signal.emit("错误", f"查询客户编码失败: {e}")
            return ''



    def insert_merged_conclusion_record(self, pe_no: str, conclusion_list: list,
                                       doctor: Dict[str, Any], db_service):
        """插入合并的总检结论记录到T_Diag_result表"""
        if not conclusion_list:
            return

        # 通过卡号查询客户编码
        client_code = self.get_client_code_by_card_no(pe_no, db_service)
        if not client_code:
            self.signal_emitter.log_signal.emit("警告", f"未找到卡号 {pe_no} 对应的客户编码")
            return

        # 合并所有结论为一条记录
        conclusion_names = []
        conclusion_descs = []

        for conclusion in conclusion_list:
            name = conclusion.get('conclusionName', '')
            if name:
                conclusion_names.append(name)

            explain = conclusion.get('explain', '')
            suggest = conclusion.get('suggest', '')
            if explain or suggest:
                desc_parts = []
                if explain:
                    desc_parts.append(f"解释：{explain}")
                if suggest:
                    desc_parts.append(f"建议：{suggest}")
                conclusion_descs.append(" | ".join(desc_parts))

        # 合并结论名称和描述
        merged_diag = " ; ".join(conclusion_names)[:255] if conclusion_names else ""
        merged_desc = " ; ".join(conclusion_descs)[:500] if conclusion_descs else ""

        # 限制字段长度
        doctor_code = doctor.get('code', '')[:9] if doctor.get('code') else ""
        doctor_name = doctor.get('name', '')[:12] if doctor.get('name') else ""
        org_code = "09"  # 使用默认机构编码
        limited_org_code = org_code[:2] if org_code else ""

        # 先删除可能存在的记录
        try:
            delete_sql = "DELETE FROM T_Diag_result WHERE cClientCode = ?"
            db_service.execute_update(delete_sql, (client_code,))
        except Exception as e:
            pass

        # 插入合并的记录 - 使用正确的字段结构
        insert_diag_sql = """
        INSERT INTO T_Diag_result (
            cClientCode, cDiag, cDiagDesc, cDoctCode, cDoctName, dDoctOperdate,
            cOperCode, cOpername, dOperDate, cShopCode
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """

        diag_params = (
            client_code,        # cClientCode - 客户编码
            merged_diag,        # cDiag - 总检结论
            merged_desc,        # cDiagDesc - 总检结论描述
            doctor_code,        # cDoctCode - 总检医生编码
            doctor_name,        # cDoctName - 总检医生姓名
            datetime.now(),     # dDoctOperdate - 总检时间
            doctor_code,        # cOperCode - 初审医生编码（暂用总检医生）
            doctor_name,        # cOpername - 初审医生姓名（暂用总检医生）
            datetime.now(),     # dOperDate - 初审时间
            limited_org_code    # cShopCode - 机构编码
        )

        try:
            db_service.execute_update(insert_diag_sql, diag_params)
        except Exception as e:
            # 只在出错时显示详细调试信息
            self.signal_emitter.log_signal.emit("错误", f"T_Diag_result插入失败: {e}")
            self.signal_emitter.log_signal.emit("调试", "T_Diag_result参数长度检查:")
            param_names = ["cClientCode", "cDiag", "cDiagDesc", "cDoctCode", "cDoctName",
                          "dDoctOperdate", "cOperCode", "cOpername", "dOperDate", "cShopCode"]
            for i, (name, value) in enumerate(zip(param_names, diag_params)):
                value_str = str(value) if value is not None else "NULL"
                length = len(value_str)
                # 标记可能超长的字段
                warning = " ⚠️" if length > 50 else ""
                self.signal_emitter.log_signal.emit("调试", f"  {name:<15}: 长度{length:3d}{warning}")
            raise

    def insert_illness_record(self, pe_no: str, conclusion: Dict[str, Any],
                             doctor: Dict[str, Any], sequence: int, db_service):
        """插入单条疾病记录到T_Check_Result_Illness表"""
        conclusion_name = conclusion.get('conclusionName', '')
        conclusion_code = conclusion.get('conclusionCode', '')
        parent_code = conclusion.get('parentCode', '')
        suggest = conclusion.get('suggest', '')
        explain = conclusion.get('explain', '')
        check_result = conclusion.get('checkResult', '')
        level = conclusion.get('level', 3)

        if not conclusion_name:
            return

        # 通过卡号查询客户编码
        client_code = self.get_client_code_by_card_no(pe_no, db_service)
        if not client_code:
            return

        # 限制字段长度
        doctor_code = doctor.get('code', '')[:9] if doctor.get('code') else ""
        doctor_name = doctor.get('name', '')[:12] if doctor.get('name') else ""

        # 在插入前先删除可能存在的重复记录
        try:
            delete_illness_sql = "DELETE FROM T_Check_Result_Illness WHERE cClientCode = ? AND cDeptcode = ? AND cMainName = ? AND cIllnessCode = ?"
            limited_parent_code_for_delete = (parent_code or 'MAIN')[:20] if parent_code else 'MAIN'
            limited_conclusion_code_for_delete = conclusion_code[:6] if conclusion_code else ""
            db_service.execute_update(delete_illness_sql, (client_code, limited_parent_code_for_delete, '总检结论', limited_conclusion_code_for_delete))
        except Exception as e:
            pass

        # 插入T_Check_Result_Illness表
        insert_illness_sql = """
        INSERT INTO T_Check_Result_Illness (
            cClientCode, cDeptcode, cMainName, cIllnessCode, cIllnessName,
            cIllExplain, cReason, cAdvice, cGrade, cDoctCode, cDoctName,
            dOperdate, nPrintIndex
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """

        # 限制字段长度
        limited_parent_code = (parent_code or 'MAIN')[:20] if parent_code else 'MAIN'
        limited_conclusion_code = conclusion_code[:6] if conclusion_code else ""
        limited_conclusion_name = conclusion_name[:100] if conclusion_name else ""
        limited_explain = explain[:500] if explain else ""
        limited_suggest = suggest[:500] if suggest else ""

        illness_params = (
            client_code,
            limited_parent_code,
            '总检结论',
            limited_conclusion_code or f'AUTO_{sequence:03d}',
            limited_conclusion_name,
            limited_explain,
            check_result[:200] if check_result else "",
            limited_suggest,
            str(level),
            doctor_code,
            doctor_name,
            datetime.now(),
            sequence
        )

        try:
            db_service.execute_update(insert_illness_sql, illness_params)
        except Exception as e:
            # 只在出错时显示详细调试信息
            self.signal_emitter.log_signal.emit("错误", f"T_Check_Result_Illness插入失败: {e}")
            self.signal_emitter.log_signal.emit("调试", "T_Check_Result_Illness参数长度检查:")
            illness_param_names = ["cClientCode", "cDeptcode", "cMainName", "cIllnessCode", "cIllnessName",
                                  "cIllExplain", "cReason", "cAdvice", "cGrade", "cDoctCode", "cDoctName",
                                  "dOperdate", "nPrintIndex"]

            # 字段长度限制参考
            field_limits = {
                "cClientCode": 10, "cDeptcode": 6, "cMainName": 40, "cIllnessCode": 6,
                "cIllnessName": 100, "cIllExplain": 500, "cReason": 200, "cAdvice": 500,
                "cGrade": 1, "cDoctCode": 9, "cDoctName": 12, "nPrintIndex": 4
            }

            for name, value in zip(illness_param_names, illness_params):
                if name == "dOperdate":  # 跳过时间字段
                    continue
                value_str = str(value) if value is not None else "NULL"
                length = len(value_str)
                limit = field_limits.get(name, 50)

                if length > limit:
                    self.signal_emitter.log_signal.emit("错误", f"  {name:<15}: 长度{length:3d} > 限制{limit:3d} ❌ 超出!")
                elif length > limit * 0.8:  # 接近限制时警告
                    self.signal_emitter.log_signal.emit("警告", f"  {name:<15}: 长度{length:3d} / 限制{limit:3d} ⚠️ 接近限制")
                else:
                    self.signal_emitter.log_signal.emit("调试", f"  {name:<15}: 长度{length:3d} / 限制{limit:3d} ✅")

            # 不抛出异常，继续处理其他记录
            pass

    def get_dict_data(self, hospital_code: str = None):
        """获取所有字典数据（08号接口使用）"""
        from database_service import get_database_service, DatabaseService
        from multi_org_config import get_org_config_by_shop_code
        
        # 根据hospital_code获取对应的数据库服务
        if hospital_code:
            org_config = get_org_config_by_shop_code(hospital_code)
            if org_config:
                # 构建连接字符串
                connection_string = (
                    f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                    f"SERVER={org_config['db_host']},{org_config['db_port']};"
                    f"DATABASE={org_config['db_name']};"
                    f"UID={org_config['db_user']};"
                    f"PWD={org_config['db_password']};"
                    f"TrustServerCertificate=yes;"
                )
                db_service = DatabaseService(connection_string)
            else:
                db_service = get_database_service()
        else:
            db_service = get_database_service()
            
        if not db_service.connect():
            raise Exception("数据库连接失败")

        try:
            all_data = []

            # 获取体检服务类型字典（从Code_Cust_Type表）
            try:
                sql_vip = """
                SELECT
                    cCode as id,
                    cName as name,
                    'OPEVIP' as type
                FROM Code_Cust_Type
                WHERE (cStopTag IS NULL OR cStopTag != '1')
                ORDER BY cCode
                """
                vip_result = db_service.execute_query(sql_vip)
                all_data.extend(vip_result)
            except Exception as e:
                # 如果表不存在或字段不匹配，记录错误但继续处理
                self.signal_emitter.log_signal.emit("警告", f"08号接口: Code_Cust_Type表查询失败: {str(e)}")

            # 获取体检类型字典（从Code_Comm_Dict表）  
            try:
                sql_type = """
                SELECT
                    cCode as id,
                    cName as name,
                    'OPBET' as type
                FROM Code_Comm_Dict
                WHERE iNameCode = 39 AND (cStopTag IS NULL OR cStopTag != '1')
                ORDER BY cCode
                """
                type_result = db_service.execute_query(sql_type)
                all_data.extend(type_result)
            except Exception as e:
                # 如果表不存在或字段不匹配，记录错误但继续处理
                self.signal_emitter.log_signal.emit("警告", f"08号接口: Code_Comm_Dict表查询失败: {str(e)}")

            return all_data

        finally:
            db_service.disconnect()

    def filter_dict_data(self, dict_data, query_id, query_type):
        """根据参数筛选字典数据（08号接口使用）"""
        filtered_data = dict_data

        # 按类型筛选
        if query_type:
            filtered_data = [item for item in filtered_data if item['type'] == query_type]

        # 按ID筛选
        if query_id:
            filtered_data = [item for item in filtered_data if item['id'] == query_id]

        return filtered_data

    def get_dept_results(self, pe_no_list, dept_id='', hospital_code: str = None):
        """获取科室结果数据（09号接口使用）"""
        from database_service import get_database_service, DatabaseService
        from multi_org_config import get_org_config_by_shop_code
        
        # 根据hospital_code获取对应的数据库服务
        if hospital_code:
            org_config = get_org_config_by_shop_code(hospital_code)
            if org_config:
                # 构建连接字符串
                connection_string = (
                    f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                    f"SERVER={org_config['db_host']},{org_config['db_port']};"
                    f"DATABASE={org_config['db_name']};"
                    f"UID={org_config['db_user']};"
                    f"PWD={org_config['db_password']};"
                    f"TrustServerCertificate=yes;"
                )
                db_service = DatabaseService(connection_string)
            else:
                db_service = get_database_service()
        else:
            db_service = get_database_service()
            
        if not db_service.connect():
            raise Exception("数据库连接失败")

        try:
            all_results = []

            for pe_no in pe_no_list:
                # 通过卡号查询客户编码
                client_code = self.get_client_code_by_card_no(pe_no, db_service)
                if not client_code:
                    continue

                # 查询科室结果数据（支持不同数据库结构）
                dept_results = self.query_dept_results_by_client(client_code, pe_no, dept_id, db_service, hospital_code)
                all_results.extend(dept_results)

            return all_results

        finally:
            db_service.disconnect()

    def query_dept_results_by_client(self, client_code, pe_no, dept_id, db_service, hospital_code: str = None):
        """查询单个客户的科室结果"""
        # 构建科室条件
        dept_condition = ""
        params = [client_code]

        if dept_id:
            dept_condition = "AND rm.cDeptCode = ?"
            params.append(dept_id)

        # 根据数据库结构选择不同的查询方式
        try:
            # 先尝试08门店的字段查询（实际验证过的字段）
            main_sql = f"""
            SELECT DISTINCT
                rm.cClientCode,
                rm.cDeptCode,
                dept.cName as dept_name,
                rm.dOperDate as check_time,
                rm.cDoctCode as check_doctor_code,
                rm.cDoctName as check_doctor_name,
                rm.csuojian as summary,
                rm.cAuditDoctCode as audit_doctor_code,
                rm.cAuditDoctName as audit_doctor_name,
                CASE WHEN rm.dAuditDate IS NOT NULL THEN 'isAudited' ELSE 'notAudited' END as audit_status_code,
                CASE WHEN rm.dAuditDate IS NOT NULL THEN '已审核' ELSE '未审核' END as audit_status_name,
                rm.dAuditDate as audit_time
            FROM T_Check_result_Main rm
            LEFT JOIN Code_Dept_dict dept ON rm.cDeptCode = dept.cCode
            WHERE rm.cClientCode = ? {dept_condition}
            ORDER BY rm.cDeptCode, rm.dOperDate
            """
            main_results = db_service.execute_query(main_sql, params)
        except Exception as e:
            # 如果标准字段查询失败，使用基础字段查询（适用于09门店等）
            self.signal_emitter.log_signal.emit("信息", f"09号接口: 使用基础字段查询科室结果 (hospitalCode={hospital_code})")
            main_sql = f"""
            SELECT DISTINCT
                rm.cClientCode,
                rm.cDeptCode,
                dept.cName as dept_name,
                rm.dOperDate as check_time,
                '' as check_doctor_code,
                '' as check_doctor_name,
                '' as summary,
                '' as audit_doctor_code,
                '' as audit_doctor_name,
                'notAudited' as audit_status_code,
                '未审核' as audit_status_name,
                NULL as audit_time
            FROM T_Check_result_Main rm
            LEFT JOIN Code_Dept_dict dept ON rm.cDeptCode = dept.cCode
            WHERE rm.cClientCode = ? {dept_condition}
            ORDER BY rm.cDeptCode, rm.dOperDate
            """
            main_results = db_service.execute_query(main_sql, params)

        dept_results = []
        for main_row in main_results:
            # 查询该科室的检查项目明细
            item_details = self.query_check_item_details(
                client_code, main_row['cDeptCode'], db_service
            )

            # 构建科室结果数据
            dept_result = {
                "peNo": pe_no,
                "dept": {
                    "code": main_row['cDeptCode'] or "",
                    "name": main_row['dept_name'] or ""
                },
                "hospital": {
                    "code": hospital_code or "09",
                    "name": "院区"
                },
                "checkTime": main_row['check_time'].strftime('%Y-%m-%d %H:%M:%S') if main_row.get('check_time') and hasattr(main_row['check_time'], 'strftime') else str(main_row.get('check_time', '')),
                "checkDoctor": {
                    "code": main_row['check_doctor_code'] or "",
                    "name": main_row['check_doctor_name'] or ""
                },
                "summary": main_row['summary'] or "未见明显异常",
                "auditDoctor": {
                    "code": main_row['audit_doctor_code'] or "",
                    "name": main_row['audit_doctor_name'] or ""
                },
                "auditStatus": {
                    "code": main_row['audit_status_code'] or "notAudited",
                    "name": main_row['audit_status_name'] or "未审核"
                },
                "auditTime": main_row['audit_time'].strftime('%Y-%m-%d %H:%M:%S') if main_row.get('audit_time') and hasattr(main_row['audit_time'], 'strftime') else str(main_row.get('audit_time', '')),
                "itemDesc": item_details
            }
            dept_results.append(dept_result)

        return dept_results

    def query_check_item_details(self, client_code, dept_code, db_service):
        """查询检查项目明细"""
        # 根据数据库结构选择不同的查询方式
        detail_results = []
        
        try:
            # 先尝试08门店的字段查询（实际验证过的字段）
            detail_sql = """
            SELECT
                rd.cMainCode as apply_item_code,
                rd.cMainName as apply_item_name,
                rd.cDetailCode as check_item_code,
                rd.cDetailName as check_item_name,
                rd.cResult as inspect_result,
                rd.cDetailUnit as unit,
                rd.fTop as reference_max,
                rd.fFlower as reference_min,
                CASE 
                    WHEN rd.fFlower IS NOT NULL AND rd.fTop IS NOT NULL 
                    THEN CAST(ISNULL(rd.fFlower, 0) AS VARCHAR) + '-' + CAST(ISNULL(rd.fTop, 0) AS VARCHAR)
                    ELSE ISNULL(rd.cTopOrFlow, '')
                END as reference,
                rd.nDetailIndex as display_sequence,
                rd.cAbnor as un_normal_flag,
                '' as digit_value,
                CASE
                    WHEN rd.cAbnor = '1' THEN '↑'
                    WHEN rd.cAbnor = '2' THEN '↓'
                    ELSE ''
                END as mds_name
            FROM T_Check_result rd
            WHERE rd.cClientCode = ? AND rd.cDeptCode = ?
            ORDER BY rd.nDetailIndex, rd.cMainCode, rd.cDetailCode
            """
            detail_results = db_service.execute_query(detail_sql, [client_code, dept_code])
            
        except Exception as e:
            # 如果标准字段查询失败，使用09门店的字段查询
            try:
                detail_sql = """
                SELECT
                    rd.cMainCode as apply_item_code,
                    rd.cMainName as apply_item_name,
                    rd.cDetailCode as check_item_code,
                    rd.cDetailName as check_item_name,
                    rd.cResult as inspect_result,
                    rd.cDetailUnit as unit,
                    rd.fTop as reference_max,
                    rd.fFlower as reference_min,
                    CASE 
                        WHEN rd.fFlower IS NOT NULL AND rd.fTop IS NOT NULL 
                        THEN CAST(ISNULL(rd.fFlower, 0) AS VARCHAR) + '-' + CAST(ISNULL(rd.fTop, 0) AS VARCHAR)
                        ELSE ''
                    END as reference,
                    rd.nDetailIndex as display_sequence,
                    rd.cAbnor as un_normal_flag,
                    '' as digit_value,
                    CASE
                        WHEN rd.cAbnor = '1' THEN '↑'
                        WHEN rd.cAbnor = '2' THEN '↓'
                        ELSE ''
                    END as mds_name
                FROM T_Check_result rd
                WHERE rd.cClientCode = ? AND rd.cDeptCode = ?
                ORDER BY rd.nDetailIndex, rd.cMainCode, rd.cDetailCode
                """
                detail_results = db_service.execute_query(detail_sql, [client_code, dept_code])
                
            except Exception as e2:
                # 如果两种查询都失败，返回空列表但不抛出异常
                print(f"[WARN] 查询检查项目明细失败: {str(e2)}")
                detail_results = []

        item_details = []
        for detail_row in detail_results:
            item_detail = {
                "bizApplyId": "",
                "applyItem": {
                    "code": detail_row.get('apply_item_code', '') or "",
                    "name": detail_row.get('apply_item_name', '') or ""
                },
                "checkDoctor": {
                    "code": "",
                    "name": ""
                },
                "checkItem": {
                    "code": detail_row.get('check_item_code', '') or "",
                    "name": detail_row.get('check_item_name', '') or ""
                },
                "inspectResult": detail_row.get('inspect_result', '') or "",
                "unit": detail_row.get('unit', '') or "",
                "referenceMax": str(detail_row.get('reference_max', 0)) if detail_row.get('reference_max') is not None else "0",
                "referenceMin": str(detail_row.get('reference_min', 0)) if detail_row.get('reference_min') is not None else "0",
                "reference": detail_row.get('reference', '') or "",
                "displaySequence": str(detail_row.get('display_sequence', 1)) if detail_row.get('display_sequence') is not None else "1",
                "digitValue": detail_row.get('digit_value', '') or "",
                "mds": {
                    "code": detail_row.get('un_normal_flag', '') or "",
                    "name": detail_row.get('mds_name', '') or ""
                }
            }
            item_details.append(item_detail)

        return item_details

    def get_pe_info_data(self, start_time='', end_time='', pe_no='', hospital_code=''):
        """获取体检单信息数据（10号接口使用）"""
        from database_service import get_database_service, DatabaseService
        from multi_org_config import get_current_org_config, get_org_config_by_shop_code

        # 根据医院编码获取对应的数据库服务
        if hospital_code:
            # 根据hospitalCode获取对应的机构配置
            org_config = get_org_config_by_shop_code(hospital_code)
            if org_config:
                # 构建连接字符串
                connection_string = (
                    f"DRIVER={{{org_config['db_driver']}}};"
                    f"SERVER={org_config['db_host']},{org_config['db_port']};"
                    f"DATABASE={org_config['db_name']};"
                    f"UID={org_config['db_user']};"
                    f"PWD={org_config['db_password']};"
                    f"TrustServerCertificate=yes;"
                )
                db_service = DatabaseService(connection_string)
            else:
                # 如果找不到配置，使用默认数据库
                db_service = get_database_service()
        else:
            # 没有指定医院编码，使用默认数据库
            db_service = get_database_service()
            
        if not db_service.connect():
            raise Exception("数据库连接失败")

        try:
            # 获取当前机构配置
            org_config = get_current_org_config()

            # 构建SQL查询 - 使用正确的字段名
            sql = """
            SELECT
                rm.cClientCode as archiveNo,
                rm.cName as name,
                rm.cIdCard as icCode,
                rm.cSex as sex_code,
                CASE rm.cSex
                    WHEN '1' THEN '男'
                    WHEN '2' THEN '女'
                    ELSE '未知'
                END as sex_name,
                CONVERT(varchar, rm.dBornDate, 112) as birthday,
                rm.cClientCode as peno,
                CONVERT(varchar, rm.dOperdate, 120) as peDate,
                ISNULL(rm.cTel, '') as phone,
                ISNULL(rm.cMarryFlag, '') as ms_code,
                CASE rm.cMarryFlag
                    WHEN '1' THEN '已婚'
                    WHEN '0' THEN '未婚'
                    ELSE '未知'
                END as ms_name,
                '' as vipLevel_code,
                '普通' as vipLevel_name,
                CASE
                    WHEN (rm.cContractCode IS NULL OR rm.cContractCode = '') THEN '1'
                    ELSE '2'
                END as medicalType_code,
                CASE
                    WHEN (rm.cContractCode IS NULL OR rm.cContractCode = '') THEN '个人体检'
                    ELSE '团体体检'
                END as medicalType_name,
                CASE WHEN (rm.cContractCode IS NULL OR rm.cContractCode = '') THEN '0' ELSE '1' END as isGroup,
                '' as company,
                '' as workDept,
                ISNULL(tc.cCode, '') as teamNo,
                '' as professional,
                '' as workAge,
                rm.cStatus as peStates_code,
                CASE rm.cStatus
                    WHEN '0' THEN '登记完成'
                    WHEN '1' THEN '分科未完成'
                    WHEN '2' THEN '分科完成'
                    WHEN '3' THEN '主检初审中'
                    WHEN '4' THEN '主检初审完成'
                    WHEN '5' THEN '主检终审中'
                    WHEN '6' THEN '主检终审完成'
                    WHEN '7' THEN '主检终审完成'
                    ELSE '未知状态'
                END as peStates_name,
                CONVERT(varchar, rm.dOperdate, 120) as deptFinishTime,
                CONVERT(varchar, rm.dOperdate, 120) as firstCheckFinishTime,
                ISNULL(rm.cOperCode, '') as firstCheckFinishDoctor_code,
                ISNULL(rm.cOperName, '') as firstCheckFinishDoctor_name,
                CONVERT(varchar, rm.dOperdate, 120) as mainCheckFinishTime,
                ISNULL(rm.cOperCode, '') as mainCheckFinishDoctor_code,
                ISNULL(rm.cOperName, '') as mainCheckFinishDoctor_name,
                '0' as forbidGoCheck,
                '0' as reportPrint,
                '0' as reportGot,
                '0' as replacementInspectionMark,
                DATEDIFF(YEAR, rm.dBornDate, GETDATE()) as age,
                CASE
                    WHEN rm.cStatus >= '4' THEN '4'
                    WHEN rm.cStatus >= '2' THEN '3'
                    WHEN rm.cStatus >= '1' THEN '2'
                    ELSE '1'
                END as currentNodeType,
                ISNULL(rm.cSuitCode, '') as pePackage_code,
                ISNULL(us.cName, '') as pePackage_name
            FROM T_Register_Main rm
            LEFT JOIN T_Contract tc ON rm.cContractCode = tc.cCode
            LEFT JOIN T_UnitsSuit_Master us ON rm.cSuitCode = us.cSuitCode
            WHERE 1=1
            """

            params = []

            # 添加时间范围条件
            if start_time:
                sql += " AND rm.dOperdate >= ?"
                params.append(start_time)

            if end_time:
                sql += " AND rm.dOperdate < ?"
                params.append(end_time)

            # 添加体检号条件
            if pe_no:
                sql += " AND rm.cClientCode = ?"
                params.append(pe_no)

            # 添加医院编码条件（如果需要）
            if hospital_code:
                sql += " AND rm.cShopCode = ?"
                params.append(hospital_code)

            sql += " ORDER BY rm.dOperdate DESC"

            exam_records = db_service.execute_query(sql, tuple(params) if params else None)

            # 构建返回数据
            data_list = []
            for record in exam_records:
                client_code = record['archiveNo']

                # 暂时使用固定值，避免查询不存在的表
                apply_items = []
                dept_count = 0
                pe_no_list = [client_code]

                # 构建标准格式的体检单信息
                exam_info = {
                    "peUserInfo": {
                        "archiveNo": record.get('archiveNo', ''),
                        "name": record.get('name', ''),
                        "icCode": record.get('icCode', ''),
                        "sex": {
                            "code": record.get('sex_code', '3'),
                            "name": record.get('sex_name', '未知')
                        },
                        "birthday": record.get('birthday', ''),
                        "peno": record.get('peno', ''),
                        "peDate": record.get('peDate', ''),
                        "phone": record.get('phone', ''),
                        "ms": {
                            "code": record.get('ms_code', ''),
                            "name": record.get('ms_name', '未知')
                        },
                        "pregnantState": {
                            "code": "",
                            "name": ""
                        },
                        "vipLevel": {
                            "code": record.get('vipLevel_code', ''),
                            "name": record.get('vipLevel_name', '普通')
                        },
                        "medicalType": {
                            "code": record.get('medicalType_code', ''),
                            "name": record.get('medicalType_name', '个人体检')
                        },
                        "isGroup": record.get('isGroup', '0'),
                        "company": record.get('company', ''),
                        "workDept": record.get('workDept', ''),
                        "teamNo": record.get('teamNo', ''),
                        "professional": record.get('professional', ''),
                        "workAge": record.get('workAge', ''),
                        "peStates": {
                            "code": record.get('peStates_code', '0'),
                            "name": record.get('peStates_name', '已登记')
                        },
                        "deptCount": dept_count,
                        "age": record.get('age', 0),
                        "deptFinishTime": record.get('deptFinishTime', ''),
                        "firstCheckFinishTime": record.get('firstCheckFinishTime', ''),
                        "firstCheckFinishDoctor": {
                            "code": record.get('firstCheckFinishDoctor_code', ''),
                            "name": record.get('firstCheckFinishDoctor_name', '')
                        },
                        "mainCheckFinishTime": record.get('mainCheckFinishTime', ''),
                        "mainCheckFinishDoctor": {
                            "code": record.get('mainCheckFinishDoctor_code', ''),
                            "name": record.get('mainCheckFinishDoctor_name', '')
                        },
                        "forbidGoCheck": record.get('forbidGoCheck', ''),
                        "reportPrint": record.get('reportPrint', ''),
                        "reportGot": record.get('reportGot', ''),
                        "replacementInspectionMark": record.get('replacementInspectionMark', ''),
                        "applyItemList": apply_items,
                        "currentNodeType": record.get('currentNodeType', ''),
                        "pePackage": {
                            "code": record.get('pePackage_code', ''),
                            "name": record.get('pePackage_name', '')
                        }
                    },
                    "archiveInfo": {
                        "name": record.get('name', ''),
                        "icCode": record.get('icCode', ''),
                        "sex": {
                            "code": record.get('sex_code', '3'),
                            "name": record.get('sex_name', '未知')
                        },
                        "birthday": record.get('birthday', ''),
                        "peNoList": pe_no_list
                    },
                    "hospital": {
                        "code": org_config.get('org_code', 'DEFAULT'),
                        "name": org_config.get('org_name', '默认医院')
                    }
                }

                data_list.append(exam_info)

            return data_list

        finally:
            db_service.disconnect()

    def start_service(self):
        """启动天健云接口统一服务"""
        if self.is_running:
            return

        def run_server():
            try:
                self.signal_emitter.log_signal.emit("信息", "天健云接口统一服务启动中...")
                self.signal_emitter.log_signal.emit("信息", "  监听端口: 5007")
                self.signal_emitter.log_signal.emit("信息", "  07号接口: /dx/inter/receiveConclusion (主检结束结论回传)")
                self.signal_emitter.log_signal.emit("信息", "  08号接口: /dx/inter/queryDict (查询字典信息)")
                self.signal_emitter.log_signal.emit("信息", "  09号接口: /dx/inter/retransmitDeptInfo (体检科室结果重传)")
                self.signal_emitter.log_signal.emit("信息", "  10号接口: /dx/inter/batchGetPeInfo (批量获取体检单信息)")
                self.signal_emitter.log_signal.emit("信息", "  11号接口: /dx/inter/getApplyItemDict (查询项目字典信息)")
                self.signal_emitter.log_signal.emit("信息", "  12号接口: /dx/inter/lockPeInfo (主检锁定解锁)")
                self.signal_emitter.log_signal.emit("信息", "  13号接口: /dx/inter/updatePeStatus (体检状态更新)")
                self.signal_emitter.log_signal.emit("信息", "  14号接口: /dx/inter/markAbnormal (重要异常标注)")
                self.signal_emitter.log_signal.emit("信息", "  15号接口: /dx/inter/returnDept (分科退回)")
                self.signal_emitter.log_signal.emit("信息", "  16号接口: /dx/inter/getImages (查询图片)")
                self.signal_emitter.log_signal.emit("信息", "  17号接口: /dx/inter/deleteAbnormal (删除重要异常)")
                self.signal_emitter.log_signal.emit("信息", "  18号接口: /dx/inter/getDoctorInfo (查询医生信息)")
                self.signal_emitter.log_signal.emit("信息", "  19号接口: /dx/inter/getDeptInfo (查询科室信息)")
                self.signal_emitter.log_signal.emit("信息", "  20号接口: /dx/inter/getPersonalOrders (查询个人开单)")
                self.signal_emitter.log_signal.emit("信息", "  21号接口: /dx/inter/getAbnormalNotice (查询异常通知)")
                self.signal_emitter.log_signal.emit("信息", "  健康检查: /health")

                # 更新状态为运行中
                self.signal_emitter.status_signal.emit("天健云接口服务: 运行中", "green")

                self.app.run(
                    host='0.0.0.0',
                    port=5007,
                    debug=False,
                    threaded=True,
                    use_reloader=False
                )
            except Exception as e:
                self.signal_emitter.log_signal.emit("错误", f"天健云接口统一服务启动失败: {e}")
                # 更新状态为失败
                self.signal_emitter.status_signal.emit("天健云接口服务: 启动失败", "red")

        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
        self.is_running = True

        # 延迟显示启动成功消息和更新状态
        def on_startup_success():
            self.signal_emitter.log_signal.emit("信息", "天健云接口统一服务启动成功")
            self.signal_emitter.status_signal.emit("天健云接口服务: 运行中", "green")

        QTimer.singleShot(1000, on_startup_success)

    def stop_service(self):
        """停止天健云接口统一服务"""
        if not self.is_running:
            return

        self.is_running = False
        self.signal_emitter.log_signal.emit("信息", "天健云接口统一服务已停止")

        # 更新状态为已停止
        self.signal_emitter.status_signal.emit("天健云接口服务: 已停止", "gray")


class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()

        # 延迟初始化的组件
        self.multi_org_manager = None
        self.org_config_window = None
        self.tianjian_interface_service = None
        self.is_services_initialized = False

        self.setWindowTitle("天健云数据同步系统 v1.3.0")

        # 设置窗口大小为800x400并居中显示
        self.resize(800, 400)
        self.center_window()

        self.setup_ui()
        self.setup_menu()
        self.setup_status_bar()

        # 显示启动日志
        self.log_widget.add_log("信息", "天健云数据同步系统已启动")
        self.log_widget.add_log("信息", "点击'开始AI同步'按钮启动数据库连接和接口服务")

        # 数据库连接和接口服务将在点击"开始AI同步"时初始化

        # 临时：自动启动服务用于测试18号接口
        QTimer.singleShot(2000, self.auto_start_services_for_test)

    def auto_start_services_for_test(self):
        """自动启动服务用于测试"""
        try:
            self.log_widget.add_log("信息", "自动启动服务用于测试...")
            if self.init_services_on_demand():
                self.log_widget.add_log("信息", "服务启动成功，18号接口可以测试")
            else:
                self.log_widget.add_log("错误", "服务启动失败")
        except Exception as e:
            self.log_widget.add_log("错误", f"自动启动服务失败: {str(e)}")
        
    def center_window(self):
        """将窗口居中显示"""
        # 获取屏幕尺寸
        screen = QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()

        # 计算窗口居中位置
        window_geometry = self.frameGeometry()
        center_point = screen_geometry.center()
        window_geometry.moveCenter(center_point)

        # 移动窗口到计算出的位置
        self.move(window_geometry.topLeft())

    def setup_ui(self):
        """设置UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 左侧面板（状态和控制）
        left_panel = QWidget()
        left_panel.setMaximumWidth(300)
        left_layout = QVBoxLayout(left_panel)
        
        # 状态监控
        self.status_widget = StatusWidget()
        left_layout.addWidget(self.status_widget)
        
        main_layout.addWidget(left_panel)
        
        # 右侧主要内容
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # 标签页
        self.tab_widget = QTabWidget()
        
        # 日志页面
        self.log_widget = LogWidget()
        self.tab_widget.addTab(self.log_widget, "系统日志")
        
        # 同步操作页面
        self.sync_widget = SyncWidget(self.log_widget, self)
        self.tab_widget.addTab(self.sync_widget, "数据同步")
        
        # 天健云接口页面
        self.interface_widget = TianjianInterfaceWidget(self.log_widget)
        self.tab_widget.addTab(self.interface_widget, "天健云接口")
        
        # 历史记录页面
        self.history_widget = HistoryWidget()
        self.tab_widget.addTab(self.history_widget, "同步历史")
        
        right_layout.addWidget(self.tab_widget)
        main_layout.addWidget(right_panel)
        
        # 初始日志
        self.log_widget.add_log("信息", "天健云数据同步系统已启动")
        self.log_widget.add_log("信息", "系统版本: v1.3.0 PySide6版")
        self.log_widget.add_log("信息", "支持功能: 增量同步、21个天健云接口、性能优化、图形界面")
    
    def setup_menu(self):
        """设置菜单"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件")
        
        config_action = QAction("配置", self)
        config_action.triggered.connect(self.show_config)
        file_menu.addAction(config_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("退出", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 机构菜单
        org_menu = menubar.addMenu("机构管理")

        org_config_action = QAction("机构配置管理", self)
        org_config_action.triggered.connect(self.show_org_config)
        org_menu.addAction(org_config_action)

        org_menu.addSeparator()

        switch_org_action = QAction("切换机构", self)
        switch_org_action.triggered.connect(self.show_org_switch)
        org_menu.addAction(switch_org_action)

        refresh_org_action = QAction("刷新机构配置", self)
        refresh_org_action.triggered.connect(self.refresh_org_config)
        org_menu.addAction(refresh_org_action)

        # 工具菜单
        tools_menu = menubar.addMenu("工具")

        refresh_action = QAction("刷新状态", self)
        refresh_action.triggered.connect(self.status_widget.update_status)
        tools_menu.addAction(refresh_action)

        clear_logs_action = QAction("清空日志", self)
        clear_logs_action.triggered.connect(self.log_widget.clear_logs)
        tools_menu.addAction(clear_logs_action)
        
        tools_menu.addSeparator()
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助")
        
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        self.status_bar.showMessage("就绪")

        # 添加天健云接口服务状态
        self.interface_service_status_label = QLabel("天健云接口服务: 未启动")
        self.interface_service_status_label.setStyleSheet("color: gray;")
        self.status_bar.addPermanentWidget(self.interface_service_status_label)

        # 添加当前机构信息
        self.org_label = QLabel()
        self.update_org_status()
        self.status_bar.addPermanentWidget(self.org_label)

        # 添加时间显示
        self.time_label = QLabel()
        self.status_bar.addPermanentWidget(self.time_label)

        # 定时更新时间
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)
        self.update_time()
    
    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)
    
    def show_config(self):
        """显示配置对话框"""
        dialog = ConfigDialog(self)
        if dialog.exec() == QDialog.Accepted:
            config = dialog.get_config()
            self.log_widget.add_log("信息", "配置已更新")
            # 这里可以保存配置到文件

    def update_org_status(self):
        """更新机构状态显示"""
        try:
            if self.is_services_initialized:
                current_org = get_current_org_config()
                org_code = current_org.get('org_code', 'N/A')
                org_name = current_org.get('org_name', '未知机构')
                self.org_label.setText(f"当前机构: {org_code} - {org_name}")
            else:
                self.org_label.setText("机构信息: 未初始化")
        except Exception as e:
            self.org_label.setText("机构信息: 获取失败")

    def show_org_config(self):
        """显示机构配置管理窗口"""
        try:
            if self.org_config_window is None:
                self.org_config_window = OrganizationConfigWindow()

            self.org_config_window.show()
            self.org_config_window.raise_()
            self.org_config_window.activateWindow()

            self.log_widget.add_log("信息", "打开机构配置管理窗口")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开机构配置管理窗口失败: {e}")
            self.log_widget.add_log("错误", f"打开机构配置管理窗口失败: {e}")

    def show_org_switch(self):
        """显示机构切换对话框"""
        try:
            # 获取所有机构
            all_orgs = get_all_organizations()
            if not all_orgs:
                QMessageBox.information(self, "提示", "没有可用的机构配置")
                return

            # 创建机构选择对话框
            from PySide6.QtWidgets import QInputDialog

            org_items = []
            org_map = {}
            for org in all_orgs:
                if org.get('status') == '1':  # 只显示启用的机构
                    display_text = f"{org.get('org_code', 'N/A')} - {org.get('org_name', '未知机构')}"
                    org_items.append(display_text)
                    org_map[display_text] = org.get('org_code')

            if not org_items:
                QMessageBox.information(self, "提示", "没有启用的机构配置")
                return

            # 显示选择对话框
            item, ok = QInputDialog.getItem(
                self, "切换机构", "请选择要切换的机构:",
                org_items, 0, False
            )

            if ok and item:
                org_code = org_map[item]
                success, message = switch_organization(org_code)

                if success:
                    # 更新窗口标题和状态
                    current_org = get_current_org_config()
                    org_name = current_org.get('org_name', '未知机构')
                    self.setWindowTitle(f"天健云数据同步系统 v1.3.0 - {org_name}")
                    self.update_org_status()

                    QMessageBox.information(self, "成功", message)
                    self.log_widget.add_log("信息", f"机构切换成功: {message}")
                else:
                    QMessageBox.warning(self, "失败", message)
                    self.log_widget.add_log("警告", f"机构切换失败: {message}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"机构切换失败: {e}")
            self.log_widget.add_log("错误", f"机构切换失败: {e}")

    def refresh_org_config(self):
        """刷新机构配置"""
        try:
            if not self.is_services_initialized:
                QMessageBox.information(self, "提示", "请先点击'开始AI同步'初始化服务后再刷新配置")
                return

            self.multi_org_manager.refresh_cache()
            self.update_org_status()

            self.log_widget.add_log("信息", "机构配置已刷新")
            QMessageBox.information(self, "成功", "机构配置已刷新")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"刷新机构配置失败: {e}")
            self.log_widget.add_log("错误", f"刷新机构配置失败: {e}")

    def show_about(self):
        """显示关于对话框"""
        about_text = """
<h3>天健云数据同步系统 v1.3.0</h3>
<p><b>开发:</b> 福能AI对接项目组</p>
<p><b>功能:</b> 嘉仁体检中心 ↔ 天健云平台数据同步</p>
<p><b>特性:</b></p>
<ul>
<li>✅ 支持21个天健云接口</li>
<li>✅ 图形化接口操作界面</li>
<li>✅ 增量数据同步</li>
<li>✅ 性能优化（连接池、缓存）</li>
<li>✅ PySide6现代化界面</li>
<li>✅ 实时状态监控</li>
</ul>
<p><b>技术栈:</b> Python 3.8+, PySide6, SQLAlchemy, SQL Server</p>
        """
        QMessageBox.about(self, "关于", about_text)

    def init_services_on_demand(self):
        """按需初始化数据库连接和接口服务"""
        if self.is_services_initialized:
            return True

        try:
            self.log_widget.add_log("信息", "正在初始化数据库连接和接口服务...")

            # 初始化多机构管理器（这会连接数据库）
            self.multi_org_manager = get_multi_org_manager()

            # 获取当前机构信息并更新窗口标题
            current_org = get_current_org_config()
            org_name = current_org.get('org_name', '未知机构')
            self.setWindowTitle(f"天健云数据同步系统 v1.3.0 - {org_name}")

            # 更新机构状态显示
            self.update_org_status()

            # 初始化天健云接口统一服务
            self.init_tianjian_interface_service()

            # 启动状态监控
            self.status_widget.start_status_monitoring()

            # 启动历史记录加载
            self.history_widget.start_history_loading()

            self.is_services_initialized = True
            self.log_widget.add_log("信息", "数据库连接和接口服务初始化完成")
            return True

        except Exception as e:
            self.log_widget.add_log("错误", f"初始化服务失败: {e}")
            return False

    def init_tianjian_interface_service(self):
        """初始化天健云接口统一服务"""
        try:
            # 创建天健云接口统一服务
            self.tianjian_interface_service = TianjianInterfaceService()

            # 连接信号槽
            self.tianjian_interface_service.signal_emitter.log_signal.connect(self.log_widget.add_log)
            self.tianjian_interface_service.signal_emitter.status_signal.connect(self.update_interface_service_status)

            # 延迟启动服务，确保GUI完全加载
            QTimer.singleShot(1000, self.start_tianjian_interface_service)

        except Exception as e:
            self.log_widget.add_log("错误", f"初始化天健云接口统一服务失败: {e}")

    def update_interface_service_status(self, text: str, color: str):
        """更新接口服务状态显示"""
        if hasattr(self, 'interface_service_status_label'):
            self.interface_service_status_label.setText(text)
            self.interface_service_status_label.setStyleSheet(f"color: {color};")

    def start_tianjian_interface_service(self):
        """启动天健云接口统一服务"""
        try:
            if self.tianjian_interface_service:
                self.tianjian_interface_service.start_service()
        except Exception as e:
            if hasattr(self, 'log_widget') and self.log_widget:
                self.log_widget.add_log("错误", f"启动天健云接口统一服务失败: {e}")
            else:
                print(f"启动天健云接口统一服务失败: {e}")

    def closeEvent(self, event):
        """关闭事件"""
        self.log_widget.add_log("信息", "系统正在关闭...")

        # 清理数据同步页签资源
        if hasattr(self, 'sync_widget') and self.sync_widget:
            self.sync_widget.cleanup()

        # 停止天健云接口统一服务
        if hasattr(self, 'tianjian_interface_service') and self.tianjian_interface_service:
            try:
                self.tianjian_interface_service.stop_service()
            except Exception as e:
                print(f"停止天健云接口统一服务失败: {e}")

        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("天健云数据同步系统")
    app.setApplicationVersion("1.3.0")
    app.setOrganizationName("福能AI对接项目组")
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())


if __name__ == '__main__':
    main()