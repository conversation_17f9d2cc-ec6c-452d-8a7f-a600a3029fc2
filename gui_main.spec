# -*- mode: python ; coding: utf-8 -*-
"""
天健云数据同步系统 PyInstaller 配置文件
用于将 gui_main.py 打包成独立的可执行文件
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = os.getcwd()
sys.path.append(project_root)

# 数据文件收集
datas = [
    # 配置文件
    ('config.yaml', '.'),
    ('env_template.txt', '.'),
    
    # health_sync 模块配置
    ('health_sync/config/defaults.yaml', 'health_sync/config/'),
    
    # SQL 文件
    ('sql', 'sql'),
    
    # 测试数据文件（如果需要）
    ('test', 'test'),
]

# 二进制文件（DLL等）
binaries = []

# 隐藏导入的模块
hiddenimports = [
    # PySide6 相关
    'PySide6.QtWidgets',
    'PySide6.QtCore', 
    'PySide6.QtGui',
    
    # SQLAlchemy 和数据库
    'sqlalchemy',
    'sqlalchemy.engine',
    'sqlalchemy.engine.default',
    'sqlalchemy.sql',
    'sqlalchemy.sql.sqltypes',
    'sqlalchemy.dialects.mssql',
    'sqlalchemy.dialects.mssql.pyodbc',
    'pyodbc',
    
    # 数据处理
    'pandas',
    'pydantic',
    'numpy',
    
    # 网络请求
    'requests',
    'urllib3',
    'certifi',
    'cryptography',
    
    # 配置管理
    'yaml',
    'python_dotenv',
    
    # 日志系统
    'loguru',
    
    # Flask 框架（用于接口服务）
    'flask',
    'werkzeug',
    
    # 时间和日期
    'dateutil',
    'datetime',
    
    # 系统和工具
    'threading',
    'subprocess',
    'json',
    'hashlib',
    'uuid',
    'time',
    'os',
    'sys',
    
    # 项目内模块
    'incremental_sync_service',
    'optimized_database_service', 
    'database_service',
    'config',
    'multi_org_config',
    'organization_config_window',
    'center_organization_service',
    
    # 天健云接口模块
    'interface_01_sendPeInfo',
    'interface_02_syncApplyItem',
    'interface_03_deptInfo',
    'interface_04_syncUser',
    'interface_05_syncDept',
    'interface_06_syncDict',
    'interface_07_sendConclusion',
    'interface_08_getDict',
    'interface_09_retransmitDeptInfo',
    'interface_10_batchGetPeInfo',
    'interface_11_getApplyItemDict_standard',
    'interface_12_lockPeInfo',
    'interface_13_updatePeStatus',
    'interface_14_markAbnormal',
    'interface_15_returnDept_multi_shop',
    'interface_16_getImages',
    'interface_17_deleteAbnormal',
    'interface_18_getDoctorInfo',
    'interface_19_getDeptInfo',
    'interface_20_getPersonalOrders',
    'interface_21_getAbnormalNotice',
    
    # health_sync 模块
    'health_sync',
    'health_sync.api',
    'health_sync.api.client',
    'health_sync.api.schemas',
    'health_sync.api.signer',
    'health_sync.services',
    'health_sync.services.apply_item_service',
    'health_sync.services.dept_return_service',
    'health_sync.db',
    'health_sync.db.models',
    'health_sync.db.session',
    'health_sync.db.crud',
    'health_sync.config',
    'health_sync.config.settings',
    'health_sync.utils',
    'health_sync.utils.exceptions',
    'health_sync.utils.helpers',
    'health_sync.utils.logger',
]

# 排除的模块
excludes = [
    'tkinter',
    'matplotlib',
    'IPython',
    'jupyter',
    'notebook',
]

# 主程序分析
a = Analysis(
    ['gui_main.py'],
    pathex=[project_root],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 打包二进制文件
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 生成可执行文件
exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='天健云数据同步系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    # 图标文件 - 如果有的话可以取消注释
    # icon='icon.ico',
)

# 生成文件夹分发
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='天健云数据同步系统'
)