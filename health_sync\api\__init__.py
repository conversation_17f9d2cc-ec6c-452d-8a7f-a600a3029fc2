"""
天健云API模块
"""

from .client import api_client, get_api_client, test_api_connection, TianjianAPIClient
from .signer import build_headers, generate_sign, generate_nonce, generate_timestamp
from .schemas import (
    BaseResponse, PeInfoRequest, DeptResultRequest, ConclusionRequest,
    ApplyItemRequest, DoctorInfoRequest, DeptInfoRequest, DictInfoRequest,
    ChargeQueryRequest, ChargeQueryResponse, TIANJIAN_ENDPOINTS,
    SyncStatus, InterfaceType
)

__all__ = [
    # 客户端
    "api_client",
    "get_api_client", 
    "test_api_connection",
    "TianjianAPIClient",
    
    # 签名
    "build_headers",
    "generate_sign",
    "generate_nonce",
    "generate_timestamp",
    
    # 数据模型
    "BaseResponse",
    "PeInfoRequest", 
    "DeptResultRequest",
    "ConclusionRequest",
    "ApplyItemRequest",
    "DoctorInfoRequest",
    "DeptInfoRequest",
    "DictInfoRequest",
    "ChargeQueryRequest",
    "ChargeQueryResponse",
    "TIANJIAN_ENDPOINTS",
    "SyncStatus",
    "InterfaceType"
] 