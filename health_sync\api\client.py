"""
天健云 API 客户端
提供HTTP请求封装、错误处理、重试机制等功能
"""
import requests
import json
import time
from typing import Dict, Any, Optional, Union, List
from urllib.parse import urljoin
import logging

from ..config.settings import settings
from .signer import build_headers, get_default_signer
from .schemas import (
    BaseResponse, SuccessResponse, ErrorResponse, TIANJIAN_ENDPOINTS,
    PeInfoRequest, DeptResultRequest, ConclusionRequest, 
    ApplyItemRequest, DoctorInfoRequest, DeptInfoRequest, DictInfoRequest,
    ChargeQueryRequest, ChargeQueryResponse, DepartmentReturnRequest
)
from ..db.crud import SyncCRUD
from ..utils.exceptions import TianjianAPIError, TianjianTimeoutError, TianjianServerError

logger = logging.getLogger(__name__)


class TianjianAPIClient:
    """天健云API客户端"""
    
    def __init__(self, base_url: Optional[str] = None, timeout: Optional[int] = None):
        """
        初始化API客户端
        
        Args:
            base_url: API基础URL，默认从配置读取
            timeout: 请求超时时间，默认从配置读取
        """
        self.base_url = base_url or settings.api.base_url
        self.timeout = timeout or settings.api.timeout
        self.retry_times = settings.api.retry_times
        self.retry_delay = settings.api.retry_delay
        
        # 创建session
        self.session = requests.Session()
        
        # 设置默认请求头
        self.session.headers.update({
            'User-Agent': 'HealthSync/1.0',
            'Accept': 'application/json',
            'Connection': 'keep-alive'
        })
        
        logger.info(f"天健云API客户端初始化完成，Base URL: {self.base_url}")
    
    def _build_url(self, endpoint: str) -> str:
        """构建完整的API URL"""
        return urljoin(self.base_url, endpoint.lstrip('/'))
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict[str, Any]] = None,
                     params: Optional[Dict[str, Any]] = None) -> requests.Response:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            data: 请求数据
            params: URL参数
            
        Returns:
            响应对象
            
        Raises:
            TianjianAPIError: API请求失败
            TianjianTimeoutError: 请求超时
            TianjianServerError: 服务器错误
        """
        url = self._build_url(endpoint)
        
        # 构建请求头（包含签名）
        try:
            headers = build_headers()
        except ValueError as e:
            logger.error(f"构建请求头失败: {e}")
            raise TianjianAPIError(f"配置错误: {e}")
        
        # 更新session头
        self.session.headers.update(headers)
        
        # 准备请求参数
        request_kwargs = {
            'timeout': self.timeout,
            'verify': False  # 如果是HTTPS且证书有问题，可以设置为False
        }
        
        if data:
            request_kwargs['json'] = data
        if params:
            request_kwargs['params'] = params
        
        # 记录请求日志
        logger.info(f"发送{method}请求到: {url}")
        logger.debug(f"请求头: {headers}")
        logger.debug(f"请求数据: {data}")
        
        # 执行请求
        last_exception = None
        for attempt in range(self.retry_times):
            try:
                response = self.session.request(method, url, **request_kwargs)
                
                # 记录响应日志
                logger.debug(f"响应状态码: {response.status_code}")
                logger.debug(f"响应内容: {response.text}")
                
                # 检查HTTP状态码
                if response.status_code >= 500:
                    raise TianjianServerError(f"服务器错误: {response.status_code}")
                elif response.status_code >= 400:
                    raise TianjianAPIError(f"客户端错误: {response.status_code}, {response.text}")
                
                return response
                
            except requests.exceptions.Timeout as e:
                last_exception = TianjianTimeoutError(f"请求超时: {e}")
                logger.warning(f"第{attempt + 1}次请求超时，剩余重试次数: {self.retry_times - attempt - 1}")
                
            except requests.exceptions.ConnectionError as e:
                last_exception = TianjianAPIError(f"连接错误: {e}")
                logger.warning(f"第{attempt + 1}次连接失败，剩余重试次数: {self.retry_times - attempt - 1}")
                
            except (TianjianServerError, TianjianAPIError) as e:
                last_exception = e
                logger.warning(f"第{attempt + 1}次请求失败: {e}")
                
            # 如果不是最后一次尝试，等待后重试
            if attempt < self.retry_times - 1:
                time.sleep(self.retry_delay)
        
        # 所有重试都失败，抛出最后一个异常
        logger.error(f"请求最终失败，已重试{self.retry_times}次")
        raise last_exception
    
    def _parse_response(self, response: requests.Response) -> BaseResponse:
        """
        解析响应数据
        
        Args:
            response: HTTP响应对象
            
        Returns:
            解析后的响应数据
            
        Raises:
            TianjianAPIError: 响应解析失败
        """
        try:
            json_data = response.json()
            return BaseResponse(**json_data)
        except json.JSONDecodeError as e:
            logger.error(f"响应JSON解析失败: {e}, 响应内容: {response.text}")
            raise TianjianAPIError(f"响应格式错误: {e}")
        except Exception as e:
            logger.error(f"响应数据解析失败: {e}")
            raise TianjianAPIError(f"响应解析错误: {e}")
    
    def _log_sync_request(self, interface_name: str, endpoint: str, 
                         request_data: Dict[str, Any], business_key: str = None) -> Optional[int]:
        """记录同步请求日志"""
        try:
            log = SyncCRUD.log_sync_request(
                interface_name=interface_name,
                request_url=self._build_url(endpoint),
                request_data=json.dumps(request_data, ensure_ascii=False),
                business_key=business_key
            )
            return log.id
        except Exception as e:
            logger.error(f"记录同步日志失败: {e}")
            return None
    
    def _update_sync_response(self, log_id: Optional[int], response: BaseResponse, 
                            status_code: int, error_message: str = None):
        """更新同步响应日志"""
        if log_id:
            try:
                SyncCRUD.update_sync_response(
                    log_id=log_id,
                    status_code=status_code,
                    response_data=response.json() if response else "",
                    success=response.code == 0 if response else False,
                    error_message=error_message
                )
            except Exception as e:
                logger.error(f"更新同步日志失败: {e}")
    
    def _debug_request_info(self, endpoint: str, data: Any, method: str = "POST"):
        """输出请求调试信息"""
        import json
        from .signer import build_headers
        
        print("=" * 80)
        print("[ROCKET] 天健云API请求调试信息")
        print("=" * 80)
        
        url = self._build_url(endpoint)
        print(f"[SIGNAL] 请求URL: {url}")
        print(f"[LIST] 请求方法: {method}")
        
        # 构建请求头
        try:
            headers = build_headers()
            print("\n[DOC] 请求头 (Request Headers):")
            for key, value in headers.items():
                if key.lower() in ['api-key', 'authorization']:
                    print(f"  {key}: {'*' * 20}")
                else:
                    print(f"  {key}: {value}")
        except Exception as e:
            print(f"  请求头构建失败: {e}")
        
        print(f"\n📦 请求体 (Request Body):")
        if isinstance(data, list):
            print(f"  数据类型: 数组 (长度: {len(data)})")
        print("  JSON格式:")
        try:
            formatted_json = json.dumps(data, ensure_ascii=False, indent=2)
            # 如果数据太长，只显示前1000字符
            if len(formatted_json) > 1000:
                print(formatted_json[:1000] + "\n  ... (数据过长，仅显示前1000字符)")
            else:
                print(formatted_json)
        except Exception as e:
            print(f"  JSON序列化失败: {e}")
        
        print("\n" + "=" * 80)
    
    def _debug_response_info(self, response: requests.Response):
        """输出响应调试信息"""
        import json
        
        print("📨 天健云API响应调试信息:")
        print(f"  HTTP状态码: {response.status_code}")
        print(f"  状态描述: {response.reason}")
        
        print("\n[DOC] 响应头 (Response Headers):")
        for key, value in response.headers.items():
            print(f"  {key}: {value}")
        
        print(f"\n📦 响应体 (Response Body):")
        try:
            if response.content:
                response_data = response.json()
                formatted_json = json.dumps(response_data, ensure_ascii=False, indent=2)
                print(formatted_json)
            else:
                print("  (空响应体)")
        except json.JSONDecodeError:
            print(f"  原始文本: {response.text}")
        except Exception as e:
            print(f"  响应解析失败: {e}")
        
        print("=" * 80)
    
    # 接口01：单次体检基本信息传输
    def send_pe_info(self, pe_info: Union[PeInfoRequest, Dict[str, Any]]) -> BaseResponse:
        """
        发送体检基本信息
        
        Args:
            pe_info: 体检信息数据
            
        Returns:
            API响应结果
        """
        if isinstance(pe_info, dict):
            pe_info = PeInfoRequest(**pe_info)
        
        endpoint = TIANJIAN_ENDPOINTS["sendPeInfo"].path
        data = pe_info.dict()
        
        # 记录日志
        log_id = self._log_sync_request("sendPeInfo", endpoint, data, pe_info.hostId)
        
        try:
            response = self._make_request("POST", endpoint, data)
            parsed_response = self._parse_response(response)
            
            # 更新日志
            self._update_sync_response(log_id, parsed_response, response.status_code)
            
            return parsed_response
            
        except Exception as e:
            self._update_sync_response(log_id, None, 0, str(e))
            raise
    
    # 接口02：申请项目字典数据传输
    def send_apply_item(self, items: Union[List[ApplyItemRequest], List[Dict[str, Any]]], debug: bool = False) -> BaseResponse:
        """
        发送申请项目字典数据
        根据接口文档，请求体直接是数组格式
        
        Args:
            items: 申请项目数据列表
            debug: 是否输出详细调试信息
        """
        if isinstance(items[0], dict):
            items = [ApplyItemRequest(**item) for item in items]
        
        endpoint = TIANJIAN_ENDPOINTS["sendApplyItem"].path
        # 接口文档要求直接发送数组，不是包装在对象中
        data = [item.dict() for item in items]
        
        if debug:
            self._debug_request_info(endpoint, data, method="POST")
        
        log_id = self._log_sync_request("sendApplyItem", endpoint, data)
        
        try:
            response = self._make_request("POST", endpoint, data)
            
            if debug:
                self._debug_response_info(response)
            
            parsed_response = self._parse_response(response)
            self._update_sync_response(log_id, parsed_response, response.status_code)
            return parsed_response
        except Exception as e:
            if debug:
                print(f"[FAIL] 请求异常: {str(e)}")
                print("=" * 80)
            self._update_sync_response(log_id, None, 0, str(e))
            raise
    
    # 接口03：体检科室结果传输
    def send_dept_result(self, dept_result: Union[DeptResultRequest, Dict[str, Any]]) -> BaseResponse:
        """发送科室结果数据"""
        if isinstance(dept_result, dict):
            dept_result = DeptResultRequest(**dept_result)
        
        endpoint = TIANJIAN_ENDPOINTS["deptInfo"].path
        data = dept_result.dict()
        
        log_id = self._log_sync_request("deptInfo", endpoint, data, dept_result.hostId)
        
        try:
            response = self._make_request("POST", endpoint, data)
            parsed_response = self._parse_response(response)
            self._update_sync_response(log_id, parsed_response, response.status_code)
            return parsed_response
        except Exception as e:
            self._update_sync_response(log_id, None, 0, str(e))
            raise
    
    # 接口04：医生信息传输
    def send_doctor_info(self, doctors: Union[List[DoctorInfoRequest], List[Dict[str, Any]]]) -> BaseResponse:
        """发送医生信息数据"""
        if isinstance(doctors[0], dict):
            doctors = [DoctorInfoRequest(**doctor) for doctor in doctors]
        
        endpoint = TIANJIAN_ENDPOINTS["sendDoctorInfo"].path
        data = {"doctors": [doctor.dict() for doctor in doctors]}
        
        log_id = self._log_sync_request("sendDoctorInfo", endpoint, data)
        
        try:
            response = self._make_request("POST", endpoint, data)
            parsed_response = self._parse_response(response)
            self._update_sync_response(log_id, parsed_response, response.status_code)
            return parsed_response
        except Exception as e:
            self._update_sync_response(log_id, None, 0, str(e))
            raise
    
    # 接口05：科室信息传输
    def send_dept_info(self, depts: Union[List[DeptInfoRequest], List[Dict[str, Any]]]) -> BaseResponse:
        """发送科室信息数据"""
        if isinstance(depts[0], dict):
            depts = [DeptInfoRequest(**dept) for dept in depts]
        
        endpoint = TIANJIAN_ENDPOINTS["sendDeptInfo"].path
        data = {"departments": [dept.dict() for dept in depts]}
        
        log_id = self._log_sync_request("sendDeptInfo", endpoint, data)
        
        try:
            response = self._make_request("POST", endpoint, data)
            parsed_response = self._parse_response(response)
            self._update_sync_response(log_id, parsed_response, response.status_code)
            return parsed_response
        except Exception as e:
            self._update_sync_response(log_id, None, 0, str(e))
            raise
    
    # 接口06：字典信息传输
    def sync_dict(self, dict_items: Union[List[DictInfoRequest], List[Dict[str, Any]]]) -> BaseResponse:
        """同步字典信息数据"""
        if isinstance(dict_items[0], dict):
            dict_items = [DictInfoRequest(**item) for item in dict_items]
        
        endpoint = TIANJIAN_ENDPOINTS["syncDict"].path
        data = {"dictItems": [item.dict() for item in dict_items]}
        
        log_id = self._log_sync_request("syncDict", endpoint, data)
        
        try:
            response = self._make_request("POST", endpoint, data)
            parsed_response = self._parse_response(response)
            self._update_sync_response(log_id, parsed_response, response.status_code)
            return parsed_response
        except Exception as e:
            self._update_sync_response(log_id, None, 0, str(e))
            raise
    
    # 接口07：主检结束结论回传
    def send_conclusion(self, conclusion: Union[ConclusionRequest, Dict[str, Any]]) -> BaseResponse:
        """发送主检结论数据"""
        if isinstance(conclusion, dict):
            conclusion = ConclusionRequest(**conclusion)
        
        endpoint = TIANJIAN_ENDPOINTS["sendConclusion"].path
        data = conclusion.dict()
        
        log_id = self._log_sync_request("sendConclusion", endpoint, data, conclusion.hostId)
        
        try:
            response = self._make_request("POST", endpoint, data)
            parsed_response = self._parse_response(response)
            self._update_sync_response(log_id, parsed_response, response.status_code)
            return parsed_response
        except Exception as e:
            self._update_sync_response(log_id, None, 0, str(e))
            raise
    
    # 接口15：分科退回
    def send_dept_return(self, dept_return: Union[DepartmentReturnRequest, Dict[str, Any]]) -> BaseResponse:
        """
        发送分科退回信息
        
        Args:
            dept_return: 分科退回数据
            
        Returns:
            API响应结果
        """
        if isinstance(dept_return, dict):
            dept_return = DepartmentReturnRequest(**dept_return)
        
        endpoint = TIANJIAN_ENDPOINTS["deptReturn"].path
        data = dept_return.dict()
        
        # 记录日志
        log_id = self._log_sync_request("deptReturn", endpoint, data, dept_return.peNo)
        
        try:
            response = self._make_request("POST", endpoint, data)
            parsed_response = self._parse_response(response)
            
            # 更新日志
            self._update_sync_response(log_id, parsed_response, response.status_code)
            
            return parsed_response
            
        except Exception as e:
            self._update_sync_response(log_id, None, 0, str(e))
            raise

    # 接口20：查询个人开单情况
    def query_charge_info(self, host_ids: Union[List[str], ChargeQueryRequest]) -> ChargeQueryResponse:
        """查询个人开单情况"""
        if isinstance(host_ids, list):
            query_request = ChargeQueryRequest(hostIds=host_ids)
        else:
            query_request = host_ids
        
        endpoint = TIANJIAN_ENDPOINTS["queryChargeInfo"].path
        params = {"hostIds": ",".join(query_request.hostIds)}
        
        log_id = self._log_sync_request("queryChargeInfo", endpoint, params)
        
        try:
            response = self._make_request("GET", endpoint, params=params)
            json_data = response.json()
            parsed_response = ChargeQueryResponse(**json_data)
            self._update_sync_response(log_id, parsed_response, response.status_code)
            return parsed_response
        except Exception as e:
            self._update_sync_response(log_id, None, 0, str(e))
            raise
    
    def test_connection(self) -> bool:
        """
        测试API连接
        
        Returns:
            连接是否成功
        """
        try:
            # 使用一个简单的请求来测试连接
            response = self.session.get(
                self.base_url,
                timeout=5,
                verify=False
            )
            logger.info(f"连接测试成功，状态码: {response.status_code}")
            return True
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False
    
    def close(self):
        """关闭客户端会话"""
        if self.session:
            self.session.close()
            logger.info("API客户端会话已关闭")


# 全局API客户端实例
api_client = TianjianAPIClient()


# 便捷函数
def get_api_client() -> TianjianAPIClient:
    """获取全局API客户端实例"""
    return api_client


def test_api_connection() -> bool:
    """测试API连接（便捷函数）"""
    return api_client.test_connection() 