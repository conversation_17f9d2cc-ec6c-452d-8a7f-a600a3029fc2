"""
天健云接口数据传输对象（DTO）
定义请求和响应的数据结构，使用 Pydantic 进行数据验证
"""
from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum


# 基础响应模型
class BaseResponse(BaseModel):
    """天健云接口基础响应模型"""
    code: int = Field(..., description="状态码，0表示成功")
    msg: str = Field(..., description="返回消息")
    data: Optional[Any] = Field(None, description="返回数据")


# 接口01：单次体检基本信息传输
class PeInfoRequest(BaseModel):
    """体检基本信息请求模型"""
    hostId: str = Field(..., description="体检号")
    name: str = Field(..., description="姓名")
    sex: str = Field(..., description="性别")
    birthDate: str = Field(..., description="出生日期 YYYY-MM-DD")
    age: int = Field(..., description="年龄")
    identityCard: str = Field(..., description="身份证号")
    telephone: Optional[str] = Field("", description="联系电话")
    marriageStatus: Optional[str] = Field("", description="婚姻状况")
    
    # 体检相关信息
    groupFlag: str = Field(..., description="团体标志：1-团体，0-个人")
    contractNo: Optional[str] = Field("", description="合同编号")
    packageCode: str = Field(..., description="套餐代码")
    registerTime: str = Field(..., description="登记时间 YYYY-MM-DD HH:mm:ss")
    operatorCode: str = Field(..., description="登记操作员代码")
    operatorName: str = Field(..., description="登记操作员姓名")
    
    # 审核信息
    confirmTime: Optional[str] = Field("", description="确认时间")
    confirmOperCode: Optional[str] = Field("", description="确认操作员")
    confirmOperName: Optional[str] = Field("", description="确认操作员姓名")
    peStatus: str = Field(..., description="体检状态")
    
    # 主检信息
    firstCheckFlag: Optional[str] = Field("", description="初检标志")
    firstCheckDoctCode: Optional[str] = Field("", description="初检医生代码")
    firstCheckDoctName: Optional[str] = Field("", description="初检医生姓名")
    firstCheckTime: Optional[str] = Field("", description="初检完成时间")
    
    finalCheckFlag: Optional[str] = Field("", description="总审标志")
    finalCheckDoctCode: Optional[str] = Field("", description="总审医生代码")
    finalCheckDoctName: Optional[str] = Field("", description="总审医生姓名")
    finalCheckTime: Optional[str] = Field("", description="总审完成时间")
    
    # 其他信息
    hospitalCode: str = Field(..., description="院区代码")
    totalAmount: Optional[float] = Field(0.0, description="总金额")
    actualAmount: Optional[float] = Field(0.0, description="实际金额")
    healthCardNo: Optional[str] = Field("", description="健康证号")
    address: Optional[str] = Field("", description="地址")
    workNumber: Optional[str] = Field("", description="工号")
    workYears: Optional[int] = Field(0, description="工龄")

    @validator('birthDate', 'registerTime', 'confirmTime', 'firstCheckTime', 'finalCheckTime')
    def validate_datetime_format(cls, v):
        """验证日期时间格式"""
        if v and v.strip():
            try:
                if len(v) == 10:  # YYYY-MM-DD
                    datetime.strptime(v, "%Y-%m-%d")
                elif len(v) == 19:  # YYYY-MM-DD HH:mm:ss
                    datetime.strptime(v, "%Y-%m-%d %H:%M:%S")
                else:
                    raise ValueError(f"Invalid datetime format: {v}")
            except ValueError:
                raise ValueError(f"Invalid datetime format: {v}")
        return v


# 接口02：申请项目字典数据传输
class CheckItemModel(BaseModel):
    """检查项目模型"""
    checkItemId: str = Field(..., description="检查项目id")
    checkItemName: str = Field(..., description="检查项目名称")
    displaySequence: Optional[str] = Field("", description="排序")


class HospitalModel(BaseModel):
    """医院/院区模型"""
    code: str = Field(..., description="医院编码")
    name: str = Field(..., description="院区名称")


class ApplyItemRequest(BaseModel):
    """申请项目字典请求模型 - 完全符合接口文档格式"""
    applyItemId: str = Field(..., description="体检系统申请项目编号")
    applyItemName: str = Field(..., description="申请项目名称")
    displaySequence: Optional[str] = Field("", description="排序")
    deptId: str = Field(..., description="科室id")
    checkItemList: List[CheckItemModel] = Field(..., description="检查项目列表")
    hospital: Optional[HospitalModel] = Field(None, description="医院信息（单院区可不传）")


# 接口03：体检科室结果传输
class DeptResultItem(BaseModel):
    """检查结果项目模型"""
    hostId: str = Field(..., description="体检号")
    deptCode: str = Field(..., description="科室代码")
    mainCode: str = Field(..., description="申请项目代码")
    mainName: str = Field(..., description="申请项目名称")
    detailCode: str = Field(..., description="检查项目代码")
    detailName: str = Field(..., description="检查项目名称")
    
    # 结果信息
    result: Optional[str] = Field("", description="检查结果")
    resultDesc: Optional[str] = Field("", description="结果描述")
    unit: Optional[str] = Field("", description="单位")
    lowerLimit: Optional[float] = Field(None, description="下限值")
    upperLimit: Optional[float] = Field(None, description="上限值")
    referenceRange: Optional[str] = Field("", description="参考范围")
    abnormalFlag: Optional[str] = Field("", description="异常标识")
    
    # 医生信息
    operatorCode: str = Field(..., description="检查操作员")
    doctorCode: str = Field(..., description="检查医生代码")
    doctorName: str = Field(..., description="检查医生姓名")
    checkTime: str = Field(..., description="检查时间")
    
    # 其他
    itemIndex: Optional[int] = Field(0, description="项目序号")
    dataType: Optional[str] = Field("", description="数据类型")


class DeptSummary(BaseModel):
    """科室小结模型"""
    hostId: str = Field(..., description="体检号")
    deptCode: str = Field(..., description="科室代码")
    mainCode: str = Field(..., description="申请项目代码")
    mainName: str = Field(..., description="申请项目名称")
    summary: str = Field(..., description="科室小结")
    doctorCode: str = Field(..., description="检查医生代码")
    doctorName: str = Field(..., description="检查医生姓名")
    checkTime: str = Field(..., description="检查时间")
    abnormalLevel: Optional[str] = Field("", description="异常等级")


class DeptResultRequest(BaseModel):
    """科室结果传输请求模型"""
    hostId: str = Field(..., description="体检号")
    deptCode: str = Field(..., description="科室代码")
    results: List[DeptResultItem] = Field(..., description="检查结果列表")
    summary: Optional[DeptSummary] = Field(None, description="科室小结")


# 接口04：医生信息传输
class DoctorInfoRequest(BaseModel):
    """医生信息请求模型"""
    doctorCode: str = Field(..., description="医生代码")
    doctorName: str = Field(..., description="医生姓名")
    searchCode: Optional[str] = Field("", description="检索码")
    identityCard: Optional[str] = Field("", description="身份证号")
    telephone: Optional[str] = Field("", description="联系电话")
    sex: Optional[str] = Field("", description="性别")
    status: str = Field(..., description="状态：1-启用，0-停用")


# 接口05：科室信息传输
class DeptInfoRequest(BaseModel):
    """科室信息请求模型"""
    deptCode: str = Field(..., description="科室代码")
    deptName: str = Field(..., description="科室名称")
    searchCode: Optional[str] = Field("", description="检索码")
    sortIndex: Optional[int] = Field(0, description="排序")
    status: str = Field(..., description="状态：1-启用，0-停用")


# 接口06：字典信息传输
class DictInfoRequest(BaseModel):
    """字典信息请求模型"""
    dictType: str = Field(..., description="字典类型")
    dictCode: str = Field(..., description="字典代码")
    dictName: str = Field(..., description="字典名称")
    parentCode: Optional[str] = Field("", description="父级代码")
    sortIndex: Optional[int] = Field(0, description="排序")
    status: str = Field(..., description="状态：1-启用，0-停用")


# 接口07：主检结束结论回传
class ConclusionItem(BaseModel):
    """诊断结论项目模型"""
    hostId: str = Field(..., description="体检号")
    deptCode: str = Field(..., description="科室代码")
    mainName: str = Field(..., description="申请项目名称")
    conclusionCode: str = Field(..., description="结论词代码")
    conclusionName: str = Field(..., description="结论词名称")
    
    # 结论内容
    medicalExplanation: Optional[str] = Field("", description="医学解释")
    resultSummary: Optional[str] = Field("", description="检查结果汇总")
    suggestion: Optional[str] = Field("", description="建议")
    grade: str = Field(..., description="重要性等级：1-重要，2-次要，3-其他")
    
    # 医生信息
    doctorCode: str = Field(..., description="诊断医生代码")
    doctorName: str = Field(..., description="诊断医生姓名")
    diagnosisTime: str = Field(..., description="诊断时间")
    
    # 其他
    printIndex: Optional[int] = Field(0, description="打印序号")
    abnormalCondition: Optional[str] = Field("", description="异常情况")


class ConclusionRequest(BaseModel):
    """主检结论请求模型"""
    hostId: str = Field(..., description="体检号")
    conclusions: List[ConclusionItem] = Field(..., description="诊断结论列表")
    finalDoctorCode: str = Field(..., description="总审医生代码")
    finalDoctorName: str = Field(..., description="总审医生姓名")
    finalTime: str = Field(..., description="总审完成时间")


# 接口15：分科退回
class DepartmentReturnRequest(BaseModel):
    """分科退回请求模型"""
    peNo: str = Field(..., description="体检号")
    markDoctor: str = Field(..., description="标记医生")
    errorItem: str = Field(..., description="错误项目")
    returnDept: Dict[str, str] = Field(..., description="退回科室信息，包含code和name")
    receiveDoctor: Dict[str, str] = Field(..., description="接收医生信息，包含code和name")
    remark: str = Field(..., description="备注")
    currentNodeType: int = Field(..., description="当前节点类型")
    
    @validator('returnDept', 'receiveDoctor')
    def validate_dept_doctor_info(cls, v):
        """验证科室和医生信息格式"""
        if not isinstance(v, dict):
            raise ValueError("Must be a dictionary")
        if 'code' not in v or 'name' not in v:
            raise ValueError("Must contain 'code' and 'name' fields")
        return v


# 接口20：查询个人开单情况
class ChargeQueryRequest(BaseModel):
    """收费查询请求模型"""
    hostIds: List[str] = Field(..., description="体检号列表")


class ChargeInfo(BaseModel):
    """收费信息模型"""
    hostId: str = Field(..., description="体检号")
    chargeCode: str = Field(..., description="收费单号")
    chargeTime: str = Field(..., description="收费时间")
    operatorCode: str = Field(..., description="收费员代码")
    operatorName: str = Field(..., description="收费员姓名")
    totalAmount: float = Field(..., description="总金额")
    actualAmount: float = Field(..., description="实收金额")
    status: str = Field(..., description="收费状态")
    payDetails: List[Dict[str, Any]] = Field([], description="支付明细")


class ChargeQueryResponse(BaseResponse):
    """收费查询响应模型"""
    data: List[ChargeInfo] = Field([], description="收费信息列表")


# 通用响应模型
class SuccessResponse(BaseResponse):
    """成功响应模型"""
    code: int = Field(0, description="成功状态码")
    msg: str = Field("success", description="成功消息")


class ErrorResponse(BaseResponse):
    """错误响应模型"""
    code: int = Field(..., description="错误状态码，非0")
    msg: str = Field(..., description="错误消息")
    data: Optional[str] = Field(None, description="错误详情")


# 批量操作模型
class BatchRequest(BaseModel):
    """批量操作请求基类"""
    batchId: str = Field(..., description="批次ID")
    totalCount: int = Field(..., description="总数量")
    currentBatch: int = Field(..., description="当前批次号")
    
    class Config:
        # 允许子类扩展
        extra = "allow"


class BatchPeInfoRequest(BatchRequest):
    """批量体检信息请求"""
    peInfoList: List[PeInfoRequest] = Field(..., description="体检信息列表")


class BatchDeptResultRequest(BatchRequest):
    """批量科室结果请求"""
    deptResults: List[DeptResultRequest] = Field(..., description="科室结果列表")


class BatchConclusionRequest(BatchRequest):
    """批量诊断结论请求"""
    conclusions: List[ConclusionRequest] = Field(..., description="诊断结论列表")


# 同步状态枚举
class SyncStatus(str, Enum):
    """同步状态枚举"""
    PENDING = "pending"      # 待同步
    SYNCING = "syncing"      # 同步中
    SUCCESS = "success"      # 成功
    FAILED = "failed"        # 失败
    RETRY = "retry"          # 重试中


class InterfaceType(str, Enum):
    """接口类型枚举"""
    PE_INFO = "sendPeInfo"              # 01-单次体检基本信息传输
    APPLY_ITEM = "sendApplyItem"        # 02-申请项目字典数据传输
    DEPT_RESULT = "deptInfo"            # 03-体检科室结果传输
    DOCTOR_INFO = "sendDoctorInfo"      # 04-医生信息传输
    DEPT_INFO = "sendDeptInfo"          # 05-科室信息传输
    DICT_INFO = "syncDict"              # 06-字典信息传输
    CONCLUSION = "sendConclusion"       # 07-主检结束结论回传
    DEPT_RETURN = "deptReturn"          # 15-分科退回
    CHARGE_QUERY = "queryChargeInfo"    # 20-查询个人开单情况


# API配置模型
class ApiEndpoint(BaseModel):
    """API端点配置"""
    name: str = Field(..., description="接口名称")
    path: str = Field(..., description="接口路径")
    method: str = Field("POST", description="请求方法")
    description: str = Field("", description="接口描述")
    request_model: Optional[str] = Field(None, description="请求模型")
    response_model: Optional[str] = Field(None, description="响应模型")


# 天健云接口端点定义
TIANJIAN_ENDPOINTS = {
    "sendPeInfo": ApiEndpoint(
        name="单次体检基本信息传输",
        path="/sendPeInfo",
        description="传输体检基本信息到天健云",
        request_model="PeInfoRequest"
    ),
    "sendApplyItem": ApiEndpoint(
        name="申请项目字典数据传输",
        path="/dx/inter/syncApplyItem", 
        description="传输申请项目字典数据",
        request_model="ApplyItemRequest"
    ),
    "deptInfo": ApiEndpoint(
        name="体检科室结果传输",
        path="/deptInfo",
        description="传输体检科室结果数据",
        request_model="DeptResultRequest"
    ),
    "sendDoctorInfo": ApiEndpoint(
        name="医生信息传输",
        path="/sendDoctorInfo",
        description="传输医生信息数据",
        request_model="DoctorInfoRequest"
    ),
    "sendDeptInfo": ApiEndpoint(
        name="科室信息传输", 
        path="/sendDeptInfo",
        description="传输科室信息数据",
        request_model="DeptInfoRequest"
    ),
    "syncDict": ApiEndpoint(
        name="字典信息传输",
        path="/syncDict",
        description="传输字典信息数据",
        request_model="DictInfoRequest"
    ),
    "sendConclusion": ApiEndpoint(
        name="主检结束结论回传",
        path="/sendConclusion", 
        description="回传主检结束结论数据",
        request_model="ConclusionRequest"
    ),
    "deptReturn": ApiEndpoint(
        name="分科退回",
        path="/deptReturn",
        description="分科退回处理",
        request_model="DepartmentReturnRequest"
    ),
    "queryChargeInfo": ApiEndpoint(
        name="查询个人开单情况",
        path="/queryChargeInfo",
        method="GET",
        description="查询个人开单收费情况",
        request_model="ChargeQueryRequest",
        response_model="ChargeQueryResponse"
    )
} 