"""
天健云接口签名模块
实现 MD5 签名算法和请求头生成
"""
import hashlib
import uuid
from datetime import datetime
from typing import Dict, Optional

from ..config.settings import settings


def generate_timestamp() -> str:
    """
    生成时间戳
    
    Returns:
        格式为 YYYYmmDDHHMMSS 的时间戳字符串
    """
    return datetime.now().strftime("%Y%m%d%H%M%S")


def generate_nonce() -> str:
    """
    生成随机字符串 (UUID)
    
    Returns:
        UUID 字符串
    """
    return str(uuid.uuid4())


def generate_sign(key: str, timestamp: str) -> str:
    """
    生成签名
    算法：MD5(key + timestamp)，返回32位小写十六进制字符串
    
    Args:
        key: API 密钥
        timestamp: 时间戳
        
    Returns:
        MD5 签名字符串
    """
    # 拼接 key + timestamp
    sign_string = key + timestamp
    
    # 计算 MD5 并转为小写十六进制
    md5_hash = hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    return md5_hash.lower()


def build_headers(
    key: Optional[str] = None,
    mic_code: Optional[str] = None,
    misc_id: Optional[str] = None,
    custom_timestamp: Optional[str] = None
) -> Dict[str, str]:
    """
    构建天健云接口请求头
    
    Args:
        key: API 密钥，默认从配置读取
        mic_code: 平台分配的 mic-code，默认从配置读取
        misc_id: 平台分配的 misc-id，默认从配置读取
        custom_timestamp: 自定义时间戳，一般用于测试
        
    Returns:
        包含所有必要字段的请求头字典
        
    Raises:
        ValueError: 当必要参数缺失时
    """
    # 从配置获取默认值
    api_config = settings.api
    
    key = key or api_config.key
    mic_code = mic_code or api_config.mic_code
    misc_id = misc_id or api_config.misc_id
    
    # 验证必要参数
    if not key:
        raise ValueError("API key 不能为空，请检查配置文件")
    if not mic_code:
        raise ValueError("mic-code 不能为空，请检查配置文件")
    if not misc_id:
        raise ValueError("misc-id 不能为空，请检查配置文件")
    
    # 生成时间戳和随机数
    timestamp = custom_timestamp or generate_timestamp()
    nonce = generate_nonce()
    
    # 生成签名
    sign = generate_sign(key, timestamp)
    
    # 构建请求头
    headers = {
        "Content-Type": "application/json",
        "sign": sign,
        "timestamp": timestamp,
        "nonce": nonce,
        "mic-code": mic_code,
        "misc-id": misc_id
    }
    
    return headers


def verify_sign(key: str, timestamp: str, received_sign: str) -> bool:
    """
    验证签名（用于测试或调试）
    
    Args:
        key: API 密钥
        timestamp: 时间戳
        received_sign: 接收到的签名
        
    Returns:
        签名是否正确
    """
    expected_sign = generate_sign(key, timestamp)
    return expected_sign == received_sign.lower()


class SignatureHelper:
    """签名辅助类，用于复杂签名场景"""
    
    def __init__(self, key: str, mic_code: str, misc_id: str):
        """
        初始化签名助手
        
        Args:
            key: API 密钥
            mic_code: 平台分配码
            misc_id: 平台分配ID
        """
        self.key = key
        self.mic_code = mic_code
        self.misc_id = misc_id
    
    def create_headers(self, custom_timestamp: Optional[str] = None) -> Dict[str, str]:
        """
        创建请求头
        
        Args:
            custom_timestamp: 自定义时间戳
            
        Returns:
            请求头字典
        """
        return build_headers(
            key=self.key,
            mic_code=self.mic_code,
            misc_id=self.misc_id,
            custom_timestamp=custom_timestamp
        )
    
    def validate_config(self) -> bool:
        """
        验证配置是否完整
        
        Returns:
            配置是否有效
        """
        return bool(self.key and self.mic_code and self.misc_id)


# 便捷函数：获取默认签名助手
def get_default_signer() -> SignatureHelper:
    """
    获取使用默认配置的签名助手
    
    Returns:
        SignatureHelper 实例
        
    Raises:
        ValueError: 当配置不完整时
    """
    api_config = settings.api
    
    if not api_config.key or not api_config.mic_code or not api_config.misc_id:
        raise ValueError("API 配置不完整，请检查 key、mic_code、misc_id")
    
    return SignatureHelper(
        key=api_config.key,
        mic_code=api_config.mic_code,
        misc_id=api_config.misc_id
    ) 