"""
健康同步系统CLI命令行接口
提供各种数据同步和管理命令
"""
import sys
import json
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from health_sync.utils.logger import app_logger, log_startup_info
from health_sync.config.settings import settings
from health_sync.services.apply_item_service import apply_item_service
from health_sync.services.dept_return_service import dept_return_service
from health_sync.db.session import init_database, test_database_connections
from health_sync.api.client import test_api_connection
from config import Config


def init_system():
    """初始化系统"""
    try:
        # 设置日志
        log_startup_info()
        
        # 初始化数据库
        init_database()
        
        app_logger.info("系统初始化完成")
        return True
        
    except Exception as e:
        print(f"系统初始化失败: {e}")
        return False


def cmd_test_connection(args):
    """测试连接命令"""
    print("[TEST] 测试系统连接状态...")
    
    # 测试数据库连接
    print("\n[DATA] 数据库连接测试:")
    db_results = test_database_connections()
    
    for db_name, result in db_results.items():
        status = "[OK] 连接正常" if result["connected"] else f"[FAIL] 连接失败: {result['error']}"
        print(f"  {db_name.upper()}数据库: {status}")
    
    # 测试API连接
    print("\n[WEB] 天健云API连接测试:")
    api_connected = test_api_connection()
    api_status = "[OK] 连接正常" if api_connected else "[FAIL] 连接失败"
    print(f"  天健云API: {api_status}")
    
    print(f"\n[CONFIG] API配置:")
    print(f"  服务地址: {settings.api.base_url}")
    print(f"  机构代码: {settings.api.mic_code}")
    print(f"  系统ID: {settings.api.misc_id}")


def cmd_apply_item_summary(args):
    """显示申请项目概览"""
    print("[LIST] 申请项目数据概览")
    print("=" * 60)
    
    try:
        summary = apply_item_service.get_apply_item_summary()
        
        print(f"[DATA] 统计信息:")
        print(f"  总项目数: {summary['total_items']}")
        print(f"  启用项目: {summary['active_items']}")
        print(f"  停用项目: {summary['stopped_items']}")
        
        if summary['type_distribution']:
            print(f"\n🏷️  项目类型分布:")
            for item_type, count in summary['type_distribution'].items():
                print(f"  {item_type}: {count}个")
        
        print(f"\n🕐 更新时间: {summary['last_updated']}")
        
        if 'error' in summary:
            print(f"\n[WARN]  获取数据时出现错误: {summary['error']}")
    
    except Exception as e:
        print(f"[FAIL] 获取申请项目概览失败: {e}")


def cmd_apply_item_list(args):
    """列出申请项目详情"""
    print("[LIST] 申请项目详细列表")
    print("=" * 80)
    
    try:
        items = apply_item_service.get_apply_items_from_db(include_stopped=args.include_stopped)
        
        if not items:
            print("📭 没有找到申请项目数据")
            return
        
        # 限制显示数量
        limit = args.limit or len(items)
        display_items = items[:limit]
        
        for i, item in enumerate(display_items, 1):
            print(f"\n{i}. 项目代码: {item['apply_item_id']}")
            print(f"   项目名称: {item['apply_item_name']}")
            print(f"   排序号: {item['display_sequence']}")
            print(f"   项目类型: {item['set_type']}")
            print(f"   状态: {'启用' if item['stop_flag'] != '1' else '停用'}")
            
            # 显示检查项目
            if item['price_items']:
                print(f"   检查项目 ({len(item['price_items'])}个):")
                for price_item in item['price_items'][:3]:  # 最多显示3个
                    price_str = f"¥{price_item['price']:.2f}" if price_item['price'] > 0 else "免费"
                    print(f"     - {price_item['check_item_name']} ({price_str})")
                if len(item['price_items']) > 3:
                    print(f"     ...还有{len(item['price_items']) - 3}个项目")
        
        if len(items) > limit:
            print(f"\n[NOTE] 显示了前 {limit} 个项目，总共 {len(items)} 个项目")
            print("   使用 --limit 参数调整显示数量")
    
    except Exception as e:
        print(f"[FAIL] 获取申请项目列表失败: {e}")


def cmd_sync_apply_items(args):
    """同步申请项目到天健云"""
    print("[ROCKET] 开始同步申请项目到天健云")
    print("=" * 60)
    
    try:
        # 显示同步参数
        print(f"[LIST] 同步参数:")
        print(f"  批量大小: {args.batch_size}")
        print(f"  包含停用项目: {'是' if args.include_stopped else '否'}")
        print(f"  测试模式: {'是' if args.dry_run else '否'}")
        print(f"  调试模式: {'是' if getattr(args, 'debug', False) else '否'}")
        
        if args.dry_run:
            print("\n[TEST] 测试模式 - 仅显示将要同步的数据，不会实际发送")
            items = apply_item_service.get_apply_items_from_db(include_stopped=args.include_stopped)
            api_items = apply_item_service.format_for_tianjian_api(items)
            
            print(f"\n[DATA] 将要同步的数据:")
            print(f"  数据库项目数: {len(items)}")
            print(f"  有效API数据: {len(api_items)}")
            
            if api_items and (args.show_sample or getattr(args, 'debug', False)):
                print(f"\n[DOC] 示例数据 (第一个项目):")
                sample = api_items[0].dict()
                print(json.dumps(sample, indent=2, ensure_ascii=False))
            
            return
        
        # 执行实际同步
        print(f"\n⏳ 开始同步...")
        start_time = datetime.now()
        
        # 如果有调试模式，使用新的同步方法
        debug_mode = getattr(args, 'debug', False)
        if debug_mode:
            # 分步执行以支持调试
            items = apply_item_service.get_apply_items_from_db(include_stopped=args.include_stopped)
            api_items = apply_item_service.format_for_tianjian_api(items)
            result = apply_item_service.sync_apply_items_to_tianjian(
                api_items, 
                batch_size=args.batch_size, 
                debug=True
            )
        else:
            result = apply_item_service.sync_all_apply_items(
                batch_size=args.batch_size,
                include_stopped=args.include_stopped
            )
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 显示结果
        print(f"\n[DATA] 同步完成！")
        print(f"  总数: {result['total_count']}")
        print(f"  成功: {result['success_count']}")
        print(f"  失败: {result['failed_count']}")
        print(f"  成功率: {result['success_rate']:.1f}%")
        print(f"  耗时: {duration:.2f}秒")
        
        if result['failed_items']:
            print(f"\n[FAIL] 失败的项目:")
            for item_id in result['failed_items'][:10]:  # 最多显示10个
                print(f"  - {item_id}")
            if len(result['failed_items']) > 10:
                print(f"  ...还有{len(result['failed_items']) - 10}个失败项目")
        
        # 保存结果到文件
        if args.save_result:
            result_file = f"apply_item_sync_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print(f"\n[SAVE] 详细结果已保存到: {result_file}")
    
    except Exception as e:
        print(f"[FAIL] 申请项目同步失败: {e}")


def cmd_dept_return(args):
    """分科退回命令"""
    print("[BACK] 执行分科退回操作")
    print("=" * 60)
    
    try:
        # 显示输入参数
        print(f"[LIST] 退回参数:")
        print(f"  体检号: {args.pe_no}")
        print(f"  标记医生: {args.mark_doctor}")
        print(f"  错误项目: {args.error_item}")
        print(f"  退回科室: {args.return_dept_name} ({args.return_dept_code})")
        print(f"  接收医生: {args.receive_doctor_name} ({args.receive_doctor_code})")
        print(f"  备注: {args.remark}")
        print(f"  节点类型: {args.current_node_type}")
        print(f"  测试模式: {'是' if args.dry_run else '否'}")
        
        if args.dry_run:
            print("\n[TEST] 测试模式 - 仅验证参数，不会实际执行退回操作")
            print("[OK] 参数验证通过，实际运行时将执行分科退回")
            return
        
        # 执行分科退回
        print(f"\n⏳ 开始执行分科退回...")
        start_time = datetime.now()
        
        result = dept_return_service.process_dept_return(
            pe_no=args.pe_no,
            mark_doctor=args.mark_doctor,
            error_item=args.error_item,
            return_dept_code=args.return_dept_code,
            return_dept_name=args.return_dept_name,
            receive_doctor_code=args.receive_doctor_code,
            receive_doctor_name=args.receive_doctor_name,
            remark=args.remark,
            current_node_type=args.current_node_type
        )
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 显示结果
        print(f"\n[DATA] 分科退回完成！")
        print(f"  记录ID: {result['record_id']}")
        print(f"  同步状态: {'成功' if result['sync_result']['success'] else '失败'}")
        
        if result['sync_result']['success']:
            print(f"  响应消息: {result['sync_result']['message']}")
        else:
            print(f"  错误信息: {result['sync_result']['error']}")
        
        print(f"  处理时间: {duration:.2f}秒")
        
        # 保存结果到文件
        if args.save_result:
            result_file = f"dept_return_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print(f"\n[SAVE] 详细结果已保存到: {result_file}")
    
    except Exception as e:
        print(f"[FAIL] 分科退回执行失败: {e}")


def cmd_dept_return_stats(args):
    """分科退回统计命令"""
    print("[CHART] 分科退回统计信息")
    print("=" * 60)
    
    try:
        stats = dept_return_service.get_return_statistics()
        
        print(f"[DATA] 统计概览:")
        print(f"  总退回次数: {stats['total_returns']}")
        print(f"  待处理: {stats['pending_returns']}")
        print(f"  已处理: {stats['processed_returns']}")
        print(f"  今日退回: {stats['today_returns']}")
        print(f"  本周退回: {stats['week_returns']}")
        print(f"  本月退回: {stats['month_returns']}")
        
        print(f"\n🕐 统计时间: {stats['last_updated']}")
        
        if 'error' in stats:
            print(f"\n[WARN] 获取统计时出现错误: {stats['error']}")
    
    except Exception as e:
        print(f"[FAIL] 获取分科退回统计失败: {e}")


def cmd_show_config(args):
    """显示配置信息"""
    print("⚙️  系统配置信息")
    print("=" * 60)
    
    print(f"📍 API配置:")
    print(f"  服务地址: {settings.api.base_url}")
    print(f"  机构代码: {settings.api.mic_code}")
    print(f"  系统ID: {settings.api.misc_id}")
    print(f"  超时时间: {settings.api.timeout}秒")
    print(f"  重试次数: {settings.api.retry_times}")
    
    print(f"\n[SAVE] 数据库配置:")
    print(f"  主数据库: {settings.database_main.server}:{settings.database_main.port}")
    print(f"  数据库名: {settings.database_main.database}")
    print(f"  用户名: {settings.database_main.username}")
    
    print(f"\n[HOSPITAL] 医院配置:")
    print(f"  医院代码: {settings.hospital.code or '未配置'}")
    print(f"  医院名称: {settings.hospital.name or '未配置'}")
    print(f"  多院区模式: {'是' if settings.hospital.is_multi_site else '否'}")
    
    print(f"\n[SYNC] 同步配置:")
    print(f"  批量大小: {settings.sync.batch_size}")
    print(f"  增量窗口: {settings.sync.incremental_window}分钟")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="健康同步系统 - 天健云数据同步工具",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 连接测试命令
    test_parser = subparsers.add_parser('test', help='测试系统连接')
    test_parser.set_defaults(func=cmd_test_connection)
    
    # 配置显示命令
    config_parser = subparsers.add_parser('config', help='显示系统配置')
    config_parser.set_defaults(func=cmd_show_config)
    
    # 申请项目相关命令
    apply_group = subparsers.add_parser('apply-item', help='申请项目相关操作')
    apply_subparsers = apply_group.add_subparsers(dest='apply_command', help='申请项目子命令')
    
    # 申请项目概览
    summary_parser = apply_subparsers.add_parser('summary', help='显示申请项目概览')
    summary_parser.set_defaults(func=cmd_apply_item_summary)
    
    # 申请项目列表
    list_parser = apply_subparsers.add_parser('list', help='列出申请项目')
    list_parser.add_argument('--limit', type=int, help='限制显示数量')
    list_parser.add_argument('--include-stopped', action='store_true', help='包含停用项目')
    list_parser.set_defaults(func=cmd_apply_item_list)
    
    # 申请项目同步
    sync_parser = apply_subparsers.add_parser('sync', help='同步申请项目到天健云')
    sync_parser.add_argument('--batch-size', type=int, default=10, help='批量大小 (默认: 10)')
    sync_parser.add_argument('--include-stopped', action='store_true', help='包含停用项目')
    sync_parser.add_argument('--dry-run', action='store_true', help='测试模式，不实际发送')
    sync_parser.add_argument('--show-sample', action='store_true', help='在测试模式下显示示例数据')
    sync_parser.add_argument('--save-result', action='store_true', help='保存同步结果到文件')
    sync_parser.add_argument('--debug', action='store_true', help='调试模式，显示完整的请求和响应报文')
    sync_parser.set_defaults(func=cmd_sync_apply_items)
    
    # 分科退回相关命令
    dept_return_group = subparsers.add_parser('dept-return', help='分科退回相关操作')
    dept_return_subparsers = dept_return_group.add_subparsers(dest='dept_return_command', help='分科退回子命令')
    
    # 执行分科退回
    return_parser = dept_return_subparsers.add_parser('execute', help='执行分科退回操作')
    return_parser.add_argument('--pe-no', required=True, help='体检号')
    return_parser.add_argument('--mark-doctor', required=True, help='标记医生')
    return_parser.add_argument('--error-item', required=True, help='错误项目')
    return_parser.add_argument('--return-dept-code', required=True, help='退回科室代码')
    return_parser.add_argument('--return-dept-name', required=True, help='退回科室名称')
    return_parser.add_argument('--receive-doctor-code', required=True, help='接收医生代码')
    return_parser.add_argument('--receive-doctor-name', required=True, help='接收医生姓名')
    return_parser.add_argument('--remark', required=True, help='备注')
    return_parser.add_argument('--current-node-type', type=int, default=2, help='当前节点类型 (默认: 2)')
    return_parser.add_argument('--dry-run', action='store_true', help='测试模式，不实际发送')
    return_parser.add_argument('--save-result', action='store_true', help='保存结果到文件')
    return_parser.set_defaults(func=cmd_dept_return)
    
    # 分科退回统计
    stats_parser = dept_return_subparsers.add_parser('stats', help='显示分科退回统计信息')
    stats_parser.set_defaults(func=cmd_dept_return_stats)
    
    # 解析参数
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 初始化系统
    if not init_system():
        sys.exit(1)
    
    # 执行命令
    try:
        args.func(args)
    except KeyboardInterrupt:
        print("\n[STOP] 操作被用户中断")
    except Exception as e:
        print(f"\n[ERROR] 执行命令时发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 