"""
配置管理模块
支持从 YAML 文件加载配置，提供类型安全的配置访问
注意：优先使用根目录的config.py进行统一配置管理
"""
import os
import sys
import yaml
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from config import Config as RootConfig
    USE_ROOT_CONFIG = True
except ImportError:
    USE_ROOT_CONFIG = False


@dataclass
class ApiConfig:
    """API 接口配置"""
    base_url: str
    key: str
    mic_code: str
    misc_id: str
    timeout: int = 30
    retry_times: int = 3
    retry_delay: int = 5


@dataclass
class DatabaseConfig:
    """数据库配置"""
    server: str
    database: str
    username: str
    password: str
    port: int = 1433
    driver: str = "ODBC Driver 17 for SQL Server"
    charset: str = "utf8"

    @property
    def connection_string(self) -> str:
        """构建数据库连接字符串"""
        return (
            f"mssql+pyodbc://{self.username}:{self.password}"
            f"@{self.server}:{self.port}/{self.database}?driver={self.driver.replace(' ', '+')}&charset={self.charset}"
        )


@dataclass
class SyncConfig:
    """同步任务配置"""
    batch_size: int = 100
    schedules: Dict[str, str] = None
    incremental_window: int = 30
    
    def __post_init__(self):
        if self.schedules is None:
            self.schedules = {}


@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    file_path: str = "logs/health_sync.log"
    max_file_size: str = "10MB"
    backup_count: int = 5
    console_output: bool = True


@dataclass
class HospitalConfig:
    """医院配置"""
    code: str
    name: str
    is_multi_site: bool = False


@dataclass
class GuiConfig:
    """GUI 配置"""
    window_title: str = "体检系统 ↔ 天健云数据同步工具"
    window_size: List[int] = None
    theme: str = "light"
    refresh_interval: int = 5
    
    def __post_init__(self):
        if self.window_size is None:
            self.window_size = [1200, 800]


@dataclass
class MonitoringConfig:
    """监控配置"""
    error_threshold: int = 10
    email_alerts: bool = False
    email_recipients: List[str] = None
    
    def __post_init__(self):
        if self.email_recipients is None:
            self.email_recipients = []


@dataclass
class FiltersConfig:
    """数据过滤配置"""
    exclude_pe_states: List[str] = None
    min_sync_date: str = "2024-01-01"
    max_batch_size: int = 1000
    
    def __post_init__(self):
        if self.exclude_pe_states is None:
            self.exclude_pe_states = []


class Settings:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径，默认为 config.yaml
        """
        self.config_file = config_file or "config.yaml"
        self._config_data: Dict[str, Any] = {}
        self.load_config()
    
    def load_config(self) -> None:
        """加载配置文件"""
        # 首先加载默认配置
        defaults_path = Path(__file__).parent / "defaults.yaml"
        if defaults_path.exists():
            with open(defaults_path, 'r', encoding='utf-8') as f:
                self._config_data = yaml.safe_load(f) or {}
        
        # 然后加载用户配置文件（覆盖默认值）
        config_path = Path(self.config_file)
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                user_config = yaml.safe_load(f) or {}
                self._merge_config(self._config_data, user_config)
        else:
            # 如果用户配置文件不存在，创建一个模板
            self.save_config()
    
    def _merge_config(self, base: Dict, overlay: Dict) -> None:
        """递归合并配置字典"""
        for key, value in overlay.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value
    
    def save_config(self) -> None:
        """保存配置到文件"""
        config_path = Path(self.config_file)
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(self._config_data, f, default_flow_style=False, 
                     allow_unicode=True, indent=2)
    
    @property
    def api(self) -> ApiConfig:
        """API 配置"""
        api_data = self._config_data.get('api', {})
        return ApiConfig(**api_data)
    
    @property
    def database_main(self) -> DatabaseConfig:
        """主数据库配置"""
        db_data = self._config_data.get('database', {}).get('main', {})
        return DatabaseConfig(**db_data)
    
    @property
    def database_pacs(self) -> DatabaseConfig:
        """PACS数据库配置"""
        db_data = self._config_data.get('database', {}).get('pacs', {})
        return DatabaseConfig(**db_data)
    
    @property
    def sync(self) -> SyncConfig:
        """同步配置"""
        sync_data = self._config_data.get('sync', {})
        return SyncConfig(**sync_data)
    
    @property
    def logging(self) -> LoggingConfig:
        """日志配置"""
        log_data = self._config_data.get('logging', {})
        return LoggingConfig(**log_data)
    
    @property
    def hospital(self) -> HospitalConfig:
        """医院配置"""
        hospital_data = self._config_data.get('hospital', {})
        return HospitalConfig(**hospital_data)
    
    @property
    def gui(self) -> GuiConfig:
        """GUI 配置"""
        gui_data = self._config_data.get('gui', {})
        return GuiConfig(**gui_data)
    
    @property
    def monitoring(self) -> MonitoringConfig:
        """监控配置"""
        monitor_data = self._config_data.get('monitoring', {})
        return MonitoringConfig(**monitor_data)
    
    @property
    def filters(self) -> FiltersConfig:
        """过滤配置"""
        filter_data = self._config_data.get('filters', {})
        return FiltersConfig(**filter_data)
    
    def update_config(self, section: str, key: str, value: Any) -> None:
        """更新配置项"""
        if section not in self._config_data:
            self._config_data[section] = {}
        self._config_data[section][key] = value
        self.save_config()


# 全局配置实例
settings = Settings()

# 配置验证函数
def validate_config() -> List[str]:
    """
    验证配置完整性
    
    Returns:
        错误信息列表，如果为空则配置正确
    """
    errors = []
    
    # 验证API配置
    api_config = settings.api
    if not api_config.key:
        errors.append("API key 未配置")
    if not api_config.mic_code:
        errors.append("mic-code 未配置")
    if not api_config.misc_id:
        errors.append("misc-id 未配置")
    
    # 验证数据库配置
    db_main = settings.database_main
    if not db_main.username or not db_main.password:
        errors.append("主数据库用户名或密码未配置")
    
    return errors


def is_config_valid() -> bool:
    """检查配置是否有效"""
    return len(validate_config()) == 0 