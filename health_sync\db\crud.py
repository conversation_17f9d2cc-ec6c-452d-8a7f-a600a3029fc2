"""
CRUD 基础操作模块
提供针对体检系统数据库的常用查询和操作功能
"""
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, timedelta
import logging

from .models import (
    TRegisterMain, TCheckResult, TCheckResultMain, TCheckResultIllness, 
    TDiagResult, CodeItemMain, CodeItemPrice, CodeDeptDict, 
    CodeOperatorDict, CodeSuitMaster, CodeSex, TContract, 
    TUnitsSuitMaster, TChargeMain, TChargePayDetail,
    TSyncLog, TSyncStatus, TCheckResultReturn, TCheckResultReturnFile,
    PeStatus, NodeType
)
from .session import with_main_session, with_pacs_session

logger = logging.getLogger(__name__)


class PeInfoCRUD:
    """体检信息 CRUD 操作"""
    
    @staticmethod
    @with_main_session
    def get_by_client_code(session: Session, client_code: str) -> Optional[TRegisterMain]:
        """根据体检号获取体检信息"""
        return session.query(TRegisterMain).filter(
            TRegisterMain.cClientCode == client_code
        ).first()
    
    @staticmethod
    @with_main_session
    def get_by_status(session: Session, status: str, limit: int = 100) -> List[TRegisterMain]:
        """根据体检状态获取体检信息列表"""
        return session.query(TRegisterMain).filter(
            TRegisterMain.cStatus == status
        ).limit(limit).all()
    
    @staticmethod
    @with_main_session
    def get_updated_since(session: Session, since_time: datetime, limit: int = 100) -> List[TRegisterMain]:
        """获取指定时间后更新的体检信息"""
        return session.query(TRegisterMain).filter(
            or_(
                TRegisterMain.dOperdate >= since_time,
                TRegisterMain.dAffirmdate >= since_time,
                TRegisterMain.cCanDiagDate >= since_time,
                TRegisterMain.cIntoDiagDate >= since_time
            )
        ).order_by(desc(TRegisterMain.dOperdate)).limit(limit).all()
    
    @staticmethod
    @with_main_session
    def get_by_date_range(session: Session, start_date: datetime, 
                         end_date: datetime) -> List[TRegisterMain]:
        """根据日期范围获取体检信息"""
        return session.query(TRegisterMain).filter(
            and_(
                TRegisterMain.dOperdate >= start_date,
                TRegisterMain.dOperdate < end_date
            )
        ).order_by(TRegisterMain.dOperdate).all()
    
    @staticmethod
    @with_main_session
    def count_by_status(session: Session, status: str) -> int:
        """统计指定状态的体检数量"""
        return session.query(TRegisterMain).filter(
            TRegisterMain.cStatus == status
        ).count()


class CheckResultCRUD:
    """检查结果 CRUD 操作"""
    
    @staticmethod
    @with_main_session
    def get_by_client_code(session: Session, client_code: str) -> List[TCheckResult]:
        """获取指定体检号的所有检查结果"""
        return session.query(TCheckResult).filter(
            TCheckResult.cClientCode == client_code
        ).order_by(TCheckResult.nDetailIndex).all()
    
    @staticmethod
    @with_main_session
    def get_by_client_and_dept(session: Session, client_code: str, 
                              dept_code: str) -> List[TCheckResult]:
        """获取指定体检号和科室的检查结果"""
        return session.query(TCheckResult).filter(
            and_(
                TCheckResult.cClientCode == client_code,
                TCheckResult.cDeptCode == dept_code
            )
        ).order_by(TCheckResult.nDetailIndex).all()
    
    @staticmethod
    @with_main_session
    def get_dept_summary(session: Session, client_code: str, 
                        dept_code: str) -> List[TCheckResultMain]:
        """获取科室小结"""
        return session.query(TCheckResultMain).filter(
            and_(
                TCheckResultMain.cClientCode == client_code,
                TCheckResultMain.cDeptCode == dept_code
            )
        ).all()
    
    @staticmethod
    @with_main_session
    def get_updated_results_since(session: Session, since_time: datetime, 
                                 limit: int = 1000) -> List[Tuple[str, str]]:
        """获取指定时间后更新的检查结果（返回体检号和科室代码）"""
        results = session.query(
            TCheckResult.cClientCode, 
            TCheckResult.cDeptCode
        ).filter(
            TCheckResult.dOperDate >= since_time
        ).distinct().limit(limit).all()
        
        return [(r.cClientCode, r.cDeptCode) for r in results]


class ConclusionCRUD:
    """诊断结论 CRUD 操作"""
    
    @staticmethod
    @with_main_session
    def get_by_client_code(session: Session, client_code: str) -> List[TCheckResultIllness]:
        """获取指定体检号的所有诊断结论"""
        return session.query(TCheckResultIllness).filter(
            TCheckResultIllness.cClientCode == client_code
        ).order_by(TCheckResultIllness.nPrintIndex).all()
    
    @staticmethod
    @with_main_session
    def get_diag_results(session: Session, client_code: str) -> List[TDiagResult]:
        """获取总检结果"""
        return session.query(TDiagResult).filter(
            TDiagResult.cClientCode == client_code
        ).all()
    
    @staticmethod
    @with_main_session
    def get_updated_conclusions_since(session: Session, since_time: datetime, 
                                    limit: int = 500) -> List[str]:
        """获取指定时间后更新的诊断结论（返回体检号列表）"""
        results = session.query(TCheckResultIllness.cClientCode).filter(
            TCheckResultIllness.dOperdate >= since_time
        ).distinct().limit(limit).all()
        
        return [r.cClientCode for r in results]


class DictCRUD:
    """字典数据 CRUD 操作"""
    
    @staticmethod
    @with_main_session
    def get_all_items(session: Session, include_stopped: bool = False) -> List[CodeItemMain]:
        """获取所有申请项目"""
        query = session.query(CodeItemMain)
        if not include_stopped:
            query = query.filter(CodeItemMain.cStopTag != '1')
        return query.order_by(CodeItemMain.nIndex).all()
    
    @staticmethod
    @with_main_session
    def get_item_prices(session: Session, main_code: str) -> List[CodeItemPrice]:
        """获取指定申请项目的价格信息"""
        return session.query(CodeItemPrice).filter(
            CodeItemPrice.cMainCode == main_code
        ).all()
    
    @staticmethod
    @with_main_session
    def get_all_depts(session: Session, include_stopped: bool = False) -> List[CodeDeptDict]:
        """获取所有科室"""
        query = session.query(CodeDeptDict)
        if not include_stopped:
            query = query.filter(CodeDeptDict.cStopTag != '1')
        return query.order_by(CodeDeptDict.nIndex).all()
    
    @staticmethod
    @with_main_session
    def get_all_operators(session: Session, include_stopped: bool = False) -> List[CodeOperatorDict]:
        """获取所有操作员（医生）"""
        query = session.query(CodeOperatorDict)
        if not include_stopped:
            query = query.filter(CodeOperatorDict.cStopTag != '1')
        return query.all()
    
    @staticmethod
    @with_main_session
    def get_all_suits(session: Session, include_stopped: bool = False) -> List[CodeSuitMaster]:
        """获取所有套餐"""
        query = session.query(CodeSuitMaster)
        if not include_stopped:
            query = query.filter(CodeSuitMaster.cStopTag != '1')
        return query.order_by(CodeSuitMaster.nIndex).all()


class ChargeCRUD:
    """收费信息 CRUD 操作"""
    
    @staticmethod
    @with_main_session
    def get_by_client_codes(session: Session, client_codes: List[str]) -> List[TChargeMain]:
        """根据体检号列表获取收费信息"""
        return session.query(TChargeMain).filter(
            TChargeMain.cClientCode.in_(client_codes)
        ).all()
    
    @staticmethod
    @with_main_session
    def get_charge_details(session: Session, charge_code: str) -> List[TChargePayDetail]:
        """获取收费明细"""
        return session.query(TChargePayDetail).filter(
            TChargePayDetail.cChargeCode == charge_code
        ).all()


class SyncCRUD:
    """同步状态 CRUD 操作"""
    
    @staticmethod
    @with_main_session
    def log_sync_request(session: Session, interface_name: str, request_url: str,
                        request_data: str, business_key: str = None) -> TSyncLog:
        """记录同步请求日志"""
        log = TSyncLog(
            interface_name=interface_name,
            request_url=request_url,
            request_data=request_data,
            business_key=business_key,
            sync_time=datetime.now()
        )
        session.add(log)
        session.flush()  # 获取ID
        return log
    
    @staticmethod
    @with_main_session
    def update_sync_response(session: Session, log_id: int, status_code: int,
                           response_data: str, success: bool, error_message: str = None):
        """更新同步响应信息"""
        log = session.query(TSyncLog).filter(TSyncLog.id == log_id).first()
        if log:
            log.status_code = status_code
            log.response_data = response_data
            log.success = success
            log.error_message = error_message
    
    @staticmethod
    @with_main_session
    def get_failed_syncs(session: Session, interface_name: str = None, 
                        hours: int = 24) -> List[TSyncLog]:
        """获取失败的同步记录"""
        since_time = datetime.now() - timedelta(hours=hours)
        query = session.query(TSyncLog).filter(
            and_(
                TSyncLog.success == False,
                TSyncLog.sync_time >= since_time
            )
        )
        
        if interface_name:
            query = query.filter(TSyncLog.interface_name == interface_name)
        
        return query.order_by(desc(TSyncLog.sync_time)).all()
    
    @staticmethod
    @with_main_session
    def update_sync_status(session: Session, client_code: str, interface_name: str):
        """更新同步状态"""
        status = session.query(TSyncStatus).filter(
            TSyncStatus.cClientCode == client_code
        ).first()
        
        if not status:
            status = TSyncStatus(cClientCode=client_code)
            session.add(status)
        
        # 根据接口名称更新对应字段
        now = datetime.now()
        if interface_name == "sendPeInfo":
            status.interface_01_sync = now
        elif interface_name == "deptInfo":
            status.interface_03_sync = now
        elif interface_name == "sendConclusion":
            status.interface_07_sync = now
        
        status.last_update = now
    
    @staticmethod
    @with_main_session
    def get_sync_statistics(session: Session, hours: int = 24) -> Dict[str, Any]:
        """获取同步统计信息"""
        since_time = datetime.now() - timedelta(hours=hours)
        
        # 总同步次数
        total_syncs = session.query(TSyncLog).filter(
            TSyncLog.sync_time >= since_time
        ).count()
        
        # 成功同步次数
        success_syncs = session.query(TSyncLog).filter(
            and_(
                TSyncLog.sync_time >= since_time,
                TSyncLog.success == True
            )
        ).count()
        
        # 失败同步次数
        failed_syncs = total_syncs - success_syncs
        
        # 各接口统计
        interface_stats = session.query(
            TSyncLog.interface_name,
            func.count(TSyncLog.id).label('total'),
            func.sum(func.cast(TSyncLog.success, func.Integer)).label('success')
        ).filter(
            TSyncLog.sync_time >= since_time
        ).group_by(TSyncLog.interface_name).all()
        
        return {
            "total_syncs": total_syncs,
            "success_syncs": success_syncs,
            "failed_syncs": failed_syncs,
            "success_rate": success_syncs / total_syncs if total_syncs > 0 else 0,
            "interface_stats": [
                {
                    "interface": stat.interface_name,
                    "total": stat.total,
                    "success": stat.success or 0,
                    "failed": stat.total - (stat.success or 0)
                }
                for stat in interface_stats
            ]
        }


class DeptReturnCRUD:
    """分科退回 CRUD 操作"""
    
    @staticmethod
    @with_main_session
    def create_return_record(session: Session, return_data: Dict[str, Any]) -> TCheckResultReturn:
        """创建分科退回记录"""
        return_record = TCheckResultReturn(**return_data)
        session.add(return_record)
        session.commit()
        return return_record
    
    @staticmethod
    @with_main_session
    def get_by_id(session: Session, return_id: str) -> Optional[TCheckResultReturn]:
        """根据ID获取分科退回记录"""
        return session.query(TCheckResultReturn).filter(
            TCheckResultReturn.id == return_id
        ).first()
    
    @staticmethod
    @with_main_session
    def get_by_client_code(session: Session, client_code: str) -> List[TCheckResultReturn]:
        """根据体检号获取分科退回记录"""
        return session.query(TCheckResultReturn).filter(
            TCheckResultReturn.cClientCode == client_code
        ).order_by(desc(TCheckResultReturn.cReturnTime)).all()
    
    @staticmethod
    @with_main_session
    def get_by_date_range(session: Session, start_date: datetime, 
                         end_date: datetime) -> List[TCheckResultReturn]:
        """根据日期范围获取分科退回记录"""
        return session.query(TCheckResultReturn).filter(
            and_(
                TCheckResultReturn.cReturnTime >= start_date,
                TCheckResultReturn.cReturnTime < end_date
            )
        ).order_by(desc(TCheckResultReturn.cReturnTime)).all()
    
    @staticmethod
    @with_main_session
    def get_by_status(session: Session, status: str) -> List[TCheckResultReturn]:
        """根据状态获取分科退回记录"""
        return session.query(TCheckResultReturn).filter(
            TCheckResultReturn.cReturnStatus == status
        ).order_by(desc(TCheckResultReturn.cReturnTime)).all()
    
    @staticmethod
    @with_main_session
    def update_return_status(session: Session, return_id: str, 
                           status: str, remark: str = None):
        """更新分科退回记录状态"""
        return_record = session.query(TCheckResultReturn).filter(
            TCheckResultReturn.id == return_id
        ).first()
        
        if return_record:
            return_record.cReturnStatus = status
            if remark:
                return_record.cModifyRemark = remark
            return_record.cModifyTime = datetime.now()
            session.commit()
            return return_record
        return None
    
    @staticmethod
    @with_main_session
    def get_return_statistics(session: Session, days: int = 30) -> Dict[str, Any]:
        """获取分科退回统计信息"""
        since_date = datetime.now() - timedelta(days=days)
        
        # 总退回次数
        total_returns = session.query(TCheckResultReturn).filter(
            TCheckResultReturn.cReturnTime >= since_date
        ).count()
        
        # 按状态统计
        status_stats = session.query(
            TCheckResultReturn.cReturnStatus,
            func.count(TCheckResultReturn.id).label('count')
        ).filter(
            TCheckResultReturn.cReturnTime >= since_date
        ).group_by(TCheckResultReturn.cReturnStatus).all()
        
        # 按科室统计
        dept_stats = session.query(
            TCheckResultReturn.cReturnDoctCode,
            TCheckResultReturn.cReturnDoctName,
            func.count(TCheckResultReturn.id).label('count')
        ).filter(
            TCheckResultReturn.cReturnTime >= since_date
        ).group_by(
            TCheckResultReturn.cReturnDoctCode,
            TCheckResultReturn.cReturnDoctName
        ).all()
        
        # 今日统计
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        today_returns = session.query(TCheckResultReturn).filter(
            TCheckResultReturn.cReturnTime >= today
        ).count()
        
        # 本周统计
        week_start = today - timedelta(days=today.weekday())
        week_returns = session.query(TCheckResultReturn).filter(
            TCheckResultReturn.cReturnTime >= week_start
        ).count()
        
        # 本月统计
        month_start = today.replace(day=1)
        month_returns = session.query(TCheckResultReturn).filter(
            TCheckResultReturn.cReturnTime >= month_start
        ).count()
        
        return {
            "total_returns": total_returns,
            "pending_returns": sum(stat.count for stat in status_stats if stat.cReturnStatus == '待处理'),
            "processed_returns": sum(stat.count for stat in status_stats if stat.cReturnStatus == '已处理'),
            "today_returns": today_returns,
            "week_returns": week_returns,
            "month_returns": month_returns,
            "status_distribution": {stat.cReturnStatus: stat.count for stat in status_stats},
            "dept_distribution": {f"{stat.cReturnDoctName}({stat.cReturnDoctCode})": stat.count for stat in dept_stats}
        }
    
    @staticmethod
    @with_main_session
    def add_return_file(session: Session, return_id: str, file_name: str, 
                       file_content: str) -> TCheckResultReturnFile:
        """添加分科退回附件"""
        file_record = TCheckResultReturnFile(
            id=f"{return_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
            cReturnId=return_id,
            cFileName=file_name,
            pFile=file_content
        )
        session.add(file_record)
        session.commit()
        return file_record
    
    @staticmethod
    @with_main_session
    def get_return_files(session: Session, return_id: str) -> List[TCheckResultReturnFile]:
        """获取分科退回附件"""
        return session.query(TCheckResultReturnFile).filter(
            TCheckResultReturnFile.cReturnId == return_id
        ).all()


# 便捷查询函数
def get_pending_pe_list(limit: int = 100) -> List[str]:
    """获取需要同步的体检号列表"""
    with Session() as session:
        # 获取已分科完成但未完成主检的体检
        results = session.query(TRegisterMain.cClientCode).filter(
            TRegisterMain.cStatus.in_([PeStatus.DEPT_FINISHED, PeStatus.FIRST_CHECKING, 
                                     PeStatus.FIRST_FINISHED, PeStatus.FINAL_CHECKING])
        ).limit(limit).all()
        
        return [r.cClientCode for r in results]


def get_incremental_sync_data(since_minutes: int = 30) -> Dict[str, List[str]]:
    """获取增量同步数据"""
    since_time = datetime.now() - timedelta(minutes=since_minutes)
    
    return {
        "pe_info": [pe.cClientCode for pe in PeInfoCRUD.get_updated_since(since_time)],
        "check_results": [f"{r[0]}_{r[1]}" for r in CheckResultCRUD.get_updated_results_since(since_time)],
        "conclusions": ConclusionCRUD.get_updated_conclusions_since(since_time)
    } 