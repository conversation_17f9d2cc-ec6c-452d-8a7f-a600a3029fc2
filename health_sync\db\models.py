"""
体检系统数据库 ORM 模型
根据 exmdb.sql 解析生成，仅包含天健云接口同步所需的核心表
"""
from sqlalchemy import Column, String, Integer, Float, DateTime, Text, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from typing import Optional
from datetime import datetime

Base = declarative_base()


class TRegisterMain(Base):
    """体检登记主表 - 对应接口01单次体检基本信息传输"""
    __tablename__ = 'T_Register_Main'
    
    # 主键：体检号
    cClientCode = Column(String(10), primary_key=True, comment="体检号")
    
    # 基本信息
    cName = Column(String(20), comment="姓名")
    cSex = Column(String(3), comment="性别代码")
    dBornDate = Column(DateTime, comment="出生日期")
    iAges = Column(Float, comment="年龄")
    cIdCard = Column(String(21), comment="身份证号")
    cTel = Column(String(20), comment="联系电话")
    cMarryFlag = Column(String(3), comment="婚姻状况")
    
    # 体检相关
    cUnitsFlag = Column(String(1), comment="团体标志")
    cContractCode = Column(String(8), comment="合同编号")
    cSuitCode = Column(String(8), comment="套餐代码")
    dOperdate = Column(DateTime, comment="登记时间")
    cOperCode = Column(String(9), comment="登记操作员")
    cOperName = Column(String(12), comment="登记操作员姓名")
    
    # 审核相关
    dAffirmdate = Column(DateTime, comment="确认时间")
    cAffirmCode = Column(String(9), comment="确认操作员")
    cAffirmName = Column(String(12), comment="确认操作员姓名")
    cStatus = Column(String(3), comment="体检状态")
    
    # 主检相关字段
    cCanDiagFlag = Column(String(50), comment="初检标志")
    cCanDiagOperCode = Column(String(50), comment="初检医生代码")
    cCanDiagOperName = Column(String(50), comment="初检医生姓名")
    cCanDiagDate = Column(DateTime, comment="初检完成时间")
    
    cIntoDiagFlag = Column(String(50), comment="总审标志")
    cIntoDiagDoctCode = Column(String(50), comment="总审医生代码")
    cIntoDiagDoctName = Column(String(50), comment="总审医生姓名")
    cIntoDiagDate = Column(DateTime, comment="总审完成时间")
    
    # 其他字段
    cShopCode = Column(String(2), comment="院区代码")
    fTotal = Column(Float, comment="总金额")
    fFactTotal = Column(Float, comment="实际金额")
    cPrintTag = Column(String(1), comment="打印标志")
    cHealthNo = Column(String(10), comment="健康证号")
    cAddress = Column(String(200), comment="地址")
    cWorkNumber = Column(String(50), comment="工号")
    iWorkYears = Column(Integer, comment="工龄")
    state = Column(Integer, comment="状态")


class TCheckResult(Base):
    """检查结果明细表 - 对应接口03体检科室结果传输"""
    __tablename__ = 'T_Check_result'
    
    # 复合主键
    cClientCode = Column(String(10), primary_key=True, comment="体检号")
    cDetailCode = Column(String(10), primary_key=True, comment="检查项目代码")
    
    # 基本信息
    cDeptCode = Column(String(6), comment="科室代码")
    cMainCode = Column(String(6), comment="申请项目代码")
    cMainName = Column(String(40), comment="申请项目名称")
    cDetailName = Column(String(40), comment="检查项目名称")
    
    # 结果相关
    cResult = Column(String(800), comment="检查结果")
    cResultDesc = Column(String(800), comment="结果描述")
    cDetailUnit = Column(String(20), comment="单位")
    fFlower = Column(Float, comment="下限值")
    fTop = Column(Float, comment="上限值")
    cTopOrFlow = Column(String(500), comment="参考范围")
    cAbnor = Column(String(1), comment="异常标识")
    
    # 医生相关
    cOperCode = Column(String(9), comment="检查操作员")
    cDoctCode = Column(String(9), comment="检查医生代码")
    cDoctName = Column(String(12), comment="检查医生姓名")
    dOperDate = Column(DateTime, comment="检查时间")
    
    # 其他
    nDetailIndex = Column(Integer, comment="项目序号")
    cDetailDataType = Column(String(3), comment="数据类型")


class TCheckResultMain(Base):
    """检查结果主表（科室小结）"""
    __tablename__ = 'T_Check_result_Main'
    
    # 复合主键
    cClientCode = Column(String(10), primary_key=True, comment="体检号")
    cDeptCode = Column(String(6), primary_key=True, comment="科室代码")
    cMainCode = Column(String(6), primary_key=True, comment="申请项目代码")
    
    # 基本信息
    cMainName = Column(String(40), comment="申请项目名称")
    cResultCure = Column(String(1000), comment="科室小结")
    
    # 医生相关
    cDoctCode = Column(String(9), comment="检查医生代码")
    cDoctName = Column(String(12), comment="检查医生姓名")
    dOperDate = Column(DateTime, comment="检查时间")
    
    # 审核相关
    cAuditDoctCode = Column(String(9), comment="审核医生代码")
    cAuditDoctName = Column(String(12), comment="审核医生姓名")
    
    # 其他
    cShopCode = Column(String(2), comment="院区代码")
    cIllnessGrade = Column(String(50), comment="异常等级")


class TCheckResultIllness(Base):
    """诊断结论表 - 对应接口07主检结束结论回传"""
    __tablename__ = 'T_Check_Result_Illness'
    
    # 复合主键
    cClientCode = Column(String(10), primary_key=True, comment="体检号")
    cDeptcode = Column(String(6), primary_key=True, comment="科室代码")
    cMainName = Column(String(40), primary_key=True, comment="申请项目名称")
    cIllnessCode = Column(String(6), primary_key=True, comment="结论词代码")
    
    # 结论相关
    cIllnessName = Column(String(100), comment="结论词名称")
    cIllExplain = Column(Text, comment="医学解释")
    cReason = Column(Text, comment="检查结果汇总")
    cAdvice = Column(Text, comment="建议")
    cGrade = Column(String(1), comment="重要性等级:1-重要;2-次要;3-其他")
    
    # 医生相关
    cDoctCode = Column(String(9), comment="诊断医生代码")
    cDoctName = Column(String(12), comment="诊断医生姓名")
    dOperdate = Column(DateTime, comment="诊断时间")
    
    # 其他
    nPrintIndex = Column(Integer, comment="打印序号")
    cCondition = Column(String(200), comment="异常情况")


class TDiagResult(Base):
    """总检结果表"""
    __tablename__ = 'T_Diag_result'
    
    cClientCode = Column(String(10), primary_key=True, comment="体检号")
    cIllnessCode = Column(String(6), primary_key=True, comment="结论词代码")
    
    cIllnessName = Column(String(100), comment="结论词名称")
    cIllExplain = Column(Text, comment="医学解释")
    cAdvice = Column(Text, comment="建议")
    cGrade = Column(String(1), comment="重要性等级")
    cDoctCode = Column(String(9), comment="医生代码")
    cDoctName = Column(String(12), comment="医生姓名")
    dOperdate = Column(DateTime, comment="操作时间")


# 字典表模型
class CodeItemMain(Base):
    """申请项目主表 - 对应接口02申请项目字典数据传输"""
    __tablename__ = 'Code_Item_Main'
    
    cCode = Column(String(6), primary_key=True, comment="项目代码")
    cName = Column(String(40), comment="项目名称")
    cSearchIndex = Column(String(20), comment="检索码")
    nIndex = Column(Integer, comment="排序")
    cSetType = Column(String(3), comment="项目类型")
    cMeaning = Column(String(100), comment="项目说明")
    cStopTag = Column(String(1), comment="停用标志")


class CodeItemPrice(Base):
    """项目价格表"""
    __tablename__ = 'code_Item_Price'
    
    cCode = Column(String(6), primary_key=True, comment="价格项目代码")
    cName = Column(String(40), comment="价格项目名称")
    cMainCode = Column(String(6), comment="申请项目代码")
    fPrice = Column(Float, comment="价格")
    cExplain = Column(String(100), comment="说明")
    cStopTag = Column(String(1), comment="停用标志")
    cSexType = Column(String(3), comment="适用性别")


class CodeDeptDict(Base):
    """科室字典表 - 对应接口05科室信息传输"""
    __tablename__ = 'Code_Dept_dict'
    
    cCode = Column(String(6), primary_key=True, comment="科室代码")
    cName = Column(String(40), comment="科室名称")
    cSearchIndex = Column(String(20), comment="检索码")
    nIndex = Column(Integer, comment="排序")
    cStopTag = Column(String(1), comment="停用标志")


class CodeOperatorDict(Base):
    """操作员字典表 - 对应接口04医生信息传输"""
    __tablename__ = 'Code_Operator_dict'
    
    cCode = Column(String(9), primary_key=True, comment="操作员代码")
    cName = Column(String(12), comment="操作员姓名")
    cPassword = Column(String(10), comment="密码")
    cSearchIndex = Column(String(20), comment="检索码")
    cIdCard = Column(String(21), comment="身份证号")
    cTel = Column(String(20), comment="联系电话")
    cSex = Column(String(3), comment="性别")
    cStopTag = Column(String(1), comment="停用标志")


class CodeSuitMaster(Base):
    """套餐主表 - 对应接口06字典信息传输"""
    __tablename__ = 'Code_Suit_Master'
    
    cCode = Column(String(8), primary_key=True, comment="套餐代码")
    cName = Column(String(40), comment="套餐名称")
    cSearchIndex = Column(String(20), comment="检索码")
    nIndex = Column(Integer, comment="排序")
    cStopTag = Column(String(1), comment="停用标志")
    cSexType = Column(String(3), comment="适用性别")
    fTotalPrice = Column(Float, comment="套餐总价")


class CodeSex(Base):
    """性别字典表"""
    __tablename__ = 'Code_Sex'
    
    cCode = Column(String(3), primary_key=True, comment="性别代码")
    cName = Column(String(10), comment="性别名称")
    cStopTag = Column(String(1), comment="停用标志")


# 合同和单位相关
class TContract(Base):
    """合同表"""
    __tablename__ = 'T_Contract'
    
    cCode = Column(String(8), primary_key=True, comment="合同编号")
    cName = Column(String(50), comment="合同名称")
    cUnitsCode = Column(String(8), comment="单位代码")
    cContractType = Column(String(3), comment="合同类型")
    dStartDate = Column(DateTime, comment="开始日期")
    dEndDate = Column(DateTime, comment="结束日期")
    fTotalPrice = Column(Float, comment="合同总金额")
    cStatus = Column(String(3), comment="合同状态")


class TUnitsSuitMaster(Base):
    """单位套餐表"""
    __tablename__ = 'T_UnitsSuit_Master'
    
    cCode = Column(String(8), primary_key=True, comment="单位套餐代码")
    cName = Column(String(80), comment="单位套餐名称")
    cUnitsCode = Column(String(8), comment="单位代码")
    cSexType = Column(String(3), comment="适用性别")
    fTotalPrice = Column(Float, comment="套餐价格")
    cStopTag = Column(String(1), comment="停用标志")


# 收费相关表（接口20查询个人开单情况）
class TChargeMain(Base):
    """收费主表"""
    __tablename__ = 'T_Charge_Main'
    
    cChargeCode = Column(String(10), primary_key=True, comment="收费单号")
    cClientCode = Column(String(10), comment="体检号")
    dChargeDate = Column(DateTime, comment="收费时间")
    cChargeOperCode = Column(String(9), comment="收费员代码")
    cChargeOperName = Column(String(12), comment="收费员姓名")
    fTotalAmount = Column(Float, comment="总金额")
    fFactAmount = Column(Float, comment="实收金额")
    cStatus = Column(String(3), comment="收费状态")


class TChargePayDetail(Base):
    """收费明细表"""
    __tablename__ = 'T_Charge_PayDetail'
    
    cChargeCode = Column(String(10), primary_key=True, comment="收费单号")
    cPayType = Column(String(3), primary_key=True, comment="支付方式")
    fPayAmount = Column(Float, comment="支付金额")
    cBankCard = Column(String(20), comment="银行卡号")
    cRemark = Column(String(100), comment="备注")


# 为支持同步状态跟踪，新增同步日志表
class TSyncLog(Base):
    """同步日志表（新增，用于跟踪天健云接口同步状态）"""
    __tablename__ = 'T_Sync_Log'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment="日志ID")
    interface_name = Column(String(50), comment="接口名称")
    request_url = Column(String(200), comment="请求URL")
    request_data = Column(Text, comment="请求数据")
    response_data = Column(Text, comment="响应数据")
    status_code = Column(Integer, comment="状态码")
    error_message = Column(Text, comment="错误信息")
    sync_time = Column(DateTime, default=func.now(), comment="同步时间")
    business_key = Column(String(50), comment="业务主键（如体检号）")
    retry_count = Column(Integer, default=0, comment="重试次数")
    success = Column(Boolean, default=False, comment="是否成功")


class TSyncStatus(Base):
    """同步状态表（新增，记录各体检号的同步状态）"""
    __tablename__ = 'T_Sync_Status'
    
    cClientCode = Column(String(10), primary_key=True, comment="体检号")
    interface_01_sync = Column(DateTime, comment="接口01最后同步时间")
    interface_03_sync = Column(DateTime, comment="接口03最后同步时间")
    interface_07_sync = Column(DateTime, comment="接口07最后同步时间")
    last_update = Column(DateTime, default=func.now(), comment="最后更新时间")
    sync_version = Column(Integer, default=1, comment="同步版本号")


class TCheckResultReturn(Base):
    """分科退回主表 - 对应接口15分科退回"""
    __tablename__ = 'T_Check_Result_Return'
    
    id = Column(String(50), primary_key=True, comment="主键ID")
    cShopCode = Column(String(10), comment="院区代码")
    cClientCode = Column(String(10), comment="体检号") 
    cMainCode = Column(String(10), comment="申请项目代码")
    cMainName = Column(String(50), comment="申请项目名称")
    cDoctCode = Column(String(10), comment="检查医生代码")
    cDoctName = Column(String(50), comment="检查医生姓名")
    cAuditDoctCode = Column(String(10), comment="审核医生代码")
    cAuditDoctName = Column(String(50), comment="审核医生姓名")
    cReturnReason = Column(String(300), comment="退回原因")
    cReturnType = Column(String(50), comment="退回类型")
    cReturnStatus = Column(String(20), comment="退回状态")
    cReturnTimes = Column(Integer, comment="退回次数")
    cReturnTime = Column(DateTime, comment="退回时间")
    cReturnDoctCode = Column(String(50), comment="退回医生代码")
    cReturnDoctName = Column(String(50), comment="退回医生姓名")
    cModifyRemark = Column(String(300), comment="修改备注")
    cModifyDoctCode = Column(String(10), comment="修改医生代码")
    cModifyDoctName = Column(String(50), comment="修改医生姓名")
    cModifyTime = Column(DateTime, comment="修改时间")
    fModifyUsedHour = Column(Integer, comment="修改用时（小时）")


class TCheckResultReturnFile(Base):
    """分科退回附件表"""
    __tablename__ = 'T_Check_Result_Return_File'
    
    id = Column(String(50), primary_key=True, comment="文件ID")
    cReturnId = Column(String(50), comment="退回记录ID")
    cFileName = Column(String(100), comment="文件名")
    pFile = Column(Text, comment="文件内容")


# 为方便调试，添加一个工具函数
def get_table_names():
    """获取所有表名"""
    return [
        'T_Register_Main',
        'T_Check_result', 
        'T_Check_result_Main',
        'T_Check_Result_Illness',
        'T_Diag_result',
        'Code_Item_Main',
        'code_Item_Price', 
        'Code_Dept_dict',
        'Code_Operator_dict',
        'Code_Suit_Master',
        'Code_Sex',
        'T_Contract',
        'T_UnitsSuit_Master',
        'T_Charge_Main',
        'T_Charge_PayDetail',
        'T_Sync_Log',
        'T_Sync_Status',
        'T_Check_Result_Return',
        'T_Check_Result_Return_File'
    ]


# 常用枚举定义
class PeStatus:
    """体检状态枚举"""
    REGISTERED = "1"      # 登记完成
    DEPT_CHECKING = "2"   # 分科未完成
    DEPT_FINISHED = "3"   # 分科完成
    FIRST_CHECKING = "4"  # 主检初审中
    FIRST_FINISHED = "5"  # 主检初审完成
    FINAL_CHECKING = "6"  # 主检终审中
    FINAL_FINISHED = "7"  # 主检终审完成


class NodeType:
    """流程节点类型"""
    REGISTER = 1    # 登记
    CHECK = 2       # 分科检查
    FIRST_DIAG = 3  # 主检
    FINAL_DIAG = 4  # 总审


class IllnessGrade:
    """异常等级"""
    IMPORTANT = "1"  # 重要
    SECONDARY = "2"  # 次要
    OTHER = "3"      # 其他 