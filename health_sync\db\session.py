"""
数据库会话管理模块
支持主数据库（examdb_center）和PACS数据库（ExamDB_Pacs）的连接
使用统一的config.py配置管理
"""
import sys
from pathlib import Path
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from contextlib import contextmanager
from typing import Generator, Optional
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from config import Config
    USE_ROOT_CONFIG = True
except ImportError:
    from ..config.settings import settings
    USE_ROOT_CONFIG = False

from .models import Base

logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理器，支持多数据库连接"""
    
    def __init__(self):
        self._main_engine = None
        self._pacs_engine = None
        self._main_session_factory = None
        self._pacs_session_factory = None
        self._initialized = False
    
    def initialize(self) -> None:
        """初始化数据库连接"""
        if self._initialized:
            return
            
        try:
            if USE_ROOT_CONFIG:
                # 使用根目录统一配置
                main_connection_string = Config.get_interface_db_connection_string()
                # 转换为SQLAlchemy格式
                db_config = Config.INTERFACE_DB_CONFIG
                main_sqlalchemy_url = (
                    f"mssql+pyodbc://{db_config['username']}:{db_config['password']}"
                    f"@{db_config['host']}:{db_config['port']}/{db_config['database']}"
                    f"?driver={db_config['driver'].replace(' ', '+')}&charset=utf8"
                )

                # 初始化主数据库连接
                self._main_engine = create_engine(
                    main_sqlalchemy_url,
                    poolclass=QueuePool,
                    pool_size=10,
                    max_overflow=20,
                    pool_pre_ping=True,
                    pool_recycle=3600,
                    echo=False  # 生产环境关闭SQL日志
                )

                # PACS数据库使用相同配置
                self._pacs_engine = create_engine(
                    main_sqlalchemy_url,
                    poolclass=QueuePool,
                    pool_size=5,
                    max_overflow=10,
                    pool_pre_ping=True,
                    pool_recycle=3600,
                    echo=False
                )
            else:
                # 使用原有配置方式
                main_config = settings.database_main
                self._main_engine = create_engine(
                    main_config.connection_string,
                    poolclass=QueuePool,
                    pool_size=10,
                    max_overflow=20,
                    pool_pre_ping=True,
                    pool_recycle=3600,
                    echo=False  # 生产环境关闭SQL日志
                )

                # 初始化PACS数据库连接
                pacs_config = settings.database_pacs
                self._pacs_engine = create_engine(
                    pacs_config.connection_string,
                    poolclass=QueuePool,
                    pool_size=5,
                    max_overflow=10,
                    pool_pre_ping=True,
                    pool_recycle=3600,
                    echo=False
                )
            
            # 创建会话工厂
            self._main_session_factory = sessionmaker(
                bind=self._main_engine,
                autocommit=False,
                autoflush=False
            )
            
            self._pacs_session_factory = sessionmaker(
                bind=self._pacs_engine,
                autocommit=False,
                autoflush=False
            )
            
            # 添加连接事件监听器
            self._setup_event_listeners()
            
            self._initialized = True
            logger.info("数据库连接初始化成功")
            
        except Exception as e:
            logger.error(f"数据库连接初始化失败: {e}")
            raise
    
    def _setup_event_listeners(self) -> None:
        """设置数据库连接事件监听器"""
        
        @event.listens_for(self._main_engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            """为SQL Server连接设置额外参数"""
            # SQL Server特定设置可以在这里添加
            pass
        
        @event.listens_for(self._main_engine, "checkout")
        def receive_checkout(dbapi_connection, connection_record, connection_proxy):
            """连接checkout事件"""
            logger.debug("Main database connection checked out")
        
        @event.listens_for(self._pacs_engine, "checkout")
        def receive_pacs_checkout(dbapi_connection, connection_record, connection_proxy):
            """PACS数据库连接checkout事件"""
            logger.debug("PACS database connection checked out")
    
    @contextmanager
    def get_main_session(self) -> Generator[Session, None, None]:
        """
        获取主数据库会话的上下文管理器
        
        使用方式:
            with db_manager.get_main_session() as session:
                # 使用session进行数据库操作
        """
        if not self._initialized:
            self.initialize()
        
        session = self._main_session_factory()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"主数据库操作失败，回滚事务: {e}")
            raise
        finally:
            session.close()
    
    @contextmanager 
    def get_pacs_session(self) -> Generator[Session, None, None]:
        """
        获取PACS数据库会话的上下文管理器
        
        使用方式:
            with db_manager.get_pacs_session() as session:
                # 使用session进行PACS数据库操作
        """
        if not self._initialized:
            self.initialize()
            
        session = self._pacs_session_factory()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"PACS数据库操作失败，回滚事务: {e}")
            raise
        finally:
            session.close()
    
    def create_tables(self, engine_type: str = "main") -> None:
        """
        创建表结构（主要用于创建同步日志表等新表）
        
        Args:
            engine_type: "main" 或 "pacs"
        """
        if not self._initialized:
            self.initialize()
            
        if engine_type == "main":
            Base.metadata.create_all(bind=self._main_engine)
            logger.info("主数据库表结构创建完成")
        elif engine_type == "pacs":
            # PACS数据库一般不需要创建新表，只读取
            logger.info("PACS数据库为只读，跳过表创建")
        else:
            raise ValueError(f"不支持的引擎类型: {engine_type}")
    
    def test_connections(self) -> dict:
        """
        测试数据库连接
        
        Returns:
            包含连接测试结果的字典
        """
        results = {
            "main": {"connected": False, "error": None},
            "pacs": {"connected": False, "error": None}
        }
        
        try:
            # 测试主数据库连接
            with self.get_main_session() as session:
                session.execute("SELECT 1")
                results["main"]["connected"] = True
                logger.info("主数据库连接测试成功")
        except Exception as e:
            results["main"]["error"] = str(e)
            logger.error(f"主数据库连接测试失败: {e}")
        
        try:
            # 测试PACS数据库连接
            with self.get_pacs_session() as session:
                session.execute("SELECT 1")
                results["pacs"]["connected"] = True
                logger.info("PACS数据库连接测试成功")
        except Exception as e:
            results["pacs"]["error"] = str(e)
            logger.error(f"PACS数据库连接测试失败: {e}")
        
        return results
    
    def close(self) -> None:
        """关闭数据库连接"""
        if self._main_engine:
            self._main_engine.dispose()
            logger.info("主数据库连接已关闭")
        
        if self._pacs_engine:
            self._pacs_engine.dispose()
            logger.info("PACS数据库连接已关闭")
        
        self._initialized = False


# 全局数据库管理器实例 - 延迟初始化
_db_manager = None


def get_db_manager() -> DatabaseManager:
    """获取数据库管理器实例（延迟初始化）"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
    return _db_manager


# 便捷函数
def get_main_session() -> Generator[Session, None, None]:
    """获取主数据库会话（便捷函数）"""
    return get_db_manager().get_main_session()


def get_pacs_session() -> Generator[Session, None, None]:
    """获取PACS数据库会话（便捷函数）"""
    return get_db_manager().get_pacs_session()


def init_database() -> None:
    """初始化数据库连接（便捷函数）"""
    get_db_manager().initialize()


def test_database_connections() -> dict:
    """测试数据库连接（便捷函数）"""
    return get_db_manager().test_connections()


def create_sync_tables() -> None:
    """创建同步相关的新表"""
    db_manager.create_tables("main")


# 用于依赖注入的会话提供器
class SessionProvider:
    """会话提供器，用于依赖注入"""
    
    @staticmethod
    def get_main_session() -> Session:
        """获取主数据库会话实例（非上下文管理器）"""
        if not db_manager._initialized:
            db_manager.initialize()
        return db_manager._main_session_factory()
    
    @staticmethod
    def get_pacs_session() -> Session:
        """获取PACS数据库会话实例（非上下文管理器）"""
        if not db_manager._initialized:
            db_manager.initialize()
        return db_manager._pacs_session_factory()


# 装饰器：自动管理数据库会话
def with_main_session(func):
    """
    装饰器：自动为函数提供主数据库会话
    
    被装饰的函数第一个参数应该是session
    """
    def wrapper(*args, **kwargs):
        with get_main_session() as session:
            return func(session, *args, **kwargs)
    return wrapper


def with_pacs_session(func):
    """
    装饰器：自动为函数提供PACS数据库会话
    
    被装饰的函数第一个参数应该是session
    """
    def wrapper(*args, **kwargs):
        with get_pacs_session() as session:
            return func(session, *args, **kwargs)
    return wrapper 