"""
健康同步系统主程序入口
"""
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from health_sync.utils.logger import log_startup_info, log_shutdown_info, app_logger
from health_sync.utils.exceptions import HealthSyncError, ConfigurationError
from health_sync.config.settings import settings
from health_sync.db.session import init_database, test_database_connections
from health_sync.api.client import test_api_connection


class HealthSyncApp:
    """健康同步系统主应用类"""
    
    def __init__(self):
        self.initialized = False
        self.running = False
    
    def initialize(self) -> bool:
        """
        初始化应用
        
        Returns:
            初始化是否成功
        """
        try:
            app_logger.info("开始初始化健康同步系统...")
            
            # 记录启动信息
            log_startup_info()
            
            # 检查配置
            self._check_configuration()
            
            # 初始化数据库
            self._initialize_database()
            
            # 测试API连接
            self._test_api_connection()
            
            self.initialized = True
            app_logger.info("健康同步系统初始化完成")
            return True
            
        except Exception as e:
            app_logger.error(f"系统初始化失败: {e}", error=e)
            return False
    
    def _check_configuration(self):
        """检查配置是否完整"""
        app_logger.info("检查配置...")
        
        # 检查API配置
        if not settings.api.key:
            raise ConfigurationError("API Key未配置", "api.key")
        
        if not settings.api.mic_code:
            raise ConfigurationError("MIC Code未配置", "api.mic_code")
        
        if not settings.api.misc_id:
            raise ConfigurationError("MISC ID未配置", "api.misc_id")
        
        # 检查数据库配置
        if not settings.database_main.server:
            raise ConfigurationError("主数据库服务器未配置", "database.main.server")
        
        if not settings.database_main.username:
            raise ConfigurationError("主数据库用户名未配置", "database.main.username")
        
        if not settings.database_pacs.server:
            raise ConfigurationError("PACS数据库服务器未配置", "database.pacs.server")
        
        app_logger.info("配置检查通过")
    
    def _initialize_database(self):
        """初始化数据库连接"""
        app_logger.info("初始化数据库连接...")
        
        try:
            # 初始化数据库连接
            init_database()
            
            # 测试数据库连接
            test_results = test_database_connections()
            
            if not test_results["main"]["connected"]:
                raise HealthSyncError(
                    f"主数据库连接失败: {test_results['main']['error']}",
                    "DB_CONNECTION_FAILED"
                )
            
            if not test_results["pacs"]["connected"]:
                app_logger.warning(f"PACS数据库连接失败: {test_results['pacs']['error']}")
                # PACS数据库连接失败不阻止系统启动，但会记录警告
            
            app_logger.info("数据库连接初始化完成")
            
        except Exception as e:
            raise HealthSyncError(f"数据库初始化失败: {e}", "DB_INIT_FAILED")
    
    def _test_api_connection(self):
        """测试API连接"""
        app_logger.info("测试天健云API连接...")
        
        try:
            if test_api_connection():
                app_logger.info("天健云API连接测试成功")
            else:
                app_logger.warning("天健云API连接测试失败，但系统仍可启动")
                
        except Exception as e:
            app_logger.warning(f"API连接测试异常: {e}")
    
    def run(self):
        """运行应用"""
        if not self.initialized:
            app_logger.error("系统未初始化，无法运行")
            return False
        
        try:
            app_logger.info("健康同步系统开始运行...")
            self.running = True
            
            # 这里将来会启动PySide6界面或其他服务
            app_logger.info("系统运行中，等待用户操作...")
            
            # 临时：输出系统状态信息
            self._show_system_status()
            
            return True
            
        except KeyboardInterrupt:
            app_logger.info("接收到停止信号，正在关闭系统...")
            return True
            
        except Exception as e:
            app_logger.error(f"系统运行异常: {e}", error=e)
            return False
        
        finally:
            self.shutdown()
    
    def _show_system_status(self):
        """显示系统状态信息"""
        app_logger.info("=" * 60)
        app_logger.info("系统状态信息")
        app_logger.info("=" * 60)
        app_logger.info(f"配置文件: {settings.config_file}")
        app_logger.info(f"API基础URL: {settings.api.base_url}")
        app_logger.info(f"主数据库: {settings.database_main.server}:{settings.database_main.port}")
        app_logger.info(f"PACS数据库: {settings.database_pacs.server}:{settings.database_pacs.port}")
        app_logger.info(f"日志目录: logs/")
        app_logger.info("=" * 60)
        app_logger.info("系统初始化完成，可以开始使用")
        app_logger.info("=" * 60)
    
    def shutdown(self):
        """关闭应用"""
        if self.running:
            app_logger.info("正在关闭健康同步系统...")
            
            try:
                # 这里将来会关闭各种服务、数据库连接等
                from health_sync.db.session import db_manager
                from health_sync.api.client import api_client
from config import Config
                
                # 关闭数据库连接
                db_manager.close()
                
                # 关闭API客户端
                api_client.close()
                
                self.running = False
                log_shutdown_info()
                
            except Exception as e:
                app_logger.error(f"系统关闭时发生异常: {e}", error=e)


def main():
    """主函数"""
    app = HealthSyncApp()
    
    try:
        # 初始化
        if not app.initialize():
            app_logger.error("系统初始化失败，程序退出")
            sys.exit(1)
        
        # 运行
        success = app.run()
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        app_logger.info("程序被用户中断")
        sys.exit(0)
        
    except Exception as e:
        app_logger.critical(f"程序发生严重错误: {e}", error=e)
        sys.exit(1)
    
    finally:
        app.shutdown()


if __name__ == "__main__":
    main() 