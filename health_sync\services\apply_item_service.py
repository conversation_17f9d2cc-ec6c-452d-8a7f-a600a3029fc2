"""
申请项目字典数据服务 - 2号接口实现
实现申请项目数据的获取、格式化和同步功能
"""
import logging
from typing import List, Dict, Optional, Any
from datetime import datetime

from ..config.settings import settings
from ..db.crud import DictCRUD, PeInfoCRUD
from ..api.client import get_api_client
from ..api.schemas import ApplyItemRequest, CheckItemModel, HospitalModel
from ..utils.exceptions import HealthSyncError

logger = logging.getLogger(__name__)


class ApplyItemService:
    """申请项目字典数据服务"""
    
    def __init__(self):
        self.api_client = get_api_client()
        
    def get_apply_items_from_db(self, include_stopped: bool = False) -> List[Dict]:
        """
        从数据库获取申请项目数据
        
        Args:
            include_stopped: 是否包含停用的项目
            
        Returns:
            申请项目列表
        """
        logger.info("从数据库获取申请项目数据...")
        
        try:
            # 获取申请项目主表数据
            items = DictCRUD.get_all_items(include_stopped=include_stopped)
            
            result = []
            for item in items:
                # 获取该项目对应的价格项目（作为检查项目）
                price_items = DictCRUD.get_item_prices(item.cCode)
                
                # 构建结果
                item_data = {
                    'apply_item_id': item.cCode or '',
                    'apply_item_name': item.cName or '',
                    'display_sequence': str(item.nIndex) if item.nIndex else '0',
                    'set_type': item.cSetType or '',
                    'meaning': item.cMeaning or '',
                    'search_index': item.cSearchIndex or '',
                    'stop_flag': item.cStopTag or '0',
                    'price_items': []
                }
                
                # 添加价格项目作为检查项目
                for price_item in price_items:
                    check_item = {
                        'check_item_id': price_item.cCode or '',
                        'check_item_name': price_item.cName or '',
                        'price': float(price_item.fPrice) if price_item.fPrice else 0.0,
                        'explain': price_item.cExplain or '',
                        'sex_type': price_item.cSexType or ''
                    }
                    item_data['price_items'].append(check_item)
                
                # 如果没有价格项目，则用主项目本身作为检查项目
                if not price_items:
                    item_data['price_items'].append({
                        'check_item_id': item.cCode or '',
                        'check_item_name': item.cName or '',
                        'price': 0.0,
                        'explain': item.cMeaning or '',
                        'sex_type': ''
                    })
                
                # 只添加有效的项目
                if item_data['apply_item_id'] and item_data['apply_item_name']:
                    result.append(item_data)
            
            logger.info(f"成功获取 {len(result)} 个申请项目")
            return result
            
        except Exception as e:
            logger.error(f"从数据库获取申请项目数据失败: {e}")
            raise HealthSyncError(f"获取申请项目数据失败: {e}", "DB_QUERY_FAILED")
    
    def get_default_dept_id(self) -> str:
        """
        获取默认科室ID
        如果系统有默认科室配置，使用配置的值，否则使用体检科
        """
        try:
            # 先尝试从数据库获取第一个有效科室
            depts = DictCRUD.get_all_depts(include_stopped=False)
            if depts:
                return depts[0].cCode or '000001'
            
            # 如果没有科室数据，返回默认值
            return '000001'
            
        except Exception as e:
            logger.warning(f"获取默认科室ID失败: {e}，使用默认值")
            return '000001'
    
    def format_for_tianjian_api(self, items: List[Dict], dept_id: Optional[str] = None) -> List[ApplyItemRequest]:
        """
        将数据库数据格式化为天健云API要求的格式
        
        Args:
            items: 数据库中的申请项目数据
            dept_id: 科室ID，如果为空则使用默认科室
            
        Returns:
            格式化后的API请求数据
        """
        logger.info(f"格式化 {len(items)} 个申请项目数据为天健云API格式...")
        
        if not dept_id:
            dept_id = self.get_default_dept_id()
        
        # 是否配置了医院信息
        hospital_info = None
        if settings.hospital.code and settings.hospital.name:
            hospital_info = HospitalModel(
                code=settings.hospital.code,
                name=settings.hospital.name
            )
        
        result = []
        for item in items:
            try:
                # 构建检查项目列表
                check_items = []
                for i, price_item in enumerate(item.get('price_items', [])):
                    check_item = CheckItemModel(
                        checkItemId=price_item['check_item_id'],
                        checkItemName=price_item['check_item_name'],
                        displaySequence=str(i + 1)  # 使用序号作为排序
                    )
                    check_items.append(check_item)
                
                # 构建申请项目
                apply_item = ApplyItemRequest(
                    applyItemId=item['apply_item_id'],
                    applyItemName=item['apply_item_name'],
                    displaySequence=item['display_sequence'],
                    deptId=dept_id,
                    checkItemList=check_items,
                    hospital=hospital_info
                )
                
                result.append(apply_item)
                
            except Exception as e:
                logger.error(f"格式化申请项目 {item.get('apply_item_id', 'unknown')} 失败: {e}")
                continue
        
        logger.info(f"成功格式化 {len(result)} 个申请项目")
        return result
    
    def sync_apply_items_to_tianjian(self, items: List[ApplyItemRequest], batch_size: int = 10, debug: bool = False) -> Dict[str, Any]:
        """
        同步申请项目数据到天健云
        
        Args:
            items: 要同步的申请项目列表
            batch_size: 批量大小，避免单次请求过大
            debug: 是否输出详细调试信息
            
        Returns:
            同步结果统计
        """
        logger.info(f"开始同步 {len(items)} 个申请项目到天健云...")
        
        total_count = len(items)
        success_count = 0
        failed_count = 0
        failed_items = []
        
        # 分批处理
        for i in range(0, total_count, batch_size):
            batch_items = items[i:i + batch_size]
            batch_number = i // batch_size + 1
            total_batches = (total_count + batch_size - 1) // batch_size
            
            logger.info(f"处理第 {batch_number}/{total_batches} 批，包含 {len(batch_items)} 个项目")
            
            try:
                # 调用API客户端的申请项目接口
                response = self.api_client.send_apply_item(batch_items, debug=debug)
                
                if response.code == 0:
                    success_count += len(batch_items)
                    logger.info(f"第 {batch_number} 批同步成功")
                else:
                    failed_count += len(batch_items)
                    logger.error(f"第 {batch_number} 批同步失败: {response.msg}")
                    failed_items.extend([item.applyItemId for item in batch_items])
                    
            except Exception as e:
                failed_count += len(batch_items)
                logger.error(f"第 {batch_number} 批同步异常: {e}")
                failed_items.extend([item.applyItemId for item in batch_items])
        
        # 统计结果
        result = {
            'total_count': total_count,
            'success_count': success_count,
            'failed_count': failed_count,
            'success_rate': (success_count / total_count * 100) if total_count > 0 else 0,
            'failed_items': failed_items,
            'sync_time': datetime.now().isoformat()
        }
        
        logger.info(f"申请项目同步完成 - 总数: {total_count}, 成功: {success_count}, 失败: {failed_count}, 成功率: {result['success_rate']:.1f}%")
        
        return result
    
    def sync_all_apply_items(self, batch_size: int = 10, include_stopped: bool = False) -> Dict[str, Any]:
        """
        同步所有申请项目数据到天健云
        
        Args:
            batch_size: 批量大小
            include_stopped: 是否包含停用的项目
            
        Returns:
            同步结果
        """
        logger.info("开始完整的申请项目数据同步流程...")
        
        try:
            # 1. 从数据库获取数据
            db_items = self.get_apply_items_from_db(include_stopped=include_stopped)
            
            if not db_items:
                logger.warning("没有找到申请项目数据")
                return {
                    'total_count': 0,
                    'success_count': 0,
                    'failed_count': 0,
                    'success_rate': 100,
                    'failed_items': [],
                    'sync_time': datetime.now().isoformat(),
                    'message': '没有找到申请项目数据'
                }
            
            # 2. 格式化为API格式
            api_items = self.format_for_tianjian_api(db_items)
            
            if not api_items:
                logger.warning("没有有效的申请项目数据可以同步")
                return {
                    'total_count': len(db_items),
                    'success_count': 0,
                    'failed_count': len(db_items),
                    'success_rate': 0,
                    'failed_items': [item['apply_item_id'] for item in db_items],
                    'sync_time': datetime.now().isoformat(),
                    'message': '数据格式化失败，无有效数据'
                }
            
            # 3. 同步到天健云
            result = self.sync_apply_items_to_tianjian(api_items, batch_size)
            
            return result
            
        except Exception as e:
            logger.error(f"申请项目同步失败: {e}")
            raise HealthSyncError(f"申请项目同步失败: {e}", "SYNC_FAILED")
    
    def get_apply_item_summary(self) -> Dict[str, Any]:
        """
        获取申请项目数据概览
        
        Returns:
            数据统计信息
        """
        try:
            items = self.get_apply_items_from_db(include_stopped=True)
            
            active_items = [item for item in items if item['stop_flag'] != '1']
            stopped_items = [item for item in items if item['stop_flag'] == '1']
            
            # 按项目类型分组统计
            type_stats = {}
            for item in active_items:
                item_type = item['set_type'] or '未分类'
                if item_type not in type_stats:
                    type_stats[item_type] = 0
                type_stats[item_type] += 1
            
            return {
                'total_items': len(items),
                'active_items': len(active_items),
                'stopped_items': len(stopped_items),
                'type_distribution': type_stats,
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取申请项目概览失败: {e}")
            return {
                'total_items': 0,
                'active_items': 0,
                'stopped_items': 0,
                'type_distribution': {},
                'last_updated': datetime.now().isoformat(),
                'error': str(e)
            }


# 全局服务实例
apply_item_service = ApplyItemService() 