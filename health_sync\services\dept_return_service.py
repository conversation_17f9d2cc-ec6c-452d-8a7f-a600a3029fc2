"""
分科退回服务 - 15号接口实现
实现分科退回数据的记录、处理和同步功能
"""
import logging
import uuid
from typing import List, Dict, Optional, Any
from datetime import datetime

from ..config.settings import settings
from ..db.crud import DictCRUD, PeInfoCRUD, DeptReturnCRUD
from ..api.client import get_api_client
from ..api.schemas import DepartmentReturnRequest
from ..utils.exceptions import HealthSyncError

logger = logging.getLogger(__name__)


class DeptReturnService:
    """分科退回服务"""
    
    def __init__(self):
        self.api_client = get_api_client()
        
    def create_return_record(self, pe_no: str, mark_doctor: str, error_item: str,
                           return_dept_code: str, return_dept_name: str,
                           receive_doctor_code: str, receive_doctor_name: str,
                           remark: str, current_node_type: int) -> str:
        """
        创建分科退回记录
        
        Args:
            pe_no: 体检号
            mark_doctor: 标记医生
            error_item: 错误项目
            return_dept_code: 退回科室代码
            return_dept_name: 退回科室名称
            receive_doctor_code: 接收医生代码
            receive_doctor_name: 接收医生姓名
            remark: 备注
            current_node_type: 当前节点类型
            
        Returns:
            退回记录ID
        """
        logger.info(f"创建分科退回记录 - 体检号: {pe_no}")
        
        try:
            # 生成记录ID
            record_id = str(uuid.uuid4())
            
            # 构建退回记录数据
            return_data = {
                'id': record_id,
                'cShopCode': settings.hospital.code or '01',
                'cClientCode': pe_no,
                'cMainCode': error_item,
                'cMainName': error_item,  # 这里可能需要根据项目代码查询名称
                'cDoctCode': mark_doctor,
                'cDoctName': mark_doctor,  # 这里可能需要根据医生代码查询姓名
                'cAuditDoctCode': receive_doctor_code,
                'cAuditDoctName': receive_doctor_name,
                'cReturnReason': remark,
                'cReturnType': '分科退回',
                'cReturnStatus': '待处理',
                'cReturnTimes': 1,
                'cReturnTime': datetime.now(),
                'cReturnDoctCode': return_dept_code,
                'cReturnDoctName': return_dept_name,
                'cModifyRemark': remark,
                'cModifyDoctCode': mark_doctor,
                'cModifyDoctName': mark_doctor,
                'cModifyTime': datetime.now(),
                'fModifyUsedHour': 0
            }
            
            # 保存到数据库
            record = DeptReturnCRUD.create_return_record(return_data)
            
            logger.info(f"分科退回记录创建成功 - ID: {record_id}")
            return record_id
            
        except Exception as e:
            logger.error(f"创建分科退回记录失败: {e}")
            raise HealthSyncError(f"创建分科退回记录失败: {e}", "CREATE_RETURN_FAILED")
    
    def send_dept_return_to_tianjian(self, pe_no: str, mark_doctor: str, error_item: str,
                                   return_dept_code: str, return_dept_name: str,
                                   receive_doctor_code: str, receive_doctor_name: str,
                                   remark: str, current_node_type: int) -> Dict[str, Any]:
        """
        发送分科退回信息到天健云
        
        Args:
            pe_no: 体检号
            mark_doctor: 标记医生
            error_item: 错误项目
            return_dept_code: 退回科室代码
            return_dept_name: 退回科室名称
            receive_doctor_code: 接收医生代码
            receive_doctor_name: 接收医生姓名
            remark: 备注
            current_node_type: 当前节点类型
            
        Returns:
            同步结果
        """
        logger.info(f"发送分科退回信息到天健云 - 体检号: {pe_no}")
        
        try:
            # 构建API请求数据
            dept_return_request = DepartmentReturnRequest(
                peNo=pe_no,
                markDoctor=mark_doctor,
                errorItem=error_item,
                returnDept={
                    "code": return_dept_code,
                    "name": return_dept_name
                },
                receiveDoctor={
                    "code": receive_doctor_code,
                    "name": receive_doctor_name
                },
                remark=remark,
                currentNodeType=current_node_type
            )
            
            # 调用API
            response = self.api_client.send_dept_return(dept_return_request)
            
            if response.code == 0:
                logger.info(f"分科退回信息发送成功 - 体检号: {pe_no}")
                return {
                    'success': True,
                    'message': response.msg,
                    'pe_no': pe_no,
                    'sync_time': datetime.now().isoformat()
                }
            else:
                logger.error(f"分科退回信息发送失败 - 体检号: {pe_no}, 错误: {response.msg}")
                return {
                    'success': False,
                    'error': response.msg,
                    'pe_no': pe_no,
                    'sync_time': datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"分科退回信息同步异常 - 体检号: {pe_no}: {e}")
            return {
                'success': False,
                'error': str(e),
                'pe_no': pe_no,
                'sync_time': datetime.now().isoformat()
            }
    
    def process_dept_return(self, pe_no: str, mark_doctor: str, error_item: str,
                          return_dept_code: str, return_dept_name: str,
                          receive_doctor_code: str, receive_doctor_name: str,
                          remark: str, current_node_type: int) -> Dict[str, Any]:
        """
        处理分科退回 - 创建记录并同步到天健云
        
        Args:
            pe_no: 体检号
            mark_doctor: 标记医生
            error_item: 错误项目
            return_dept_code: 退回科室代码
            return_dept_name: 退回科室名称
            receive_doctor_code: 接收医生代码
            receive_doctor_name: 接收医生姓名
            remark: 备注
            current_node_type: 当前节点类型
            
        Returns:
            处理结果
        """
        logger.info(f"开始处理分科退回 - 体检号: {pe_no}")
        
        try:
            # 1. 创建本地退回记录
            record_id = self.create_return_record(
                pe_no, mark_doctor, error_item,
                return_dept_code, return_dept_name,
                receive_doctor_code, receive_doctor_name,
                remark, current_node_type
            )
            
            # 2. 同步到天健云
            sync_result = self.send_dept_return_to_tianjian(
                pe_no, mark_doctor, error_item,
                return_dept_code, return_dept_name,
                receive_doctor_code, receive_doctor_name,
                remark, current_node_type
            )
            
            # 3. 更新本地记录状态
            if sync_result['success']:
                DeptReturnCRUD.update_return_status(record_id, '已处理', '同步成功')
            else:
                DeptReturnCRUD.update_return_status(record_id, '同步失败', sync_result.get('error', ''))
            
            result = {
                'record_id': record_id,
                'sync_result': sync_result,
                'process_time': datetime.now().isoformat()
            }
            
            if sync_result['success']:
                logger.info(f"分科退回处理成功 - 体检号: {pe_no}, 记录ID: {record_id}")
            else:
                logger.error(f"分科退回处理失败 - 体检号: {pe_no}, 错误: {sync_result.get('error')}")
            
            return result
            
        except Exception as e:
            logger.error(f"分科退回处理异常 - 体检号: {pe_no}: {e}")
            raise HealthSyncError(f"分科退回处理失败: {e}", "PROCESS_RETURN_FAILED")
    
    def get_return_records(self, pe_no: Optional[str] = None, 
                          start_date: Optional[datetime] = None,
                          end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        获取分科退回记录
        
        Args:
            pe_no: 体检号（可选）
            start_date: 开始日期（可选）
            end_date: 结束日期（可选）
            
        Returns:
            退回记录列表
        """
        logger.info("查询分科退回记录")
        
        try:
            # 从数据库查询退回记录
            records = []
            
            if pe_no:
                # 查询指定体检号的退回记录
                records = DeptReturnCRUD.get_by_client_code(pe_no)
            elif start_date and end_date:
                # 查询指定日期范围的退回记录
                records = DeptReturnCRUD.get_by_date_range(start_date, end_date)
            else:
                # 查询最近的退回记录
                end = datetime.now()
                start = end - timedelta(days=30)  # 默认查询最近30天
                records = DeptReturnCRUD.get_by_date_range(start, end)
            
            # 转换为字典格式
            result = []
            for record in records:
                result.append({
                    'id': record.id,
                    'pe_no': record.cClientCode,
                    'shop_code': record.cShopCode,
                    'main_code': record.cMainCode,
                    'main_name': record.cMainName,
                    'doctor_code': record.cDoctCode,
                    'doctor_name': record.cDoctName,
                    'audit_doctor_code': record.cAuditDoctCode,
                    'audit_doctor_name': record.cAuditDoctName,
                    'return_reason': record.cReturnReason,
                    'return_type': record.cReturnType,
                    'return_status': record.cReturnStatus,
                    'return_times': record.cReturnTimes,
                    'return_time': record.cReturnTime.isoformat() if record.cReturnTime else None,
                    'return_doctor_code': record.cReturnDoctCode,
                    'return_doctor_name': record.cReturnDoctName,
                    'modify_remark': record.cModifyRemark,
                    'modify_time': record.cModifyTime.isoformat() if record.cModifyTime else None
                })
            
            logger.info(f"查询到 {len(result)} 条分科退回记录")
            return result
            
        except Exception as e:
            logger.error(f"查询分科退回记录失败: {e}")
            raise HealthSyncError(f"查询分科退回记录失败: {e}", "QUERY_RETURN_FAILED")
    
    def get_return_statistics(self) -> Dict[str, Any]:
        """
        获取分科退回统计信息
        
        Returns:
            统计信息
        """
        try:
            # 使用CRUD获取统计信息
            stats = DeptReturnCRUD.get_return_statistics()
            
            return stats
            
        except Exception as e:
            logger.error(f"获取分科退回统计失败: {e}")
            return {
                'total_returns': 0,
                'pending_returns': 0,
                'processed_returns': 0,
                'today_returns': 0,
                'week_returns': 0,
                'month_returns': 0,
                'last_updated': datetime.now().isoformat(),
                'error': str(e)
            }


# 全局服务实例
dept_return_service = DeptReturnService()