"""
健康同步系统配置向导
帮助用户交互式配置数据库连接和API设置
"""
import sys
import click
import pymssql
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from health_sync.config.settings import settings
from health_sync.utils.logger import app_logger
from health_sync.api.client import test_api_connection
from config import Config


def test_database_connection(server, port, database, username, password):
    """测试数据库连接"""
    try:
        conn = pymssql.connect(
            server=server,
            port=port,
            database=database,
            user=username,
            password=password,
            timeout=10,
            login_timeout=10
        )
        conn.close()
        return True, "连接成功"
    except Exception as e:
        return False, str(e)


def test_api_connection_with_params(base_url, key, mic_code, misc_id):
    """使用指定参数测试API连接"""
    try:
        # 临时设置API参数
        original_values = {
            'base_url': settings.api.base_url,
            'key': settings.api.key,
            'mic_code': settings.api.mic_code,
            'misc_id': settings.api.misc_id
        }
        
        settings.api.base_url = base_url
        settings.api.key = key
        settings.api.mic_code = mic_code
        settings.api.misc_id = misc_id
        
        result = test_api_connection()
        
        # 恢复原始值
        for attr, value in original_values.items():
            setattr(settings.api, attr, value)
        
        return result
    except Exception as e:
        return False


@click.command()
def setup_wizard():
    """健康同步系统配置向导"""
    click.echo("=" * 60)
    click.echo("[HOSPITAL] 健康同步系统配置向导")
    click.echo("=" * 60)
    click.echo("本向导将帮助您配置数据库连接和天健云API设置")
    click.echo()
    
    # 配置主数据库
    click.echo("[DATA] 配置主数据库连接")
    click.echo("-" * 30)
    
    main_server = click.prompt("数据库服务器地址", 
                              default=settings.database_main.server or "localhost")
    main_port = click.prompt("数据库端口", 
                            default=settings.database_main.port, type=int)
    main_database = click.prompt("数据库名称", 
                                default=settings.database_main.database or ${INTERFACE_DB_NAME:-Examdb})
    main_username = click.prompt("用户名", 
                                default=settings.database_main.username or "sa")
    main_password = click.prompt("密码", hide_input=True, 
                                default=settings.database_main.password or "")
    
    # 测试主数据库连接
    click.echo("正在测试主数据库连接...")
    success, message = test_database_connection(
        main_server, main_port, main_database, main_username, main_password
    )
    
    if success:
        click.echo("[OK] 主数据库连接成功！")
        
        # 保存主数据库配置
        settings.database_main.server = main_server
        settings.database_main.port = main_port
        settings.database_main.database = main_database
        settings.database_main.username = main_username
        settings.database_main.password = main_password
        
    else:
        click.echo(f"[FAIL] 主数据库连接失败: {message}")
        if not click.confirm("是否继续配置其他选项？"):
            sys.exit(1)
    
    click.echo()
    
    # 配置PACS数据库
    click.echo("🖼️ 配置PACS数据库连接")
    click.echo("-" * 30)
    
    use_separate_pacs = click.confirm("PACS数据库是否与主数据库分离？", default=False)
    
    if use_separate_pacs:
        pacs_server = click.prompt("PACS数据库服务器地址", default=main_server)
        pacs_port = click.prompt("PACS数据库端口", default=main_port, type=int)
        pacs_database = click.prompt("PACS数据库名称", default="ExamDB_Pacs")
        pacs_username = click.prompt("PACS用户名", default=main_username)
        pacs_password = click.prompt("PACS密码", hide_input=True, default=main_password)
    else:
        pacs_server = main_server
        pacs_port = main_port
        pacs_database = "ExamDB_Pacs"
        pacs_username = main_username
        pacs_password = main_password
    
    # 测试PACS数据库连接
    click.echo("正在测试PACS数据库连接...")
    success, message = test_database_connection(
        pacs_server, pacs_port, pacs_database, pacs_username, pacs_password
    )
    
    if success:
        click.echo("[OK] PACS数据库连接成功！")
        
        # 保存PACS数据库配置
        settings.database_pacs.server = pacs_server
        settings.database_pacs.port = pacs_port
        settings.database_pacs.database = pacs_database
        settings.database_pacs.username = pacs_username
        settings.database_pacs.password = pacs_password
        
    else:
        click.echo(f"[WARN] PACS数据库连接失败: {message}")
        click.echo("注意：PACS数据库连接失败不会影响主要功能，但图片相关功能将不可用")
    
    click.echo()
    
    # 配置天健云API
    click.echo("☁️ 配置天健云API")
    click.echo("-" * 30)
    
    api_base_url = click.prompt("API基础URL", 
                               default=settings.api.base_url or "http://203.83.237.114:9300")
    api_key = click.prompt("API Key", 
                          default=settings.api.key or "")
    mic_code = click.prompt("MIC Code", 
                           default=settings.api.mic_code or "MIC1.0001")
    misc_id = click.prompt("MISC ID", 
                          default=settings.api.misc_id or "MISC1.000001")
    
    # 测试API连接
    click.echo("正在测试天健云API连接...")
    api_success = test_api_connection_with_params(api_base_url, api_key, mic_code, misc_id)
    
    if api_success:
        click.echo("[OK] 天健云API连接成功！")
        
        # 保存API配置
        settings.api.base_url = api_base_url
        settings.api.key = api_key
        settings.api.mic_code = mic_code
        settings.api.misc_id = misc_id
        
    else:
        click.echo("[WARN] 天健云API连接失败")
        click.echo("请检查网络连接和API参数，稍后可以通过配置文件修改")
        
        if click.confirm("是否仍要保存这些API设置？"):
            settings.api.base_url = api_base_url
            settings.api.key = api_key
            settings.api.mic_code = mic_code
            settings.api.misc_id = misc_id
    
    click.echo()
    
    # 配置其他选项
    click.echo("⚙️ 其他配置选项")
    click.echo("-" * 30)
    
    # 同步任务配置
    enable_auto_sync = click.confirm("是否启用自动同步任务？", default=True)
    settings.sync.auto_sync_enabled = enable_auto_sync
    
    if enable_auto_sync:
        sync_interval = click.prompt("同步间隔（分钟）", 
                                   default=settings.sync.sync_interval_minutes, type=int)
        settings.sync.sync_interval_minutes = sync_interval
    
    # 日志配置
    log_level = click.prompt("日志级别", 
                           default=settings.logging.level,
                           type=click.Choice(['DEBUG', 'INFO', 'WARNING', 'ERROR']))
    settings.logging.level = log_level
    
    # 保存配置
    click.echo()
    click.echo("[SAVE] 保存配置...")
    
    try:
        settings.save_config()
        click.echo("[OK] 配置已保存到: " + str(settings.config_file))
    except Exception as e:
        click.echo(f"[FAIL] 配置保存失败: {e}")
        sys.exit(1)
    
    # 配置完成总结
    click.echo()
    click.echo("🎉 配置完成！")
    click.echo("=" * 60)
    click.echo("配置总结:")
    click.echo(f"✓ 主数据库: {main_server}:{main_port}/{main_database}")
    click.echo(f"✓ PACS数据库: {pacs_server}:{pacs_port}/{pacs_database}")
    click.echo(f"✓ API服务器: {api_base_url}")
    click.echo(f"✓ 自动同步: {'启用' if enable_auto_sync else '禁用'}")
    click.echo(f"✓ 日志级别: {log_level}")
    click.echo()
    click.echo("您现在可以使用以下命令测试系统:")
    click.echo("  python -m health_sync.cli status")
    click.echo("  python -m health_sync.cli test connection")
    click.echo("  python -m health_sync.cli run")
    click.echo("=" * 60)


if __name__ == '__main__':
    setup_wizard() 