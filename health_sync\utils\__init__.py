"""
工具模块
"""

from .logger import (
    app_logger, api_logger, db_logger, sync_logger, get_logger,
    HealthSyncLogger, ApiLogger, DatabaseLogger, SyncLogger,
    LogContext, log_performance, log_startup_info, log_shutdown_info
)
from .exceptions import (
    HealthSyncError, ConfigurationError, DatabaseError, DatabaseConnectionError,
    TianjianAPIError, TianjianTimeoutError, TianjianServerError, TianjianAuthError,
    DataValidationError, DataTransformError, SyncError, BusinessLogicError,
    FileOperationError, SchedulerError, ErrorCodes,
    handle_exceptions, format_error_for_logging, format_error_for_user
)
from .helpers import (
    safe_get, safe_str, safe_int, safe_float, safe_decimal,
    format_datetime, parse_datetime, get_age_from_birth,
    validate_id_card, extract_birth_from_id_card, extract_gender_from_id_card,
    generate_uuid, generate_short_uuid, calculate_md5, mask_sensitive_data,
    clean_phone_number, validate_phone_number, chunk_list, flatten_dict,
    deep_merge_dict, encode_base64, decode_base64, json_serialize, json_deserialize,
    sanitize_filename, calculate_time_diff, retry_on_exception
)

__all__ = [
    # 日志
    "app_logger",
    "api_logger",
    "db_logger", 
    "sync_logger",
    "get_logger",
    "HealthSyncLogger",
    "ApiLogger",
    "DatabaseLogger",
    "SyncLogger",
    "LogContext",
    "log_performance",
    "log_startup_info", 
    "log_shutdown_info",
    
    # 异常
    "HealthSyncError",
    "ConfigurationError",
    "DatabaseError",
    "DatabaseConnectionError", 
    "TianjianAPIError",
    "TianjianTimeoutError",
    "TianjianServerError",
    "TianjianAuthError",
    "DataValidationError",
    "DataTransformError",
    "SyncError",
    "BusinessLogicError",
    "FileOperationError",
    "SchedulerError",
    "ErrorCodes",
    "handle_exceptions",
    "format_error_for_logging",
    "format_error_for_user",
    
    # 辅助函数
    "safe_get",
    "safe_str",
    "safe_int",
    "safe_float", 
    "safe_decimal",
    "format_datetime",
    "parse_datetime",
    "get_age_from_birth",
    "validate_id_card",
    "extract_birth_from_id_card",
    "extract_gender_from_id_card",
    "generate_uuid",
    "generate_short_uuid",
    "calculate_md5",
    "mask_sensitive_data",
    "clean_phone_number",
    "validate_phone_number",
    "chunk_list",
    "flatten_dict",
    "deep_merge_dict",
    "encode_base64",
    "decode_base64",
    "json_serialize",
    "json_deserialize",
    "sanitize_filename",
    "calculate_time_diff",
    "retry_on_exception"
] 