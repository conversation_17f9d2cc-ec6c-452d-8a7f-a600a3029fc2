"""
自定义异常类
提供详细的错误信息和错误分类，便于错误处理和调试
"""


class HealthSyncError(Exception):
    """健康同步基础异常类"""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        """
        初始化异常
        
        Args:
            message: 错误消息
            error_code: 错误代码
            details: 详细信息字典
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "UNKNOWN_ERROR"
        self.details = details or {}
    
    def __str__(self) -> str:
        if self.details:
            return f"[{self.error_code}] {self.message} - 详情: {self.details}"
        return f"[{self.error_code}] {self.message}"


class ConfigurationError(HealthSyncError):
    """配置错误异常"""
    
    def __init__(self, message: str, config_key: str = None, details: dict = None):
        super().__init__(
            message=message,
            error_code="CONFIG_ERROR",
            details=details or {}
        )
        self.config_key = config_key
        if config_key:
            self.details["config_key"] = config_key


class DatabaseError(HealthSyncError):
    """数据库错误异常"""
    
    def __init__(self, message: str, sql_error: str = None, table_name: str = None, details: dict = None):
        error_details = details or {}
        if sql_error:
            error_details["sql_error"] = sql_error
        if table_name:
            error_details["table_name"] = table_name
            
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            details=error_details
        )
        self.sql_error = sql_error
        self.table_name = table_name


class DatabaseConnectionError(DatabaseError):
    """数据库连接错误"""
    
    def __init__(self, message: str, connection_string: str = None, details: dict = None):
        error_details = details or {}
        if connection_string:
            # 隐藏密码信息
            safe_conn_str = connection_string.split("@")[-1] if "@" in connection_string else connection_string
            error_details["connection"] = safe_conn_str
            
        super().__init__(
            message=message,
            sql_error=None,
            table_name=None,
            details=error_details
        )
        self.error_code = "DB_CONNECTION_ERROR"


class TianjianAPIError(HealthSyncError):
    """天健云API错误异常"""
    
    def __init__(self, message: str, status_code: int = None, response_data: str = None, 
                 interface_name: str = None, details: dict = None):
        error_details = details or {}
        if status_code:
            error_details["status_code"] = status_code
        if response_data:
            error_details["response_data"] = response_data[:500]  # 截取前500字符
        if interface_name:
            error_details["interface_name"] = interface_name
            
        super().__init__(
            message=message,
            error_code="API_ERROR",
            details=error_details
        )
        self.status_code = status_code
        self.response_data = response_data
        self.interface_name = interface_name


class TianjianTimeoutError(TianjianAPIError):
    """天健云API超时错误"""
    
    def __init__(self, message: str, timeout_seconds: int = None, details: dict = None):
        error_details = details or {}
        if timeout_seconds:
            error_details["timeout_seconds"] = timeout_seconds
            
        super().__init__(
            message=message,
            status_code=None,
            response_data=None,
            interface_name=None,
            details=error_details
        )
        self.error_code = "API_TIMEOUT_ERROR"
        self.timeout_seconds = timeout_seconds


class TianjianServerError(TianjianAPIError):
    """天健云服务器错误"""
    
    def __init__(self, message: str, status_code: int = None, details: dict = None):
        super().__init__(
            message=message,
            status_code=status_code,
            response_data=None,
            interface_name=None,
            details=details
        )
        self.error_code = "API_SERVER_ERROR"


class TianjianAuthError(TianjianAPIError):
    """天健云认证错误"""
    
    def __init__(self, message: str = "认证失败", signature_info: dict = None, details: dict = None):
        error_details = details or {}
        if signature_info:
            error_details.update(signature_info)
            
        super().__init__(
            message=message,
            status_code=401,
            response_data=None,
            interface_name=None,
            details=error_details
        )
        self.error_code = "API_AUTH_ERROR"


class DataValidationError(HealthSyncError):
    """数据验证错误"""
    
    def __init__(self, message: str, field_name: str = None, field_value: str = None, 
                 validation_rule: str = None, details: dict = None):
        error_details = details or {}
        if field_name:
            error_details["field_name"] = field_name
        if field_value:
            error_details["field_value"] = str(field_value)[:100]  # 截取前100字符
        if validation_rule:
            error_details["validation_rule"] = validation_rule
            
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            details=error_details
        )
        self.field_name = field_name
        self.field_value = field_value
        self.validation_rule = validation_rule


class DataTransformError(HealthSyncError):
    """数据转换错误"""
    
    def __init__(self, message: str, source_format: str = None, target_format: str = None, 
                 source_data: str = None, details: dict = None):
        error_details = details or {}
        if source_format:
            error_details["source_format"] = source_format
        if target_format:
            error_details["target_format"] = target_format
        if source_data:
            error_details["source_data"] = str(source_data)[:200]  # 截取前200字符
            
        super().__init__(
            message=message,
            error_code="TRANSFORM_ERROR",
            details=error_details
        )
        self.source_format = source_format
        self.target_format = target_format
        self.source_data = source_data


class SyncError(HealthSyncError):
    """同步错误异常"""
    
    def __init__(self, message: str, sync_type: str = None, business_key: str = None, 
                 retry_count: int = None, details: dict = None):
        error_details = details or {}
        if sync_type:
            error_details["sync_type"] = sync_type
        if business_key:
            error_details["business_key"] = business_key
        if retry_count is not None:
            error_details["retry_count"] = retry_count
            
        super().__init__(
            message=message,
            error_code="SYNC_ERROR",
            details=error_details
        )
        self.sync_type = sync_type
        self.business_key = business_key
        self.retry_count = retry_count


class BusinessLogicError(HealthSyncError):
    """业务逻辑错误"""
    
    def __init__(self, message: str, business_rule: str = None, entity_id: str = None, 
                 entity_type: str = None, details: dict = None):
        error_details = details or {}
        if business_rule:
            error_details["business_rule"] = business_rule
        if entity_id:
            error_details["entity_id"] = entity_id
        if entity_type:
            error_details["entity_type"] = entity_type
            
        super().__init__(
            message=message,
            error_code="BUSINESS_ERROR",
            details=error_details
        )
        self.business_rule = business_rule
        self.entity_id = entity_id
        self.entity_type = entity_type


class FileOperationError(HealthSyncError):
    """文件操作错误"""
    
    def __init__(self, message: str, file_path: str = None, operation: str = None, 
                 file_size: int = None, details: dict = None):
        error_details = details or {}
        if file_path:
            error_details["file_path"] = file_path
        if operation:
            error_details["operation"] = operation
        if file_size:
            error_details["file_size"] = file_size
            
        super().__init__(
            message=message,
            error_code="FILE_ERROR",
            details=error_details
        )
        self.file_path = file_path
        self.operation = operation
        self.file_size = file_size


class SchedulerError(HealthSyncError):
    """调度器错误"""
    
    def __init__(self, message: str, job_id: str = None, job_name: str = None, 
                 schedule_time: str = None, details: dict = None):
        error_details = details or {}
        if job_id:
            error_details["job_id"] = job_id
        if job_name:
            error_details["job_name"] = job_name
        if schedule_time:
            error_details["schedule_time"] = schedule_time
            
        super().__init__(
            message=message,
            error_code="SCHEDULER_ERROR",
            details=error_details
        )
        self.job_id = job_id
        self.job_name = job_name
        self.schedule_time = schedule_time


# 错误代码常量
class ErrorCodes:
    """错误代码常量"""
    
    # 配置相关
    CONFIG_MISSING = "CONFIG_001"
    CONFIG_INVALID = "CONFIG_002"
    CONFIG_LOAD_FAILED = "CONFIG_003"
    
    # 数据库相关
    DB_CONNECTION_FAILED = "DB_001"
    DB_QUERY_FAILED = "DB_002"
    DB_TRANSACTION_FAILED = "DB_003"
    DB_CONSTRAINT_VIOLATION = "DB_004"
    
    # API相关
    API_REQUEST_FAILED = "API_001"
    API_RESPONSE_INVALID = "API_002"
    API_AUTH_FAILED = "API_003"
    API_TIMEOUT = "API_004"
    API_SERVER_ERROR = "API_005"
    
    # 数据相关
    DATA_VALIDATION_FAILED = "DATA_001"
    DATA_TRANSFORM_FAILED = "DATA_002"
    DATA_NOT_FOUND = "DATA_003"
    DATA_DUPLICATE = "DATA_004"
    
    # 同步相关
    SYNC_FAILED = "SYNC_001"
    SYNC_TIMEOUT = "SYNC_002"
    SYNC_RETRY_EXHAUSTED = "SYNC_003"
    
    # 业务相关
    BUSINESS_RULE_VIOLATION = "BIZ_001"
    BUSINESS_STATE_INVALID = "BIZ_002"
    BUSINESS_PERMISSION_DENIED = "BIZ_003"


# 异常处理装饰器
def handle_exceptions(error_type: type = HealthSyncError, 
                     error_message: str = "操作失败", 
                     reraise: bool = True):
    """
    异常处理装饰器
    
    Args:
        error_type: 要捕获并转换的异常类型
        error_message: 默认错误消息
        reraise: 是否重新抛出异常
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except error_type as e:
                # 如果已经是我们的自定义异常，直接传递
                if isinstance(e, HealthSyncError):
                    if reraise:
                        raise
                    return None
                
                # 转换为自定义异常
                custom_error = HealthSyncError(
                    message=f"{error_message}: {str(e)}",
                    error_code="WRAPPED_ERROR",
                    details={"original_error": str(e), "error_type": type(e).__name__}
                )
                
                if reraise:
                    raise custom_error
                return None
            except Exception as e:
                # 处理未预期的异常
                custom_error = HealthSyncError(
                    message=f"未预期的错误: {str(e)}",
                    error_code="UNEXPECTED_ERROR",
                    details={"original_error": str(e), "error_type": type(e).__name__}
                )
                
                if reraise:
                    raise custom_error
                return None
        
        return wrapper
    return decorator


# 错误格式化函数
def format_error_for_logging(error: Exception) -> dict:
    """
    将异常格式化为适合日志记录的字典
    
    Args:
        error: 异常对象
        
    Returns:
        格式化后的错误信息字典
    """
    error_info = {
        "error_type": type(error).__name__,
        "error_message": str(error),
    }
    
    if isinstance(error, HealthSyncError):
        error_info.update({
            "error_code": error.error_code,
            "details": error.details
        })
        
        # 添加特定异常类型的额外信息
        if isinstance(error, TianjianAPIError):
            error_info.update({
                "status_code": error.status_code,
                "interface_name": error.interface_name
            })
        elif isinstance(error, DatabaseError):
            error_info.update({
                "table_name": error.table_name,
                "sql_error": error.sql_error
            })
        elif isinstance(error, DataValidationError):
            error_info.update({
                "field_name": error.field_name,
                "validation_rule": error.validation_rule
            })
    
    return error_info


def format_error_for_user(error: Exception) -> str:
    """
    将异常格式化为用户友好的错误消息
    
    Args:
        error: 异常对象
        
    Returns:
        用户友好的错误消息
    """
    if isinstance(error, HealthSyncError):
        return error.message
    
    # 对于系统异常，返回通用消息
    error_type = type(error).__name__
    if "Connection" in error_type or "Timeout" in error_type:
        return "网络连接异常，请检查网络连接后重试"
    elif "Permission" in error_type or "Access" in error_type:
        return "权限不足，请联系管理员"
    elif "File" in error_type:
        return "文件操作失败，请检查文件路径和权限"
    else:
        return "系统异常，请联系技术支持" 