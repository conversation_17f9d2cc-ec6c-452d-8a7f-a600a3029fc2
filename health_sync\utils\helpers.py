"""
辅助工具函数
提供常用的数据处理、时间处理、字符串处理等功能
"""
import re
import uuid
import hashlib
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta, date
from decimal import Decimal
import json
import base64


def safe_get(data: Dict[str, Any], key: str, default: Any = None) -> Any:
    """
    安全获取字典值
    
    Args:
        data: 数据字典
        key: 键名，支持嵌套键如 "user.profile.name"
        default: 默认值
        
    Returns:
        获取到的值或默认值
    """
    if not isinstance(data, dict):
        return default
    
    keys = key.split('.')
    current = data
    
    for k in keys:
        if isinstance(current, dict) and k in current:
            current = current[k]
        else:
            return default
    
    return current


def safe_str(value: Any, default: str = "") -> str:
    """
    安全转换为字符串
    
    Args:
        value: 要转换的值
        default: 默认值
        
    Returns:
        转换后的字符串
    """
    if value is None:
        return default
    
    if isinstance(value, str):
        return value
    
    try:
        return str(value)
    except Exception:
        return default


def safe_int(value: Any, default: int = 0) -> int:
    """
    安全转换为整数
    
    Args:
        value: 要转换的值
        default: 默认值
        
    Returns:
        转换后的整数
    """
    if value is None or value == "":
        return default
    
    if isinstance(value, int):
        return value
    
    if isinstance(value, float):
        return int(value)
    
    if isinstance(value, str):
        # 移除非数字字符（保留负号）
        cleaned = re.sub(r'[^\d\-]', '', value)
        if cleaned and cleaned != '-':
            try:
                return int(cleaned)
            except ValueError:
                pass
    
    return default


def safe_float(value: Any, default: float = 0.0) -> float:
    """
    安全转换为浮点数
    
    Args:
        value: 要转换的值
        default: 默认值
        
    Returns:
        转换后的浮点数
    """
    if value is None or value == "":
        return default
    
    if isinstance(value, (int, float)):
        return float(value)
    
    if isinstance(value, str):
        # 移除非数字字符（保留小数点和负号）
        cleaned = re.sub(r'[^\d\.\-]', '', value)
        if cleaned and cleaned not in ['-', '.', '-.']:
            try:
                return float(cleaned)
            except ValueError:
                pass
    
    return default


def safe_decimal(value: Any, default: Decimal = None) -> Decimal:
    """
    安全转换为Decimal
    
    Args:
        value: 要转换的值
        default: 默认值
        
    Returns:
        转换后的Decimal
    """
    if default is None:
        default = Decimal('0')
    
    if value is None or value == "":
        return default
    
    if isinstance(value, Decimal):
        return value
    
    try:
        return Decimal(str(value))
    except Exception:
        return default


def format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    格式化日期时间
    
    Args:
        dt: 日期时间对象
        format_str: 格式字符串
        
    Returns:
        格式化后的日期时间字符串
    """
    if dt is None:
        return ""
    
    if not isinstance(dt, datetime):
        return str(dt)
    
    try:
        return dt.strftime(format_str)
    except Exception:
        return str(dt)


def parse_datetime(date_str: str, format_str: str = None) -> Optional[datetime]:
    """
    解析日期时间字符串
    
    Args:
        date_str: 日期时间字符串
        format_str: 格式字符串，如果为None则尝试多种常见格式
        
    Returns:
        解析后的datetime对象
    """
    if not date_str or not isinstance(date_str, str):
        return None
    
    date_str = date_str.strip()
    
    if format_str:
        try:
            return datetime.strptime(date_str, format_str)
        except ValueError:
            return None
    
    # 尝试多种常见格式
    formats = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M:%S.%f",
        "%Y-%m-%d",
        "%Y/%m/%d %H:%M:%S",
        "%Y/%m/%d",
        "%m/%d/%Y %H:%M:%S",
        "%m/%d/%Y",
        "%d/%m/%Y %H:%M:%S",
        "%d/%m/%Y",
        "%Y%m%d%H%M%S",
        "%Y%m%d"
    ]
    
    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt)
        except ValueError:
            continue
    
    return None


def get_age_from_birth(birth_date: Union[datetime, date, str]) -> int:
    """
    根据出生日期计算年龄
    
    Args:
        birth_date: 出生日期
        
    Returns:
        年龄
    """
    if isinstance(birth_date, str):
        birth_dt = parse_datetime(birth_date)
        if not birth_dt:
            return 0
        birth_date = birth_dt.date()
    elif isinstance(birth_date, datetime):
        birth_date = birth_date.date()
    elif not isinstance(birth_date, date):
        return 0
    
    today = date.today()
    age = today.year - birth_date.year
    
    # 如果还没过生日，年龄减1
    if today < birth_date.replace(year=today.year):
        age -= 1
    
    return max(0, age)


def validate_id_card(id_card: str) -> bool:
    """
    验证身份证号码
    
    Args:
        id_card: 身份证号码
        
    Returns:
        是否有效
    """
    if not id_card or not isinstance(id_card, str):
        return False
    
    id_card = id_card.strip().upper()
    
    # 15位身份证
    if len(id_card) == 15:
        return re.match(r'^\d{15}$', id_card) is not None
    
    # 18位身份证
    if len(id_card) == 18:
        if not re.match(r'^\d{17}[\dX]$', id_card):
            return False
        
        # 验证校验码
        weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
        check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
        
        sum_value = sum(int(id_card[i]) * weights[i] for i in range(17))
        check_index = sum_value % 11
        
        return id_card[17] == check_codes[check_index]
    
    return False


def extract_birth_from_id_card(id_card: str) -> Optional[date]:
    """
    从身份证号码提取出生日期
    
    Args:
        id_card: 身份证号码
        
    Returns:
        出生日期
    """
    if not validate_id_card(id_card):
        return None
    
    id_card = id_card.strip().upper()
    
    try:
        if len(id_card) == 15:
            # 15位身份证，年份补19
            year = 1900 + int(id_card[6:8])
            month = int(id_card[8:10])
            day = int(id_card[10:12])
        elif len(id_card) == 18:
            # 18位身份证
            year = int(id_card[6:10])
            month = int(id_card[10:12])
            day = int(id_card[12:14])
        else:
            return None
        
        return date(year, month, day)
    except (ValueError, IndexError):
        return None


def extract_gender_from_id_card(id_card: str) -> Optional[str]:
    """
    从身份证号码提取性别
    
    Args:
        id_card: 身份证号码
        
    Returns:
        性别代码：'1'-男，'2'-女
    """
    if not validate_id_card(id_card):
        return None
    
    id_card = id_card.strip().upper()
    
    try:
        if len(id_card) == 15:
            gender_digit = int(id_card[14])
        elif len(id_card) == 18:
            gender_digit = int(id_card[16])
        else:
            return None
        
        return '1' if gender_digit % 2 == 1 else '2'
    except (ValueError, IndexError):
        return None


def generate_uuid() -> str:
    """生成UUID字符串"""
    return str(uuid.uuid4())


def generate_short_uuid() -> str:
    """生成短UUID字符串（去除连字符）"""
    return str(uuid.uuid4()).replace('-', '')


def calculate_md5(text: str) -> str:
    """
    计算MD5哈希值
    
    Args:
        text: 要计算哈希的字符串
        
    Returns:
        MD5哈希值（小写）
    """
    if not text:
        return ""
    
    return hashlib.md5(text.encode('utf-8')).hexdigest().lower()


def mask_sensitive_data(text: str, mask_char: str = "*", keep_start: int = 3, keep_end: int = 3) -> str:
    """
    掩码敏感数据
    
    Args:
        text: 原始文本
        mask_char: 掩码字符
        keep_start: 保留开头字符数
        keep_end: 保留结尾字符数
        
    Returns:
        掩码后的文本
    """
    if not text or len(text) <= keep_start + keep_end:
        return mask_char * len(text) if text else ""
    
    start = text[:keep_start]
    end = text[-keep_end:] if keep_end > 0 else ""
    middle = mask_char * (len(text) - keep_start - keep_end)
    
    return start + middle + end


def clean_phone_number(phone: str) -> str:
    """
    清理电话号码（移除非数字字符）
    
    Args:
        phone: 原始电话号码
        
    Returns:
        清理后的电话号码
    """
    if not phone:
        return ""
    
    return re.sub(r'[^\d]', '', phone)


def validate_phone_number(phone: str) -> bool:
    """
    验证电话号码格式
    
    Args:
        phone: 电话号码
        
    Returns:
        是否有效
    """
    if not phone:
        return False
    
    clean_phone = clean_phone_number(phone)
    
    # 手机号（11位）
    if len(clean_phone) == 11 and clean_phone.startswith(('1')):
        return True
    
    # 固定电话（7-8位区号****位号码）
    if 10 <= len(clean_phone) <= 12:
        return True
    
    return False


def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """
    将列表分块
    
    Args:
        lst: 原始列表
        chunk_size: 块大小
        
    Returns:
        分块后的列表
    """
    if chunk_size <= 0:
        return [lst] if lst else []
    
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]


def flatten_dict(d: Dict[str, Any], parent_key: str = '', sep: str = '.') -> Dict[str, Any]:
    """
    扁平化字典
    
    Args:
        d: 原始字典
        parent_key: 父键名
        sep: 分隔符
        
    Returns:
        扁平化后的字典
    """
    items = []
    
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    
    return dict(items)


def deep_merge_dict(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """
    深度合并字典
    
    Args:
        dict1: 字典1
        dict2: 字典2
        
    Returns:
        合并后的字典
    """
    result = dict1.copy()
    
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = deep_merge_dict(result[key], value)
        else:
            result[key] = value
    
    return result


def encode_base64(text: str) -> str:
    """Base64编码"""
    if not text:
        return ""
    
    return base64.b64encode(text.encode('utf-8')).decode('utf-8')


def decode_base64(encoded_text: str) -> str:
    """Base64解码"""
    if not encoded_text:
        return ""
    
    try:
        return base64.b64decode(encoded_text).decode('utf-8')
    except Exception:
        return ""


def json_serialize(obj: Any) -> str:
    """
    JSON序列化（支持datetime等特殊类型）
    
    Args:
        obj: 要序列化的对象
        
    Returns:
        JSON字符串
    """
    def json_serial(obj):
        if isinstance(obj, (datetime, date)):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return float(obj)
        raise TypeError(f"Type {type(obj)} not serializable")
    
    try:
        return json.dumps(obj, default=json_serial, ensure_ascii=False)
    except Exception:
        return str(obj)


def json_deserialize(json_str: str) -> Any:
    """
    JSON反序列化
    
    Args:
        json_str: JSON字符串
        
    Returns:
        反序列化后的对象
    """
    if not json_str:
        return None
    
    try:
        return json.loads(json_str)
    except Exception:
        return None


def sanitize_filename(filename: str) -> str:
    """
    清理文件名（移除非法字符）
    
    Args:
        filename: 原始文件名
        
    Returns:
        清理后的文件名
    """
    if not filename:
        return "unnamed"
    
    # 移除非法字符
    sanitized = re.sub(r'[<>:"/\\|?*]', '', filename)
    
    # 移除控制字符
    sanitized = re.sub(r'[\x00-\x1f\x7f]', '', sanitized)
    
    # 限制长度
    if len(sanitized) > 200:
        sanitized = sanitized[:200]
    
    return sanitized or "unnamed"


def calculate_time_diff(start_time: datetime, end_time: datetime = None) -> Dict[str, Any]:
    """
    计算时间差
    
    Args:
        start_time: 开始时间
        end_time: 结束时间，默认为当前时间
        
    Returns:
        时间差信息字典
    """
    if end_time is None:
        end_time = datetime.now()
    
    if start_time > end_time:
        start_time, end_time = end_time, start_time
    
    diff = end_time - start_time
    
    total_seconds = int(diff.total_seconds())
    days = diff.days
    hours = total_seconds // 3600 % 24
    minutes = total_seconds // 60 % 60
    seconds = total_seconds % 60
    
    return {
        "total_seconds": total_seconds,
        "days": days,
        "hours": hours,
        "minutes": minutes,
        "seconds": seconds,
        "formatted": f"{days}天{hours:02d}时{minutes:02d}分{seconds:02d}秒"
    }


def retry_on_exception(max_retries: int = 3, delay: float = 1.0, 
                      exceptions: tuple = (Exception,)):
    """
    异常重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 重试延迟（秒）
        exceptions: 要捕获的异常类型
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        import time
                        time.sleep(delay * (attempt + 1))  # 递增延迟
                    else:
                        raise last_exception
            
        return wrapper
    return decorator 