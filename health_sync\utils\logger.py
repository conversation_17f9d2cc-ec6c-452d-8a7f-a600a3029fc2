"""
日志管理模块
提供结构化日志记录、日志配置管理、日志轮转等功能
"""
import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional
from loguru import logger
from datetime import datetime
import json

from ..config.settings import settings


class LoggerConfig:
    """日志配置管理器"""
    
    def __init__(self):
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        # 移除默认的 loguru 处理器
        logger.remove()
        
        # 配置日志
        self._setup_console_logging()
        self._setup_file_logging()
        self._setup_api_logging()
        self._setup_error_logging()
    
    def _setup_console_logging(self):
        """配置控制台日志"""
        console_format = (
            "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
        
        logger.add(
            sys.stderr,
            format=console_format,
            level="INFO",
            colorize=True,
            backtrace=True,
            diagnose=True
        )
    
    def _setup_file_logging(self):
        """配置文件日志"""
        file_format = (
            "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | "
            "{name}:{function}:{line} | {message}"
        )
        
        # 主日志文件
        logger.add(
            self.log_dir / "health_sync.log",
            format=file_format,
            level="DEBUG",
            rotation="10 MB",
            retention="30 days",
            compression="zip",
            encoding="utf-8"
        )
        
        # 每日日志文件
        logger.add(
            self.log_dir / "health_sync_{time:YYYY-MM-DD}.log",
            format=file_format,
            level="INFO",
            rotation="00:00",
            retention="7 days",
            encoding="utf-8"
        )
    
    def _setup_api_logging(self):
        """配置API专用日志"""
        api_format = (
            "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | "
            "API | {extra[interface_name]:-} | {extra[business_key]:-} | {message}"
        )
        
        logger.add(
            self.log_dir / "api_{time:YYYY-MM-DD}.log",
            format=api_format,
            level="INFO",
            filter=lambda record: "api" in record["extra"],
            rotation="00:00",
            retention="30 days",
            encoding="utf-8"
        )
    
    def _setup_error_logging(self):
        """配置错误专用日志"""
        error_format = (
            "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | "
            "{name}:{function}:{line} | {message} | {extra}"
        )
        
        logger.add(
            self.log_dir / "errors_{time:YYYY-MM-DD}.log",
            format=error_format,
            level="ERROR",
            rotation="00:00",
            retention="60 days",
            backtrace=True,
            diagnose=True,
            encoding="utf-8"
        )


class HealthSyncLogger:
    """健康同步专用日志器"""
    
    def __init__(self, name: str = "health_sync"):
        self.name = name
        self.logger = logger.bind(logger_name=name)
    
    def info(self, message: str, **kwargs):
        """记录信息日志"""
        self.logger.info(message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        """记录调试日志"""
        self.logger.debug(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """记录警告日志"""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, error: Exception = None, **kwargs):
        """记录错误日志"""
        if error:
            kwargs.update({
                "error_type": type(error).__name__,
                "error_message": str(error)
            })
        self.logger.error(message, **kwargs)
    
    def critical(self, message: str, error: Exception = None, **kwargs):
        """记录严重错误日志"""
        if error:
            kwargs.update({
                "error_type": type(error).__name__,
                "error_message": str(error)
            })
        self.logger.critical(message, **kwargs)
    
    def api_request(self, interface_name: str, business_key: str = None, 
                   request_data: Dict[str, Any] = None, **kwargs):
        """记录API请求日志"""
        extra = {
            "api": True,
            "interface_name": interface_name,
            "business_key": business_key or "",
            "request_size": len(str(request_data)) if request_data else 0
        }
        extra.update(kwargs)
        
        message = f"API请求: {interface_name}"
        if business_key:
            message += f" | 业务键: {business_key}"
        
        self.logger.bind(**extra).info(message)
    
    def api_response(self, interface_name: str, business_key: str = None,
                    status_code: int = None, success: bool = None, 
                    response_time_ms: float = None, **kwargs):
        """记录API响应日志"""
        extra = {
            "api": True,
            "interface_name": interface_name,
            "business_key": business_key or "",
            "status_code": status_code,
            "success": success,
            "response_time_ms": response_time_ms
        }
        extra.update(kwargs)
        
        level = "info" if success else "error"
        message = f"API响应: {interface_name} | 状态: {'成功' if success else '失败'}"
        if status_code:
            message += f" | 状态码: {status_code}"
        if response_time_ms:
            message += f" | 耗时: {response_time_ms:.2f}ms"
        
        getattr(self.logger.bind(**extra), level)(message)
    
    def database_operation(self, operation: str, table_name: str = None,
                          record_count: int = None, execution_time_ms: float = None,
                          **kwargs):
        """记录数据库操作日志"""
        extra = {
            "database": True,
            "operation": operation,
            "table_name": table_name or "",
            "record_count": record_count,
            "execution_time_ms": execution_time_ms
        }
        extra.update(kwargs)
        
        message = f"数据库操作: {operation}"
        if table_name:
            message += f" | 表: {table_name}"
        if record_count is not None:
            message += f" | 记录数: {record_count}"
        if execution_time_ms:
            message += f" | 耗时: {execution_time_ms:.2f}ms"
        
        self.logger.bind(**extra).info(message)
    
    def sync_operation(self, sync_type: str, business_key: str = None,
                      status: str = None, record_count: int = None,
                      **kwargs):
        """记录同步操作日志"""
        extra = {
            "sync": True,
            "sync_type": sync_type,
            "business_key": business_key or "",
            "status": status,
            "record_count": record_count
        }
        extra.update(kwargs)
        
        message = f"同步操作: {sync_type}"
        if business_key:
            message += f" | 业务键: {business_key}"
        if status:
            message += f" | 状态: {status}"
        if record_count is not None:
            message += f" | 记录数: {record_count}"
        
        level = "info" if status == "success" else "error" if status == "failed" else "info"
        getattr(self.logger.bind(**extra), level)(message)


class ApiLogger:
    """API专用日志器"""
    
    def __init__(self):
        self.logger = HealthSyncLogger("api")
    
    def log_request_start(self, interface_name: str, business_key: str = None,
                         request_data: Dict[str, Any] = None):
        """记录请求开始"""
        self.logger.api_request(
            interface_name=interface_name,
            business_key=business_key,
            request_data=request_data,
            phase="request_start"
        )
    
    def log_request_success(self, interface_name: str, business_key: str = None,
                           status_code: int = 200, response_time_ms: float = None):
        """记录请求成功"""
        self.logger.api_response(
            interface_name=interface_name,
            business_key=business_key,
            status_code=status_code,
            success=True,
            response_time_ms=response_time_ms
        )
    
    def log_request_failure(self, interface_name: str, business_key: str = None,
                           status_code: int = None, error_message: str = None,
                           response_time_ms: float = None):
        """记录请求失败"""
        self.logger.api_response(
            interface_name=interface_name,
            business_key=business_key,
            status_code=status_code,
            success=False,
            response_time_ms=response_time_ms,
            error_message=error_message
        )


class DatabaseLogger:
    """数据库专用日志器"""
    
    def __init__(self):
        self.logger = HealthSyncLogger("database")
    
    def log_query(self, operation: str, table_name: str = None,
                 record_count: int = None, execution_time_ms: float = None):
        """记录数据库查询"""
        self.logger.database_operation(
            operation=operation,
            table_name=table_name,
            record_count=record_count,
            execution_time_ms=execution_time_ms
        )
    
    def log_connection_error(self, database_name: str, error: Exception):
        """记录数据库连接错误"""
        self.logger.error(
            f"数据库连接失败: {database_name}",
            error=error,
            database=database_name
        )


class SyncLogger:
    """同步专用日志器"""
    
    def __init__(self):
        self.logger = HealthSyncLogger("sync")
    
    def log_sync_start(self, sync_type: str, business_key: str = None):
        """记录同步开始"""
        self.logger.sync_operation(
            sync_type=sync_type,
            business_key=business_key,
            status="started"
        )
    
    def log_sync_success(self, sync_type: str, business_key: str = None,
                        record_count: int = None):
        """记录同步成功"""
        self.logger.sync_operation(
            sync_type=sync_type,
            business_key=business_key,
            status="success",
            record_count=record_count
        )
    
    def log_sync_failure(self, sync_type: str, business_key: str = None,
                        error: Exception = None):
        """记录同步失败"""
        self.logger.sync_operation(
            sync_type=sync_type,
            business_key=business_key,
            status="failed",
            error_message=str(error) if error else None
        )


# 性能监控装饰器
def log_performance(operation_name: str = None, log_args: bool = False):
    """
    性能监控装饰器
    
    Args:
        operation_name: 操作名称
        log_args: 是否记录参数
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            import time
            from config import Config
            
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            start_time = time.time()
            
            logger_instance = HealthSyncLogger("performance")
            
            if log_args:
                logger_instance.debug(f"开始执行: {op_name}", args=str(args)[:200], kwargs=str(kwargs)[:200])
            else:
                logger_instance.debug(f"开始执行: {op_name}")
            
            try:
                result = func(*args, **kwargs)
                execution_time = (time.time() - start_time) * 1000
                
                logger_instance.info(
                    f"执行完成: {op_name}",
                    execution_time_ms=execution_time,
                    success=True
                )
                
                return result
                
            except Exception as e:
                execution_time = (time.time() - start_time) * 1000
                
                logger_instance.error(
                    f"执行失败: {op_name}",
                    error=e,
                    execution_time_ms=execution_time,
                    success=False
                )
                
                raise
        
        return wrapper
    return decorator


# 日志上下文管理器
class LogContext:
    """日志上下文管理器，用于添加统一的上下文信息"""
    
    def __init__(self, **context):
        self.context = context
        self.logger = logger
    
    def __enter__(self):
        self.logger = logger.bind(**self.context)
        return self.logger
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        pass


# 初始化日志配置
_logger_config = LoggerConfig()

# 导出常用的日志器实例
app_logger = HealthSyncLogger("app")
api_logger = ApiLogger()
db_logger = DatabaseLogger()
sync_logger = SyncLogger()


def get_logger(name: str = "health_sync") -> HealthSyncLogger:
    """获取指定名称的日志器"""
    return HealthSyncLogger(name)


def log_startup_info():
    """记录启动信息"""
    app_logger.info("=" * 50)
    app_logger.info("健康同步系统启动")
    app_logger.info(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    app_logger.info(f"Python版本: {sys.version}")
    app_logger.info(f"工作目录: {os.getcwd()}")
    app_logger.info("=" * 50)


def log_shutdown_info():
    """记录关闭信息"""
    app_logger.info("健康同步系统正在关闭...")
    app_logger.info(f"关闭时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    app_logger.info("系统已关闭")


# 导出主要接口
__all__ = [
    'HealthSyncLogger',
    'ApiLogger', 
    'DatabaseLogger',
    'SyncLogger',
    'LogContext',
    'log_performance',
    'app_logger',
    'api_logger',
    'db_logger',
    'sync_logger',
    'get_logger',
    'log_startup_info',
    'log_shutdown_info'
] 