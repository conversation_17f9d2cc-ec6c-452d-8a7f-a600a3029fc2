#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTTP报文日志管理器
为天健云接口提供专门的HTTP请求和响应报文日志记录功能
每个接口一个独立的日志文件
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional


class HTTPMessageLogger:
    """HTTP报文日志管理器"""
    
    def __init__(self, interface_name: str, logs_dir: str = "logs"):
        """
        初始化HTTP报文日志管理器
        
        Args:
            interface_name: 接口名称，如 "02", "03", "04" 等
            logs_dir: 日志目录路径
        """
        self.interface_name = interface_name
        self.logs_dir = logs_dir
        self.logger = None
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志记录器"""
        # 确保日志目录存在
        if not os.path.exists(self.logs_dir):
            os.makedirs(self.logs_dir)
        
        # 生成日志文件名：interface_XX_http_messages_YYYY-MM-DD.log
        today = datetime.now().strftime('%Y-%m-%d')
        log_filename = f"interface_{self.interface_name}_http_messages_{today}.log"
        log_filepath = os.path.join(self.logs_dir, log_filename)
        
        # 创建专门的logger
        logger_name = f"http_messages_{self.interface_name}"
        self.logger = logging.getLogger(logger_name)
        
        # 避免重复添加handler
        if not self.logger.handlers:
            self.logger.setLevel(logging.INFO)
            
            # 创建文件handler
            file_handler = logging.FileHandler(log_filepath, encoding='utf-8')
            file_handler.setLevel(logging.INFO)
            
            # 创建格式器
            formatter = logging.Formatter(
                '%(asctime)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(formatter)
            
            # 添加handler到logger
            self.logger.addHandler(file_handler)
            
            # 防止日志传播到根logger
            self.logger.propagate = False
    
    def log_request(self, url: str, method: str, headers: Dict[str, str], 
                   request_data: Any, request_id: Optional[str] = None):
        """
        记录HTTP请求报文
        
        Args:
            url: 请求URL
            method: 请求方法
            headers: 请求头
            request_data: 请求体数据
            request_id: 请求ID（可选）
        """
        separator = "=" * 80
        request_id_str = f" [{request_id}]" if request_id else ""
        
        self.logger.info(separator)
        self.logger.info(f"【{self.interface_name}号接口】HTTP请求报文{request_id_str}")
        self.logger.info(separator)
        self.logger.info(f"请求URL: {url}")
        self.logger.info(f"请求方法: {method}")
        self.logger.info("请求头:")
        for key, value in headers.items():
            self.logger.info(f"  {key}: {value}")
        self.logger.info("请求体:")
        
        # 格式化请求体
        if isinstance(request_data, (dict, list)):
            formatted_data = json.dumps(request_data, ensure_ascii=False, indent=2)
            self.logger.info(formatted_data)
        else:
            self.logger.info(str(request_data))
        
        self.logger.info(separator)
    
    def log_response(self, status_code: int, headers: Dict[str, str], 
                    response_data: Any, request_id: Optional[str] = None):
        """
        记录HTTP响应报文
        
        Args:
            status_code: 响应状态码
            headers: 响应头
            response_data: 响应体数据
            request_id: 请求ID（可选）
        """
        separator = "=" * 80
        request_id_str = f" [{request_id}]" if request_id else ""
        
        self.logger.info(f"【{self.interface_name}号接口】HTTP响应报文{request_id_str}")
        self.logger.info(separator)
        self.logger.info(f"响应状态: HTTP {status_code}")
        self.logger.info("响应头:")
        for key, value in headers.items():
            self.logger.info(f"  {key}: {value}")
        self.logger.info("响应体:")
        
        # 格式化响应体
        if isinstance(response_data, (dict, list)):
            formatted_data = json.dumps(response_data, ensure_ascii=False, indent=2)
            self.logger.info(formatted_data)
        else:
            self.logger.info(str(response_data))
        
        self.logger.info(separator)
        self.logger.info("")  # 添加空行分隔不同的请求
    
    def log_error(self, error_type: str, error_message: str, 
                 request_id: Optional[str] = None):
        """
        记录HTTP错误信息
        
        Args:
            error_type: 错误类型
            error_message: 错误消息
            request_id: 请求ID（可选）
        """
        request_id_str = f" [{request_id}]" if request_id else ""
        separator = "=" * 80
        
        self.logger.error(f"【{self.interface_name}号接口】HTTP错误{request_id_str}")
        self.logger.error(separator)
        self.logger.error(f"错误类型: {error_type}")
        self.logger.error(f"错误消息: {error_message}")
        self.logger.error(separator)
        self.logger.error("")  # 添加空行分隔


def create_http_logger(interface_name: str) -> HTTPMessageLogger:
    """
    创建HTTP报文日志记录器的便捷函数
    
    Args:
        interface_name: 接口名称，如 "02", "03", "04" 等
        
    Returns:
        HTTPMessageLogger实例
    """
    return HTTPMessageLogger(interface_name)


# 全局日志记录器缓存
_loggers_cache = {}


def get_http_logger(interface_name: str) -> HTTPMessageLogger:
    """
    获取HTTP报文日志记录器（单例模式）
    
    Args:
        interface_name: 接口名称
        
    Returns:
        HTTPMessageLogger实例
    """
    if interface_name not in _loggers_cache:
        _loggers_cache[interface_name] = HTTPMessageLogger(interface_name)
    return _loggers_cache[interface_name]


if __name__ == '__main__':
    # 测试HTTP报文日志记录器
    print("测试HTTP报文日志记录器...")
    
    # 创建02号接口的日志记录器
    logger = create_http_logger("02")
    
    # 测试请求日志
    test_headers = {
        "Content-Type": "application/json",
        "mic-code": "MIC1.001E",
        "misc-id": "MISC1.00001A"
    }
    
    test_data = [
        {
            "applyItemId": "JB0002",
            "applyItemName": "内科",
            "displaySequence": "1"
        }
    ]
    
    logger.log_request(
        url="http://203.83.237.114:9300/dx/inter/syncApplyItem",
        method="POST",
        headers=test_headers,
        request_data=test_data,
        request_id="TEST001"
    )
    
    # 测试响应日志
    test_response = {
        "code": 0,
        "msg": "成功",
        "data": None
    }
    
    response_headers = {
        "Content-Type": "application/json",
        "Server": "nginx/1.28.0"
    }
    
    logger.log_response(
        status_code=200,
        headers=response_headers,
        response_data=test_response,
        request_id="TEST001"
    )
    
    print("✅ HTTP报文日志记录器测试完成")
    print(f"日志文件已保存到: logs/interface_02_http_messages_{datetime.now().strftime('%Y-%m-%d')}.log")
