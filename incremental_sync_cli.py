#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增量同步命令行工具
管理和执行增量数据同步操作
"""

import argparse
import sys
from datetime import datetime
from incremental_sync_service import create_incremental_sync_service


def show_sync_status(sync_service):
    """显示同步状态"""
    print("=" * 60)
    print("增量同步状态概览")
    print("=" * 60)
    
    status = sync_service.get_sync_status()
    
    if not status['sync_records']:
        print("[INFO] 尚未进行任何同步操作")
        return
    
    print(f"同步类型总数: {status['total_sync_types']}")
    print(f"状态更新时间: {status['last_update']}")
    
    print("\n[同步记录状态]")
    for record in status['sync_records']:
        sync_type = record['sync_type']
        last_sync = datetime.fromisoformat(record['last_sync_time']).strftime('%Y-%m-%d %H:%M:%S')
        total = record['total_synced']
        success_rate = record['success_rate']
        
        print(f"  {sync_type:15} | 最后同步: {last_sync} | 总计: {total:4}条 | 成功率: {success_rate:5.1f}%")
    
    if status['recent_history']:
        print("\n[最近同步历史]")
        print("  类型           | 同步时间         | 记录数 | 成功数 | 失败数 | 耗时(秒)")
        print("  " + "-" * 70)
        for history in status['recent_history'][:5]:  # 显示最近5条
            sync_type = history['sync_type']
            sync_time = datetime.fromisoformat(history['sync_time']).strftime('%m-%d %H:%M')
            records = history['records_count']
            success = history['success_count']
            error = history['error_count']
            duration = history['duration']
            
            print(f"  {sync_type:15} | {sync_time:15} | {records:4} | {success:4} | {error:4} | {duration:8.2f}")


def run_incremental_sync(sync_service, sync_types=None, dry_run=False):
    """运行增量同步"""
    print("=" * 60)
    if dry_run:
        print("增量同步 - 演练模式（不实际发送数据）")
    else:
        print("增量同步 - 正式执行")
    print("=" * 60)
    
    if not sync_types:
        sync_types = ['exam_data', 'apply_items', 'departments', 'operators']
    
    print(f"同步类型: {', '.join(sync_types)}")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if dry_run:
        print("\n[DRY RUN MODE] 检查增量数据，但不实际同步")
        
        for sync_type in sync_types:
            try:
                if sync_type == "exam_data":
                    data, record = sync_service.get_incremental_exam_data()
                elif sync_type == "apply_items":
                    data, record = sync_service.get_incremental_apply_items()
                elif sync_type in ["departments", "operators", "items"]:
                    data, record = sync_service.get_incremental_dict_data(sync_type)
                else:
                    print(f"[WARN] 未知的同步类型: {sync_type}")
                    continue
                
                print(f"  {sync_type:15} | 增量数据: {len(data):4}条")
                
            except Exception as e:
                print(f"  {sync_type:15} | 错误: {e}")
    else:
        # 实际执行同步
        results = sync_service.sync_incremental_data(sync_types)
        
        print(f"\n[同步完成]")
        print(f"总记录数: {results['total_records']}")
        print(f"成功数量: {results['total_success']}")
        print(f"失败数量: {results['total_errors']}")
        print(f"总耗时: {results['total_duration']:.2f}秒")
        
        if results['total_errors'] > 0:
            print(f"[WARN] 发现 {results['total_errors']} 个错误，请检查日志")


def reset_sync_state(sync_service, sync_type=None):
    """重置同步状态"""
    if sync_type:
        confirm = input(f"确认要重置 {sync_type} 的同步状态吗？(y/N): ")
    else:
        confirm = input("确认要重置所有同步状态吗？这将清除所有历史记录！(y/N): ")
    
    if confirm.lower() == 'y':
        sync_service.reset_sync_state(sync_type)
        if sync_type:
            print(f"[OK] 已重置 {sync_type} 的同步状态")
        else:
            print("[OK] 已重置所有同步状态")
    else:
        print("[CANCEL] 操作已取消")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="增量同步命令行工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python incremental_sync_cli.py status                    # 查看同步状态
  python incremental_sync_cli.py sync                      # 执行所有类型的增量同步
  python incremental_sync_cli.py sync --types exam_data    # 只同步体检数据
  python incremental_sync_cli.py sync --dry-run            # 演练模式，不实际同步
  python incremental_sync_cli.py reset                     # 重置所有同步状态
  python incremental_sync_cli.py reset --type apply_items  # 重置指定类型的同步状态
        """
    )

    subparsers = parser.add_subparsers(dest='command', help='可用命令')

    # status 命令
    status_parser = subparsers.add_parser('status', help='查看同步状态')

    # sync 命令
    sync_parser = subparsers.add_parser('sync', help='执行增量同步')
    sync_parser.add_argument('--types', nargs='+',
                           choices=['exam_data', 'apply_items', 'departments', 'operators', 'items'],
                           help='指定要同步的数据类型')
    sync_parser.add_argument('--dry-run', action='store_true', help='演练模式，不实际同步')

    # reset 命令
    reset_parser = subparsers.add_parser('reset', help='重置同步状态')
    reset_parser.add_argument('--type',
                            choices=['exam_data', 'apply_items', 'departments', 'operators', 'items'],
                            help='指定要重置的数据类型')

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    # 使用统一配置
    from config import Config
    connection_string = Config.get_interface_db_connection_string()

    try:
        sync_service = create_incremental_sync_service(connection_string)

        if args.command == 'status':
            show_sync_status(sync_service)
        elif args.command == 'sync':
            run_incremental_sync(sync_service, args.types, args.dry_run)
        elif args.command == 'reset':
            reset_sync_state(sync_service, args.type)

    except Exception as e:
        print(f"[ERROR] 执行失败: {e}")
        sys.exit(1)
    finally:
        if 'sync_service' in locals():
            sync_service.close()


if __name__ == '__main__':
    main()