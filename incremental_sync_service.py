#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增量同步服务
基于时间戳的增量数据同步机制，提升大数据量同步效率
"""

import json
import os
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from optimized_database_service import create_optimized_db_service
from dataclasses import dataclass
from config import Config


@dataclass
class SyncRecord:
    """同步记录"""
    sync_type: str          # 同步类型：exam_data, apply_items, departments, operators
    last_sync_time: datetime # 最后同步时间
    last_sync_id: str       # 最后同步的记录ID
    total_synced: int       # 总同步数量
    success_count: int      # 成功数量
    error_count: int        # 错误数量
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'sync_type': self.sync_type,
            'last_sync_time': self.last_sync_time.isoformat(),
            'last_sync_id': self.last_sync_id,
            'total_synced': self.total_synced,
            'success_count': self.success_count,
            'error_count': self.error_count
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SyncRecord':
        """从字典创建"""
        return cls(
            sync_type=data['sync_type'],
            last_sync_time=datetime.fromisoformat(data['last_sync_time']),
            last_sync_id=data['last_sync_id'],
            total_synced=data['total_synced'],
            success_count=data['success_count'],
            error_count=data['error_count']
        )


class IncrementalSyncService:
    """增量同步服务"""
    
    def __init__(self, connection_string: str, sync_state_file: str = "sync_state.db"):
        """
        初始化增量同步服务
        
        Args:
            connection_string: 数据库连接字符串
            sync_state_file: 同步状态存储文件
        """
        self.connection_string = connection_string
        self.sync_state_file = sync_state_file
        self.db_service = create_optimized_db_service(connection_string)
        
        # 初始化同步状态数据库
        self._init_sync_state_db()
    
    def _init_sync_state_db(self):
        """初始化同步状态数据库"""
        conn = sqlite3.connect(self.sync_state_file)
        try:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sync_records (
                    sync_type TEXT PRIMARY KEY,
                    last_sync_time TEXT NOT NULL,
                    last_sync_id TEXT,
                    total_synced INTEGER DEFAULT 0,
                    success_count INTEGER DEFAULT 0,
                    error_count INTEGER DEFAULT 0,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sync_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sync_type TEXT NOT NULL,
                    sync_time TEXT NOT NULL,
                    records_count INTEGER DEFAULT 0,
                    success_count INTEGER DEFAULT 0,
                    error_count INTEGER DEFAULT 0,
                    duration_seconds REAL DEFAULT 0,
                    details TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            print("[OK] 同步状态数据库初始化完成")
        except Exception as e:
            print(f"[ERROR] 同步状态数据库初始化失败: {e}")
        finally:
            conn.close()
    
    def get_last_sync_record(self, sync_type: str) -> Optional[SyncRecord]:
        """获取最后同步记录"""
        conn = sqlite3.connect(self.sync_state_file)
        try:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT sync_type, last_sync_time, last_sync_id, 
                       total_synced, success_count, error_count
                FROM sync_records 
                WHERE sync_type = ?
            ''', (sync_type,))
            
            row = cursor.fetchone()
            if row:
                return SyncRecord(
                    sync_type=row[0],
                    last_sync_time=datetime.fromisoformat(row[1]),
                    last_sync_id=row[2] or "",
                    total_synced=row[3],
                    success_count=row[4],
                    error_count=row[5]
                )
            return None
        finally:
            conn.close()
    
    def update_sync_record(self, record: SyncRecord):
        """更新同步记录"""
        conn = sqlite3.connect(self.sync_state_file)
        try:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO sync_records 
                (sync_type, last_sync_time, last_sync_id, total_synced, 
                 success_count, error_count, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                record.sync_type,
                record.last_sync_time.isoformat(),
                record.last_sync_id,
                record.total_synced,
                record.success_count,
                record.error_count,
                datetime.now().isoformat()
            ))
            conn.commit()
        finally:
            conn.close()
    
    def add_sync_history(self, sync_type: str, sync_time: datetime, 
                        records_count: int, success_count: int, error_count: int,
                        duration: float, details: str = ""):
        """添加同步历史记录"""
        conn = sqlite3.connect(self.sync_state_file)
        try:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO sync_history 
                (sync_type, sync_time, records_count, success_count, 
                 error_count, duration_seconds, details)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                sync_type,
                sync_time.isoformat(),
                records_count,
                success_count,
                error_count,
                duration,
                details
            ))
            conn.commit()
        finally:
            conn.close()
    
    def get_incremental_exam_data(self, sync_type: str = "exam_data") -> Tuple[List[Dict[str, Any]], SyncRecord]:
        """获取增量体检数据"""
        print(f"\n[INCREMENTAL] 开始获取增量{sync_type}数据")
        
        # 获取最后同步记录
        last_record = self.get_last_sync_record(sync_type)
        
        if last_record:
            # 增量同步：从最后同步时间开始
            last_sync_time = last_record.last_sync_time
            print(f"[INFO] 上次同步时间: {last_sync_time}")
            
            # 计算增量时间范围（从上次同步到现在）
            time_diff = datetime.now() - last_sync_time
            days = max(1, time_diff.days + 1)  # 至少查询1天
        else:
            # 首次同步：获取最近30天数据
            last_sync_time = datetime.now() - timedelta(days=30)
            days = 30
            print(f"[INFO] 首次同步，获取最近{days}天数据")
        
        # 查询增量数据
        incremental_data = self.db_service.get_exam_data_optimized(
            days=days, 
            limit=None,  # 不限制条数，获取所有增量数据
            use_cache=False  # 增量同步不使用缓存
        )
        
        # 过滤出真正的增量数据（基于操作时间）
        if last_record:
            filtered_data = []
            for record in incremental_data:
                exam_date_str = record.get('exam_date', '')
                if exam_date_str:
                    try:
                        # 解析体检日期
                        if isinstance(exam_date_str, str):
                            exam_date = datetime.strptime(exam_date_str[:19], '%Y-%m-%d %H:%M:%S')
                        else:
                            exam_date = exam_date_str
                        
                        # 只包含比上次同步时间更新的记录
                        if exam_date > last_sync_time:
                            filtered_data.append(record)
                    except (ValueError, TypeError) as e:
                        print(f"[WARN] 日期解析失败: {exam_date_str}, 错误: {e}")
                        # 如果日期解析失败，保守起见包含此记录
                        filtered_data.append(record)
            
            incremental_data = filtered_data
        
        print(f"[INFO] 获取到 {len(incremental_data)} 条增量数据")
        
        # 创建新的同步记录
        new_record = SyncRecord(
            sync_type=sync_type,
            last_sync_time=datetime.now(),
            last_sync_id=incremental_data[-1].get('reg_code', '') if incremental_data else '',
            total_synced=(last_record.total_synced if last_record else 0) + len(incremental_data),
            success_count=last_record.success_count if last_record else 0,
            error_count=last_record.error_count if last_record else 0
        )
        
        return incremental_data, new_record
    
    def get_incremental_apply_items(self, sync_type: str = "apply_items") -> Tuple[List[Dict[str, Any]], SyncRecord]:
        """获取增量申请项目数据"""
        print(f"\n[INCREMENTAL] 开始获取增量{sync_type}数据")
        
        # 获取最后同步记录
        last_record = self.get_last_sync_record(sync_type)
        
        # 申请项目数据变化较少，使用简单的全量对比策略
        current_data = self.db_service.get_apply_items_with_check_items_optimized(
            limit=None,
            use_cache=False
        )
        
        if last_record:
            # 对比数据变化（简化版本，实际可以基于修改时间）
            # 这里假设如果数量变化就是有更新
            if len(current_data) == last_record.total_synced:
                print(f"[INFO] 申请项目数据无变化，跳过同步")
                return [], last_record
        
        print(f"[INFO] 获取到 {len(current_data)} 条申请项目数据")
        
        # 创建新的同步记录
        new_record = SyncRecord(
            sync_type=sync_type,
            last_sync_time=datetime.now(),
            last_sync_id=current_data[-1].get('applyItemId', '') if current_data else '',
            total_synced=len(current_data),
            success_count=last_record.success_count if last_record else 0,
            error_count=last_record.error_count if last_record else 0
        )
        
        return current_data, new_record
    
    def get_incremental_dict_data(self, dict_type: str) -> Tuple[List[Dict[str, Any]], SyncRecord]:
        """获取增量字典数据"""
        sync_type = f"dict_{dict_type}"
        print(f"\n[INCREMENTAL] 开始获取增量{sync_type}数据")
        
        # 获取最后同步记录
        last_record = self.get_last_sync_record(sync_type)
        
        # 字典数据变化很少，使用全量对比策略
        current_data = self.db_service.get_dict_data_optimized(
            dict_type=dict_type,
            use_cache=False
        )
        
        if last_record:
            # 对比数据数量变化
            if len(current_data) == last_record.total_synced:
                print(f"[INFO] {dict_type}字典数据无变化，跳过同步")
                return [], last_record
        
        print(f"[INFO] 获取到 {len(current_data)} 条{dict_type}字典数据")
        
        # 创建新的同步记录
        new_record = SyncRecord(
            sync_type=sync_type,
            last_sync_time=datetime.now(),
            last_sync_id=current_data[-1].get('code', '') if current_data else '',
            total_synced=len(current_data),
            success_count=last_record.success_count if last_record else 0,
            error_count=last_record.error_count if last_record else 0
        )
        
        return current_data, new_record
    
    def sync_incremental_data(self, sync_types: List[str] = None) -> Dict[str, Any]:
        """执行增量数据同步"""
        if sync_types is None:
            sync_types = ["exam_data", "apply_items", "departments", "operators"]
        
        print("=" * 60)
        print("增量数据同步开始")
        print("=" * 60)
        
        sync_results = {
            'start_time': datetime.now(),
            'results': [],
            'total_records': 0,
            'total_success': 0,
            'total_errors': 0
        }
        
        for sync_type in sync_types:
            start_time = datetime.now()
            
            try:
                # 根据同步类型获取增量数据
                if sync_type == "exam_data":
                    incremental_data, sync_record = self.get_incremental_exam_data()
                elif sync_type == "apply_items":
                    incremental_data, sync_record = self.get_incremental_apply_items()
                elif sync_type in ["departments", "operators", "items"]:
                    incremental_data, sync_record = self.get_incremental_dict_data(sync_type)
                else:
                    print(f"[WARN] 未知的同步类型: {sync_type}")
                    continue
                
                # 模拟API发送（实际项目中会调用真实的API接口）
                success_count = len(incremental_data)
                error_count = 0
                
                if incremental_data:
                    print(f"[SYNC] 正在同步 {len(incremental_data)} 条 {sync_type} 数据...")
                    # TODO: 这里应该调用实际的API接口进行数据同步
                    # 现在只是模拟成功
                    
                    # 更新同步记录统计
                    sync_record.success_count += success_count
                    sync_record.error_count += error_count
                    
                    # 更新同步记录
                    self.update_sync_record(sync_record)
                    
                    print(f"[OK] {sync_type} 同步完成: {success_count} 成功, {error_count} 失败")
                else:
                    print(f"[INFO] {sync_type} 无增量数据需要同步")
                
                # 记录同步历史
                duration = (datetime.now() - start_time).total_seconds()
                self.add_sync_history(
                    sync_type=sync_type,
                    sync_time=start_time,
                    records_count=len(incremental_data),
                    success_count=success_count,
                    error_count=error_count,
                    duration=duration,
                    details=f"增量同步，获取{len(incremental_data)}条记录"
                )
                
                # 添加到结果
                sync_results['results'].append({
                    'sync_type': sync_type,
                    'records_count': len(incremental_data),
                    'success_count': success_count,
                    'error_count': error_count,
                    'duration': duration
                })
                
                sync_results['total_records'] += len(incremental_data)
                sync_results['total_success'] += success_count
                sync_results['total_errors'] += error_count
                
            except Exception as e:
                print(f"[ERROR] {sync_type} 同步失败: {e}")
                sync_results['results'].append({
                    'sync_type': sync_type,
                    'error': str(e)
                })
        
        sync_results['end_time'] = datetime.now()
        sync_results['total_duration'] = (sync_results['end_time'] - sync_results['start_time']).total_seconds()
        
        print("\n" + "=" * 60)
        print("增量数据同步完成")
        print(f"总记录数: {sync_results['total_records']}")
        print(f"成功数量: {sync_results['total_success']}")
        print(f"失败数量: {sync_results['total_errors']}")
        print(f"总耗时: {sync_results['total_duration']:.2f}秒")
        print("=" * 60)
        
        return sync_results
    
    def get_sync_status(self) -> Dict[str, Any]:
        """获取同步状态概览"""
        conn = sqlite3.connect(self.sync_state_file)
        try:
            cursor = conn.cursor()
            
            # 获取所有同步记录
            cursor.execute('''
                SELECT sync_type, last_sync_time, total_synced, 
                       success_count, error_count
                FROM sync_records
                ORDER BY updated_at DESC
            ''')
            
            records = []
            for row in cursor.fetchall():
                records.append({
                    'sync_type': row[0],
                    'last_sync_time': row[1],
                    'total_synced': row[2],
                    'success_count': row[3],
                    'error_count': row[4],
                    'success_rate': (row[3] / max(row[2], 1)) * 100
                })
            
            # 获取最近的同步历史
            cursor.execute('''
                SELECT sync_type, sync_time, records_count, success_count, 
                       error_count, duration_seconds
                FROM sync_history
                ORDER BY created_at DESC
                LIMIT 10
            ''')
            
            recent_history = []
            for row in cursor.fetchall():
                recent_history.append({
                    'sync_type': row[0],
                    'sync_time': row[1],
                    'records_count': row[2],
                    'success_count': row[3],
                    'error_count': row[4],
                    'duration': row[5]
                })
            
            return {
                'sync_records': records,
                'recent_history': recent_history,
                'total_sync_types': len(records),
                'last_update': datetime.now().isoformat()
            }
            
        finally:
            conn.close()
    
    def reset_sync_state(self, sync_type: str = None):
        """重置同步状态"""
        conn = sqlite3.connect(self.sync_state_file)
        try:
            cursor = conn.cursor()
            if sync_type:
                cursor.execute('DELETE FROM sync_records WHERE sync_type = ?', (sync_type,))
                print(f"[OK] 已重置 {sync_type} 的同步状态")
            else:
                cursor.execute('DELETE FROM sync_records')
                cursor.execute('DELETE FROM sync_history')
                print("[OK] 已重置所有同步状态")
            conn.commit()
        finally:
            conn.close()
    
    def close(self):
        """关闭服务"""
        if self.db_service:
            self.db_service.close()


def create_incremental_sync_service(connection_string: str) -> IncrementalSyncService:
    """创建增量同步服务实例"""
    return IncrementalSyncService(connection_string)


# 测试和演示
if __name__ == "__main__":
    # 连接字符串
    connection_string = Config.get_interface_db_connection_string()