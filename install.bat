@echo off
chcp 65001 > nul
echo ============================================================
echo 健康同步系统 - 依赖包安装脚本
echo 福能AI对接项目 - 嘉仁体检中心数据同步系统
echo ============================================================
echo.

echo [1] 检查Python版本...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未加入PATH环境变量
    echo 请先安装Python 3.8+并确保加入PATH
    pause
    exit /b 1
)

echo.
echo [2] 升级pip到最新版本...
python -m pip install --upgrade pip

echo.
echo [3] 安装核心依赖包...
echo 正在安装 SQLAlchemy (数据库ORM)...
pip install sqlalchemy==1.4.53

echo 正在安装 pyodbc (SQL Server驱动)...
pip install pyodbc==5.0.1

echo 正在安装 requests (HTTP请求)...
pip install requests==2.31.0

echo.
echo [4] 安装可选依赖包...
echo 正在安装 loguru (日志系统)...
pip install loguru==0.7.2

echo 正在安装 python-dotenv (环境变量)...
pip install python-dotenv==1.0.0

echo.
echo [5] 验证安装...
echo 正在验证核心模块导入...
python -c "import sqlalchemy; print('✅ SQLAlchemy:', sqlalchemy.__version__)"
python -c "import pyodbc; print('✅ pyodbc:', pyodbc.version)"
python -c "import requests; print('✅ requests:', requests.__version__)"

echo.
echo ============================================================
echo 安装完成！
echo ============================================================
echo.
echo 现在您可以运行以下命令测试系统：
echo   python main.py status         # 检查系统状态
echo   python main.py config         # 查看配置信息
echo   python main.py preview        # 预览数据
echo.
echo 注意事项：
echo 1. 确保已安装 "ODBC Driver 17 for SQL Server"
echo 2. 如需图形界面，后续可安装 PySide6
echo 3. 如遇到问题，请检查网络连接和权限设置
echo.
pause 