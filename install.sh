#!/bin/bash

echo "============================================================"
echo "健康同步系统 - 依赖包安装脚本"
echo "福能AI对接项目 - 嘉仁体检中心数据同步系统"
echo "============================================================"
echo

echo "[1] 检查Python版本..."
python3 --version
if [ $? -ne 0 ]; then
    echo "❌ Python3未安装"
    echo "请先安装Python 3.8+:"
    echo "  Ubuntu/Debian: sudo apt-get install python3 python3-pip"
    echo "  CentOS/RHEL: sudo yum install python3 python3-pip"
    echo "  macOS: brew install python3"
    exit 1
fi

echo
echo "[2] 升级pip到最新版本..."
python3 -m pip install --upgrade pip

echo
echo "[3] 安装核心依赖包..."
echo "正在安装 SQLAlchemy (数据库ORM)..."
pip3 install sqlalchemy==1.4.53

echo "正在安装 pyodbc (SQL Server驱动)..."
pip3 install pyodbc==5.0.1

echo "正在安装 requests (HTTP请求)..."
pip3 install requests==2.31.0

echo
echo "[4] 安装可选依赖包..."
echo "正在安装 loguru (日志系统)..."
pip3 install loguru==0.7.2

echo "正在安装 python-dotenv (环境变量)..."
pip3 install python-dotenv==1.0.0

echo
echo "[5] 验证安装..."
echo "正在验证核心模块导入..."
python3 -c "import sqlalchemy; print('✅ SQLAlchemy:', sqlalchemy.__version__)"
python3 -c "import pyodbc; print('✅ pyodbc:', pyodbc.version)"
python3 -c "import requests; print('✅ requests:', requests.__version__)"

echo
echo "============================================================"
echo "安装完成！"
echo "============================================================"
echo
echo "现在您可以运行以下命令测试系统："
echo "  python3 main.py status         # 检查系统状态"
echo "  python3 main.py config         # 查看配置信息"
echo "  python3 main.py preview        # 预览数据"
echo
echo "注意事项："
echo "1. Linux系统需要安装 ODBC 驱动程序："
echo "   Ubuntu/Debian: sudo apt-get install unixodbc-dev"
echo "   CentOS/RHEL: sudo yum install unixODBC-devel"
echo "2. 如需图形界面，后续可安装 PySide6"
echo "3. 如遇到问题，请检查网络连接和权限设置"
echo 