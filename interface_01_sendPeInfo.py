from api_config_manager import get_tianjian_base_url
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云01号接口实现 - 单次体检基本信息传输
/dx/inter/sendPeInfo
"""

import json
import hashlib
import requests
import uuid
import time
import os
from datetime import datetime
from typing import Dict, Any, List, Optional

# 防止GUI实例化：检查是否是GUI子进程
if os.environ.get('GUI_SUBPROCESS') == '1' or os.environ.get('TIANJIAN_NO_GUI') == '1':
    # 当作为GUI子进程运行时，避免导入可能启动GUI的模块
    import sys
    # 禁用任何可能的GUI导入
    sys.modules['tkinter'] = None
    sys.modules['PySide6'] = None
    sys.modules['PyQt5'] = None
    sys.modules['gui_main'] = None
    # 设置无GUI模式标记
    os.environ['NO_GUI_MODE'] = '1'

from optimized_database_service import create_optimized_db_service
from config import Config
from multi_org_config import get_current_org_config
from org_code_prefix_manager import create_org_prefix_manager
from sync_logger import create_sync_logger


def safe_print(text, fallback_text=None):
    """安全打印函数，避免编码错误"""
    try:
        print(text)
    except UnicodeEncodeError:
        if fallback_text:
            print(fallback_text)
        else:
            # 移除可能有问题的Unicode字符
            safe_text = text.encode('ascii', errors='ignore').decode('ascii')
            print(f"[SAFE] {safe_text}")
    except Exception:
        print("[PRINT ERROR] Unable to display message")


class TianjianInterface01:
    """天健云01号接口 - 单次体检基本信息传输"""

    def __init__(self, api_config: Dict[str, Any] = None, org_config: Dict[str, Any] = None):
        """
        初始化接口配置

        Args:
            api_config: API配置信息（可选，默认使用中心库配置）
            org_config: 机构配置信息（可选，默认使用当前机构配置）
        """
        # 获取当前机构配置
        self.org_config = org_config or get_current_org_config()

        # 使用机构配置中的天健云API配置
        if api_config:
            self.api_config = api_config
        else:
            self.api_config = {
                'base_url': self.org_config.get('tianjian_base_url', get_tianjian_base_url()),
                'api_key': self.org_config.get('tianjian_api_key', ''),
                'mic_code': self.org_config.get('tianjian_mic_code', ''),
                'misc_id': self.org_config.get('tianjian_misc_id', ''),
                'timeout': 30
            }

        # 创建优化的数据库服务实例，使用机构配置中的数据库连接
        if self.org_config.get('db_host'):
            connection_string = (
                f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                f"SERVER={self.org_config['db_host']},{self.org_config.get('db_port', 1433)};"
                f"DATABASE={self.org_config['db_name']};"
                f"UID={self.org_config['db_user']};"
                f"PWD={self.org_config['db_password']}"
            )
        else:
            # 兜底使用统一配置
            connection_string = Config.get_interface_db_connection_string()

        self.db_service = create_optimized_db_service(connection_string)

        # 创建机构编码前缀管理器
        org_code = self.org_config.get('org_code', 'DEFAULT')
        self.prefix_manager = create_org_prefix_manager(org_code)
        
        # 创建同步日志记录器
        shop_code = self.org_config.get('shop_code', 'DEFAULT')
        self.sync_logger = create_sync_logger(shop_code, org_code)
    
    def generate_signature(self, api_key: str, timestamp: str) -> str:
        """生成MD5签名"""
        sign_string = api_key + timestamp
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    def create_headers(self) -> Dict[str, str]:
        """创建请求头"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        nonce = str(uuid.uuid4())
        signature = self.generate_signature(self.api_config['api_key'], timestamp)

        return {
            'Content-Type': 'application/json',
            'sign': signature,
            'timestamp': timestamp,
            'nonce': nonce,
            'mic-code': self.api_config['mic_code'],
            'misc-id': self.api_config['misc_id']
        }
    
    def format_exam_data(self, exam_record: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化体检数据为天健云01号接口格式
        根据数据库结构和接口要求进行精确映射

        Args:
            exam_record: 数据库中的体检记录

        Returns:
            格式化后的接口数据
        """
        # 格式化生日 - 转换为yyyyMMdd格式
        birthday = exam_record.get('dBirthday', '')
        if birthday:
            if hasattr(birthday, 'strftime'):
                birthday = birthday.strftime('%Y%m%d')  # yyyyMMdd格式
            elif isinstance(birthday, str) and len(birthday) >= 10:
                birthday = birthday[:10].replace('-', '')  # 移除横线
            else:
                birthday = ''

        # 格式化体检日期 - 转换为yyyy-MM-dd HH:mm:ss格式
        exam_date = exam_record.get('exam_date', '')
        if exam_date:
            if hasattr(exam_date, 'strftime'):
                exam_date = exam_date.strftime('%Y-%m-%d %H:%M:%S')
            elif isinstance(exam_date, str):
                if len(exam_date) >= 19:
                    exam_date = exam_date[:19]  # 保持完整的日期时间
                elif len(exam_date) >= 10:
                    exam_date = exam_date[:10] + ' 00:00:00'  # 补充时间部分
            else:
                exam_date = ''

        # 计算年龄
        age = 0
        if birthday and len(birthday) >= 4:
            try:
                birth_year = int(birthday[:4])
                age = 2025 - birth_year
            except:
                age = 0

        # 获取体检状态映射 - 根据复杂逻辑判断
        status_code = exam_record.get('cStatus', '0')
        client_code = exam_record.get('archiveNo', '')
        dept_status_type = exam_record.get('dept_status_type', '')

        # 根据业务逻辑确定体检状态
        if dept_status_type == 'incomplete_dept':
            # 分科未完成状态 (cCanDiagDate为空且AIDiagnosisStatus=2)
            pe_states = {'code': '2', 'name': '分科未完成'}
        elif status_code == '0':
            # T_Register_Main.cStatus = 0 → 登记完成
            pe_states = {'code': '1', 'name': '登记完成'}
        elif status_code == '1':
            # T_Register_Main.cStatus = 1 → 检查分科完成状态
            # 修改为根据T_Register_Main.cCanDiagDate是否有数据来判断分科是否完成
            c_can_diag_date = exam_record.get('cCanDiagDate')
            if c_can_diag_date is not None and c_can_diag_date != '':
                # 有初检完成时间，表示分科已完成
                pe_states = {'code': '3', 'name': '分科完成'}
            else:
                # 没有初检完成时间，表示分科未完成
                pe_states = {'code': '2', 'name': '分科未完成'}
        elif status_code == '2':
            # T_Register_Main.cStatus = 2 → 主检终审完成
            pe_states = {'code': '7', 'name': '主检终审完成'}
        else:
            # 默认状态
            pe_states = {'code': '1', 'name': '登记完成'}



        # 获取婚姻状态映射
        marry_flag = exam_record.get('cMarryFlag', '')
        if marry_flag == '0':
            ms_info = {'code': 'unmarried', 'name': '未婚'}
        elif marry_flag == '1':
            ms_info = {'code': 'married', 'name': '已婚'}
        else:
            ms_info = {'code': 'unknown', 'name': '未知'}

        # 判断是否团体 - 通过cUnitsFlag字段判断（1为true，其他为false）
        units_flag = exam_record.get('cUnitsFlag', '0')
        is_group = (str(units_flag) == '1')

        # 获取公司名称 - 从查询结果中直接获取
        company_name = exam_record.get('company_name', '')

        # 获取工作部门 - 从查询结果中直接获取
        work_dept_name = exam_record.get('work_dept_name', '')

        # 获取套餐信息 - 从T_UnitsSuit_Master表获取
        raw_suit_code = exam_record.get('cSuitCode', '')
        # 去掉前缀，如果有"08_"前缀则去掉
        suit_code = raw_suit_code.replace('08_', '') if raw_suit_code.startswith('08_') else raw_suit_code
        suit_name = exam_record.get('suit_name', '') or exam_record.get('cCommanSuitName', '')

        # 获取团队编号 - 从T_Register_Main.cContractCode获取
        team_no = exam_record.get('cContractCode', '')

        # 获取申请项目列表
        client_code = exam_record.get('archiveNo', '')
        apply_items = self.get_apply_items(client_code) if client_code else []

        request_data = {
            "peUserInfo": {
                "archiveNo": exam_record.get('archiveNo', ''),
                "name": exam_record.get('name', ''),
                "icCode": exam_record.get('cert_id', ''),
                "sex": {
                    "code": exam_record.get('sex', '3'),
                    "name": "男" if exam_record.get('sex') == '1' else "女" if exam_record.get('sex') == '2' else "未知"
                },
                "birthday": birthday,
                "peno": exam_record.get('peno', ''),
                "peDate": exam_date,
                "phone": exam_record.get('mobile', ''),
                "ms": ms_info,
                "pregnantState": {
                    "code": "unknown",
                    "name": "未知"
                },
                "vipLevel": {
                    "code": "NORMAL",
                    "name": "普通"
                },
                "medicalType": {
                    "code": "PERSONAL" if not is_group else "GROUP",
                    "name": "个人体检" if not is_group else "团体体检"
                },
                "isGroup": is_group,
                "company": company_name,
                "workDept": work_dept_name,
                "teamNo": team_no,
                "professional": "",
                "workAge": "",
                "peStates": pe_states,
                "deptCount": 1,
                "age": age,
                "deptFinishTime": "",
                "firstCheckFinishTime": "",
                "firstCheckFinishDoctor": {
                    "code": "",
                    "name": ""
                },
                "mainCheckFinishTime": "",
                "mainCheckFinishDoctor": {
                    "code": "",
                    "name": ""
                },
                "forbidGoCheck": False,
                "reportPrint": status_code == '2',  # 主检终审完成时已打印
                "reportGot": False,
                "replacementInspectionMark": False,
                "applyItemList": apply_items,
                "currentNodeType": 1,  # 1=登记
                "pePackage": {
                    "code": suit_code,
                    "name": suit_name
                }
            },
            "archiveInfo": {
                "name": exam_record.get('name', ''),
                "icCode": exam_record.get('cert_id', ''),
                "sex": {
                    "code": exam_record.get('sex', '3'),
                    "name": "男" if exam_record.get('sex') == '1' else "女" if exam_record.get('sex') == '2' else "未知"
                },
                "birthday": birthday,
                "peNoList": [exam_record.get('archiveNo', '')]
            }
        }

        # 使用机构编码前缀管理器处理数据
        processed_data = self.prefix_manager.process_data(request_data, 'sendPeInfo')

        return processed_data

    def get_apply_items(self, client_code: str) -> List[str]:
        """
        获取体检者的申请项目列表

        Args:
            client_code: 客户编码

        Returns:
            申请项目ID列表
        """
        try:
            # 方法1: 尝试从T_Register_Detail表获取
            sql = f"""
            SELECT DISTINCT rd.cPriceCode
            FROM T_Register_Detail rd
            WHERE rd.cClientCode = '{client_code}'
            """

            result = self.db_service.connection_manager.execute_query_with_cache(
                self.db_service.connection_string,
                sql,
                cache_key=f"apply_items_{client_code}",
                use_cache=False
            )

            # 提取价格编码列表
            apply_items = [row['cPriceCode'] for row in result if row['cPriceCode']]

            if apply_items:
                return apply_items

            # 方法2: 如果Detail表没有数据，尝试使用套餐编码
            print(f"[INFO] T_Register_Detail表中没有找到客户 {client_code} 的申请项目，尝试使用套餐编码")

            suit_sql = f"""
            SELECT cSuitCode
            FROM T_Register_Main
            WHERE cClientCode = '{client_code}'
            """

            suit_result = self.db_service.connection_manager.execute_query_with_cache(
                self.db_service.connection_string,
                suit_sql,
                cache_key=f"suit_code_{client_code}",
                use_cache=False
            )

            if suit_result and suit_result[0]['cSuitCode']:
                suit_code = suit_result[0]['cSuitCode']
                print(f"[INFO] 使用套餐编码作为申请项目: {suit_code}")
                return [suit_code]

            # 方法3: 如果都没有，返回空列表
            print(f"[WARNING] 无法获取客户 {client_code} 的申请项目")
            return []

        except Exception as e:
            print(f"[WARNING] 获取申请项目失败: {e}")
            return []

    def get_pending_ai_diagnosis_records_from_local(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        直接从本部库获取AI诊断记录，不查询中心库
        支持两种轮询条件：
        1. cCanDiagDate为当天且AIDiagnosisStatus=1 (分科完成状态)
        2. cCanDiagDate为空且AIDiagnosisStatus=2 (分科未完成状态)
        
        Args:
            limit: 限制获取的记录数
            
        Returns:
            待传输的体检记录列表（来自本部库）
        """
        try:
            # 检查必要字段是否存在
            required_fields = ['AIDiagnosisStatus', 'cCanDiagDate']
            for field in required_fields:
                column_check_sql = f"""
                SELECT COUNT(*) as column_exists
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'T_Register_Main' 
                AND COLUMN_NAME = '{field}'
                """
                
                column_check = self.db_service.connection_manager.execute_query_with_cache(
                    self.db_service.connection_string,
                    column_check_sql,
                    cache_key=f"field_check_{field}",
                    use_cache=True
                )
                
                if not column_check or column_check[0]['column_exists'] == 0:
                    print(f"[WARNING] T_Register_Main表中不存在{field}字段，无法进行AI诊断轮询")
                    return []
            
            # 查询两种情况的记录
            local_sql = f"""
            SELECT TOP ({limit})
                rm.cClientCode as archiveNo,
                rm.cName as name,
                rm.cSex as sex,
                rm.dBornDate as dBirthday,
                rm.cIdCard as cert_id,
                rm.cTel as mobile,
                rm.dOperdate as exam_date,
                rm.cStatus,
                rm.cMarryFlag,
                rm.cCardNo as peno,
                rm.cSuitCode,
                rm.cContractCode,
                rm.cCommanSuitName,
                rm.cUnitsFlag,
                rm.iDeptIndex,
                rm.cCanDiagDate,
                rm.AIDiagnosisStatus,
                '' as company_name,
                '' as work_dept_name,
                '' as units_code,
                '' as suit_name,
                CASE 
                    WHEN rm.cCanDiagDate IS NULL OR rm.cCanDiagDate = '' THEN 'incomplete_dept'
                    ELSE 'completed_dept'
                END as dept_status_type
            FROM T_Register_Main rm
            WHERE (
                -- 情况1: 分科完成，当天cCanDiagDate且AIDiagnosisStatus=1
                (CONVERT(date, rm.cCanDiagDate) = CONVERT(date, GETDATE()) AND rm.AIDiagnosisStatus = 1)
                OR 
                -- 情况2: 分科未完成，cCanDiagDate为空且AIDiagnosisStatus=2  
                ((rm.cCanDiagDate IS NULL OR rm.cCanDiagDate = '') AND rm.AIDiagnosisStatus = 2)
            )
            ORDER BY rm.AIDiagnosisStatus ASC, rm.cCanDiagDate ASC
            """
            
            local_records = self.db_service.connection_manager.execute_query_with_cache(
                self.db_service.connection_string,
                local_sql,
                cache_key=f"pending_ai_diagnosis_local_dual_{limit}",
                use_cache=False
            )
            
            if local_records:
                print(f"[INFO] 本部库找到 {len(local_records)} 条符合条件的完整记录")
                completed_count = 0
                incomplete_count = 0
                
                for record in local_records:
                    name = record.get('name', '未知')
                    client_code = record.get('archiveNo', '')
                    dept_status = record.get('dept_status_type', '')
                    ai_status = record.get('AIDiagnosisStatus', '')
                    can_diag_date = record.get('cCanDiagDate', '')
                    
                    if dept_status == 'incomplete_dept':
                        incomplete_count += 1
                        print(f"[DEBUG] 分科未完成记录: 客户编码={client_code}, 姓名={name}, AI状态={ai_status}")
                    else:
                        completed_count += 1
                        print(f"[DEBUG] 分科完成记录: 客户编码={client_code}, 姓名={name}, 分科时间={can_diag_date}, AI状态={ai_status}")
                
                print(f"[INFO] 记录类型统计: 分科完成={completed_count}条, 分科未完成={incomplete_count}条")
                return local_records
            else:
                print("[INFO] 本部库中没有找到符合条件的AI诊断记录")
                return []
                
        except Exception as e:
            print(f"[ERROR] 从本部库获取AI诊断记录失败: {e}")
            return []
        """
        获取需要AI诊断传输的记录 (cCanDiagDate为当天 且 AIDiagnosisStatus = 1)
        先从本部库获取符合条件的记录，然后去中心库查询完整数据
        
        Args:
            limit: 限制获取的记录数
            
        Returns:
            待传输的体检记录列表（来自中心库）
        """
        try:
            # 检查必要字段是否存在
            required_fields = ['AIDiagnosisStatus', 'cCanDiagDate']
            for field in required_fields:
                column_check_sql = f"""
                SELECT COUNT(*) as column_exists
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'T_Register_Main' 
                AND COLUMN_NAME = '{field}'
                """
                
                column_check = self.db_service.connection_manager.execute_query_with_cache(
                    self.db_service.connection_string,
                    column_check_sql,
                    cache_key=f"field_check_{field}",
                    use_cache=True
                )
                
                if not column_check or column_check[0]['column_exists'] == 0:
                    print(f"[WARNING] T_Register_Main表中不存在{field}字段，无法进行AI诊断轮询")
                    return []
            
            # 从本部库查询符合条件的记录（只获取客户编码）
            # 不使用cShopCode过滤，因为已经通过机构配置连接到对应的数据库
            local_sql = f"""
            SELECT TOP ({limit})
                rm.cClientCode as client_code,
                rm.cName as name,
                rm.cCanDiagDate
            FROM T_Register_Main rm
            WHERE CONVERT(date, rm.cCanDiagDate) = CONVERT(date, GETDATE())
            AND rm.AIDiagnosisStatus = 1
            ORDER BY rm.cCanDiagDate ASC
            """
            
            local_records = self.db_service.connection_manager.execute_query_with_cache(
                self.db_service.connection_string,
                local_sql,
                cache_key=f"pending_ai_diagnosis_local_{limit}",
                use_cache=False
            )
            
            if not local_records:
                print(f"[INFO] 本部库中没有找到符合条件的AI诊断记录（当天cCanDiagDate且AIDiagnosisStatus=1）")
                return []
            
            print(f"[INFO] 本部库找到 {len(local_records)} 条符合条件的记录，准备从中心库获取完整数据")
            
            # 从中心库查询完整的体检数据
            center_records = []
            from database_service import get_database_service
            
            # 使用中心库连接
            center_db_service = get_database_service()
            if not center_db_service.connect():
                print("[ERROR] 无法连接中心库获取体检数据")
                return []
            
            try:
                for local_record in local_records:
                    client_code = local_record['client_code']
                    name = local_record['name']
                    
                    print(f"[DEBUG] 正在中心库查询客户: {name}({client_code})")
                    
                    # 从中心库查询完整的体检数据
                    center_sql = f"""
                    SELECT 
                        rm.cClientCode as archiveNo,
                        rm.cName as name,
                        rm.cSex as sex,
                        rm.dBornDate as dBirthday,
                        rm.cIdCard as cert_id,
                        rm.cTel as mobile,
                        rm.dOperdate as exam_date,
                        rm.cStatus,
                        rm.cMarryFlag,
                        rm.cCardNo as peno,
                        rm.cSuitCode,
                        rm.cContractCode,
                        rm.cCommanSuitName,
                        rm.cUnitsFlag,
                        rm.iDeptIndex,
                        rm.cCanDiagDate,
                        ISNULL(u.cName, '') as company_name,
                        '' as work_dept_name,
                        ISNULL(u.cCode, '') as units_code,
                        ISNULL(us.cName, '') as suit_name
                    FROM T_Register_Main rm
                    LEFT JOIN T_Contract tc ON rm.cContractCode = tc.cCode
                    LEFT JOIN T_Units u ON tc.cUnitsCode = u.cCode
                    LEFT JOIN T_UnitsSuit_Master us ON rm.cSuitCode = us.cSuitCode
                    WHERE rm.cClientCode = '{client_code}'
                    """
                    
                    print(f"[DEBUG] 中心库查询SQL:")
                    print(f"[DEBUG] {center_sql.strip()}")
                    
                    try:
                        center_result = center_db_service.execute_query(center_sql)
                        print(f"[DEBUG] 中心库查询执行完成，返回结果数: {len(center_result) if center_result else 0}")
                        
                        if center_result:
                            center_records.extend(center_result)
                            print(f"[INFO] 从中心库获取到客户 {name}({client_code}) 的完整数据")
                            # 显示找到的记录的一些关键信息
                            for record in center_result:
                                print(f"[DEBUG] 中心库记录: 客户编码={record.get('archiveNo')}, 姓名={record.get('name')}, 卡号={record.get('peno')}")
                        else:
                            print(f"[WARNING] 中心库中未找到客户 {name}({client_code}) 的数据")
                            
                            # 尝试用模糊查询看看是否有类似的记录
                            fuzzy_sql = f"""
                            SELECT TOP 5 cClientCode, cName, cCardNo 
                            FROM T_Register_Main 
                            WHERE cName LIKE '%{name}%' OR cClientCode LIKE '%{client_code[-6:]}%'
                            """
                            print(f"[DEBUG] 尝试模糊查询: {fuzzy_sql.strip()}")
                            
                            fuzzy_result = center_db_service.execute_query(fuzzy_sql)
                            if fuzzy_result:
                                print(f"[DEBUG] 中心库中找到 {len(fuzzy_result)} 条相似记录:")
                                for record in fuzzy_result:
                                    print(f"[DEBUG]   客户编码: {record.get('cClientCode')}, 姓名: {record.get('cName')}, 卡号: {record.get('cCardNo')}")
                            else:
                                print(f"[DEBUG] 中心库中也没有找到相似的记录")
                                
                    except Exception as query_error:
                        print(f"[ERROR] 中心库查询执行异常: {query_error}")
                        continue
                
            finally:
                center_db_service.disconnect()
            
            print(f"[INFO] 最终获取到 {len(center_records)} 条完整的中心库数据")
            return center_records
            
        except Exception as e:
            print(f"[ERROR] 获取待AI诊断记录失败: {e}")
            return []
    
    def update_ai_diagnosis_status(self, client_code: str, current_status: int, new_status: int) -> bool:
        """
        更新本部库体检记录的AI诊断状态
        
        Args:
            client_code: 客户编码
            current_status: 当前状态值 (用于验证)
            new_status: 新状态值
            
        Returns:
            更新是否成功
        """
        try:
            # 检查字段是否存在（使用本部库连接）
            column_checks = [
                ("AIDiagnosisStatus", "AIDiagnosisStatus"),
                ("cAICanDiagnosisTime", "cAICanDiagnosisTime")
            ]
            
            existing_columns = []
            for field_name, column_name in column_checks:
                check_sql = f"""
                SELECT COUNT(*) as column_exists
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'T_Register_Main' 
                AND COLUMN_NAME = '{column_name}'
                """
                
                result = self.db_service.connection_manager.execute_query_with_cache(
                    self.db_service.connection_string,
                    check_sql,
                    cache_key=f"column_check_{column_name}",
                    use_cache=True
                )
                
                if result and result[0]['column_exists'] > 0:
                    existing_columns.append(column_name)
            
            if not existing_columns:
                print("[WARNING] 本部库相关字段不存在，无法更新AI诊断状态")
                return False
            
            # 构建更新SQL
            set_clauses = []
            if "AIDiagnosisStatus" in existing_columns:
                set_clauses.append(f"AIDiagnosisStatus = {new_status}")
            if "cAICanDiagnosisTime" in existing_columns:
                set_clauses.append("cAICanDiagnosisTime = GETDATE()")
            
            if not set_clauses:
                return False
                
            # 构建更新SQL - 增加当前状态验证，确保安全更新
            update_sql = f"""
            UPDATE T_Register_Main 
            SET {', '.join(set_clauses)}
            WHERE cClientCode = '{client_code}' 
            AND AIDiagnosisStatus = {current_status}
            """
            
            # 使用本部库连接进行更新（通过当前优化数据库服务使用的连接）
            from database_service import DatabaseService
            
            # 创建本部库数据库服务来执行更新
            # 使用当前实例的数据库连接字符串（本部库）
            local_connection_string = self.db_service.connection_string
            local_db_service = DatabaseService(local_connection_string)
            
            if not local_db_service.connect():
                print("[ERROR] 无法连接本部库进行状态更新")
                return False
            
            try:
                affected_rows = local_db_service.execute_update(update_sql)
                
                if affected_rows > 0:
                    print(f"[INFO] 成功更新本部库客户 {client_code} 的AI诊断状态: {current_status} → {new_status}")
                    return True
                else:
                    print(f"[WARNING] 本部库中未找到客户 {client_code} (当前状态={current_status}) 或更新失败")
                    return False
            finally:
                local_db_service.disconnect()
                
        except Exception as e:
            print(f"[ERROR] 更新本部库AI诊断状态失败: {e}")
            return False
    
    def poll_and_send_ai_diagnosis(self, limit: int = 10, test_mode: bool = False) -> Dict[str, Any]:
        """
        轮询并发送AI诊断数据
        先发送01接口，成功后使用cClientCode发送03接口，整个完成后再修改状态
        
        Args:
            limit: 每次轮询的最大记录数
            test_mode: 测试模式，不实际发送请求
            
        Returns:
            处理结果
        """
        # 启用详细报文输出（轮询时始终显示报文）
        global VERBOSE_MESSAGE
        original_verbose = VERBOSE_MESSAGE
        VERBOSE_MESSAGE = True
        
        start_time = time.time()
        
        try:
            # 直接获取本部库的完整记录（不查询中心库）
            pending_records = self.get_pending_ai_diagnosis_records_from_local(limit)
            
            if not pending_records:
                return {
                    'success': True,
                    'message': '没有待传输的AI诊断记录（当天cCanDiagDate且AIDiagnosisStatus=1）',
                    'total': 0,
                    'sent_01': 0,
                    'sent_03': 0,
                    'failed': 0,
                    'updated': 0,
                    'processed_records': []  # 确保也有这个字段
                }
            
            total = len(pending_records)
            sent_01 = 0
            sent_03 = 0
            failed = 0
            updated = 0
            errors = []
            
            print(f"[INFO] AI诊断轮询 - 从本地库获取到 {total} 条待传输记录，将顺序调用01→03接口")
            
            # 逐条处理记录
            for i, record in enumerate(pending_records, 1):
                client_code = record.get('archiveNo', '')
                name = record.get('name', '未知')
                card_no = record.get('peno', '')
                dept_status_type = record.get('dept_status_type', '')
                current_ai_status = record.get('AIDiagnosisStatus', 0)
                
                print(f"[{i}/{total}] 处理客户: {name} (编码: {client_code}) - {dept_status_type}")
                
                # 记录同步开始日志
                log_id = None
                if not test_mode:
                    try:
                        log_id = self.sync_logger.log_sync_start(
                            interface_type="01",
                            interface_name="体检基本信息传输",
                            client_code=client_code,
                            client_name=name,
                            card_no=card_no,
                            dept_status_type=dept_status_type,
                            sync_type="AUTO",
                            request_data=self.format_exam_data(record)
                        )
                    except Exception as e:
                        print(f"[WARNING] 记录同步日志失败: {e}")
                
                # 接口调用成功标记
                interface_01_success = False
                interface_03_success = False
                processing_start = time.time()
                
                try:
                    if test_mode:
                        print(f"[TEST] 测试模式 - 01接口和03接口报文模拟")
                        
                        # 01接口测试
                        self._test_mode_show_01_message(record)
                        interface_01_success = True
                        sent_01 += 1
                        
                        # 只有分科完成的记录才需要调用03接口
                        if dept_status_type == 'completed_dept':
                            # 03接口测试
                            self._test_mode_show_03_message(client_code)
                            interface_03_success = True
                            sent_03 += 1
                        else:
                            # 分科未完成的记录不需要03接口
                            interface_03_success = True  # 标记为成功，因为不需要调用
                            print(f"[INFO] 客户 {name} 为分科未完成状态，跳过03接口调用")
                        
                    else:
                        # 实际模式：先调用01接口
                        print(f"[STEP 1] 调用01接口发送体检基本信息")
                        interface_01_result = self._send_01_interface(record)
                        
                        if interface_01_result['success']:
                            interface_01_success = True
                            sent_01 += 1
                            print(f"[SUCCESS] 客户 {name} 01接口发送成功")
                            
                            # 只有分科完成的记录才需要调用03接口
                            if dept_status_type == 'completed_dept':
                                # 接着调用03接口
                                print(f"[STEP 2] 调用03接口发送科室结果")
                                interface_03_result = self._send_03_interface(client_code)
                                
                                if interface_03_result['success']:
                                    interface_03_success = True
                                    sent_03 += 1
                                    print(f"[SUCCESS] 客户 {name} 03接口发送成功")
                                else:
                                    print(f"[FAILED] 客户 {name} 03接口发送失败: {interface_03_result.get('error', '未知错误')}")
                                    errors.append(f"客户 {name} 03接口失败: {interface_03_result.get('error', '未知错误')}")
                            else:
                                # 分科未完成的记录不需要03接口
                                interface_03_success = True
                                print(f"[INFO] 客户 {name} 为分科未完成状态，跳过03接口调用")
                        else:
                            print(f"[FAILED] 客户 {name} 01接口发送失败: {interface_01_result.get('error', '未知错误')}")
                            errors.append(f"客户 {name} 01接口失败: {interface_01_result.get('error', '未知错误')}")
                    
                    # 只有所有需要的接口都成功才更新状态
                    if interface_01_success and interface_03_success:
                        print(f"[STEP 3] 所有接口完成，更新本地库状态")
                        
                        # 根据记录类型确定新的状态值
                        if dept_status_type == 'incomplete_dept':
                            # 分科未完成：2 → 1 (传输成功后设置为1，等待分科完成)
                            new_status = 1
                            print(f"[INFO] 分科未完成记录，状态更新: {current_ai_status} → {new_status}")
                        else:
                            # 分科完成：1 → 2 (传输完成)
                            new_status = 2
                            print(f"[INFO] 分科完成记录，状态更新: {current_ai_status} → {new_status}")
                        
                        if self.update_ai_diagnosis_status(client_code, current_ai_status, new_status):
                            updated += 1
                            print(f"[SUCCESS] 客户 {name} 本地库状态已更新")
                            
                            # 记录同步成功日志
                            if log_id and not test_mode:
                                try:
                                    processing_time = int((time.time() - processing_start) * 1000)
                                    self.sync_logger.log_sync_success(
                                        log_id=log_id,
                                        interface_01_status="SUCCESS" if interface_01_success else "FAILED",
                                        interface_03_status="SUCCESS" if interface_03_success else ("SKIPPED" if dept_status_type == 'incomplete_dept' else "FAILED"),
                                        status_updated=True,
                                        processing_time=processing_time,
                                        remarks=f"状态更新: {current_ai_status} → {new_status}"
                                    )
                                except Exception as e:
                                    print(f"[WARNING] 记录成功日志失败: {e}")
                        else:
                            print(f"[WARNING] 客户 {name} 本地库状态更新失败")
                    else:
                        failed += 1
                        print(f"[FAILED] 客户 {name} 接口调用未全部成功，不更新状态")
                        
                        # 记录同步失败日志
                        if log_id and not test_mode:
                            try:
                                processing_time = int((time.time() - processing_start) * 1000)
                                error_msg = f"接口调用失败 - 01接口:{'成功' if interface_01_success else '失败'} 03接口:{'成功' if interface_03_success else '失败'}"
                                self.sync_logger.log_sync_failed(
                                    log_id=log_id,
                                    error_message=error_msg,
                                    interface_01_status="SUCCESS" if interface_01_success else "FAILED",
                                    interface_03_status="SUCCESS" if interface_03_success else "FAILED",
                                    processing_time=processing_time
                                )
                            except Exception as e:
                                print(f"[WARNING] 记录失败日志失败: {e}")
                
                except Exception as e:
                    failed += 1
                    error_msg = f"客户 {name} 处理异常: {str(e)}"
                    print(f"[ERROR] {error_msg}")
                    errors.append(error_msg)
                    
                    # 记录处理异常日志
                    if log_id and not test_mode:
                        try:
                            processing_time = int((time.time() - processing_start) * 1000)
                            self.sync_logger.log_sync_failed(
                                log_id=log_id,
                                error_message=error_msg,
                                processing_time=processing_time
                            )
                        except Exception as log_e:
                            print(f"[WARNING] 记录异常日志失败: {log_e}")
            
            # 记录批次汇总日志
            if not test_mode and total > 0:
                try:
                    self.sync_logger.log_batch_summary(
                        total_count=total,
                        success_count=updated,
                        failed_count=failed,
                        interface_type="01",
                        interface_name="AI诊断轮询批次"
                    )
                except Exception as e:
                    print(f"[WARNING] 记录批次汇总失败: {e}")
            
            # 返回结果
            success_rate_01 = (sent_01 / total * 100) if total > 0 else 0
            success_rate_03 = (sent_03 / total * 100) if total > 0 else 0
            message = f"AI诊断轮询完成 - 01接口: {sent_01}/{total} ({success_rate_01:.1f}%), 03接口: {sent_03}/{total} ({success_rate_03:.1f}%), 状态更新: {updated}"
            
            # 收集处理记录的详细信息
            processed_records = []
            for record in pending_records:
                client_code = record.get('archiveNo', '')
                name = record.get('name', '未知')
                peno = record.get('peno', '')
                dept_status_type = record.get('dept_status_type', '')
                current_ai_status = record.get('AIDiagnosisStatus', 0)
                
                # 获取门店编码
                org_code = self.org_config.get('org_code', 'DEFAULT')
                shop_code = self.org_config.get('shop_code', '')
                
                processed_records.append({
                    'client_code': client_code,
                    'name': name,
                    'peno': peno,
                    'shop_code': shop_code,
                    'org_code': org_code,
                    'dept_status_type': dept_status_type,
                    'ai_status': current_ai_status
                })
            
            return {
                'success': failed == 0,
                'message': message,
                'total': total,
                'sent_01': sent_01,
                'sent_03': sent_03,
                'sent': updated,  # 兼容性字段，表示完全成功的记录数
                'failed': failed,
                'updated': updated,
                'errors': errors,
                'processed_records': processed_records  # 新增：处理记录的详细信息
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'AI诊断轮询异常: {str(e)}',
                'total': 0,
                'sent_01': 0,
                'sent_03': 0,
                'sent': 0,
                'failed': 0,
                'updated': 0,
                'processed_records': []  # 确保异常情况下也有这个字段
            }
        finally:
            # 恢复原来的VERBOSE_MESSAGE设置
            VERBOSE_MESSAGE = original_verbose

    def _send_01_interface(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送01接口
        
        Args:
            record: 体检记录数据
            
        Returns:
            发送结果
        """
        try:
            # 格式化数据
            formatted_data = self.format_exam_data(record)
            
            # 发送请求
            return self._send_request(formatted_data)
            
        except Exception as e:
            return {
                'success': False,
                'error': f'01接口调用异常: {str(e)}'
            }

    def _send_03_interface(self, client_code: str) -> Dict[str, Any]:
        """
        发送03接口（科室结果）
        
        Args:
            client_code: 客户编码
            
        Returns:
            发送结果
        """
        try:
            # 导入03接口
            from interface_03_deptInfo import TianjianInterface03
            
            # 创建03接口实例，使用相同的配置
            interface_03 = TianjianInterface03(self.api_config, self.org_config)
            
            # 调用03接口发送科室结果
            result = interface_03.sync_dept_results(
                client_codes=[client_code],
                limit=100,
                test_mode=False,
                batch_size=1,
                verbose_message=True
            )
            
            if result.get('success'):
                return {
                    'success': True,
                    'response': result
                }
            else:
                return {
                    'success': False,
                    'error': result.get('error', '03接口发送失败')
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'03接口调用异常: {str(e)}'
            }

    def _test_mode_show_01_message(self, record: Dict[str, Any]):
        """
        测试模式下显示01接口报文
        
        Args:
            record: 体检记录数据
        """
        try:
            # 格式化数据以便显示报文格式
            formatted_data = self.format_exam_data(record)
            
            # 显示01接口报文内容
            print("=" * 80)
            print("【01号接口】测试模式 - 模拟HTTP请求报文")
            print("=" * 80)
            print(f"请求URL: {self.api_config['base_url']}/dx/inter/sendPeInfo")
            print(f"请求方法: POST")
            print("请求头:")
            headers = self.create_headers()
            for key, value in headers.items():
                print(f"  {key}: {value}")
            print("请求体:")
            import json
            print(json.dumps(formatted_data, ensure_ascii=False, indent=2))
            print("=" * 80)
            print("【01号接口】测试模式完成，不会实际发送请求")
            print("=" * 80)
            
        except Exception as e:
            print(f"[ERROR] 01接口报文显示异常: {str(e)}")

    def _test_mode_show_03_message(self, client_code: str):
        """
        测试模式下显示03接口报文
        
        Args:
            client_code: 客户编码
        """
        try:
            # 导入03接口并获取数据
            from interface_03_deptInfo import TianjianInterface03
            
            # 创建03接口实例，使用相同的配置
            interface_03 = TianjianInterface03(self.api_config, self.org_config)
            
            # 获取科室结果数据（用于显示报文）
            dept_results = interface_03.get_dept_results_data(
                client_codes=[client_code],
                limit=100
            )
            
            print("=" * 80)
            print("【03号接口】测试模式 - 模拟HTTP请求报文")
            print("=" * 80)
            print(f"请求URL: {self.api_config['base_url']}/dx/inter/deptInfo")
            print(f"请求方法: POST")
            print("请求头:")
            headers = interface_03.create_headers()
            for key, value in headers.items():
                print(f"  {key}: {value}")
            print("请求体:")
            
            if dept_results:
                import json
                print(json.dumps(dept_results, ensure_ascii=False, indent=2))
            else:
                print(f"[INFO] 客户 {client_code} 没有找到科室结果数据")
                print("[]")
            
            print("=" * 80)
            print("【03号接口】测试模式完成，不会实际发送请求")
            print("=" * 80)
            
        except Exception as e:
            print(f"[ERROR] 03接口报文显示异常: {str(e)}")
            # 即使出错也显示基本格式
            print("=" * 80)
            print("【03号接口】测试模式 - 模拟HTTP请求报文（简化版）")
            print("=" * 80)
            print(f"请求URL: {self.api_config['base_url']}/dx/inter/deptInfo")
            print(f"请求方法: POST")
            print(f"客户编码: {client_code}")
            print("请求体: []  # 获取数据失败")
            print("=" * 80)
            print("【03号接口】测试模式完成")
            print("=" * 80)

    def send_exam_info(self, client_code: str = None, days: int = None, limit: int = None, test_mode: bool = False) -> Dict[str, Any]:
        """
        发送体检信息到天健云
        
        Args:
            client_code: 特定客户编号
            days: 获取最近N天的数据
            limit: 限制发送条数
            test_mode: 测试模式，不实际发送请求
            
        Returns:
            发送结果
        """
        # 数据库连接检查（OptimizedDatabaseService自动管理连接）
        try:
            # 测试数据库连接
            test_query = "SELECT 1 as test"
            self.db_service.connection_manager.execute_query_with_cache(
                self.db_service.connection_string,
                test_query,
                cache_key="connection_test",
                use_cache=False
            )

            # 打印实际连接的数据库信息
            print("="*80)
            print("数据库连接信息")
            print("="*80)
            print(f"连接字符串: {self.db_service.connection_string}")

            # 获取数据库服务器详细信息
            db_info_query = "SELECT @@SERVERNAME as server_name, DB_NAME() as database_name"

            db_info_result = self.db_service.connection_manager.execute_query_with_cache(
                self.db_service.connection_string,
                db_info_query,
                cache_key="db_info_check",
                use_cache=False
            )

            if db_info_result:
                info = db_info_result[0]
                print(f"实际服务器名: {info['server_name']}")
                print(f"实际数据库名: {info['database_name']}")



            print("="*80)

        except Exception as e:
            return {
                'success': False,
                'error': f'数据库连接失败: {e}',
                'total': 0,
                'sent': 0,
                'failed': 0
            }
        
        try:
            # 设置默认参数
            if days is None:
                days = 180  # 增加默认天数到180天，确保能获取到更多数据
            if limit is None:
                limit = 10

            # 首先检查cType字段是否存在
            check_column_sql = """
            SELECT COUNT(*) as column_exists
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'T_Register_Main' AND COLUMN_NAME = 'cType'
            """

            column_check = self.db_service.connection_manager.execute_query_with_cache(
                self.db_service.connection_string,
                check_column_sql,
                cache_key="check_ctype_column",
                use_cache=True
            )

            has_ctype = column_check[0]['column_exists'] > 0 if column_check else False

            # 根据字段存在情况构建SQL
            if has_ctype:
                sql = f"""
                SELECT TOP ({limit})
                    rm.cClientCode as archiveNo,
                    rm.cName as name,
                    rm.cSex as sex,
                    rm.dBornDate as dBirthday,
                    rm.cIdCard as cert_id,
                    rm.cTel as mobile,
                    rm.dOperdate as exam_date,
                    rm.cStatus,
                    rm.cMarryFlag,
                    rm.cCardNo as peno,
                    rm.cSuitCode,
                    rm.cContractCode,
                    rm.cCommanSuitName,
                    rm.cType,
                    rm.cUnitsFlag,
                    rm.iDeptIndex,
                    rm.cCanDiagDate,
                    ISNULL(u.cName, '') as company_name,
                    '' as work_dept_name,
                    ISNULL(u.cCode, '') as units_code,
                    ISNULL(us.cName, '') as suit_name
                FROM T_Register_Main rm
                LEFT JOIN T_Contract tc ON rm.cContractCode = tc.cCode
                LEFT JOIN T_Units u ON tc.cUnitsCode = u.cCode
                LEFT JOIN T_UnitsSuit_Master us ON rm.cSuitCode = us.cSuitCode
                WHERE rm.dAffirmdate >= DATEADD(day, -{days}, GETDATE())
                AND rm.cShopCode = '{self.org_config.get('shop_code')}'
                ORDER BY rm.dOperdate DESC
                """
            else:
                sql = f"""
                SELECT TOP ({limit})
                    rm.cClientCode as archiveNo,
                    rm.cName as name,
                    rm.cSex as sex,
                    rm.dBornDate as dBirthday,
                    rm.cIdCard as cert_id,
                    rm.cTel as mobile,
                    rm.dOperdate as exam_date,
                    rm.cStatus,
                    rm.cMarryFlag,
                    rm.cCardNo as peno,
                    rm.cSuitCode,
                    rm.cContractCode,
                    rm.cCommanSuitName,
                    '健康体检' as cType,
                    rm.cUnitsFlag,
                    rm.iDeptIndex,
                    rm.cCanDiagDate,
                    ISNULL(u.cName, '') as company_name,
                    '' as work_dept_name,
                    ISNULL(u.cCode, '') as units_code,
                    ISNULL(us.cName, '') as suit_name
                FROM T_Register_Main rm
                LEFT JOIN T_Contract tc ON rm.cContractCode = tc.cCode
                LEFT JOIN T_Units u ON tc.cUnitsCode = u.cCode
                LEFT JOIN T_UnitsSuit_Master us ON rm.cSuitCode = us.cSuitCode
                WHERE rm.dAffirmdate >= DATEADD(day, -{days}, GETDATE())
                AND rm.cShopCode = '{self.org_config.get('shop_code')}'
                ORDER BY rm.dOperdate DESC
                """

            # 使用优化的数据库服务执行查询
            exam_records = self.db_service.connection_manager.execute_query_with_cache(
                self.db_service.connection_string,
                sql,
                cache_key=f"exam_records_{days}_{limit}",
                use_cache=False
            )

            print(f"1. 获取数据库数据 - 共 {len(exam_records)} 条记录")
            for i, record in enumerate(exam_records[:5], 1):  # 显示前5条
                print(f"   - {record.get('name', '未知')}: {record.get('archiveNo', '未知')}")
            if len(exam_records) > 5:
                print(f"   ... 还有 {len(exam_records)-5} 条记录")

        except Exception as e:
            print(f"[FAIL] 获取体检数据失败: {e}")
            return {
                'success': False,
                'error': f'获取体检数据失败: {e}',
                'total': 0,
                'sent': 0,
                'failed': 0
            }

        if not exam_records:
            return {
                'success': True,
                'message': '没有找到符合条件的体检记录',
                'total': 0,
                'sent': 0,
                'failed': 0
            }

        # 发送结果统计
        total = len(exam_records)
        sent = 0
        failed = 0
        errors = []

        print(f"2. 数据格式化完成 - {total} 条")

        if test_mode:
            print("3. 测试模式 - 数据格式验证")
            print("   API地址: http://203.83.237.114:9300/dx/inter/sendPeInfo")
            print("   请求方法: POST")
            print("   认证方式: MD5签名")
        else:
            print("3. 正式模式 - 调用真实01号接口")
            print("   API地址: http://203.83.237.114:9300/dx/inter/sendPeInfo")
            print("   请求方法: POST")
            print("   认证方式: MD5签名")
            print("   正在准备API调用...")

        # 逐条发送
        for i, record in enumerate(exam_records, 1):
                try:
                    print(f"   正在发送第 {i}/{total} 条记录到: {self.api_config['base_url']}/dx/inter/sendPeInfo")

                    # 格式化数据
                    request_data = self.format_exam_data(record)

                    if test_mode:
                        print("   测试模式 - 显示请求数据格式:")
                        print(json.dumps(request_data, ensure_ascii=False, indent=2))
                        sent += 1
                        continue

                    # 发送请求
                    result = self._send_request(request_data)

                    if result['success']:
                        sent += 1
                        print(f"   [SUCCESS] 第 {i} 条记录发送成功")
                        if VERBOSE_MESSAGE:
                            print("   " + "="*50)
                    else:
                        failed += 1
                        error_msg = f"发送失败: {result.get('error', '未知错误')}"
                        print(f"   [FAIL] 第 {i} 条记录{error_msg}")
                        errors.append(f"记录 {record.get('archiveNo', '')}: {error_msg}")
                        if VERBOSE_MESSAGE:
                            print("   " + "="*50)

                except Exception as e:
                    failed += 1
                    error_msg = f"处理异常: {str(e)}"
                    print(f"   [FAIL] 第 {i} 条记录{error_msg}")
                    errors.append(f"记录 {record.get('archiveNo', '')}: {error_msg}")
                    if VERBOSE_MESSAGE:
                        print("   " + "="*50)

        # 输出最终结果
        if test_mode:
            print("4. 测试结果")
            print("   [SUCCESS] 01号接口测试通过")
            print(f"   [SUCCESS] 数据验证成功: {total} 条记录")
            print("   [SUCCESS] 格式验证通过")
        else:
            print("4. 实际发送结果")
            print("   [SUCCESS] 01号接口发送成功" if failed == 0 else "   [FAIL] 01号接口发送部分失败")
            print(f"   [SUCCESS] 总计: {total}, 成功: {sent}, 失败: {failed}")
            if failed == 0:
                print(f"   [OK] 01号接口发送完成，共处理 {total} 条记录")

        # 返回结果
        result = {
            'success': failed == 0,
            'total': total,
            'sent': sent,
            'failed': failed,
            'errors': errors
        }

        if test_mode:
            result['message'] = f"01号接口测试成功，验证 {total} 条记录"
        else:
            result['message'] = f"01号接口发送完成，共处理 {total} 条记录"

        return result
    
    def send_pe_info(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送体检信息接口（API适配方法）
        这个方法是为了兼容GUI调用而添加的适配器
        
        Args:
            data: 接收到的请求数据
            
        Returns:
            标准的天健云API响应格式
        """
        # 启用详细报文输出（GUI调用时显示完整HTTP报文）
        global VERBOSE_MESSAGE
        original_verbose = VERBOSE_MESSAGE
        VERBOSE_MESSAGE = True
        
        try:
            # 获取机构编码
            org_code = self.org_config.get('org_code', 'DEFAULT')
            
            # 调用现有的send_exam_info方法
            result = self.send_exam_info(
                client_code=None,
                days=180,
                limit=100, 
                test_mode=False
            )
            
            # 简化日志输出
            if result.get('success'):
                sent_count = result.get('sent', 0)
                total_count = result.get('total', 0)
                print(f"01号接口 | 机构编码:{org_code} | 处理:{total_count}条 | 传输成功:{sent_count}条")
                
                response_data = {
                    'code': 0,
                    'msg': '传输成功',
                    'data': result.get('data', [])
                }
                return response_data
            else:
                error_msg = result.get('error', '未知错误')
                print(f"01号接口 | 机构编码:{org_code} | 传输失败:{error_msg}")
                
                response_data = {
                    'code': -1,
                    'msg': f"传输失败: {error_msg}",
                    'data': []
                }
                return response_data
                
        except Exception as e:
            org_code = self.org_config.get('org_code', 'DEFAULT')
            print(f"01号接口 | 机构编码:{org_code} | 传输失败:异常{str(e)}")
            
            error_response = {
                'code': -1,
                'msg': f'传输失败: {str(e)}',
                'data': []
            }
            return error_response
        finally:
            # 恢复原来的VERBOSE_MESSAGE设置
            VERBOSE_MESSAGE = original_verbose

    def _send_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送HTTP请求到天健云

        Args:
            request_data: 请求数据

        Returns:
            请求结果
        """
        url = f"{self.api_config['base_url']}/dx/inter/sendPeInfo"
        headers = self.create_headers()

        # 始终输出详细的HTTP请求信息到日志
        print("="*80)
        print("【01号接口】HTTP请求详情")
        print("="*80)
        print(f"请求URL: {url}")
        print(f"请求方法: POST")
        print("请求头:")
        for key, value in headers.items():
            print(f"  {key}: {value}")
        print("请求体:")
        print(json.dumps(request_data, ensure_ascii=False, indent=2))
        print("="*80)

        # 输出详细的HTTP请求信息（与VERBOSE_MESSAGE无关）
        if VERBOSE_MESSAGE:
            print("   " + "="*50)
            print("   --- HTTP请求信息 ---")
            print("   HTTP请求详情:")
            print(f"   请求URL: {url}")
            print(f"   请求方法: POST")
            print("   请求头:")
            for key, value in headers.items():
                print(f"     {key}: {value}")
            print("   --- 请求体 ---")
            print(f"   {json.dumps(request_data, ensure_ascii=False, indent=2)}")
            print("   " + "="*50)

        try:
            response = requests.post(
                url,
                headers=headers,
                json=request_data,
                timeout=self.api_config.get('timeout', 30),
                verify=False
            )

            # 始终输出详细的HTTP响应信息到日志
            print("="*80)
            print("【01号接口】HTTP响应详情")
            print("="*80)
            print(f"响应状态码: {response.status_code}")
            print("响应头:")
            for key, value in response.headers.items():
                print(f"  {key}: {value}")
            print("响应体:")

            # 输出详细的HTTP响应信息（与VERBOSE_MESSAGE无关）
            if VERBOSE_MESSAGE:
                print("   --- HTTP响应信息 ---")
                print("   HTTP响应详情:")
                print(f"   响应状态码: {response.status_code}")
                print("   响应头:")
                for key, value in response.headers.items():
                    print(f"     {key}: {value}")
                print("   --- 响应体 ---")

            if response.status_code == 200:
                try:
                    response_json = response.json()

                    # 始终输出响应JSON到日志
                    print(json.dumps(response_json, ensure_ascii=False, indent=2))
                    print("="*80)

                    if VERBOSE_MESSAGE:
                        print(f"   {json.dumps(response_json, ensure_ascii=False, indent=2)}")

                    if response_json.get('code') == 0:
                        return {
                            'success': True,
                            'response': response_json
                        }
                    else:
                        return {
                            'success': False,
                            'error': f"API错误: code={response_json.get('code')}, msg={response_json.get('msg', '未知错误')}",
                            'response': response_json
                        }
                except json.JSONDecodeError:
                    # 始终输出响应文本到日志
                    print(response.text)
                    print("="*80)
                    
                    if VERBOSE_MESSAGE:
                        print(f"   {response.text}")
                    return {
                        'success': False,
                        'error': f"响应解析失败: {response.text}",
                        'response': response.text
                    }
            else:
                # 始终输出错误响应到日志
                print(f"HTTP错误: {response.status_code}")
                print(response.text)
                print("="*80)
                
                return {
                    'success': False,
                    'error': f"HTTP错误: {response.status_code}",
                    'response': response.text
                }
        
        except requests.exceptions.Timeout:
            error_msg = "请求超时"
            print(f"错误: {error_msg}")
            print("="*80)
            return {
                'success': False,
                'error': error_msg
            }
        except requests.exceptions.ConnectionError:
            error_msg = "连接错误"
            print(f"错误: {error_msg}")
            print("="*80)
            return {
                'success': False,
                'error': error_msg
            }
        except Exception as e:
            error_msg = f"请求异常: {str(e)}"
            print(f"错误: {error_msg}")
            print("="*80)
            return {
                'success': False,
                'error': error_msg
            }


# 全局变量：是否启用详细报文输出
VERBOSE_MESSAGE = False


def main():
    """主函数 - 支持命令行参数和GUI调用"""
    import argparse
    global VERBOSE_MESSAGE

    # 防止在GUI子进程中启动GUI
    if os.environ.get('GUI_SUBPROCESS') == '1' or os.environ.get('TIANJIAN_NO_GUI') == '1':
        # 确保不会启动任何GUI组件
        import sys
        if 'gui_main' in sys.modules:
            del sys.modules['gui_main']
        # 设置严格的无GUI模式
        os.environ['NO_GUI_MODE'] = '1'

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='天健云01号接口 - 单次体检基本信息传输')
    parser.add_argument('--test-mode', action='store_true', help='测试模式，不实际发送请求')
    parser.add_argument('--limit', type=int, default=10, help='限制处理的记录数量')
    parser.add_argument('--days', type=int, default=30, help='获取最近N天的数据')
    parser.add_argument('--batch-size', type=int, default=10, help='批处理大小（暂未使用）')
    parser.add_argument('--verbose-message', action='store_true', help='输出详细的HTTP报文信息')

    args = parser.parse_args()

    # 设置详细输出标志
    VERBOSE_MESSAGE = args.verbose_message

    print("天健云01号接口测试 - 体检基本信息传输")
    print("=" * 60)

    # 创建接口实例（使用统一配置）
    interface = TianjianInterface01()

    try:
        if args.test_mode:
            print(f"1. 测试模式 - 数据格式验证")
            print(f"   获取最近{args.days}天的数据，限制{args.limit}条记录")
            result = interface.send_exam_info(days=args.days, limit=args.limit, test_mode=True)
        else:
            print(f"1. 正式模式 - 调用真实01号接口")
            print(f"   获取最近{args.days}天的数据，限制{args.limit}条记录")
            result = interface.send_exam_info(days=args.days, limit=args.limit, test_mode=False)

        # 输出结果
        if result.get('success'):
            print(f"2. 执行结果")
            print(f"   [SUCCESS] 01号接口执行成功")
            print(f"   [SUCCESS] 总计: {result.get('total', 0)}, 成功: {result.get('sent', 0)}, 失败: {result.get('failed', 0)}")
            if result.get('message'):
                print(f"   [INFO] {result['message']}")
        else:
            print(f"2. 执行结果")
            print(f"   [FAIL] 01号接口执行失败")
            if result.get('error'):
                print(f"   [ERROR] {result['error']}")

    except Exception as e:
        print(f"[ERROR] 接口执行异常: {e}")
        return {
            'success': False,
            'error': str(e),
            'total': 0,
            'sent': 0,
            'failed': 0
        }


if __name__ == '__main__':
    main()