#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云02号接口实现 - 申请项目字典数据传输
/dx/inter/syncApplyItem

数据库表关系：
- code_Item_Price (申请项目主表) - 价格项目作为申请项目
- code_Item_Price.cMainCode → Code_Item_Main.cCode (关联申请项目基础信息)
- Code_Item_Main.cCode → Code_Item_Detail.cMainCode (获取检查项目明细)
- Code_Item_Main.cCode → Code_Dept_Main.cMainCode → Code_Dept_dict.cCode (科室信息)
"""

import json
import hashlib
import requests
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from config import Config
from optimized_database_service import create_optimized_db_service
from multi_org_config import get_current_org_config
from org_code_prefix_manager import create_org_prefix_manager


class TianjianInterface02:
    """天健云02号接口 - 申请项目字典数据传输"""

    def __init__(self, api_config: Dict[str, Any] = None, org_config: Dict[str, Any] = None):
        """
        初始化接口配置

        Args:
            api_config: API配置信息（可选，默认使用中心库配置）
            org_config: 机构配置信息（可选，默认使用当前机构配置）
        """
        # 获取当前机构配置
        self.org_config = org_config or get_current_org_config()

        # 使用机构配置中的天健云API配置
        if api_config:
            self.api_config = api_config
        else:
            self.api_config = {
                'base_url': self.org_config.get('tianjian_base_url', 'http://**************:9300'),
                'api_key': self.org_config.get('tianjian_api_key', ''),
                'mic_code': self.org_config.get('tianjian_mic_code', ''),
                'misc_id': self.org_config.get('tianjian_misc_id', ''),
                'timeout': 30
            }

        # 创建优化的数据库服务实例，使用机构配置中的数据库连接
        if self.org_config.get('db_host'):
            connection_string = (
                f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                f"SERVER={self.org_config['db_host']},{self.org_config.get('db_port', 1433)};"
                f"DATABASE={self.org_config['db_name']};"
                f"UID={self.org_config['db_user']};"
                f"PWD={self.org_config['db_password']}"
            )
        else:
            # 兜底使用统一配置
            connection_string = Config.get_interface_db_connection_string()

        self.db_service = create_optimized_db_service(connection_string)

        # 创建机构编码前缀管理器
        org_code = self.org_config.get('org_code', 'DEFAULT')
        self.prefix_manager = create_org_prefix_manager(org_code)
    
    def generate_signature(self, api_key: str, timestamp: str) -> str:
        """生成MD5签名"""
        sign_string = api_key + timestamp
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    def create_headers(self) -> Dict[str, str]:
        """创建请求头"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        nonce = str(uuid.uuid4())
        signature = self.generate_signature(self.api_config['api_key'], timestamp)
        
        return {
            'Content-Type': 'application/json',
            'sign': signature,
            'timestamp': timestamp,
            'nonce': nonce,
            'mic-code': self.api_config['mic_code'],
            'misc-id': self.api_config['misc_id']
        }
    
    def get_apply_items_data(self, limit: int = None) -> List[Dict[str, Any]]:
        """
        获取申请项目字典数据
        使用code_Item_Price作为申请项目主表，通过Code_Item_Main关联Code_Item_Detail获取检查项目明细

        表关系：
        code_Item_Price.cMainCode → Code_Item_Main.cCode → Code_Item_Detail.cMainCode

        Args:
            limit: 限制返回条数

        Returns:
            申请项目字典数据列表
        """
        try:
            # 获取申请项目主表数据（使用code_Item_Price作为申请项目）
            # 移除TOP限制，获取所有有效的申请项目
            sql_main = """
            SELECT
                ip.cCode as applyItemId,
                ip.cName as applyItemName,
                CAST(ROW_NUMBER() OVER (ORDER BY ip.cCode) AS VARCHAR) as displaySequence,
                ISNULL(dm.cDeptCode, 'UNKNOWN') as deptId,
                ip.fPrice as price,
                ip.cExplain as explain,
                ip.cSexType as sexType
            FROM code_Item_Price ip
            LEFT JOIN Code_Item_Main im ON ip.cMainCode = im.cCode
            LEFT JOIN Code_Dept_Main dm ON im.cCode = dm.cMainCode
            WHERE (ip.cStopTag = '0' OR ip.cStopTag IS NULL)
            AND (im.cStopTag = '0' OR im.cStopTag IS NULL)
            ORDER BY ip.cCode
            """

            apply_items = self.db_service.connection_manager.execute_query_with_cache(
                self.db_service.connection_string,
                sql_main,
                cache_key="apply_items_price_all",  # 更新缓存键名
                use_cache=False
            )

            print(f"[OK] 获取到 {len(apply_items)} 个申请项目（基于价格表）")

            # 为每个申请项目获取检查项目明细
            result = []
            for i, item in enumerate(apply_items, 1):
                apply_item_id = item['applyItemId']

                # 通过code_Item_Price关联Code_Item_Main，再关联Code_Item_Detail获取检查项目明细
                sql_detail = f"""
                SELECT
                    id.cCode as checkItemId,
                    id.cName as checkItemName,
                    CAST(ROW_NUMBER() OVER (ORDER BY id.cCode) AS VARCHAR) as displaySequence,
                    id.cUnit as unit,
                    id.cConsult as referenceRange
                FROM code_Item_Price ip
                INNER JOIN Code_Item_Main im ON ip.cMainCode = im.cCode
                INNER JOIN Code_Item_Detail id ON im.cCode = id.cMainCode
                WHERE ip.cCode = '{apply_item_id}'
                AND (id.cStopTag = '0' OR id.cStopTag IS NULL)
                ORDER BY id.cCode
                """

                check_items = self.db_service.connection_manager.execute_query_with_cache(
                    self.db_service.connection_string,
                    sql_detail,
                    cache_key=f"check_items_detail_{apply_item_id}",  # 更新缓存键名
                    use_cache=False
                )

                # 如果没有找到检查项目明细，则使用申请项目本身作为检查项目
                if not check_items:
                    check_items = [{
                        "checkItemId": apply_item_id,
                        "checkItemName": item['applyItemName'],
                        "displaySequence": "1",
                        "unit": "",
                        "referenceRange": ""
                    }]

                # 构建申请项目数据
                apply_item_data = {
                    "applyItemId": apply_item_id,
                    "applyItemName": item['applyItemName'],
                    "displaySequence": str(i),  # 使用序号作为显示顺序
                    "deptId": item['deptId'],
                    "price": item.get('price', 0),
                    "explain": item.get('explain', ''),
                    "sexType": item.get('sexType', ''),
                    "checkItemList": check_items
                }
                result.append(apply_item_data)

                if i <= 3:  # 只显示前3个的详细信息
                    print(f"   - {item['applyItemName']}: {apply_item_id}, 价格: {item.get('price', 0)}, 包含 {len(check_items)} 个检查项目")

            return result

        except Exception as e:
            print(f"[ERROR] 获取申请项目数据失败: {e}")
            return []
    
    def sync_apply_items(self, limit: int = None, test_mode: bool = False, batch_size: int = 50, verbose_message: bool = False) -> Dict[str, Any]:
        """
        同步申请项目字典数据到天健云
        
        Args:
            limit: 限制同步条数
            test_mode: 测试模式，不实际发送请求
            batch_size: 批量发送大小
            
        Returns:
            同步结果
        """
        try:
            # 获取申请项目数据
            apply_items = self.get_apply_items_data(limit)

            if not apply_items:
                return {
                    'success': True,
                    'message': '没有找到申请项目数据',
                    'total': 0,
                    'sent': 0,
                    'failed': 0
                }

            # 为每个申请项目添加机构编码前缀
            processed_items = []
            for item in apply_items:
                processed_item = self.prefix_manager.process_data(item, 'syncApplyItem')
                processed_items.append(processed_item)

            apply_items = processed_items
            
            total = len(apply_items)
            sent = 0
            failed = 0
            errors = []
            
            print(f"准备同步 {total} 个申请项目到天健云02号接口")

            # 显示纯净报文（实际发送模式）
            if verbose_message and not test_mode:
                print("\n" + "="*80)
                print("【02号接口】纯净报文内容")
                print("="*80)
                print(json.dumps(apply_items, ensure_ascii=False, indent=2))
                print("="*80)
                print("【02号接口】纯净报文结束")
                print("="*80)

            if test_mode:
                print("测试模式 - 显示前3个申请项目的数据格式:")
                for i, item in enumerate(apply_items[:3], 1):
                    print(f"\n第 {i} 个申请项目:")
                    print(json.dumps(item, ensure_ascii=False, indent=2))

                # 显示纯净报文
                if verbose_message:
                    print("\n" + "="*80)
                    print("【02号接口】纯净报文内容")
                    print("="*80)
                    print(json.dumps(apply_items, ensure_ascii=False, indent=2))
                    print("="*80)
                    print("【02号接口】纯净报文结束")
                    print("="*80)

                return {
                    'success': True,
                    'message': f"测试模式完成 - 共 {total} 个申请项目格式正确",
                    'total': total,
                    'sent': total,
                    'failed': 0,
                    'errors': []
                }
            
            # 分批发送
            for i in range(0, total, batch_size):
                batch = apply_items[i:i + batch_size]
                batch_num = i // batch_size + 1
                total_batches = (total + batch_size - 1) // batch_size
                
                print(f"\n发送第 {batch_num}/{total_batches} 批次，包含 {len(batch)} 个申请项目")
                
                try:
                    # 发送请求
                    result = self._send_request(batch)
                    
                    if result['success']:
                        sent += len(batch)
                        print(f"✓ 第 {batch_num} 批次发送成功")
                    else:
                        failed += len(batch)
                        error_msg = f"第 {batch_num} 批次发送失败: {result.get('error', '未知错误')}"
                        print(f"✗ {error_msg}")
                        errors.append(error_msg)
                
                except Exception as e:
                    failed += len(batch)
                    error_msg = f"第 {batch_num} 批次处理异常: {str(e)}"
                    print(f"✗ {error_msg}")
                    errors.append(error_msg)
            
            # 返回结果
            return {
                'success': failed == 0,
                'message': f"同步完成 - 成功: {sent}, 失败: {failed}",
                'total': total,
                'sent': sent,
                'failed': failed,
                'errors': errors
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"同步过程异常: {str(e)}",
                'total': 0,
                'sent': 0,
                'failed': 0
            }
    
    def _send_request(self, request_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        发送HTTP请求到天健云
        
        Args:
            request_data: 请求数据
            
        Returns:
            请求结果
        """
        url = f"{self.api_config['base_url']}/dx/inter/syncApplyItem"
        headers = self.create_headers()
        
        try:
            response = requests.post(
                url,
                headers=headers,
                json=request_data,
                timeout=self.api_config.get('timeout', 30),
                verify=False
            )
            
            if response.status_code == 200:
                try:
                    response_json = response.json()
                    if response_json.get('code') == 0:
                        return {
                            'success': True,
                            'response': response_json
                        }
                    else:
                        return {
                            'success': False,
                            'error': f"API错误: code={response_json.get('code')}, msg={response_json.get('msg', '未知错误')}",
                            'response': response_json
                        }
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': f"响应解析失败: {response.text}",
                        'response': response.text
                    }
            else:
                return {
                    'success': False,
                    'error': f"HTTP错误: {response.status_code}",
                    'response': response.text
                }
        
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'error': "请求超时"
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'error': "连接错误"
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"请求异常: {str(e)}"
            }

    def sync_apply_item(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        申请项目字典传输接口（GUI调用适配方法）
        
        Args:
            data: 接收到的请求数据
            
        Returns:
            标准的天健云API响应格式
        """
        try:
            # 获取机构编码
            org_code = self.org_config.get('org_code', 'DEFAULT')
            
            # 调用现有的sync_apply_items方法，不限制数量
            result = self.sync_apply_items(
                limit=None,  # 不限制数量，传输所有数据
                test_mode=False,
                batch_size=50,  # 增大批量大小提高效率
                verbose_message=True
            )
            
            # 简化日志输出
            if result.get('success'):
                sent_count = result.get('sent', 0)
                total_count = result.get('total', 0)
                print(f"02号接口 | 机构编码:{org_code} | 处理:{total_count}条 | 传输成功:{sent_count}条")
                
                response_data = {
                    'code': 0,
                    'msg': '传输成功',
                    'data': result.get('data', [])
                }
                return response_data
            else:
                error_msg = result.get('error', '未知错误')
                print(f"02号接口 | 机构编码:{org_code} | 传输失败:{error_msg}")
                
                response_data = {
                    'code': -1,
                    'msg': f"传输失败: {error_msg}",
                    'data': []
                }
                return response_data
                
        except Exception as e:
            org_code = self.org_config.get('org_code', 'DEFAULT')
            print(f"02号接口 | 机构编码:{org_code} | 传输失败:异常{str(e)}")
            
            error_response = {
                'code': -1,
                'msg': f'传输失败: {str(e)}',
                'data': []
            }
            return error_response





def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='天健云02号接口 - 申请项目字典数据传输')
    parser.add_argument('--limit', type=int, default=None, help='限制同步条数（不指定则传输所有数据）')
    parser.add_argument('--test-mode', action='store_true', help='测试模式，不实际发送请求')
    parser.add_argument('--batch-size', type=int, default=50, help='批量发送大小（默认50条）')
    parser.add_argument('--verbose-message', action='store_true', help='显示详细报文信息')
    parser.add_argument('--days', type=int, help='天数参数（02号接口不使用，但保持兼容性）')

    args = parser.parse_args()

    # 创建接口实例
    interface = TianjianInterface02()

    print("天健云02号接口 - 申请项目字典数据传输")
    print("=" * 50)

    # 执行同步
    result = interface.sync_apply_items(
        limit=args.limit,
        test_mode=args.test_mode,
        batch_size=args.batch_size,
        verbose_message=args.verbose_message
    )

    # 输出结果
    if result['success']:
        print(f"\n✓ {result['message']}")
    else:
        print(f"\n✗ 同步失败: {result.get('error', result.get('message', '未知错误'))}")

    return result


if __name__ == '__main__':
    main()