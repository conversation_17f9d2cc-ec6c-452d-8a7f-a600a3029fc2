from api_config_manager import get_tianjian_base_url
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云02号接口实现 - 申请项目字典数据传输
/dx/inter/syncApplyItem

数据库表关系：
- code_Item_Price (申请项目主表) - 价格项目作为申请项目
- code_Item_Price.cMainCode → Code_Item_Main.cCode (关联申请项目基础信息)
- Code_Item_Main.cCode → Code_Item_Detail.cMainCode (获取检查项目明细)
- Code_Item_Main.cCode → Code_Dept_Main.cMainCode → Code_Dept_dict.cCode (科室信息)
"""

import json
import hashlib
import requests
import uuid
import time
import os
from datetime import datetime
from typing import Dict, Any, List, Optional

# 防止GUI实例化：检查是否是GUI子进程
if os.environ.get('GUI_SUBPROCESS') == '1' or os.environ.get('TIANJIAN_NO_GUI') == '1':
    # 当作为GUI子进程运行时，避免导入可能启动GUI的模块
    import sys
    # 禁用任何可能的GUI导入
    sys.modules['tkinter'] = None
    sys.modules['PySide6'] = None
    sys.modules['PyQt5'] = None
    sys.modules['gui_main'] = None
    # 设置无GUI模式标记
    os.environ['NO_GUI_MODE'] = '1'

from config import Config
from optimized_database_service import create_optimized_db_service
from multi_org_config import get_current_org_config
from org_code_prefix_manager import create_org_prefix_manager
from http_message_logger import get_http_logger


def safe_print(text, fallback_text=None):
    """安全打印函数，避免编码错误"""
    try:
        print(text)
    except UnicodeEncodeError:
        if fallback_text:
            print(fallback_text)
        else:
            # 移除可能有问题的Unicode字符
            safe_text = text.encode('ascii', errors='ignore').decode('ascii')
            print(f"[SAFE] {safe_text}")
    except Exception:
        print("[PRINT ERROR] Unable to display message")


class TianjianInterface02:
    """天健云02号接口 - 申请项目字典数据传输"""

    def __init__(self, api_config: Dict[str, Any] = None, org_config: Dict[str, Any] = None):
        """
        初始化接口配置

        Args:
            api_config: API配置信息（可选，默认使用中心库配置）
            org_config: 机构配置信息（可选，默认使用当前机构配置）
        """
        # 获取当前机构配置
        self.org_config = org_config or get_current_org_config()

        # 使用传入的API配置（优先）或机构配置中的天健云API配置
        if api_config:
            self.api_config = api_config.copy()
            safe_print(f"[DEBUG] 使用传入的API配置: {self.api_config['base_url']}", f"[DEBUG] Using passed API config: {self.api_config['base_url']}")
        else:
            self.api_config = {
                'base_url': self.org_config.get('tianjian_base_url', get_tianjian_base_url()),
                'api_key': self.org_config.get('tianjian_api_key', ''),
                'mic_code': self.org_config.get('tianjian_mic_code', ''),
                'misc_id': self.org_config.get('tianjian_misc_id', ''),
                'timeout': 30
            }
            safe_print(f"[DEBUG] 使用机构配置的API配置: {self.api_config['base_url']}", f"[DEBUG] 使用机构配置的API配置: {self.api_config['base_url']}".encode("ascii", errors="ignore").decode("ascii"))

        # 创建HTTP报文日志记录器
        self.http_logger = get_http_logger("02")

        # 创建优化的数据库服务实例，使用机构配置中的数据库连接
        if self.org_config.get('db_host'):
            connection_string = (
                f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                f"SERVER={self.org_config['db_host']},{self.org_config.get('db_port', 1433)};"
                f"DATABASE={self.org_config['db_name']};"
                f"UID={self.org_config['db_user']};"
                f"PWD={self.org_config['db_password']}"
            )
        else:
            # 兜底使用统一配置
            connection_string = Config.get_interface_db_connection_string()

        self.db_service = create_optimized_db_service(connection_string)

        # 创建机构编码前缀管理器
        org_code = self.org_config.get('org_code', 'DEFAULT')
        self.prefix_manager = create_org_prefix_manager(org_code)

    def generate_signature(self, api_key: str, timestamp: str) -> str:
        """生成MD5签名"""
        sign_string = api_key + timestamp
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()

    def create_headers(self) -> Dict[str, str]:
        """创建请求头，确保时间戳唯一性"""
        # 使用微秒级时间戳确保唯一性
        now = datetime.now()
        timestamp = now.strftime('%Y%m%d%H%M%S')
        microsecond_suffix = str(now.microsecond)[:3]  # 取前3位微秒
        unique_timestamp = timestamp + microsecond_suffix

        nonce = str(uuid.uuid4())
        signature = self.generate_signature(self.api_config['api_key'], timestamp)

        return {
            'Content-Type': 'application/json',
            'sign': signature,
            'timestamp': timestamp,  # 签名仍使用标准格式
            'nonce': nonce,
            'mic-code': self.api_config['mic_code'],
            'misc-id': self.api_config['misc_id'],
            'x-request-id': unique_timestamp  # 添加唯一请求ID
        }

    def get_apply_items_data(self, limit: int = None) -> List[Dict[str, Any]]:
        """
        获取申请项目字典数据
        使用code_Item_Price作为申请项目主表，通过Code_Item_Main关联Code_Item_Detail获取检查项目明细

        表关系：
        code_Item_Price.cMainCode → Code_Item_Main.cCode → Code_Item_Detail.cMainCode

        Args:
            limit: 限制返回条数

        Returns:
            申请项目字典数据列表
        """
        try:
            import time
            # 使用安全的打印函数避免编码问题
            safe_print(f"   🔍 【子步骤1.1】查询申请项目主表数据...", "   [DEBUG] 查询申请项目主表数据...")
            substep_start = time.time()

            # 获取申请项目主表数据（使用code_Item_Price作为申请项目）
            # 根据limit参数限制查询数量，提升性能
            limit_clause = f"TOP {limit}" if limit else ""

            # 获取当前门店编码
            shop_code = self.org_config.get('shop_code', '08')  # 默认门店编码为08
            safe_print(f"   🏪 门店编码过滤: {shop_code}", f"   [SHOP] 门店编码过滤: {shop_code}")

            sql_main = f"""
            SELECT {limit_clause}
                ip.cCode as applyItemId,
                ip.cName as applyItemName,
                CAST(ROW_NUMBER() OVER (ORDER BY ip.cCode) AS VARCHAR) as displaySequence,
                ISNULL(dm.cDeptCode, 'UNKNOWN') as deptId,
                ip.fPrice as price,
                ip.cExplain as explain,
                ip.cSexType as sexType
            FROM code_Item_Price ip
            LEFT JOIN Code_Item_Main im ON ip.cMainCode = im.cCode
            LEFT JOIN Code_Dept_Main dm ON im.cCode = dm.cMainCode
            WHERE (ip.cStopTag = '0' OR ip.cStopTag IS NULL)
            AND (im.cStopTag = '0' OR im.cStopTag IS NULL)
            AND (ip.cPrivateShopCodes LIKE '%{shop_code}%' OR ip.cPrivateShopCodes IS NULL OR ip.cPrivateShopCodes = '')
            ORDER BY ip.cCode
            """

            apply_items = self.db_service.connection_manager.execute_query_with_cache(
                self.db_service.connection_string,
                sql_main,
                cache_key="apply_items_price_all",  # 更新缓存键名
                use_cache=False
            )

            substep_end = time.time()
            safe_print(f"   ✅ 【子步骤1.1完成】查询到 {len(apply_items)} 个申请项目，耗时: {substep_end - substep_start:.2f} 秒",
                       f"   [COMPLETE] 查询到 {len(apply_items)} 个申请项目，耗时: {substep_end - substep_start:.2f} 秒")
            safe_print(f"   🔍 【子步骤1.2】开始处理检查项目明细...", "   [DEBUG] 开始处理检查项目明细...")

            # 批量获取所有申请项目的检查明细（解决N+1查询问题）
            detail_start = time.time()
            safe_print(f"      使用批量查询优化检查明细获取...", f"      使用批量查询优化检查明细获取...".encode("ascii", errors="ignore").decode("ascii"))

            # 构建申请项目ID列表用于批量查询
            apply_item_ids = [item['applyItemId'] for item in apply_items]
            ids_str = "','".join(apply_item_ids)

            # 一次性查询所有检查明细
            sql_all_details = f"""
            SELECT
                ip.cCode as applyItemId,
                id.cCode as checkItemId,
                id.cName as checkItemName,
                CAST(ROW_NUMBER() OVER (PARTITION BY ip.cCode ORDER BY id.cCode) AS VARCHAR) as displaySequence,
                id.cUnit as unit,
                id.cConsult as referenceRange
            FROM code_Item_Price ip
            INNER JOIN Code_Item_Main im ON ip.cMainCode = im.cCode
            INNER JOIN Code_Item_Detail id ON im.cCode = id.cMainCode
            WHERE ip.cCode IN ('{ids_str}')
            AND (id.cStopTag = '0' OR id.cStopTag IS NULL)
            ORDER BY ip.cCode, id.cCode
            """

            all_check_items = self.db_service.connection_manager.execute_query_with_cache(
                self.db_service.connection_string,
                sql_all_details,
                cache_key=f"batch_check_items_{len(apply_items)}",
                use_cache=False
            )

            # 将检查明细按申请项目ID分组
            check_items_dict = {}
            for detail in all_check_items:
                apply_id = detail['applyItemId']
                if apply_id not in check_items_dict:
                    check_items_dict[apply_id] = []
                check_items_dict[apply_id].append({
                    "checkItemId": detail['checkItemId'],
                    "checkItemName": detail['checkItemName'],
                    "displaySequence": detail['displaySequence'],
                    "unit": detail.get('unit', ''),
                    "referenceRange": detail.get('referenceRange', '')
                })

            # 构建最终结果
            result = []
            for i, item in enumerate(apply_items, 1):
                apply_item_id = item['applyItemId']

                # 从字典中获取检查明细
                check_items = check_items_dict.get(apply_item_id, [])

                # 如果没有找到检查项目明细，则使用申请项目本身作为检查项目
                if not check_items:
                    check_items = [{
                        "checkItemId": apply_item_id,
                        "checkItemName": item['applyItemName'],
                        "displaySequence": "1",
                        "unit": "",
                        "referenceRange": ""
                    }]

                # 构建申请项目数据
                apply_item_data = {
                    "applyItemId": apply_item_id,
                    "applyItemName": item['applyItemName'],
                    "displaySequence": str(i),  # 使用序号作为显示顺序
                    "deptId": item['deptId'],
                    "price": item.get('price', 0),
                    "explain": item.get('explain', ''),
                    "sexType": item.get('sexType', ''),
                    "checkItemList": check_items
                }
                result.append(apply_item_data)

                if i <= 3:  # 只显示前3个的详细信息
                    safe_print(f"      - {item['applyItemName']}: {apply_item_id}, 价格: {item.get('price', 0)}, 包含 {len(check_items)} 个检查项目", f"      - {item['applyItemName']}: {apply_item_id}, 价格: {item.get('price', 0)}, 包含 {len(check_items)} 个检查项目".encode("ascii", errors="ignore").decode("ascii"))

            detail_end = time.time()
            safe_print(f"   ✅ 【子步骤1.2完成】检查项目明细处理耗时: {detail_end - detail_start:.2f} 秒",
                       f"   [COMPLETE] 检查项目明细处理耗时: {detail_end - detail_start:.2f} 秒")

            return result

        except Exception as e:
            safe_print(f"[ERROR] 获取申请项目数据失败: {e}", f"[ERROR] 获取申请项目数据失败: {e}".encode("ascii", errors="ignore").decode("ascii"))
            return []

    def sync_apply_items(self, limit: int = None, test_mode: bool = False, batch_size: int = 50, verbose_message: bool = False) -> Dict[str, Any]:
        """
        同步申请项目字典数据到天健云

        Args:
            limit: 限制同步条数
            test_mode: 测试模式，不实际发送请求
            batch_size: 批量发送大小

        Returns:
            同步结果
        """
        import time
        start_time = time.time()

        try:
            safe_print(f"\n🚀 【步骤1】开始获取申请项目数据...", "\n[START] 步骤1：开始获取申请项目数据...")
            safe_print(f"   📍 目标服务器: {self.api_config['base_url']}", f"   [SERVER] 目标服务器: {self.api_config['base_url']}")
            safe_print(f"   🏥 机构代码: {self.api_config.get('mic_code', 'unknown')}", f"   [ORG] 机构代码: {self.api_config.get('mic_code', 'unknown')}")
            step1_start = time.time()

            # 获取申请项目数据
            apply_items = self.get_apply_items_data(limit)

            step1_end = time.time()
            safe_print(f"✅ 【步骤1完成】数据获取耗时: {step1_end - step1_start:.2f} 秒", f"✅ 【步骤1完成】数据获取耗时: {step1_end - step1_start:.2f} 秒".encode("ascii", errors="ignore").decode("ascii"))

            if not apply_items:
                safe_print(f"⚠️  未找到申请项目数据", f"⚠️  未找到申请项目数据".encode("ascii", errors="ignore").decode("ascii"))
                return {
                    'success': True,
                    'message': '没有找到申请项目数据',
                    'total': 0,
                    'sent': 0,
                    'failed': 0
                }

            safe_print(f"\n🔄 【步骤2】开始处理机构编码前缀...", f"\n🔄 【步骤2】开始处理机构编码前缀...".encode("ascii", errors="ignore").decode("ascii"))
            step2_start = time.time()

            # 为每个申请项目添加机构编码前缀
            processed_items = []
            for i, item in enumerate(apply_items, 1):
                if i <= 5 or i % 100 == 0:  # 只显示前5个和每100个的进度
                    safe_print(f"   处理第 {i}/{len(apply_items)} 项...", f"   处理第 {i}/{len(apply_items)} 项...".encode("ascii", errors="ignore").decode("ascii"))
                processed_item = self.prefix_manager.process_data(item, 'syncApplyItem')
                processed_items.append(processed_item)

            apply_items = processed_items
            step2_end = time.time()
            safe_print(f"✅ 【步骤2完成】编码处理耗时: {step2_end - step2_start:.2f} 秒", f"✅ 【步骤2完成】编码处理耗时: {step2_end - step2_start:.2f} 秒".encode("ascii", errors="ignore").decode("ascii"))

            total = len(apply_items)
            sent = 0
            failed = 0
            errors = []

            safe_print(f"准备同步 {total} 个申请项目到天健云02号接口", f"准备同步 {total} 个申请项目到天健云02号接口".encode("ascii", errors="ignore").decode("ascii"))

            # 显示纯净报文（实际发送模式）
            if verbose_message and not test_mode:
                print("\n" + "="*80)
                print("【02号接口】纯净报文内容")
                print("="*80)
                print(json.dumps(apply_items, ensure_ascii=False, indent=2))
                print("="*80)
                print("【02号接口】纯净报文结束")
                print("="*80)

            if test_mode:
                print("测试模式 - 显示前3个申请项目的数据格式:")
                for i, item in enumerate(apply_items[:3], 1):
                    safe_print(f"\n第 {i} 个申请项目:", f"\n第 {i} 个申请项目:".encode("ascii", errors="ignore").decode("ascii"))
                    print(json.dumps(item, ensure_ascii=False, indent=2))

                # 显示纯净报文
                if verbose_message:
                    print("\n" + "="*80)
                    print("【02号接口】纯净报文内容")
                    print("="*80)
                    print(json.dumps(apply_items, ensure_ascii=False, indent=2))
                    print("="*80)
                    print("【02号接口】纯净报文结束")
                    print("="*80)

                return {
                    'success': True,
                    'message': f"测试模式完成 - 共 {total} 个申请项目格式正确",
                    'total': total,
                    'sent': total,
                    'failed': 0,
                    'errors': []
                }

            safe_print(f"\n📤 【步骤3】开始分批发送数据到天健云...", f"\n📤 【步骤3】开始分批发送数据到天健云...".encode("ascii", errors="ignore").decode("ascii"))
            safe_print(f"   总数据量: {total} 条", f"   总数据量: {total} 条".encode("ascii", errors="ignore").decode("ascii"))
            safe_print(f"   批次大小: {batch_size} 条", f"   批次大小: {batch_size} 条".encode("ascii", errors="ignore").decode("ascii"))
            total_batches = (total + batch_size - 1) // batch_size
            safe_print(f"   总批次数: {total_batches} 批", f"   总批次数: {total_batches} 批".encode("ascii", errors="ignore").decode("ascii"))

            batch_start_time = time.time()

            # 分批发送
            for i in range(0, total, batch_size):
                batch = apply_items[i:i + batch_size]
                batch_num = i // batch_size + 1

                safe_print(f"\n📦 【批次 {batch_num}/{total_batches}】准备发送 {len(batch)} 条数据", f"\n📦 【批次 {batch_num}/{total_batches}】准备发送 {len(batch)} 条数据".encode("ascii", errors="ignore").decode("ascii"))
                single_batch_start = time.time()

                # 为非首批次添加延迟，避免时间戳冲突
                if batch_num > 1:
                    safe_print(f"   ⏳ 延迟0.8秒避免时间戳冲突...", f"   ⏳ 延迟0.8秒避免时间戳冲突...".encode("ascii", errors="ignore").decode("ascii"))
                    time.sleep(0.8)  # 减少到0.8秒延迟，加快处理速度

                try:
                    # 发送请求
                    safe_print(f"   🌐 开始网络请求...", f"   🌐 开始网络请求...".encode("ascii", errors="ignore").decode("ascii"))
                    request_start = time.time()
                    result = self._send_request(batch)
                    request_end = time.time()

                    single_batch_end = time.time()
                    batch_total_time = single_batch_end - single_batch_start
                    network_time = request_end - request_start

                    if result['success']:
                        sent += len(batch)
                        safe_print(f"   ✅ 批次 {batch_num} 发送成功", f"   ✅ 批次 {batch_num} 发送成功".encode("ascii", errors="ignore").decode("ascii"))
                        safe_print(f"   ⏱️  批次总耗时: {batch_total_time:.2f}秒 (网络: {network_time:.2f}秒)", f"   ⏱️  批次总耗时: {batch_total_time:.2f}秒 (网络: {network_time:.2f}秒)".encode("ascii", errors="ignore").decode("ascii"))

                        # 显示关键的成功信息给GUI用户
                        if 'response' in result and result['response']:
                            response_data = result['response']
                            safe_print(f"   📋 天健云响应: code={response_data.get('code', 'unknown')}, msg={response_data.get('msg', 'no message')}", f"   📋 天健云响应: code={response_data.get('code', 'unknown')}, msg={response_data.get('msg', 'no message')}".encode("ascii", errors="ignore").decode("ascii"))
                    else:
                        failed += len(batch)
                        error_msg = f"第 {batch_num} 批次发送失败: {result.get('error', '未知错误')}"
                        safe_print(f"   ❌ {error_msg}", f"   ❌ {error_msg}".encode("ascii", errors="ignore").decode("ascii"))
                        safe_print(f"   ⏱️  批次总耗时: {batch_total_time:.2f}秒 (网络: {network_time:.2f}秒)", f"   ⏱️  批次总耗时: {batch_total_time:.2f}秒 (网络: {network_time:.2f}秒)".encode("ascii", errors="ignore").decode("ascii"))
                        errors.append(error_msg)

                except Exception as e:
                    failed += len(batch)
                    single_batch_end = time.time()
                    batch_total_time = single_batch_end - single_batch_start
                    error_msg = f"第 {batch_num} 批次处理异常: {str(e)}"
                    safe_print(f"   ❌ {error_msg}", f"   ❌ {error_msg}".encode("ascii", errors="ignore").decode("ascii"))
                    safe_print(f"   ⏱️  批次总耗时: {batch_total_time:.2f}秒", f"   ⏱️  批次总耗时: {batch_total_time:.2f}秒".encode("ascii", errors="ignore").decode("ascii"))
                    errors.append(error_msg)

                # 显示整体进度
                current_time = time.time()
                elapsed = current_time - batch_start_time
                avg_time_per_batch = elapsed / batch_num
                remaining_batches = total_batches - batch_num
                estimated_remaining = remaining_batches * avg_time_per_batch

                safe_print(f"   📊 进度: {batch_num}/{total_batches} ({batch_num/total_batches*100:.1f}%)", f"   📊 进度: {batch_num}/{total_batches} ({batch_num/total_batches*100:.1f}%)".encode("ascii", errors="ignore").decode("ascii"))
                safe_print(f"   ⏱️  已用时: {elapsed:.1f}秒, 预计剩余: {estimated_remaining:.1f}秒", f"   ⏱️  已用时: {elapsed:.1f}秒, 预计剩余: {estimated_remaining:.1f}秒".encode("ascii", errors="ignore").decode("ascii"))

            batch_end_time = time.time()
            safe_print(f"\n✅ 【步骤3完成】所有批次发送耗时: {batch_end_time - batch_start_time:.2f} 秒", f"\n✅ 【步骤3完成】所有批次发送耗时: {batch_end_time - batch_start_time:.2f} 秒".encode("ascii", errors="ignore").decode("ascii"))

            # 返回结果
            total_end_time = time.time()
            total_elapsed = total_end_time - start_time

            safe_print(f"\n🏁 【同步完成】总体统计:", f"\n🏁 【同步完成】总体统计:".encode("ascii", errors="ignore").decode("ascii"))
            safe_print(f"   📊 成功: {sent} 条", f"   📊 成功: {sent} 条".encode("ascii", errors="ignore").decode("ascii"))
            safe_print(f"   ❌ 失败: {failed} 条", f"   ❌ 失败: {failed} 条".encode("ascii", errors="ignore").decode("ascii"))
            safe_print(f"   ⏱️  总耗时: {total_elapsed:.2f} 秒", f"   ⏱️  总耗时: {total_elapsed:.2f} 秒".encode("ascii", errors="ignore").decode("ascii"))
            safe_print(f"   🚀 平均速度: {total/total_elapsed:.1f} 条/秒", f"   [SPEED] 平均速度: {total/total_elapsed:.1f} 条/秒")

            # 显示传输确认状态
            if failed == 0:
                safe_print(f"\n✅ 【传输确认】所有数据已成功传输到天健云!", f"\n✅ 【传输确认】所有数据已成功传输到天健云!".encode("ascii", errors="ignore").decode("ascii"))
                safe_print(f"   🎯 目标地址: {self.api_config['base_url']}/dx/inter/syncApplyItem", f"   🎯 目标地址: {self.api_config['base_url']}/dx/inter/syncApplyItem".encode("ascii", errors="ignore").decode("ascii"))
                safe_print(f"   📦 传输数据: {sent} 条申请项目字典数据", f"   📦 传输数据: {sent} 条申请项目字典数据".encode("ascii", errors="ignore").decode("ascii"))
                safe_print(f"   🏥 机构代码: {self.api_config.get('mic_code', 'unknown')}", f"   🏥 机构代码: {self.api_config.get('mic_code', 'unknown')}".encode("ascii", errors="ignore").decode("ascii"))
            else:
                safe_print(f"\n⚠️  【传输警告】部分数据传输失败!", f"\n⚠️  【传输警告】部分数据传输失败!".encode("ascii", errors="ignore").decode("ascii"))
                safe_print(f"   ✅ 成功传输: {sent} 条", f"   ✅ 成功传输: {sent} 条".encode("ascii", errors="ignore").decode("ascii"))
                safe_print(f"   ❌ 失败数量: {failed} 条", f"   ❌ 失败数量: {failed} 条".encode("ascii", errors="ignore").decode("ascii"))
                safe_print(f"   📋 错误详情: {'; '.join(errors[:3]) if errors else '无详细错误信息'}", f"   📋 错误详情: {'; '.join(errors[:3]) if errors else '无详细错误信息'}".encode("ascii", errors="ignore").decode("ascii"))

            return {
                'success': failed == 0,
                'message': f"同步完成 - 成功: {sent}, 失败: {failed}, 总耗时: {total_elapsed:.2f}秒",
                'total': total,
                'sent': sent,
                'failed': failed,
                'errors': errors,
                'elapsed_time': total_elapsed
            }

        except Exception as e:
            error_time = time.time()
            error_elapsed = error_time - start_time
            safe_print(f"\n❌ 【异常终止】耗时: {error_elapsed:.2f} 秒", f"\n❌ 【异常终止】耗时: {error_elapsed:.2f} 秒".encode("ascii", errors="ignore").decode("ascii"))
            safe_print(f"   异常详情: {str(e)}", f"   异常详情: {str(e)}".encode("ascii", errors="ignore").decode("ascii"))

            return {
                'success': False,
                'error': f"同步过程异常: {str(e)}",
                'total': 0,
                'sent': 0,
                'failed': 0,
                'elapsed_time': error_elapsed
            }

    def _send_request(self, request_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        发送HTTP请求到天健云

        Args:
            request_data: 请求数据

        Returns:
            请求结果
        """
        url = f"{self.api_config['base_url']}/dx/inter/syncApplyItem"
        headers = self.create_headers()

        # 生成请求ID用于日志关联
        request_id = headers.get('x-request-id', str(uuid.uuid4()))

        # 打印完整的HTTP请求报文到控制台
        print("=" * 80)
        print("【02号接口】HTTP请求报文")
        print("=" * 80)
        print(f"请求URL: {url}")
        print(f"请求方法: POST")
        print("请求头:")
        for key, value in headers.items():
            print(f"  {key}: {value}")
        print("请求体:")
        print(json.dumps(request_data, ensure_ascii=False, indent=2))
        print("=" * 80)

        # 记录HTTP请求报文到日志文件
        self.http_logger.log_request(
            url=url,
            method="POST",
            headers=headers,
            request_data=request_data,
            request_id=request_id
        )

        try:
            # 针对504错误优化超时时间
            timeout = max(180, len(request_data) * 5)  # 动态超时：最少180秒，每条数据5秒

            safe_print(f"🚀 正在发送请求到天健云...", f"🚀 正在发送请求到天健云...".encode("ascii", errors="ignore").decode("ascii"))
            safe_print(f"⏳ 超时设置: {timeout} 秒", f"⏳ 超时设置: {timeout} 秒".encode("ascii", errors="ignore").decode("ascii"))

            response = requests.post(
                url,
                headers=headers,
                json=request_data,
                timeout=timeout,
                verify=False
            )

            # 打印完整的HTTP响应报文到控制台
            print("【02号接口】HTTP响应报文")
            print("=" * 80)
            print(f"响应状态: HTTP {response.status_code}")
            print("响应头:")
            for key, value in response.headers.items():
                print(f"  {key}: {value}")
            print("响应体:")

            # 尝试解析响应内容
            response_data = None
            try:
                response_data = response.json()
                print(json.dumps(response_data, ensure_ascii=False, indent=2))
            except:
                response_data = response.text
                print(response_data)
            print("=" * 80)

            # 记录HTTP响应报文到日志文件
            self.http_logger.log_response(
                status_code=response.status_code,
                headers=dict(response.headers),
                response_data=response_data,
                request_id=request_id
            )

            if response.status_code == 200:
                try:
                    response_json = response.json()
                    if response_json.get('code') == 0:
                        safe_print(f"\n✅ 请求成功!", f"\n✅ 请求成功!".encode("ascii", errors="ignore").decode("ascii"))
                        safe_print(f"   返回码: {response_json.get('code')}", f"   返回码: {response_json.get('code')}".encode("ascii", errors="ignore").decode("ascii"))
                        safe_print(f"   返回消息: {response_json.get('msg', '成功')}", f"   返回消息: {response_json.get('msg', '成功')}".encode("ascii", errors="ignore").decode("ascii"))
                        if 'data' in response_json and response_json.get('data') is not None:
                            data = response_json.get('data', [])
                            if isinstance(data, list):
                                safe_print(f"   返回数据条数: {len(data)}", f"   返回数据条数: {len(data)}".encode("ascii", errors="ignore").decode("ascii"))
                            else:
                                safe_print(f"   返回数据: {data}", f"   返回数据: {data}".encode("ascii", errors="ignore").decode("ascii"))
                        return {
                            'success': True,
                            'response': response_json
                        }
                    else:
                        safe_print(f"\n⚠️  API返回错误!", f"\n⚠️  API返回错误!".encode("ascii", errors="ignore").decode("ascii"))
                        safe_print(f"   错误码: {response_json.get('code')}", f"   错误码: {response_json.get('code')}".encode("ascii", errors="ignore").decode("ascii"))
                        safe_print(f"   错误消息: {response_json.get('msg', '未知错误')}", f"   错误消息: {response_json.get('msg', '未知错误')}".encode("ascii", errors="ignore").decode("ascii"))
                        return {
                            'success': False,
                            'error': f"API错误: code={response_json.get('code')}, msg={response_json.get('msg', '未知错误')}",
                            'response': response_json
                        }
                except json.JSONDecodeError:
                    safe_print(f"\n❌ JSON解析失败!", f"\n❌ JSON解析失败!".encode("ascii", errors="ignore").decode("ascii"))
                    safe_print(f"   原始响应: {response.text[:200]}...", f"   原始响应: {response.text[:200]}...".encode("ascii", errors="ignore").decode("ascii"))
                    return {
                        'success': False,
                        'error': f"响应解析失败: {response.text}",
                        'response': response.text
                    }
            else:
                safe_print(f"\n❌ HTTP错误!", f"\n❌ HTTP错误!".encode("ascii", errors="ignore").decode("ascii"))
                safe_print(f"   状态码: {response.status_code}", f"   状态码: {response.status_code}".encode("ascii", errors="ignore").decode("ascii"))
                safe_print(f"   响应内容: {response.text[:200]}...", f"   响应内容: {response.text[:200]}...".encode("ascii", errors="ignore").decode("ascii"))
                return {
                    'success': False,
                    'error': f"HTTP错误: {response.status_code}",
                    'response': response.text
                }

        except requests.exceptions.Timeout:
            error_msg = f"请求超时 - 超时时间: {timeout} 秒"
            safe_print(f"\n❌ 请求超时!", f"\n❌ 请求超时!".encode("ascii", errors="ignore").decode("ascii"))
            safe_print(f"   超时时间: {timeout} 秒", f"   超时时间: {timeout} 秒".encode("ascii", errors="ignore").decode("ascii"))
            safe_print(f"   目标地址: {url}", f"   目标地址: {url}".encode("ascii", errors="ignore").decode("ascii"))

            # 记录错误到日志文件
            self.http_logger.log_error("请求超时", error_msg, request_id)

            return {
                'success': False,
                'error': "请求超时"
            }
        except requests.exceptions.ConnectionError as e:
            error_msg = f"连接错误 - {str(e)}"
            safe_print(f"\n❌ 连接错误!", f"\n❌ 连接错误!".encode("ascii", errors="ignore").decode("ascii"))
            safe_print(f"   目标地址: {url}", f"   目标地址: {url}".encode("ascii", errors="ignore").decode("ascii"))
            safe_print(f"   错误详情: {str(e)}", f"   错误详情: {str(e)}".encode("ascii", errors="ignore").decode("ascii"))

            # 记录错误到日志文件
            self.http_logger.log_error("连接错误", error_msg, request_id)

            return {
                'success': False,
                'error': "连接错误"
            }
        except Exception as e:
            error_msg = f"请求异常 - {str(e)}"
            safe_print(f"\n❌ 请求异常!", f"\n❌ 请求异常!".encode("ascii", errors="ignore").decode("ascii"))
            safe_print(f"   目标地址: {url}", f"   目标地址: {url}".encode("ascii", errors="ignore").decode("ascii"))
            safe_print(f"   异常详情: {str(e)}", f"   异常详情: {str(e)}".encode("ascii", errors="ignore").decode("ascii"))

            # 记录错误到日志文件
            self.http_logger.log_error("请求异常", error_msg, request_id)

            return {
                'success': False,
                'error': f"请求异常: {str(e)}"
            }

    def sync_apply_item(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        申请项目字典传输接口（GUI调用适配方法）

        Args:
            data: 接收到的请求数据

        Returns:
            标准的天健云API响应格式
        """
        try:
            # 获取机构编码
            org_code = self.org_config.get('org_code', 'DEFAULT')

            # 调用现有的sync_apply_items方法，不限制数量
            result = self.sync_apply_items(
                limit=None,  # 不限制数量，传输所有数据
                test_mode=False,
                batch_size=50,  # 增大批量大小提高效率
                verbose_message=True
            )

            # 简化日志输出
            if result.get('success'):
                sent_count = result.get('sent', 0)
                total_count = result.get('total', 0)
                safe_print(f"02号接口 | 机构编码:{org_code} | 处理:{total_count}条 | 传输成功:{sent_count}条", f"02号接口 | 机构编码:{org_code} | 处理:{total_count}条 | 传输成功:{sent_count}条".encode("ascii", errors="ignore").decode("ascii"))

                response_data = {
                    'code': 0,
                    'msg': '传输成功',
                    'data': result.get('data', [])
                }
                return response_data
            else:
                error_msg = result.get('error', '未知错误')
                safe_print(f"02号接口 | 机构编码:{org_code} | 传输失败:{error_msg}", f"02号接口 | 机构编码:{org_code} | 传输失败:{error_msg}".encode("ascii", errors="ignore").decode("ascii"))

                response_data = {
                    'code': -1,
                    'msg': f"传输失败: {error_msg}",
                    'data': []
                }
                return response_data

        except Exception as e:
            org_code = self.org_config.get('org_code', 'DEFAULT')
            safe_print(f"02号接口 | 机构编码:{org_code} | 传输失败:异常{str(e)}", f"02号接口 | 机构编码:{org_code} | 传输失败:异常{str(e)}".encode("ascii", errors="ignore").decode("ascii"))

            error_response = {
                'code': -1,
                'msg': f'传输失败: {str(e)}',
                'data': []
            }
            return error_response


# API配置
API_CONFIG = {
    'base_url': get_tianjian_base_url(),
    'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
    'mic_code': 'MIC1.001E',
    'misc_id': 'MISC1.00001A',
    'timeout': 30
}



def main():
    """主函数"""
    import argparse

    # 防止在GUI子进程中启动GUI
    if os.environ.get('GUI_SUBPROCESS') == '1' or os.environ.get('TIANJIAN_NO_GUI') == '1':
        # 确保不会启动任何GUI组件
        import sys
        if 'gui_main' in sys.modules:
            del sys.modules['gui_main']
        # 设置严格的无GUI模式
        os.environ['NO_GUI_MODE'] = '1'

    parser = argparse.ArgumentParser(description='天健云02号接口 - 申请项目字典数据传输')
    parser.add_argument('--limit', type=int, default=None, help='限制同步条数（不指定则传输所有数据）')
    parser.add_argument('--test-mode', action='store_true', help='测试模式，不实际发送请求')
    parser.add_argument('--batch-size', type=int, default=50, help='批量发送大小（默认50条）')
    parser.add_argument('--verbose-message', action='store_true', help='显示详细报文信息')
    parser.add_argument('--days', type=int, help='天数参数（02号接口不使用，但保持兼容性）')

    args = parser.parse_args()

    # 使用和其他接口一致的API配置方式
    print(f"使用API配置: {API_CONFIG['base_url']}")

    # 创建接口实例，传入API配置
    interface = TianjianInterface02(API_CONFIG)

    print("天健云02号接口 - 申请项目字典数据传输")
    print("=" * 50)

    # 执行同步
    result = interface.sync_apply_items(
        limit=args.limit,
        test_mode=args.test_mode,
        batch_size=args.batch_size,
        verbose_message=args.verbose_message
    )

    # 输出结果
    if result['success']:
        safe_print(f"\n✓ {result['message']}", f"\n✓ {result['message']}".encode("ascii", errors="ignore").decode("ascii"))
    else:
        safe_print(f"\n✗ 同步失败: {result.get('error', result.get('message', '未知错误'))}", f"\n✗ 同步失败: {result.get('error', result.get('message', '未知错误'))}".encode("ascii", errors="ignore").decode("ascii"))

    return result


if __name__ == '__main__':
    main()