#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云03号接口实现 - 体检科室结果传输
/dx/inter/deptInfo
"""

import json
import hashlib
import requests
import uuid
import os
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from api_config_manager import get_tianjian_base_url

# 防止GUI实例化：检查是否是GUI子进程
if os.environ.get('GUI_SUBPROCESS') == '1' or os.environ.get('TIANJIAN_NO_GUI') == '1':
    # 当作为GUI子进程运行时，避免导入可能启动GUI的模块
    import sys
    # 禁用任何可能的GUI导入
    sys.modules['tkinter'] = None
    sys.modules['PySide6'] = None
    sys.modules['PyQt5'] = None
    sys.modules['gui_main'] = None
    # 设置无GUI模式标记
    os.environ['NO_GUI_MODE'] = '1'

from database_service import get_database_service
from multi_org_config import get_current_org_config
from org_code_prefix_manager import create_org_prefix_manager
from http_message_logger import get_http_logger


def safe_print(text, fallback_text=None):
    """安全打印函数，避免编码错误"""
    try:
        print(text)
    except UnicodeEncodeError:
        if fallback_text:
            print(fallback_text)
        else:
            # 移除可能有问题的Unicode字符
            safe_text = text.encode('ascii', errors='ignore').decode('ascii')
            print(f"[SAFE] {safe_text}")
    except Exception:
        print("[PRINT ERROR] Unable to display message")


def setup_logging():
    """设置日志记录"""
    # 获取当前脚本的目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    logs_dir = os.path.join(script_dir, "logs")
    os.makedirs(logs_dir, exist_ok=True)
    
    # 使用独特的日志记录器名称
    logger_name = 'interface_03_deptInfo'
    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.INFO)
    
    # 清除现有处理器，避免重复
    logger.handlers.clear()
    
    # 创建文件处理器
    today = datetime.now().strftime('%Y-%m-%d')
    log_file = os.path.join(logs_dir, f"interface_03_{today}.log")
    
    try:
        # 确保可以写入日志文件
        with open(log_file, 'a', encoding='utf-8') as test_file:
            test_file.write('')  # 测试写入权限
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8', mode='a')
        file_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)s | 03号接口 | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        
        # 测试日志写入
        logger.info("03号接口日志记录器初始化成功")
        logger.info(f"日志文件路径: {log_file}")
        
        print(f"03号接口日志文件: {log_file}")
        
    except Exception as e:
        print(f"警告: 日志记录器初始化失败: {e}")
        print(f"警告: 尝试的日志文件路径: {log_file}")
        # 创建空的记录器，避免程序崩溃
        logger = logging.getLogger('dummy')
    
    return logger

# 初始化日志记录器
logger = setup_logging()


class TianjianInterface03:
    """天健云03号接口 - 体检科室结果传输"""

    def __init__(self, api_config: Dict[str, Any] = None, org_config: Dict[str, Any] = None):
        """
        初始化接口配置

        Args:
            api_config: API配置信息（可选，默认使用中心库配置）
            org_config: 机构配置信息（可选，默认使用当前机构配置）
        """
        # 获取当前机构配置
        self.org_config = org_config or get_current_org_config()

        # 使用机构配置中的天健云API配置
        if api_config:
            self.api_config = api_config
        else:
            self.api_config = {
                'base_url': self.org_config.get('tianjian_base_url', get_tianjian_base_url()),
                'api_key': self.org_config.get('tianjian_api_key', ''),
                'mic_code': self.org_config.get('tianjian_mic_code', ''),
                'misc_id': self.org_config.get('tianjian_misc_id', ''),
                'timeout': 30
            }

        # 创建HTTP报文日志记录器
        self.http_logger = get_http_logger("03")

        self.db_service = get_database_service()

        # 创建机构编码前缀管理器
        org_code = self.org_config.get('org_code', 'DEFAULT')
        self.prefix_manager = create_org_prefix_manager(org_code)
    
    def generate_signature(self, api_key: str, timestamp: str) -> str:
        """生成MD5签名"""
        sign_string = api_key + timestamp
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    def create_headers(self) -> Dict[str, str]:
        """创建请求头"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        nonce = str(uuid.uuid4())
        signature = self.generate_signature(self.api_config['api_key'], timestamp)
        
        return {
            'Content-Type': 'application/json',
            'sign': signature,
            'timestamp': timestamp,
            'nonce': nonce,
            'mic-code': self.api_config['mic_code'],
            'misc-id': self.api_config['misc_id']
        }
    
    def get_dept_results_data(self, client_codes: List[str] = None, days: int = None, limit: int = None) -> List[Dict[str, Any]]:
        """
        获取科室结果数据
        
        Args:
            client_codes: 客户编号列表
            days: 获取最近N天的数据
            limit: 限制返回条数
            
        Returns:
            科室结果数据列表
        """
        if not self.db_service.connect():
            raise Exception("数据库连接失败")
        
        try:
            # 构建SQL查询科室结果（使用正确的列名）
            sql = """
            SELECT 
                rm.cClientCode as peNo,
                crm.cDeptCode as dept_code,
                dd.cName as dept_name,
                crm.dOperDate as checkTime,
                crm.cDoctCode as checkDoctor_code,
                crm.cDoctName as checkDoctor_name,
                crm.cResultCure as summary,
                crm.cAuditDoctCode as auditDoctor_code,
                crm.cAuditDoctName as auditDoctor_name,
                CASE 
                    WHEN crm.cResultCure IS NOT NULL AND crm.cResultCure != '' THEN 'isAudited'
                    ELSE 'notAudited'
                END as auditStatus_code,
                CASE 
                    WHEN crm.cResultCure IS NOT NULL AND crm.cResultCure != '' THEN '已审核'
                    ELSE '未审核'
                END as auditStatus_name,
                crm.dOperDate as auditTime
            FROM T_Register_Main rm
            INNER JOIN T_Check_result_Main crm ON rm.cClientCode = crm.cClientCode
            INNER JOIN Code_Dept_dict dd ON crm.cDeptCode = dd.cCode
            WHERE 1=1
            """
            
            params = []
            
            # 添加客户编号条件
            if client_codes:
                placeholders = ','.join(['?' for _ in client_codes])
                sql += f" AND rm.cClientCode IN ({placeholders})"
                params.extend(client_codes)
            
            # 添加时间条件
            if days:
                sql += " AND crm.dOperDate >= DATEADD(day, -?, GETDATE())"
                params.append(days)
            
            # 如果有limit限制，直接在主查询中使用TOP
            if limit:
                # 在SELECT后面直接添加TOP
                sql = sql.replace("SELECT ", f"SELECT TOP {limit} ")
            
            sql += " ORDER BY rm.cClientCode, crm.cDeptCode"
            
            dept_results = self.db_service.execute_query(sql, tuple(params) if params else None)
            
            # 为每个科室结果获取检查明细
            result = []
            for dept_result in dept_results:
                # 获取该科室的检查明细
                item_desc = self._get_item_desc_for_dept(dept_result['peNo'], dept_result['dept_code'])
                
                dept_data = {
                    "peNo": dept_result['peNo'],
                    "dept": {
                        "code": dept_result['dept_code'],
                        "name": dept_result['dept_name']
                    },
                    "checkTime": dept_result['checkTime'] or '',
                    "checkDoctor": {
                        "code": dept_result['checkDoctor_code'] or '',
                        "name": dept_result['checkDoctor_name'] or ''
                    },
                    "summary": dept_result['summary'] or '检查完成',
                    "auditDoctor": {
                        "code": dept_result['auditDoctor_code'] or '',
                        "name": dept_result['auditDoctor_name'] or ''
                    },
                    "auditStatus": {
                        "code": dept_result['auditStatus_code'],
                        "name": dept_result['auditStatus_name']
                    },
                    "auditTime": dept_result['auditTime'] or '',
                    "deleted": 0,  # 是否删除 0--未删除 1--已删除
                    "fileUrl": "",  # 图片url
                    "itemDesc": item_desc
                }
                result.append(dept_data)
            
            return result
            
        finally:
            self.db_service.disconnect()
    
    def _get_item_desc_for_dept(self, client_code: str, dept_code: str) -> List[Dict[str, Any]]:
        """
        获取特定科室的检查明细
        
        Args:
            client_code: 客户编号
            dept_code: 科室编码
            
        Returns:
            检查明细列表
        """
        sql = """
        SELECT 
            im.cCode as bizApplyId,
            im.cCode as applyItem_code,
            im.cName as applyItem_name,
            cr.cDoctCode as checkDoctor_code,
            cr.cDoctName as checkDoctor_name,
            id.cCode as checkItem_code,
            id.cName as checkItem_name,
            cr.cResult as inspectResult,
            id.cUnit as unit,
            id.cConsult as reference,
            CAST(ROW_NUMBER() OVER (ORDER BY id.cCode) AS VARCHAR) as displaySequence,
            cr.cResult as digitValue,
            CASE cr.cAbnor
                WHEN '1' THEN '↑'
                WHEN '2' THEN '↓'
                ELSE ''
            END as mds_name
        FROM T_Check_result cr
        INNER JOIN Code_Item_Detail id ON cr.cDetailCode = id.cCode
        INNER JOIN Code_Item_Main im ON id.cMainCode = im.cCode
        WHERE cr.cClientCode = ?
        AND cr.cDeptCode = ?
        ORDER BY id.cCode
        """
        
        result = self.db_service.execute_query(sql, (client_code, dept_code))
        
        # 格式化结果
        item_desc = []
        for item in result:
            # 解析参考值范围
            reference = item.get('reference', '')
            reference_min = 0
            reference_max = 0
            
            if reference and '-' in reference:
                try:
                    parts = reference.split('-')
                    if len(parts) == 2:
                        reference_min = float(parts[0].strip())
                        reference_max = float(parts[1].strip())
                except:
                    pass
            
            item_data = {
                "applyItem": {
                    "code": item['applyItem_code'],
                    "name": item['applyItem_name']
                },
                "checkItem": {
                    "code": item['checkItem_code'],
                    "name": item['checkItem_name']
                },
                "inspectResult": item['inspectResult'] or '',
                "unit": item['unit'] or '',
                "referenceMax": float(reference_max),  # 确保是浮点数格式
                "referenceMin": float(reference_min),  # 确保是浮点数格式
                "reference": reference,
                "displaySequence": item['displaySequence'],
                "unNormalFlag": "",  # 异常标识
                "digitValue": item['digitValue'] or '',
                "mds": {
                    "code": "",  # 冗余字段（传空）
                    "name": item['mds_name']  # 异常标识 1+, ↑
                },
                "summary": ""  # 检查项目小结
            }
            item_desc.append(item_data)
        
        return item_desc
    
    def sync_dept_results(self, client_codes: List[str] = None, days: int = None, limit: int = None, 
                         test_mode: bool = False, batch_size: int = 10, verbose_message: bool = False) -> Dict[str, Any]:
        """
        同步科室结果数据到天健云
        
        Args:
            client_codes: 客户编号列表
            days: 获取最近N天的数据
            limit: 限制同步条数
            test_mode: 测试模式
            batch_size: 批量发送大小
            
        Returns:
            同步结果
        """
        try:
            # 获取科室结果数据
            dept_results = self.get_dept_results_data(client_codes, days, limit)

            if not dept_results:
                return {
                    'success': True,
                    'message': '没有找到科室结果数据',
                    'total': 0,
                    'sent': 0,
                    'failed': 0
                }

            # 为每条科室结果添加机构编码前缀
            processed_results = []
            for result in dept_results:
                processed_result = self.prefix_manager.process_data(result, 'deptInfo')
                processed_results.append(processed_result)

            dept_results = processed_results
            
            total = len(dept_results)
            sent = 0
            failed = 0
            errors = []
            
            print(f"准备同步 {total} 条科室结果到天健云03号接口")
            
            if test_mode:
                print("测试模式 - 显示前2条科室结果的数据格式:")
                for i, item in enumerate(dept_results[:2], 1):
                    print(f"\n第 {i} 条科室结果:")
                    print(json.dumps(item, ensure_ascii=False, indent=2))
                
                # 显示纯净报文（仅在verbose模式下）
                if verbose_message:
                    separator = "="*80
                    pure_message = json.dumps(dept_results, ensure_ascii=False, indent=2)
                    
                    # 输出到控制台（供GUI界面捕获显示）
                    print(f"\n{separator}")
                    print("【03号接口】纯净报文内容")
                    print(separator)
                    print(pure_message)
                    print(separator)
                    print("【03号接口】纯净报文结束")
                    print(separator)
                
                return {
                    'success': True,
                    'message': f"测试模式完成 - 共 {total} 条科室结果格式正确",
                    'total': total,
                    'sent': total,
                    'failed': 0,
                    'errors': []
                }
            
            # 显示纯净报文（实际发送模式下也显示）
            if verbose_message:
                separator = "="*80
                pure_message = json.dumps(dept_results, ensure_ascii=False, indent=2)
                
                # 输出到控制台（供GUI界面捕获显示）
                print(f"\n{separator}")
                print("【03号接口】纯净报文内容")
                print(separator)
                print(pure_message)
                print(separator)
                print("【03号接口】纯净报文结束")
                print(separator)
            
            # 分批发送
            for i in range(0, total, batch_size):
                batch = dept_results[i:i + batch_size]
                batch_num = i // batch_size + 1
                total_batches = (total + batch_size - 1) // batch_size
                
                print(f"\n发送第 {batch_num}/{total_batches} 批次，包含 {len(batch)} 条科室结果")
                
                try:
                    # 发送请求
                    result = self._send_request(batch)
                    
                    if result['success']:
                        sent += len(batch)
                        print(f"[SUCCESS] 第 {batch_num} 批次发送成功")
                    else:
                        failed += len(batch)
                        error_msg = f"第 {batch_num} 批次发送失败: {result.get('error', '未知错误')}"
                        print(f"[FAILED] {error_msg}")
                        errors.append(error_msg)
                
                except Exception as e:
                    failed += len(batch)
                    error_msg = f"第 {batch_num} 批次处理异常: {str(e)}"
                    print(f"[ERROR] {error_msg}")
                    errors.append(error_msg)
            
            # 返回结果
            return {
                'success': failed == 0,
                'message': f"同步完成 - 成功: {sent}, 失败: {failed}",
                'total': total,
                'sent': sent,
                'failed': failed,
                'errors': errors
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"同步过程异常: {str(e)}",
                'total': 0,
                'sent': 0,
                'failed': 0
            }
    
    def _send_request(self, request_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        发送HTTP请求到天健云
        
        Args:
            request_data: 请求数据
            
        Returns:
            请求结果
        """
        url = f"{self.api_config['base_url']}/dx/inter/deptInfo"
        headers = self.create_headers()

        # 生成请求ID用于日志关联
        request_id = headers.get('x-request-id', str(uuid.uuid4()))

        # 打印完整的HTTP请求报文到控制台
        print("=" * 80)
        print("【03号接口】HTTP请求报文")
        print("=" * 80)
        print(f"请求URL: {url}")
        print(f"请求方法: POST")
        print("请求头:")
        for key, value in headers.items():
            print(f"  {key}: {value}")
        print("请求体:")
        print(json.dumps(request_data, ensure_ascii=False, indent=2))
        print("=" * 80)

        # 记录HTTP请求报文到日志文件
        self.http_logger.log_request(
            url=url,
            method="POST",
            headers=headers,
            request_data=request_data,
            request_id=request_id
        )

        try:
            response = requests.post(
                url,
                headers=headers,
                json=request_data,
                timeout=self.api_config.get('timeout', 30),
                verify=False
            )

            # 打印完整的HTTP响应报文到控制台
            print("【03号接口】HTTP响应报文")
            print("=" * 80)
            print(f"响应状态: HTTP {response.status_code}")
            print("响应头:")
            for key, value in response.headers.items():
                print(f"  {key}: {value}")
            print("响应体:")

            # 尝试解析响应内容
            response_data = None
            try:
                response_data = response.json()
                print(json.dumps(response_data, ensure_ascii=False, indent=2))
            except:
                response_data = response.text
                print(response_data)
            print("=" * 80)

            # 记录HTTP响应报文到日志文件
            self.http_logger.log_response(
                status_code=response.status_code,
                headers=dict(response.headers),
                response_data=response_data,
                request_id=request_id
            )
            
            if response.status_code == 200:
                try:
                    response_json = response.json()
                    if response_json.get('code') == 0:
                        return {
                            'success': True,
                            'response': response_json
                        }
                    else:
                        return {
                            'success': False,
                            'error': f"API错误: code={response_json.get('code')}, msg={response_json.get('msg', '未知错误')}",
                            'response': response_json
                        }
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': f"响应解析失败: {response.text}",
                        'response': response.text
                    }
            else:
                return {
                    'success': False,
                    'error': f"HTTP错误: {response.status_code}",
                    'response': response.text
                }
        
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'error': "请求超时"
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'error': "连接错误"
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"请求异常: {str(e)}"
            }

    def send_dept_info(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        体检科室结果传输接口（GUI调用适配方法）
        
        Args:
            data: 接收到的请求数据
            
        Returns:
            标准的天健云API响应格式
        """
        try:
            # 获取机构编码
            org_config = get_current_org_config()
            org_code = org_config.get('org_code', 'DEFAULT')
            
            # 调用现有的sync_dept_results方法
            result = self.sync_dept_results(
                days=30,
                limit=100,
                test_mode=False,
                batch_size=5,
                verbose_message=True
            )
            
            # 简化日志输出
            if result.get('success'):
                sent_count = result.get('sent', 0)
                total_count = result.get('total', 0)
                print(f"03号接口 | 机构编码:{org_code} | 处理:{total_count}条 | 传输成功:{sent_count}条")
                
                response_data = {
                    'code': 0,
                    'msg': '传输成功',
                    'data': result.get('data', [])
                }
                return response_data
            else:
                error_msg = result.get('error', '未知错误')
                print(f"03号接口 | 机构编码:{org_code} | 传输失败:{error_msg}")
                
                response_data = {
                    'code': -1,
                    'msg': f"传输失败: {error_msg}",
                    'data': []
                }
                return response_data
                
        except Exception as e:
            org_config = get_current_org_config()
            org_code = org_config.get('org_code', 'DEFAULT')
            print(f"03号接口 | 机构编码:{org_code} | 传输失败:异常{str(e)}")
            
            error_response = {
                'code': -1,
                'msg': f'传输失败: {str(e)}',
                'data': []
            }
            return error_response


# API配置
API_CONFIG = {
    'base_url': get_tianjian_base_url(),
    'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
    'mic_code': 'MIC1.001E',
    'misc_id': 'MISC1.00001A',
    'timeout': 30
}


def main():
    """主函数 - 支持命令行参数的03号接口"""
    import argparse
    from config import Config

    # 防止在GUI子进程中启动GUI
    if os.environ.get('GUI_SUBPROCESS') == '1' or os.environ.get('TIANJIAN_NO_GUI') == '1':
        # 确保不会启动任何GUI组件
        import sys
        if 'gui_main' in sys.modules:
            del sys.modules['gui_main']
        # 设置严格的无GUI模式
        os.environ['NO_GUI_MODE'] = '1'

    parser = argparse.ArgumentParser(description='天健云03号接口 - 体检科室结果传输')
    parser.add_argument('--test-mode', action='store_true', help='测试模式，只验证数据格式不实际发送')
    parser.add_argument('--limit', type=int, default=10, help='限制处理的记录数量 (默认: 10)')
    parser.add_argument('--days', type=int, default=30, help='获取最近N天的数据 (默认: 30)')
    parser.add_argument('--batch-size', type=int, default=5, help='批量发送大小 (默认: 5)')
    parser.add_argument('--card-no', type=str, help='指定卡号，只处理该卡号的数据')
    parser.add_argument('--verbose-message', action='store_true', help='输出详细的报文信息')
    
    args = parser.parse_args()
    
    print("天健云03号接口测试 - 体检科室结果传输")
    print("=" * 60)
    
    # 显示参数信息
    print(f"运行参数: 测试模式={args.test_mode}, 限制数量={args.limit}, 天数={args.days}, 批量大小={args.batch_size}")
    
    # 创建接口实例
    interface = TianjianInterface03(API_CONFIG)
    
    if args.test_mode:
        # 测试模式 - 只验证数据格式
        if args.card_no:
            print(f"\n1. 测试模式 - 检查指定卡号 {args.card_no} 的科室结果数据格式")
            result = interface.sync_dept_results(
                client_codes=[args.card_no],
                test_mode=True,
                verbose_message=args.verbose_message
            )
        else:
            print(f"\n1. 测试模式 - 检查最近{args.days}天内前{args.limit}条科室结果的数据格式")
            result = interface.sync_dept_results(
                days=args.days,
                limit=args.limit,
                test_mode=True,
                verbose_message=args.verbose_message
            )
        print(f"测试结果: {result}")
    else:
        # 实际发送模式
        if args.card_no:
            print(f"\n2. 实际发送模式 - 指定卡号 {args.card_no} 的科室结果")
            result = interface.sync_dept_results(
                client_codes=[args.card_no],
                test_mode=False,
                batch_size=args.batch_size,
                verbose_message=args.verbose_message
            )
        else:
            print(f"\n2. 实际发送模式 - 最近{args.days}天内前{args.limit}条科室结果")
            result = interface.sync_dept_results(
                days=args.days,
                limit=args.limit,
                test_mode=False,
                batch_size=args.batch_size,
                verbose_message=args.verbose_message
            )
        print(f"发送结果: {result}")


if __name__ == '__main__':
    main()