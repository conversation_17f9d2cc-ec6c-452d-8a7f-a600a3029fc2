#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云05号接口实现 - 科室信息传输
/dx/inter/syncDept
"""

import json
import hashlib
import requests
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from database_service import get_database_service
from multi_org_config import get_current_org_config
from org_code_prefix_manager import create_org_prefix_manager


class TianjianInterface05:
    """天健云05号接口 - 科室信息传输"""

    def __init__(self, api_config: Dict[str, Any] = None, org_config: Dict[str, Any] = None):
        """
        初始化接口配置

        Args:
            api_config: API配置信息（可选，默认使用中心库配置）
            org_config: 机构配置信息（可选，默认使用当前机构配置）
        """
        # 获取当前机构配置
        self.org_config = org_config or get_current_org_config()

        # 使用机构配置中的天健云API配置
        if api_config:
            self.api_config = api_config
        else:
            self.api_config = {
                'base_url': self.org_config.get('tianjian_base_url', 'http://**************:9300'),
                'api_key': self.org_config.get('tianjian_api_key', ''),
                'mic_code': self.org_config.get('tianjian_mic_code', ''),
                'misc_id': self.org_config.get('tianjian_misc_id', ''),
                'timeout': 30
            }

        self.db_service = get_database_service()

        # 创建机构编码前缀管理器
        org_code = self.org_config.get('org_code', 'DEFAULT')
        self.prefix_manager = create_org_prefix_manager(org_code)
    
    def generate_signature(self, api_key: str, timestamp: str) -> str:
        """生成MD5签名"""
        sign_string = api_key + timestamp
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    def create_headers(self) -> Dict[str, str]:
        """创建请求头"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        nonce = str(uuid.uuid4())
        signature = self.generate_signature(self.api_config['api_key'], timestamp)
        
        return {
            'Content-Type': 'application/json',
            'sign': signature,
            'timestamp': timestamp,
            'nonce': nonce,
            'mic-code': self.api_config['mic_code'],
            'misc-id': self.api_config['misc_id']
        }
    
    def get_departments_data(self) -> List[Dict[str, Any]]:
        """
        获取科室信息数据
        
        Returns:
            科室信息数据列表
        """
        if not self.db_service.connect():
            raise Exception("数据库连接失败")
        
        try:
            departments = self.db_service.get_departments_dict()
            
            # 从机构配置获取医院信息
            hospital_code = self.org_config.get('org_code', 'DEFAULT')
            hospital_name = self.org_config.get('org_name', '默认医院')
            
            # 格式化为天健云05号接口格式
            result = []
            for dept in departments:
                dept_data = {
                    "name": dept['name'],
                    "id": dept['id'],
                    "displaySequence": dept['displaySequence'],
                    "hospital": {
                        "code": hospital_code,
                        "name": hospital_name
                    }
                }
                result.append(dept_data)
            
            return result
            
        finally:
            self.db_service.disconnect()
    
    def sync_departments(self, test_mode: bool = False, batch_size: int = 20, verbose_message: bool = False, limit: int = 0) -> Dict[str, Any]:
        """
        同步科室信息到天健云
        
        Args:
            test_mode: 测试模式
            batch_size: 批量发送大小
            verbose_message: 显示详细报文信息
            limit: 限制同步的科室数量 (0表示所有)
            
        Returns:
            同步结果
        """
        try:
            # 获取科室信息数据
            departments = self.get_departments_data()

            if not departments:
                return {
                    'success': True,
                    'message': '没有找到科室信息数据',
                    'total': 0,
                    'sent': 0,
                    'failed': 0
                }

            # 应用limit限制
            if limit > 0 and len(departments) > limit:
                departments = departments[:limit]
                print(f"应用limit参数，限制为前 {limit} 个科室")

            # 为每个科室信息添加机构编码前缀
            processed_departments = []
            for dept in departments:
                processed_dept = self.prefix_manager.process_data(dept, 'syncDept')
                processed_departments.append(processed_dept)

            departments = processed_departments
            
            total = len(departments)
            sent = 0
            failed = 0
            errors = []
            
            print(f"准备同步 {total} 个科室信息到天健云05号接口")
            
            if test_mode:
                print("测试模式 - 显示前5个科室的数据格式:")
                for i, item in enumerate(departments[:5], 1):
                    print(f"\n第 {i} 个科室:")
                    print(json.dumps(item, ensure_ascii=False, indent=2))
                
                # 显示纯净报文（测试模式）
                if verbose_message:
                    separator = "="*80
                    pure_message = json.dumps(departments, ensure_ascii=False, indent=2)
                    
                    # 输出到控制台（供GUI界面捕获显示）
                    print(f"\n{separator}")
                    print("【05号接口】纯净报文内容")
                    print(separator)
                    print(pure_message)
                    print(separator)
                    print("【05号接口】纯净报文结束")
                    print(separator)
                
                return {
                    'success': True,
                    'message': f"测试模式完成 - 共 {total} 个科室信息格式正确",
                    'total': total,
                    'sent': total,
                    'failed': 0,
                    'errors': []
                }
            
            # 显示纯净报文（实际发送模式下也显示）
            if verbose_message:
                separator = "="*80
                pure_message = json.dumps(departments, ensure_ascii=False, indent=2)
                
                # 输出到控制台（供GUI界面捕获显示）
                print(f"\n{separator}")
                print("【05号接口】纯净报文内容")
                print(separator)
                print(pure_message)
                print(separator)
                print("【05号接口】纯净报文结束")
                print(separator)
            
            # 分批发送
            for i in range(0, total, batch_size):
                batch = departments[i:i + batch_size]
                batch_num = i // batch_size + 1
                total_batches = (total + batch_size - 1) // batch_size
                
                print(f"\n发送第 {batch_num}/{total_batches} 批次，包含 {len(batch)} 个科室信息")
                
                try:
                    # 发送请求
                    result = self._send_request(batch)
                    
                    if result['success']:
                        sent += len(batch)
                        print(f"✓ 第 {batch_num} 批次发送成功")
                    else:
                        failed += len(batch)
                        error_msg = f"第 {batch_num} 批次发送失败: {result.get('error', '未知错误')}"
                        print(f"✗ {error_msg}")
                        errors.append(error_msg)
                
                except Exception as e:
                    failed += len(batch)
                    error_msg = f"第 {batch_num} 批次处理异常: {str(e)}"
                    print(f"✗ {error_msg}")
                    errors.append(error_msg)
            
            # 返回结果
            return {
                'success': failed == 0,
                'message': f"同步完成 - 成功: {sent}, 失败: {failed}",
                'total': total,
                'sent': sent,
                'failed': failed,
                'errors': errors
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"同步过程异常: {str(e)}",
                'total': 0,
                'sent': 0,
                'failed': 0
            }
    
    def _send_request(self, request_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        发送HTTP请求到天健云
        
        Args:
            request_data: 请求数据
            
        Returns:
            请求结果
        """
        url = f"{self.api_config['base_url']}/dx/inter/syncDept"
        headers = self.create_headers()
        
        try:
            response = requests.post(
                url,
                headers=headers,
                json=request_data,
                timeout=self.api_config.get('timeout', 30),
                verify=False
            )
            
            if response.status_code == 200:
                try:
                    response_json = response.json()
                    if response_json.get('code') == 0:
                        return {
                            'success': True,
                            'response': response_json
                        }
                    else:
                        return {
                            'success': False,
                            'error': f"API错误: code={response_json.get('code')}, msg={response_json.get('msg', '未知错误')}",
                            'response': response_json
                        }
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': f"响应解析失败: {response.text}",
                        'response': response.text
                    }
            else:
                return {
                    'success': False,
                    'error': f"HTTP错误: {response.status_code}",
                    'response': response.text
                }
        
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'error': "请求超时"
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'error': "连接错误"
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"请求异常: {str(e)}"
            }

    def sync_dept(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        科室信息传输接口（GUI调用适配方法）
        
        Args:
            data: 接收到的请求数据
            
        Returns:
            标准的天健云API响应格式
        """
        try:
            # 获取机构编码
            org_code = self.org_config.get('org_code', 'DEFAULT')
            
            # 调用现有的sync_departments方法，不限制数量
            result = self.sync_departments(
                test_mode=False,
                batch_size=50,  # 增大批量大小提高效率
                verbose_message=True,
                limit=0  # 0表示传输所有数据
            )
            
            # 简化日志输出
            if result.get('success'):
                sent_count = result.get('sent', 0)
                total_count = result.get('total', 0)
                print(f"05号接口 | 机构编码:{org_code} | 处理:{total_count}条 | 传输成功:{sent_count}条")
                
                response_data = {
                    'code': 0,
                    'msg': '传输成功',
                    'data': result.get('data', [])
                }
                return response_data
            else:
                error_msg = result.get('error', '未知错误')
                print(f"05号接口 | 机构编码:{org_code} | 传输失败:{error_msg}")
                
                response_data = {
                    'code': -1,
                    'msg': f"传输失败: {error_msg}",
                    'data': []
                }
                return response_data
                
        except Exception as e:
            org_code = self.org_config.get('org_code', 'DEFAULT')
            print(f"05号接口 | 机构编码:{org_code} | 传输失败:异常{str(e)}")
            
            error_response = {
                'code': -1,
                'msg': f'传输失败: {str(e)}',
                'data': []
            }
            return error_response


# API配置
API_CONFIG = {
    'base_url': 'http://**************:9300',
    'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
    'mic_code': 'MIC1.001E',
    'misc_id': 'MISC1.00001A',
    'timeout': 30
}


def main():
    """主函数 - 05号接口命令行调用"""
    import argparse
    from config import Config
    
    parser = argparse.ArgumentParser(description='天健云05号接口 - 科室信息传输')
    parser.add_argument('--test-mode', action='store_true', help='测试模式，只验证数据格式不实际发送')
    parser.add_argument('--batch-size', type=int, default=50, help='批量发送大小 (默认: 50)')
    parser.add_argument('--limit', type=int, default=0, help='限制同步的科室数量 (0表示传输所有数据)')
    parser.add_argument('--days', type=int, default=7, help='同步最近N天的数据 (对05号接口无效，仅为兼容性)')
    parser.add_argument('--verbose-message', action='store_true', help='显示详细报文信息')
    
    args = parser.parse_args()
    
    print("天健云05号接口测试 - 科室信息传输")
    print("=" * 60)
    test_mode_status = "测试模式" if args.test_mode else "实际发送模式" 
    limit_info = f"前{args.limit}个科室" if args.limit > 0 else "所有科室数据"
    print(f"运行参数: 测试模式={args.test_mode}, 批量大小={args.batch_size}, 数据范围={limit_info}")
    
    # 创建接口实例
    interface = TianjianInterface05(API_CONFIG)
    
    if args.test_mode:
        print(f"\n1. {test_mode_status} - 检查科室信息的数据格式")
    else:
        print(f"\n2. {test_mode_status} - {limit_info}")
    
    # 执行同步
    result = interface.sync_departments(
        test_mode=args.test_mode, 
        batch_size=args.batch_size,
        verbose_message=args.verbose_message,
        limit=args.limit
    )
    print(f"执行结果: {result}")


if __name__ == '__main__':
    main()