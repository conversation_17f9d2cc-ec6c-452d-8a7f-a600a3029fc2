#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云07号接口实现 - 主检结束结论回传
根据实际数据库结构完善的版本
"""

import json
import hashlib
import requests
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from database_service import get_database_service
from config import Config


class TianjianInterface07:
    """天健云07号接口 - 主检结束结论回传"""
    
    def __init__(self, api_config: Dict[str, Any]):
        """
        初始化接口配置
        
        Args:
            api_config: API配置信息
        """
        self.api_config = api_config
        self.db_service = get_database_service()
        self.endpoint = "/dx/inter/sendConclusion"
    
    def generate_signature(self, api_key: str, timestamp: str) -> str:
        """生成MD5签名"""
        sign_string = f"{api_key}{timestamp}"
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    def create_headers(self) -> Dict[str, str]:
        """创建请求头"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        nonce = str(uuid.uuid4())
        signature = self.generate_signature(self.api_config['api_key'], timestamp)
        
        return {
            'Content-Type': 'application/json',
            'sign': signature,
            'timestamp': timestamp,
            'nonce': nonce,
            'mic-code': self.api_config['mic_code'],
            'misc-id': self.api_config['misc_id']
        }
    
    def get_conclusion_data(self, pe_no: str, current_node_type: int = 4) -> Dict[str, Any]:
        """
        获取主检结束结论数据（基于实际数据库结构）
        
        Args:
            pe_no: 体检号（客户编码）
            current_node_type: 当前节点类型（3=主检, 4=总审）
            
        Returns:
            结论数据字典
        """
        if not self.db_service.connect():
            raise Exception("数据库连接失败")
        
        try:
            # 获取主体检信息
            main_sql = """
            SELECT 
                cClientCode,
                cName,
                cSex,
                dBornDate,
                cIdCard,
                cStatus,
                cOperCode as doctorCode,
                cOperName as doctorName,
                dOperdate as operDate,
                cMemo as remarks
            FROM T_Register_Main 
            WHERE cClientCode = ?
            """
            
            main_result = self.db_service.execute_query(main_sql, (pe_no,))
            if not main_result:
                return {}
            
            main_info = main_result[0]
            
            # 获取各科室检查结果作为结论
            dept_results_sql = """
            SELECT 
                crm.cDeptCode,
                crm.cMainCode,
                crm.cMainName,
                crm.cResultCure as conclusion,
                crm.cDoctCode,
                crm.cDoctName,
                crm.dOperDate,
                crm.csuojian as findings,
                crm.ctishi as suggestions,
                crm.cAuditDoctCode,
                crm.cAuditDoctName,
                crm.cIllnessGrade
            FROM T_Check_result_Main crm
            WHERE crm.cClientCode = ?
            AND (crm.cResultCure IS NOT NULL AND crm.cResultCure <> '')
            ORDER BY crm.cDeptCode, crm.cMainCode
            """
            
            dept_results = self.db_service.execute_query(dept_results_sql, (pe_no,))
            
            # 获取疾病诊断信息
            illness_sql = """
            SELECT 
                cri.cDeptcode,
                cri.cMainCode,
                cri.cMainName,
                cri.cIllnessCode,
                cri.cIllnessName,
                cri.cDoctCode,
                cri.cDoctName,
                cri.dOperdate,
                cri.cIllExplain,
                cri.cReason,
                cri.cAdvice,
                cri.cGrade,
                cri.cCondition
            FROM T_Check_Result_Illness cri
            WHERE cri.cClientCode = ?
            ORDER BY cri.cDeptcode, cri.cIllnessCode
            """
            
            illness_results = self.db_service.execute_query(illness_sql, (pe_no,))
            
            # 构建结论列表（符合天健云07号接口格式）
            conclusion_list = []
            
            # 将科室检查结果转换为结论
            for i, result in enumerate(dept_results, 1):
                if result.get('conclusion'):
                    conclusion_data = {
                        "mappingId": f"{result.get('cDeptCode', '')}-{result.get('cMainCode', '')}-{i}",
                        "conclusionName": result.get('conclusion', ''),
                        "conclusionCode": result.get('cMainCode', ''),
                        "parentCode": result.get('cDeptCode', ''),
                        "suggest": result.get('suggestions', ''),
                        "explain": result.get('findings', ''),
                        "checkResult": result.get('conclusion', ''),
                        "level": 3,  # 默认级别
                        "displaySequnce": i,  # 注意拼写错误，按接口文档保持
                        "deptId": result.get('cDeptCode', ''),
                        "abnormalLevel": self._determine_abnormal_level(result.get('cIllnessGrade', '')),
                        "childrenCode": []
                    }
                    conclusion_list.append(conclusion_data)
            
            # 将疾病诊断转换为结论
            for i, illness in enumerate(illness_results, len(conclusion_list) + 1):
                if illness.get('cIllnessName'):
                    conclusion_data = {
                        "mappingId": f"ILL-{illness.get('cDeptcode', '')}-{illness.get('cIllnessCode', '')}-{i}",
                        "conclusionName": illness.get('cIllnessName', ''),
                        "conclusionCode": illness.get('cIllnessCode', ''),
                        "parentCode": illness.get('cDeptcode', ''),
                        "suggest": illness.get('cAdvice', ''),
                        "explain": illness.get('cIllExplain', ''),
                        "checkResult": illness.get('cCondition', ''),
                        "level": self._determine_level_from_grade(illness.get('cGrade', '')),
                        "displaySequnce": i,
                        "deptId": illness.get('cDeptcode', ''),
                        "abnormalLevel": self._determine_abnormal_level(illness.get('cGrade', '')),
                        "childrenCode": []
                    }
                    conclusion_list.append(conclusion_data)
            
            # 构建返回数据（符合天健云07号接口格式）
            result = {
                "peNo": pe_no,
                "firstCheckFinishTime": str(main_info.get('operDate', ''))[:19] if main_info.get('operDate') else '',
                "firstCheckFinishDoctor": {
                    "code": main_info.get('doctorCode', ''),
                    "name": main_info.get('doctorName', ''),
                    "synonyms": None,
                    "zero": None
                },
                "mainCheckFinishTime": str(main_info.get('operDate', ''))[:19] if main_info.get('operDate') else '',
                "mainCheckFinishDoctor": {
                    "code": main_info.get('doctorCode', ''),
                    "name": main_info.get('doctorName', ''),
                    "synonyms": None,
                    "zero": None
                },
                "currentNodeType": current_node_type,
                "conclusionList": conclusion_list
            }
            
            return result
            
        finally:
            self.db_service.disconnect()
    
    def _determine_abnormal_level(self, grade: str) -> int:
        """根据等级确定异常级别"""
        grade_mapping = {
            '1': 1,  # 正常
            '2': 2,  # 轻度异常
            '3': 3,  # 中度异常
            '4': 4,  # 重度异常
            'A': 1,
            'B': 2,
            'C': 3,
            'D': 4
        }
        return grade_mapping.get(str(grade).upper(), 9)  # 默认9-其他
    
    def _determine_level_from_grade(self, grade: str) -> int:
        """根据等级确定级别"""
        if not grade:
            return 3
        grade_upper = str(grade).upper()
        if grade_upper in ['1', 'A']:
            return 1
        elif grade_upper in ['2', 'B']:
            return 2
        elif grade_upper in ['3', 'C']:
            return 3
        elif grade_upper in ['4', 'D']:
            return 4
        else:
            return 3
    
    def send_conclusion_data(self, pe_no: str, current_node_type: int = 4, test_mode: bool = False) -> Dict[str, Any]:
        """
        发送主检结论数据到天健云
        
        Args:
            pe_no: 体检号
            current_node_type: 当前操作节点 3-主检, 4-总审
            test_mode: 测试模式
            
        Returns:
            发送结果
        """
        try:
            print(f"[LIST] 准备发送体检号 {pe_no} 的主检结论")
            print(f"   当前节点类型: {current_node_type} ({'主检' if current_node_type == 3 else '总审'})")
            
            # 获取结论数据
            conclusion_data = self.get_conclusion_data(pe_no, current_node_type)
            
            if not conclusion_data or not conclusion_data.get('conclusionList'):
                return {
                    'success': False,
                    'message': f'未找到体检号 {pe_no} 的结论数据',
                    'error': 'No conclusion data found'
                }
            
            print(f"[OK] 获取到 {len(conclusion_data['conclusionList'])} 条结论数据")
            
            if test_mode:
                print("[TEST] 测试模式 - 显示结论数据格式:")
                print(json.dumps(conclusion_data, ensure_ascii=False, indent=2))
                
                return {
                    'success': True,
                    'message': f"测试模式完成 - 体检号 {pe_no} 结论数据格式正确",
                    'data': conclusion_data,
                    'conclusion_count': len(conclusion_data['conclusionList'])
                }
            
            # 发送请求
            result = self._send_request(conclusion_data)
            
            if result['success']:
                return {
                    'success': True,
                    'message': f"体检号 {pe_no} 主检结论发送成功",
                    'conclusion_count': len(conclusion_data['conclusionList']),
                    'response': result['response']
                }
            else:
                return {
                    'success': False,
                    'message': f"体检号 {pe_no} 主检结论发送失败",
                    'error': result['error'],
                    'data': conclusion_data
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f"发送主检结论异常: {str(e)}",
                'error': str(e)
            }
    
    def _send_request(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送HTTP请求到天健云
        
        Args:
            data: 请求数据
            
        Returns:
            请求结果
        """
        url = f"{self.api_config['base_url']}{self.endpoint}"
        headers = self.create_headers()
        
        try:
            response = requests.post(
                url,
                headers=headers,
                json=data,
                timeout=self.api_config.get('timeout', 30),
                verify=False
            )
            
            if response.status_code == 200:
                try:
                    response_json = response.json()
                    if response_json.get('code') == 0:
                        return {
                            'success': True,
                            'response': response_json
                        }
                    else:
                        return {
                            'success': False,
                            'error': f"API错误: code={response_json.get('code')}, msg={response_json.get('msg', '未知错误')}",
                            'response': response_json
                        }
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': f"响应解析失败: {response.text}",
                        'response': response.text
                    }
            else:
                return {
                    'success': False,
                    'error': f"HTTP错误: {response.status_code}",
                    'response': response.text
                }
        
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'error': "请求超时"
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'error': "连接错误"
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"请求异常: {str(e)}"
            }
    
    def batch_send_conclusions(self, pe_no_list: List[str], current_node_type: int = 4, 
                              test_mode: bool = False) -> Dict[str, Any]:
        """
        批量发送主检结论
        
        Args:
            pe_no_list: 体检号列表
            current_node_type: 当前操作节点
            test_mode: 测试模式
            
        Returns:
            批量发送结果
        """
        total_count = len(pe_no_list)
        success_count = 0
        failed_count = 0
        errors = []
        
        print(f"[LIST] 开始批量发送主检结论，共 {total_count} 个体检号")
        
        for i, pe_no in enumerate(pe_no_list, 1):
            try:
                result = self.send_conclusion_data(pe_no, current_node_type, test_mode)
                
                if result['success']:
                    success_count += 1
                    print(f"[OK] [{i}/{total_count}] 体检号 {pe_no} 结论发送成功")
                else:
                    failed_count += 1
                    error_msg = f"体检号 {pe_no} 发送失败: {result.get('message', '未知错误')}"
                    errors.append(error_msg)
                    print(f"[FAIL] [{i}/{total_count}] {error_msg}")
                    
            except Exception as e:
                failed_count += 1
                error_msg = f"体检号 {pe_no} 处理异常: {str(e)}"
                errors.append(error_msg)
                print(f"[FAIL] [{i}/{total_count}] {error_msg}")
        
        return {
            'total': total_count,
            'success': success_count,
            'failed': failed_count,
            'errors': errors,
            'success_rate': f"{(success_count/total_count*100):.1f}%" if total_count > 0 else "0%"
        }


# API配置
API_CONFIG = {
    'base_url': 'http://203.83.237.114:9300',
    'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
    'mic_code': 'MIC1.001E',
    'misc_id': 'MISC1.00001A',
    'timeout': 30
}


def test_interface_07():
    """测试07号接口"""
    print("测试天健云07号接口 - 主检结束结论回传")
    print("=" * 60)
    
    # 创建接口实例
    interface = TianjianInterface07(API_CONFIG)
    
    # 测试场景1：单个体检号结论发送
    print("\n[LIST] 测试场景1：单个体检号结论发送")
    result1 = interface.send_conclusion_data(
        pe_no="0220000001", 
        current_node_type=4,  # 总审
        test_mode=True
    )
    print(f"结果: {json.dumps(result1, ensure_ascii=False, indent=2)}")
    
    # 测试场景2：主检节点结论发送
    print("\n[LIST] 测试场景2：主检节点结论发送")
    result2 = interface.send_conclusion_data(
        pe_no="0220000002",
        current_node_type=3,  # 主检
        test_mode=True
    )
    print(f"结果: {json.dumps(result2, ensure_ascii=False, indent=2)}")
    
    print("\n[OK] 天健云07号接口测试完成")


if __name__ == "__main__":
    test_interface_07()