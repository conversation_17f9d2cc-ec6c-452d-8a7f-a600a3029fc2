#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云08号接口实现 - 查询字典信息接口
/dx/inter-info/syncDict
"""

import json
import hashlib
import requests
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from database_service import get_database_service, DatabaseService
from multi_org_config import get_org_config_by_shop_code
from config import Config
from api_config_manager import get_tianjian_base_url


class TianjianInterface08:
    """天健云08号接口 - 查询字典信息接口"""
    
    def __init__(self, api_config: Dict[str, Any]):
        """
        初始化接口配置
        
        Args:
            api_config: API配置信息
        """
        self.api_config = api_config
        self.db_service = None  # 延迟初始化

    def _get_database_service_by_hospital_code(self, hospital_code: str = None) -> DatabaseService:
        """
        根据hospitalCode获取对应的数据库服务
        
        Args:
            hospital_code: 医院编码
            
        Returns:
            DatabaseService实例
        """
        if hospital_code:
            # 根据hospitalCode获取对应的机构配置
            org_config = get_org_config_by_shop_code(hospital_code)
            if org_config:
                # 构建连接字符串
                connection_string = (
                    f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                    f"SERVER={org_config['db_host']},{org_config['db_port']};"
                    f"DATABASE={org_config['db_name']};"
                    f"UID={org_config['db_user']};"
                    f"PWD={org_config['db_password']};"
                    f"TrustServerCertificate=yes;"
                )
                return DatabaseService(connection_string)
        
        # 如果没有指定hospitalCode或找不到配置，使用默认数据库服务
        if self.db_service is None:
            self.db_service = get_database_service()
        return self.db_service
    
    def generate_signature(self, api_key: str, timestamp: str) -> str:
        """生成MD5签名"""
        sign_string = api_key + timestamp
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    def create_headers(self) -> Dict[str, str]:
        """创建请求头"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        nonce = str(uuid.uuid4())
        signature = self.generate_signature(self.api_config['api_key'], timestamp)
        
        return {
            'Content-Type': 'application/json',
            'sign': signature,
            'timestamp': timestamp,
            'nonce': nonce,
            'mic-code': self.api_config['mic_code'],
            'misc-id': self.api_config['misc_id']
        }
    
    def get_dict_data(self, dict_type: str = "all", dict_code: str = "", 
                     hospital_code: str = None) -> Dict[str, Any]:
        """
        获取字典数据（基于实际数据库结构）
        
        Args:
            dict_type: 字典类型 (item/dept/operator/all)
            dict_code: 具体字典编码（可选）
            hospital_code: 医院编码
            
        Returns:
            字典数据列表
        """
        # 根据hospital_code获取对应的数据库服务
        db_service = self._get_database_service_by_hospital_code(hospital_code)
        
        if not db_service.connect():
            raise Exception("数据库连接失败")
        
        try:
            dict_data = {
                "items": [],
                "departments": [],
                "operators": []
            }
            
            # 查询检查项目字典
            if dict_type in ["item", "all"]:
                # 根据数据库结构选择不同的查询方式
                try:
                    # 08门店实际字段查询（已验证的正确字段）
                    item_sql = """
                    SELECT 
                        cCode as code,
                        cName as name,
                        '' as type,
                        '' as unit,
                        ISNULL(nIndex, 0) as sort_order,
                        CASE WHEN cStopTag = '1' THEN '0' ELSE '1' END as status,
                        '' as operator_name,
                        GETDATE() as operate_date
                    FROM Code_Item_Main 
                    WHERE (? = '' OR cCode = ?)
                    AND (cStopTag IS NULL OR cStopTag != '1')
                    ORDER BY nIndex, cCode
                    """
                    
                    item_results = db_service.execute_query(item_sql, (dict_code, dict_code))
                except Exception as e:
                    # 如果标准字段查询失败，使用基础字段查询（适用于09门店等）
                    print("   [INFO] 使用基础字段查询检查项目字典")
                    item_sql = "SELECT cCode as code, cName as name, '' as type, '' as unit, ISNULL(nIndex, 0) as sort_order, CASE WHEN cStopTag = '1' THEN '0' ELSE '1' END as status, '' as operator_name, GETDATE() as operate_date FROM Code_Item_Main WHERE (? = '' OR cCode = ?) AND (cStopTag IS NULL OR cStopTag != '1') ORDER BY nIndex, cCode"
                    
                    item_results = db_service.execute_query(item_sql, (dict_code, dict_code))
                
                for item in item_results or []:
                    item_data = {
                        "code": item.get('code', ''),
                        "name": item.get('name', ''),
                        "type": item.get('type', ''),
                        "unit": item.get('unit', ''),
                        "sortOrder": int(item.get('sort_order', 0)) if item.get('sort_order') else 0,
                        "status": item.get('status', ''),
                        "operatorName": item.get('operator_name', ''),
                        "operateDate": str(item.get('operate_date', ''))[:19] if item.get('operate_date') else ''
                    }
                    dict_data["items"].append(item_data)
            
            # 查询科室字典
            if dict_type in ["dept", "all"]:
                # 根据数据库结构选择不同的查询方式
                try:
                    # 08门店实际字段查询（已验证的正确字段）
                    dept_sql = """
                    SELECT 
                        cCode as code,
                        cName as name,
                        '' as type,
                        ISNULL(nCheckIndex, 0) as sort_order,
                        CASE WHEN cStopTag = '1' THEN '0' ELSE '1' END as status,
                        '' as operator_name,
                        GETDATE() as operate_date
                    FROM Code_Dept_dict 
                    WHERE (? = '' OR cCode = ?)
                    AND (cStopTag IS NULL OR cStopTag != '1')
                    ORDER BY nCheckIndex, cCode
                    """
                    
                    dept_results = db_service.execute_query(dept_sql, (dict_code, dict_code))
                except Exception as e:
                    # 如果标准字段查询失败，使用基础字段查询（适用于09门店等）
                    print("   [INFO] 使用基础字段查询科室字典")
                    dept_sql = "SELECT cCode as code, cName as name, '' as type, ISNULL(nCheckIndex, 0) as sort_order, CASE WHEN cStopTag = '1' THEN '0' ELSE '1' END as status, '' as operator_name, GETDATE() as operate_date FROM Code_Dept_dict WHERE (? = '' OR cCode = ?) AND (cStopTag IS NULL OR cStopTag != '1') ORDER BY nCheckIndex, cCode"
                    
                    dept_results = db_service.execute_query(dept_sql, (dict_code, dict_code))
                
                for dept in dept_results or []:
                    dept_data = {
                        "code": dept.get('code', ''),
                        "name": dept.get('name', ''),
                        "type": dept.get('type', ''),
                        "sortOrder": int(dept.get('sort_order', 0)) if dept.get('sort_order') else 0,
                        "status": dept.get('status', ''),
                        "operatorName": dept.get('operator_name', ''),
                        "operateDate": str(dept.get('operate_date', ''))[:19] if dept.get('operate_date') else ''
                    }
                    dict_data["departments"].append(dept_data)
            
            # 查询操作员字典
            if dict_type in ["operator", "all"]:
                # 根据数据库结构选择不同的查询方式
                try:
                    # 08门店实际字段查询（已验证的正确字段）
                    operator_sql = """
                    SELECT 
                        cCode as code,
                        cName as name,
                        '' as type,
                        CASE WHEN cStopTag = '1' THEN '0' ELSE '1' END as status,
                        '' as operator_name,
                        GETDATE() as operate_date
                    FROM Code_Operator_dict 
                    WHERE (? = '' OR cCode = ?)
                    AND (cStopTag IS NULL OR cStopTag != '1')
                    ORDER BY cCode
                    """
                    
                    operator_results = db_service.execute_query(operator_sql, (dict_code, dict_code))
                except Exception as e:
                    # 如果标准字段查询失败，使用基础字段查询（适用于09门店等）
                    print("   [INFO] 使用基础字段查询操作员字典")
                    operator_sql = "SELECT cCode as code, cName as name, '' as type, CASE WHEN cStopTag = '1' THEN '0' ELSE '1' END as status, '' as operator_name, GETDATE() as operate_date FROM Code_Operator_dict WHERE (? = '' OR cCode = ?) AND (cStopTag IS NULL OR cStopTag != '1') ORDER BY cCode"
                    
                    operator_results = db_service.execute_query(operator_sql, (dict_code, dict_code))
                
                for operator in operator_results or []:
                    operator_data = {
                        "code": operator.get('code', ''),
                        "name": operator.get('name', ''),
                        "type": operator.get('type', ''),
                        "status": operator.get('status', ''),
                        "operatorName": operator.get('operator_name', ''),
                        "operateDate": str(operator.get('operate_date', ''))[:19] if operator.get('operate_date') else ''
                    }
                    dict_data["operators"].append(operator_data)
            
            # 构建返回数据（符合天健云08号接口格式）
            result = {
                "dictType": dict_type,
                "dictCode": dict_code,
                "updateTime": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "data": dict_data,
                "totalCount": {
                    "items": len(dict_data["items"]),
                    "departments": len(dict_data["departments"]),
                    "operators": len(dict_data["operators"])
                }
            }
            
            return result
            
        finally:
            db_service.disconnect()
    
    def get_item_details(self, main_code: str, hospital_code: str = None) -> List[Dict[str, Any]]:
        """
        获取检查项目明细信息
        
        Args:
            main_code: 主项目编码
            hospital_code: 医院编码
            
        Returns:
            项目明细列表
        """
        # 根据hospital_code获取对应的数据库服务
        db_service = self._get_database_service_by_hospital_code(hospital_code)
        
        if not db_service.connect():
            raise Exception("数据库连接失败")
        
        try:
            detail_sql = """
            SELECT 
                cDetailCode as detail_code,
                cName as name,
                cUnit as unit,
                cConsult as reference_value,
                cSort as sort_order,
                cStatus as status
            FROM Code_Item_Detail 
            WHERE cMainCode = ?
            AND (cStatus IS NULL OR cStatus != '0')
            ORDER BY cSort, cDetailCode
            """
            
            detail_results = db_service.execute_query(detail_sql, (main_code,))
            
            details = []
            for detail in detail_results or []:
                detail_data = {
                    "detailCode": detail.get('detail_code', ''),
                    "name": detail.get('name', ''),
                    "unit": detail.get('unit', ''),
                    "referenceValue": detail.get('reference_value', ''),
                    "sortOrder": int(detail.get('sort_order', 0)) if detail.get('sort_order') else 0,
                    "status": detail.get('status', '')
                }
                details.append(detail_data)
            
            return details
            
        finally:
            db_service.disconnect()
    
    def query_dict_info(self, dict_type: str = "all", dict_code: str = "", 
                       include_details: bool = False, test_mode: bool = False, 
                       hospital_code: str = None) -> Dict[str, Any]:
        """
        查询字典信息
        
        Args:
            dict_type: 字典类型 (item/dept/operator/all)
            dict_code: 具体字典编码
            include_details: 是否包含项目明细
            test_mode: 测试模式
            hospital_code: 医院编码
            
        Returns:
            查询结果
        """
        try:
            print(f"📚 查询字典信息")
            print(f"   字典类型: {dict_type}")
            print(f"   字典编码: {dict_code if dict_code else '全部'}")
            print(f"   包含明细: {'是' if include_details else '否'}")
            print(f"   医院编码: {hospital_code or '默认'}")
            
            # 获取字典数据
            dict_data = self.get_dict_data(dict_type, dict_code, hospital_code)
            
            # 如果需要包含项目明细
            if include_details and dict_type in ["item", "all"]:
                for item in dict_data["data"]["items"]:
                    item_code = item.get("code", "")
                    if item_code:
                        item["details"] = self.get_item_details(item_code, hospital_code)
            
            total_items = dict_data["totalCount"]["items"]
            total_depts = dict_data["totalCount"]["departments"]
            total_operators = dict_data["totalCount"]["operators"]
            
            print(f"[OK] 获取到字典数据:")
            print(f"   检查项目: {total_items} 条")
            print(f"   科室信息: {total_depts} 条")
            print(f"   操作员: {total_operators} 条")
            
            if test_mode:
                print("[TEST] 测试模式 - 显示字典数据格式:")
                print(json.dumps(dict_data, ensure_ascii=False, indent=2))
                
                return {
                    'success': True,
                    'message': f"测试模式完成 - 字典类型 {dict_type} 数据格式正确",
                    'data': dict_data,
                    'item_count': total_items,
                    'dept_count': total_depts,
                    'operator_count': total_operators
                }
            
            # 发送请求
            result = self._send_request(dict_data)
            
            if result['success']:
                return {
                    'success': True,
                    'message': f"字典类型 {dict_type} 查询成功",
                    'item_count': total_items,
                    'dept_count': total_depts,
                    'operator_count': total_operators,
                    'response': result['response']
                }
            else:
                return {
                    'success': False,
                    'message': f"字典类型 {dict_type} 查询失败",
                    'error': result['error'],
                    'data': dict_data
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f"查询字典信息异常: {str(e)}",
                'error': str(e)
            }
    
    def _send_request(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送HTTP请求到天健云
        
        Args:
            data: 请求数据
            
        Returns:
            请求结果
        """
        url = f"{self.api_config['base_url']}/dx/inter-info/syncDict"
        headers = self.create_headers()
        
        try:
            response = requests.post(
                url,
                headers=headers,
                json=data,
                timeout=self.api_config.get('timeout', 30),
                verify=False
            )
            
            if response.status_code == 200:
                try:
                    response_json = response.json()
                    if response_json.get('code') == 0:
                        return {
                            'success': True,
                            'response': response_json
                        }
                    else:
                        return {
                            'success': False,
                            'error': f"API错误: code={response_json.get('code')}, msg={response_json.get('msg', '未知错误')}",
                            'response': response_json
                        }
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': f"响应解析失败: {response.text}",
                        'response': response.text
                    }
            else:
                return {
                    'success': False,
                    'error': f"HTTP错误: {response.status_code}",
                    'response': response.text
                }
        
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'error': "请求超时"
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'error': "连接错误"
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"请求异常: {str(e)}"
            }
    
    def sync_all_dict_data(self, test_mode: bool = False, hospital_code: str = None) -> Dict[str, Any]:
        """
        同步所有字典数据
        
        Args:
            test_mode: 测试模式
            hospital_code: 医院编码
            
        Returns:
            同步结果
        """
        print("📚 开始同步所有字典数据")
        print(f"   医院编码: {hospital_code or '默认'}")
        
        sync_results = {
            "items": None,
            "departments": None,
            "operators": None
        }
        
        # 同步检查项目字典
        print("\n1. 同步检查项目字典...")
        sync_results["items"] = self.query_dict_info("item", "", True, test_mode, hospital_code)
        
        # 同步科室字典
        print("\n2. 同步科室字典...")
        sync_results["departments"] = self.query_dict_info("dept", "", False, test_mode, hospital_code)
        
        # 同步操作员字典
        print("\n3. 同步操作员字典...")
        sync_results["operators"] = self.query_dict_info("operator", "", False, test_mode, hospital_code)
        
        # 统计结果
        success_count = sum(1 for result in sync_results.values() if result and result.get('success'))
        total_count = len(sync_results)
        
        return {
            'total': total_count,
            'success': success_count,
            'failed': total_count - success_count,
            'results': sync_results,
            'success_rate': f"{(success_count/total_count*100):.1f}%" if total_count > 0 else "0%"
        }

    def get_dict(self, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        查询字典信息（路由接口方法）

        Args:
            data: 请求数据，包含dict_type、dict_code和hospitalCode字段

        Returns:
            查询结果
        """
        try:
            # 从请求数据中提取参数
            if data:
                # 支持多种参数名映射
                dict_type = data.get('dict_type') or data.get('type') or 'all'
                dict_code = data.get('dict_code') or data.get('id') or ''
                # 支持多种字段名：hospitalCode（新标准）、shopcode（兼容）、cshopcode（兼容）
                hospital_code = data.get('hospitalCode') or data.get('shopcode') or data.get('cshopcode', '')
            else:
                dict_type = 'all'
                dict_code = ''
                hospital_code = ''

            # 当type为空字符串时，默认查询所有类型
            if not dict_type or dict_type == '':
                dict_type = 'all'

            print(f"[08接口] 收到请求: type={dict_type}, id={dict_code}, hospitalCode={hospital_code}")

            # 调用查询方法
            return self.get_dict_data(dict_type, dict_code, hospital_code)

        except Exception as e:
            return {
                'success': False,
                'error': f'查询失败: {str(e)}',
                'data': {}
            }


# API配置
API_CONFIG = {
    'base_url': get_tianjian_base_url(),
    'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
    'mic_code': 'MIC1.001E',
    'misc_id': 'MISC1.00001A',
    'timeout': 30
}


def test_interface_08():
    """测试08号接口"""
    print("测试天健云08号接口 - 查询字典信息接口")
    print("=" * 60)
    
    # 创建接口实例
    interface = TianjianInterface08(API_CONFIG)
    
    # 测试场景1：查询所有字典信息
    print("\n📚 测试场景1：查询所有字典信息")
    result1 = interface.query_dict_info(
        dict_type="all",
        include_details=False,
        test_mode=True
    )
    print(f"结果: {json.dumps(result1, ensure_ascii=False, indent=2)}")
    
    # 测试场景2：查询检查项目字典（包含明细）
    print("\n📚 测试场景2：查询检查项目字典（包含明细）")
    result2 = interface.query_dict_info(
        dict_type="item",
        include_details=True,
        test_mode=True
    )
    print(f"结果: {json.dumps(result2, ensure_ascii=False, indent=2)}")
    
    # 测试场景3：查询指定科室字典
    print("\n📚 测试场景3：查询指定科室字典")
    result3 = interface.query_dict_info(
        dict_type="dept",
        dict_code="01",
        test_mode=True
    )
    print(f"结果: {json.dumps(result3, ensure_ascii=False, indent=2)}")
    
    print("\n[OK] 天健云08号接口测试完成")


if __name__ == "__main__":
    test_interface_08()