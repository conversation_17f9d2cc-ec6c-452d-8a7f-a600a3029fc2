#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云09号接口实现 - 体检科室结果重传接口
基于03号接口的数据重传功能
"""

import json
import hashlib
import requests
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from database_service import get_database_service
from multi_org_config import get_current_org_config


class TianjianInterface09:
    """天健云09号接口 - 体检科室结果重传接口"""
    
    def __init__(self, api_config: Dict[str, Any] = None, org_config: Dict[str, Any] = None):
        """
        初始化接口配置
        
        Args:
            api_config: API配置信息（可选，默认使用中心库配置）
            org_config: 机构配置信息（可选，默认使用当前机构配置）
        """
        # 获取当前机构配置
        self.org_config = org_config or get_current_org_config()

        # 使用机构配置中的天健云API配置
        if api_config:
            self.api_config = api_config
        else:
            self.api_config = {
                'base_url': self.org_config.get('tianjian_base_url', 'http://203.83.237.114:9300'),
                'api_key': self.org_config.get('tianjian_api_key', ''),
                'mic_code': self.org_config.get('tianjian_mic_code', ''),
                'misc_id': self.org_config.get('tianjian_misc_id', ''),
                'timeout': 30
            }

        self.db_service = get_database_service()
    
    def generate_signature(self, api_key: str, timestamp: str) -> str:
        """生成MD5签名"""
        sign_string = api_key + timestamp
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    def create_headers(self) -> Dict[str, str]:
        """创建请求头"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        nonce = str(uuid.uuid4())
        signature = self.generate_signature(self.api_config['api_key'], timestamp)
        
        return {
            'Content-Type': 'application/json',
            'sign': signature,
            'timestamp': timestamp,
            'nonce': nonce,
            'mic-code': self.api_config['mic_code'],
            'misc-id': self.api_config['misc_id']
        }
    
    def get_dept_results_for_retransmit(self, client_codes: List[str] = None, 
                                       pe_nos: List[str] = None,
                                       start_date: str = None, end_date: str = None,
                                       dept_codes: List[str] = None,
                                       limit: int = None) -> List[Dict[str, Any]]:
        """
        获取需要重传的科室结果数据
        
        Args:
            client_codes: 客户编号列表
            pe_nos: 体检号列表  
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            dept_codes: 科室编码列表
            limit: 限制返回条数
            
        Returns:
            科室结果数据列表
        """
        if not self.db_service.connect():
            raise Exception("数据库连接失败")
        
        try:
            # 构建SQL查询科室结果（与03号接口相同的数据结构）
            sql = """
            SELECT 
                rm.cClientCode as peNo,
                dm.cDeptCode as dept_code,
                dd.cName as dept_name,
                crm.dOperDate as checkTime,
                crm.cOperName as checkDoctor_code,
                crm.cOperName as checkDoctor_name,
                crm.cResult as summary,
                crm.cDoctName as auditDoctor_code,
                crm.cDoctName as auditDoctor_name,
                CASE 
                    WHEN crm.cResult IS NOT NULL AND crm.cResult != '' THEN 'isAudited'
                    ELSE 'notAudited'
                END as auditStatus_code,
                CASE 
                    WHEN crm.cResult IS NOT NULL AND crm.cResult != '' THEN '已审核'
                    ELSE '未审核'
                END as auditStatus_name,
                crm.dOperDate as auditTime,
                rm.dOperdate as peDate
            FROM T_Register_Main rm
            INNER JOIN T_Check_result_Main crm ON rm.cClientCode = crm.cClientCode
            INNER JOIN Code_Dept_Main dm ON crm.cMainCode = dm.cMainCode
            INNER JOIN Code_Dept_dict dd ON dm.cDeptCode = dd.cCode
            WHERE 1=1
            """
            
            params = []
            
            # 添加客户编号条件
            if client_codes:
                placeholders = ','.join(['?' for _ in client_codes])
                sql += f" AND rm.cClientCode IN ({placeholders})"
                params.extend(client_codes)
            
            # 添加体检号条件
            if pe_nos:
                placeholders = ','.join(['?' for _ in pe_nos])
                sql += f" AND rm.cClientCode IN ({placeholders})"
                params.extend(pe_nos)
            
            # 添加日期范围条件
            if start_date:
                sql += " AND rm.dOperdate >= ?"
                params.append(start_date)
            
            if end_date:
                sql += " AND rm.dOperdate <= ?"
                params.append(end_date)
            
            # 添加科室条件
            if dept_codes:
                placeholders = ','.join(['?' for _ in dept_codes])
                sql += f" AND dm.cDeptCode IN ({placeholders})"
                params.extend(dept_codes)
            
            # 只查询有检查结果的记录（重传通常针对已有结果的记录）
            sql += " AND crm.cResult IS NOT NULL AND crm.cResult != ''"
            
            sql += " ORDER BY rm.cClientCode, dm.cDeptCode"
            
            if limit:
                # 直接在主查询中使用TOP
                sql = sql.replace("SELECT ", f"SELECT TOP {limit} ")
            
            dept_results = self.db_service.execute_query(sql, tuple(params) if params else None)
            
            # 为每个科室结果获取检查明细
            result = []
            for dept_result in dept_results:
                # 获取该科室的检查明细
                item_desc = self._get_item_desc_for_dept(dept_result['peNo'], dept_result['dept_code'])
                
                dept_data = {
                    "peNo": dept_result['peNo'],
                    "dept": {
                        "code": dept_result['dept_code'],
                        "name": dept_result['dept_name']
                    },
                    "hospital": {
                        "code": self.org_config.get('org_code', 'DEFAULT'),
                        "name": self.org_config.get('org_name', '默认医院')
                    },
                    "checkTime": dept_result['checkTime'] or '',
                    "checkDoctor": {
                        "code": dept_result['checkDoctor_code'] or '',
                        "name": dept_result['checkDoctor_name'] or ''
                    },
                    "summary": dept_result['summary'] or '检查完成',
                    "auditDoctor": {
                        "code": dept_result['auditDoctor_code'] or '',
                        "name": dept_result['auditDoctor_name'] or ''
                    },
                    "auditStatus": {
                        "code": dept_result['auditStatus_code'],
                        "name": dept_result['auditStatus_name']
                    },
                    "auditTime": dept_result['auditTime'] or '',
                    "itemDesc": item_desc,
                    "retransmitFlag": True,  # 标记为重传数据
                    "originalPeDate": dept_result['peDate'] or ''
                }
                result.append(dept_data)
            
            return result
            
        finally:
            self.db_service.disconnect()
    
    def _get_item_desc_for_dept(self, client_code: str, dept_code: str) -> List[Dict[str, Any]]:
        """
        获取特定科室的检查明细（与03号接口相同）
        
        Args:
            client_code: 客户编号
            dept_code: 科室编码
            
        Returns:
            检查明细列表
        """
        sql = """
        SELECT 
            im.cCode as bizApplyId,
            im.cCode as applyItem_code,
            im.cName as applyItem_name,
            cr.cOperName as checkDoctor_code,
            cr.cOperName as checkDoctor_name,
            id.cCode as checkItem_code,
            id.cName as checkItem_name,
            cr.cResult as inspectResult,
            id.cUnit as unit,
            id.cConsult as reference,
            CAST(ROW_NUMBER() OVER (ORDER BY id.cCode) AS VARCHAR) as displaySequence,
            cr.cResult as digitValue,
            CASE cr.cAbnor
                WHEN '1' THEN '↑'
                WHEN '2' THEN '↓'
                ELSE ''
            END as mds_name
        FROM T_Check_result cr
        INNER JOIN Code_Item_Detail id ON cr.cDetailCode = id.cCode
        INNER JOIN Code_Item_Main im ON id.cMainCode = im.cCode
        INNER JOIN Code_Dept_Main dm ON im.cCode = dm.cMainCode
        WHERE cr.cClientCode = ?
        AND dm.cDeptCode = ?
        ORDER BY id.cCode
        """
        
        result = self.db_service.execute_query(sql, (client_code, dept_code))
        
        # 格式化结果
        item_desc = []
        for item in result:
            # 解析参考值范围
            reference = item.get('reference', '')
            reference_min = 0
            reference_max = 0
            
            if reference and '-' in reference:
                try:
                    parts = reference.split('-')
                    if len(parts) == 2:
                        reference_min = float(parts[0].strip())
                        reference_max = float(parts[1].strip())
                except:
                    pass
            
            item_data = {
                "bizApplyId": item['bizApplyId'],
                "applyItem": {
                    "code": item['applyItem_code'],
                    "name": item['applyItem_name']
                },
                "checkDoctor": {
                    "code": item['checkDoctor_code'] or '',
                    "name": item['checkDoctor_name'] or ''
                },
                "checkItem": {
                    "code": item['checkItem_code'],
                    "name": item['checkItem_name']
                },
                "inspectResult": item['inspectResult'] or '',
                "unit": item['unit'] or '',
                "referenceMax": reference_max,
                "referenceMin": reference_min,
                "reference": reference,
                "displaySequence": item['displaySequence'],
                "digitValue": item['digitValue'] or '',
                "mds": {
                    "code": "",
                    "name": item['mds_name']
                }
            }
            item_desc.append(item_data)
        
        return item_desc
    
    def retransmit_dept_results(self, client_codes: List[str] = None,
                               pe_nos: List[str] = None,
                               start_date: str = None, end_date: str = None,
                               dept_codes: List[str] = None,
                               limit: int = None, test_mode: bool = False,
                               batch_size: int = 10) -> Dict[str, Any]:
        """
        重传科室结果数据到天健云
        
        Args:
            client_codes: 客户编号列表
            pe_nos: 体检号列表
            start_date: 开始日期
            end_date: 结束日期
            dept_codes: 科室编码列表
            limit: 限制重传条数
            test_mode: 测试模式
            batch_size: 批量发送大小
            
        Returns:
            重传结果
        """
        try:
            # 获取需要重传的科室结果数据
            dept_results = self.get_dept_results_for_retransmit(
                client_codes, pe_nos, start_date, end_date, dept_codes, limit
            )
            
            if not dept_results:
                return {
                    'success': True,
                    'message': '没有找到需要重传的科室结果数据',
                    'total': 0,
                    'sent': 0,
                    'failed': 0
                }
            
            total = len(dept_results)
            sent = 0
            failed = 0
            errors = []
            
            print(f"准备重传 {total} 条科室结果到天健云")
            
            if test_mode:
                print("测试模式 - 显示前2条重传科室结果的数据格式:")
                for i, item in enumerate(dept_results[:2], 1):
                    print(f"\n第 {i} 条重传科室结果:")
                    print(json.dumps(item, ensure_ascii=False, indent=2))
                
                return {
                    'success': True,
                    'message': f"测试模式完成 - 共 {total} 条科室结果格式正确",
                    'total': total,
                    'sent': total,
                    'failed': 0,
                    'errors': []
                }
            
            # 分批重传
            for i in range(0, total, batch_size):
                batch = dept_results[i:i + batch_size]
                batch_num = i // batch_size + 1
                total_batches = (total + batch_size - 1) // batch_size
                
                print(f"\n重传第 {batch_num}/{total_batches} 批次，包含 {len(batch)} 条科室结果")
                
                try:
                    # 发送重传请求
                    result = self._send_retransmit_request(batch)
                    
                    if result['success']:
                        sent += len(batch)
                        print(f"✓ 第 {batch_num} 批次重传成功")
                    else:
                        failed += len(batch)
                        error_msg = f"第 {batch_num} 批次重传失败: {result.get('error', '未知错误')}"
                        print(f"✗ {error_msg}")
                        errors.append(error_msg)
                
                except Exception as e:
                    failed += len(batch)
                    error_msg = f"第 {batch_num} 批次处理异常: {str(e)}"
                    print(f"✗ {error_msg}")
                    errors.append(error_msg)
            
            # 返回结果
            return {
                'success': failed == 0,
                'message': f"重传完成 - 成功: {sent}, 失败: {failed}",
                'total': total,
                'sent': sent,
                'failed': failed,
                'errors': errors
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"重传过程异常: {str(e)}",
                'total': 0,
                'sent': 0,
                'failed': 0
            }
    
    def _send_retransmit_request(self, request_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        发送重传HTTP请求到天健云
        
        Args:
            request_data: 请求数据
            
        Returns:
            请求结果
        """
        # 使用03号接口的URL，但添加重传标识
        url = f"{self.api_config['base_url']}/dx/inter/deptInfo"
        headers = self.create_headers()
        
        # 在请求头中添加重传标识
        headers['X-Retransmit'] = 'true'
        headers['X-Retransmit-Reason'] = 'dept_result_retransmit'
        
        try:
            response = requests.post(
                url,
                headers=headers,
                json=request_data,
                timeout=self.api_config.get('timeout', 30),
                verify=False
            )
            
            if response.status_code == 200:
                try:
                    response_json = response.json()
                    if response_json.get('code') == 0:
                        return {
                            'success': True,
                            'response': response_json
                        }
                    else:
                        return {
                            'success': False,
                            'error': f"API错误: code={response_json.get('code')}, msg={response_json.get('msg', '未知错误')}",
                            'response': response_json
                        }
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': f"响应解析失败: {response.text}",
                        'response': response.text
                    }
            else:
                return {
                    'success': False,
                    'error': f"HTTP错误: {response.status_code}",
                    'response': response.text
                }
        
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'error': "请求超时"
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'error': "连接错误"
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"请求异常: {str(e)}"
            }


# API配置
API_CONFIG = {
    'base_url': 'http://203.83.237.114:9300',
    'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
    'mic_code': 'MIC1.001E',
    'misc_id': 'MISC1.00001A',
    'timeout': 30
}


def main():
    """主函数 - 测试09号接口"""
    print("天健云09号接口测试 - 体检科室结果重传接口")
    print("=" * 60)
    
    # 创建接口实例
    interface = TianjianInterface09(API_CONFIG)
    
    # 测试模式 - 重传最近7天的科室结果
    print("\n1. 测试模式 - 重传最近7天内前3条科室结果")
    from datetime import datetime, timedelta
    from config import Config

    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
    
    result = interface.retransmit_dept_results(
        start_date=start_date, 
        end_date=end_date, 
        limit=3, 
        test_mode=True
    )
    print(f"测试结果: {result}")
    
    # 测试重传特定客户的科室结果
    print("\n2. 测试模式 - 重传特定客户的科室结果")
    result = interface.retransmit_dept_results(
        client_codes=['2024001', '2024002'], 
        limit=2, 
        test_mode=True
    )
    print(f"特定客户重传测试结果: {result}")
    
    # 实际重传（小批量测试）
    print("\n3. 实际重传 - 最近7天内前2条科室结果")
    confirm = input("是否继续实际重传到天健云？(y/N): ")
    if confirm.lower() == 'y':
        result = interface.retransmit_dept_results(
            start_date=start_date,
            end_date=end_date,
            limit=2, 
            test_mode=False, 
            batch_size=1
        )
        print(f"重传结果: {result}")
    else:
        print("已取消实际重传")


if __name__ == '__main__':
    main()