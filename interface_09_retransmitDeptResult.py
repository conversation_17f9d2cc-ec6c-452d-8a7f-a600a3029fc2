#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云09号接口实现 - 体检科室结果重传接口
根据实际数据库结构完善的版本
"""

import json
import hashlib
import requests
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from database_service import get_database_service
from config import Config
from multi_org_config import get_current_org_config


class TianjianInterface09:
    """天健云09号接口 - 体检科室结果重传接口"""
    
    def __init__(self, api_config: Dict[str, Any] = None, org_config: Dict[str, Any] = None):
        """
        初始化接口配置
        
        Args:
            api_config: API配置信息（可选，默认使用中心库配置）
            org_config: 机构配置信息（可选，默认使用当前机构配置）
        """
        # 获取当前机构配置
        self.org_config = org_config or get_current_org_config()

        # 使用机构配置中的天健云API配置
        if api_config:
            self.api_config = api_config
        else:
            self.api_config = {
                'base_url': self.org_config.get('tianjian_base_url', 'http://203.83.237.114:9300'),
                'api_key': self.org_config.get('tianjian_api_key', ''),
                'mic_code': self.org_config.get('tianjian_mic_code', ''),
                'misc_id': self.org_config.get('tianjian_misc_id', ''),
                'timeout': 30
            }

        self.db_service = get_database_service()
        self.endpoint = "/dx/inter/retransmitDeptResult"
    
    def generate_signature(self, api_key: str, timestamp: str) -> str:
        """生成MD5签名"""
        sign_string = f"{api_key}{timestamp}"
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    def create_headers(self) -> Dict[str, str]:
        """创建请求头"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        nonce = str(uuid.uuid4())
        signature = self.generate_signature(self.api_config['api_key'], timestamp)
        
        return {
            'Content-Type': 'application/json',
            'sign': signature,
            'timestamp': timestamp,
            'nonce': nonce,
            'mic-code': self.api_config['mic_code'],
            'misc-id': self.api_config['misc_id']
        }
    
    def get_dept_result_data(self, pe_no: str, dept_code: str = "", item_code: str = "") -> Dict[str, Any]:
        """
        获取科室结果数据（基于实际数据库结构）
        
        Args:
            pe_no: 体检号（客户编码）
            dept_code: 科室代码（可选）
            item_code: 项目代码（可选）
            
        Returns:
            科室结果数据
        """
        if not self.db_service.connect():
            raise Exception("数据库连接失败")
        
        try:
            # 获取基本体检信息
            pe_info_sql = """
            SELECT 
                cClientCode,
                cName,
                cSex,
                dBornDate,
                cIdCard,
                cStatus,
                dOperdate as register_date
            FROM T_Register_Main 
            WHERE cClientCode = ?
            """
            
            pe_info_result = self.db_service.execute_query(pe_info_sql, (pe_no,))
            if not pe_info_result:
                return {}
            
            pe_info = pe_info_result[0]
            
            # 获取科室检查结果主数据
            dept_result_sql = """
            SELECT 
                crm.cDeptCode,
                crm.cMainCode,
                crm.cMainName,
                crm.cResultCure as main_result,
                crm.cDoctCode,
                crm.cDoctName,
                crm.dOperDate,
                crm.csuojian as findings,
                crm.ctishi as suggestions,
                crm.cAuditDoctCode,
                crm.cAuditDoctName,
                crm.cIllnessGrade,
                dd.cName as dept_name
            FROM T_Check_result_Main crm
            LEFT JOIN Code_Dept_dict dd ON crm.cDeptCode = dd.cCode
            WHERE crm.cClientCode = ?
            AND (? = '' OR crm.cDeptCode = ?)
            AND (? = '' OR crm.cMainCode = ?)
            ORDER BY crm.cDeptCode, crm.cMainCode
            """
            
            dept_results = self.db_service.execute_query(
                dept_result_sql, 
                (pe_no, dept_code, dept_code, item_code, item_code)
            )
            
            # 获取明细检查结果
            detail_result_sql = """
            SELECT 
                cr.cDeptCode,
                cr.cMainCode,
                cr.cDetailCode,
                cr.cResult,
                cr.cResultDesc,
                cr.cAbnor,
                cr.cDoctName,
                cr.dOperDate,
                cid.cName as detail_name,
                cid.cUnit as unit,
                cid.cConsult as reference_value
            FROM T_Check_result cr
            LEFT JOIN Code_Item_Detail cid ON cr.cDetailCode = cid.cDetailCode
            WHERE cr.cClientCode = ?
            AND (? = '' OR cr.cDeptCode = ?)
            AND (? = '' OR cr.cMainCode = ?)
            ORDER BY cr.cDeptCode, cr.cMainCode, cr.cDetailCode
            """
            
            detail_results = self.db_service.execute_query(
                detail_result_sql,
                (pe_no, dept_code, dept_code, item_code, item_code)
            )
            
            # 构建科室结果数据
            dept_result_list = []
            
            for dept_result in dept_results or []:
                dept_code = dept_result.get('cDeptCode', '')
                main_code = dept_result.get('cMainCode', '')
                
                # 获取该科室该项目的明细结果
                dept_details = []
                for detail in detail_results or []:
                    if (detail.get('cDeptCode') == dept_code and 
                        detail.get('cMainCode') == main_code):
                        detail_data = {
                            "detailCode": detail.get('cDetailCode', ''),
                            "detailName": detail.get('detail_name', ''),
                            "result": detail.get('cResult', ''),
                            "resultDesc": detail.get('cResultDesc', ''),
                            "abnormal": detail.get('cAbnor', '0') == '1',
                            "unit": detail.get('unit', ''),
                            "referenceValue": detail.get('reference_value', ''),
                            "checkDoctor": detail.get('cDoctName', ''),
                            "checkDate": str(detail.get('dOperDate', ''))[:19] if detail.get('dOperDate') else ''
                        }
                        dept_details.append(detail_data)
                
                # 构建科室结果项
                dept_item = {
                    "deptCode": dept_code,
                    "deptName": dept_result.get('dept_name', ''),
                    "itemCode": main_code,
                    "itemName": dept_result.get('cMainName', ''),
                    "mainResult": dept_result.get('main_result', ''),
                    "findings": dept_result.get('findings', ''),
                    "suggestions": dept_result.get('suggestions', ''),
                    "checkDoctor": {
                        "code": dept_result.get('cDoctCode', ''),
                        "name": dept_result.get('cDoctName', '')
                    },
                    "auditDoctor": {
                        "code": dept_result.get('cAuditDoctCode', ''),
                        "name": dept_result.get('cAuditDoctName', '')
                    },
                    "checkDate": str(dept_result.get('dOperDate', ''))[:19] if dept_result.get('dOperDate') else '',
                    "illnessGrade": dept_result.get('cIllnessGrade', ''),
                    "detailResults": dept_details
                }
                dept_result_list.append(dept_item)
            
            # 构建返回数据（符合天健云09号接口格式）
            result = {
                "peNo": pe_no,
                "patientInfo": {
                    "name": pe_info.get('cName', ''),
                    "sex": pe_info.get('cSex', ''),
                    "birthDate": str(pe_info.get('dBornDate', ''))[:10] if pe_info.get('dBornDate') else '',
                    "idCard": pe_info.get('cIdCard', ''),
                    "registerDate": str(pe_info.get('register_date', ''))[:19] if pe_info.get('register_date') else ''
                },
                "retransmitType": "dept_result",
                "retransmitTime": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "deptResults": dept_result_list,
                "totalCount": len(dept_result_list)
            }
            
            return result
            
        finally:
            self.db_service.disconnect()
    
    def retransmit_dept_result(self, pe_no: str, dept_code: str = "", item_code: str = "", 
                              test_mode: bool = False) -> Dict[str, Any]:
        """
        重传科室结果数据到天健云
        
        Args:
            pe_no: 体检号
            dept_code: 科室代码（可选，空则重传所有科室）
            item_code: 项目代码（可选，空则重传该科室所有项目）
            test_mode: 测试模式
            
        Returns:
            重传结果
        """
        try:
            print(f"[SYNC] 准备重传体检号 {pe_no} 的科室结果")
            print(f"   科室代码: {dept_code if dept_code else '全部科室'}")
            print(f"   项目代码: {item_code if item_code else '全部项目'}")
            
            # 获取科室结果数据
            dept_result_data = self.get_dept_result_data(pe_no, dept_code, item_code)
            
            if not dept_result_data or not dept_result_data.get('deptResults'):
                return {
                    'success': False,
                    'message': f'未找到体检号 {pe_no} 的科室结果数据',
                    'error': 'No dept result data found'
                }
            
            result_count = len(dept_result_data['deptResults'])
            print(f"[OK] 获取到 {result_count} 条科室结果数据")
            
            if test_mode:
                print("[TEST] 测试模式 - 显示科室结果数据格式:")
                print(json.dumps(dept_result_data, ensure_ascii=False, indent=2))
                
                return {
                    'success': True,
                    'message': f"测试模式完成 - 体检号 {pe_no} 科室结果数据格式正确",
                    'data': dept_result_data,
                    'result_count': result_count
                }
            
            # 发送重传请求
            result = self._send_request(dept_result_data)
            
            if result['success']:
                return {
                    'success': True,
                    'message': f"体检号 {pe_no} 科室结果重传成功",
                    'result_count': result_count,
                    'response': result['response']
                }
            else:
                return {
                    'success': False,
                    'message': f"体检号 {pe_no} 科室结果重传失败",
                    'error': result['error'],
                    'data': dept_result_data
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f"重传科室结果异常: {str(e)}",
                'error': str(e)
            }
    
    def _send_request(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送HTTP请求到天健云
        
        Args:
            data: 请求数据
            
        Returns:
            请求结果
        """
        url = f"{self.api_config['base_url']}{self.endpoint}"
        headers = self.create_headers()
        
        try:
            response = requests.post(
                url,
                headers=headers,
                json=data,
                timeout=self.api_config.get('timeout', 30),
                verify=False
            )
            
            if response.status_code == 200:
                try:
                    response_json = response.json()
                    if response_json.get('code') == 0:
                        return {
                            'success': True,
                            'response': response_json
                        }
                    else:
                        return {
                            'success': False,
                            'error': f"API错误: code={response_json.get('code')}, msg={response_json.get('msg', '未知错误')}",
                            'response': response_json
                        }
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': f"响应解析失败: {response.text}",
                        'response': response.text
                    }
            else:
                return {
                    'success': False,
                    'error': f"HTTP错误: {response.status_code}",
                    'response': response.text
                }
        
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'error': "请求超时"
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'error': "连接错误"
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"请求异常: {str(e)}"
            }
    
    def batch_retransmit_dept_results(self, pe_no_list: List[str], dept_code: str = "", 
                                     item_code: str = "", test_mode: bool = False) -> Dict[str, Any]:
        """
        批量重传科室结果
        
        Args:
            pe_no_list: 体检号列表
            dept_code: 科室代码
            item_code: 项目代码
            test_mode: 测试模式
            
        Returns:
            批量重传结果
        """
        total_count = len(pe_no_list)
        success_count = 0
        failed_count = 0
        errors = []
        
        print(f"[SYNC] 开始批量重传科室结果，共 {total_count} 个体检号")
        
        for i, pe_no in enumerate(pe_no_list, 1):
            try:
                result = self.retransmit_dept_result(pe_no, dept_code, item_code, test_mode)
                
                if result['success']:
                    success_count += 1
                    print(f"[OK] [{i}/{total_count}] 体检号 {pe_no} 科室结果重传成功")
                else:
                    failed_count += 1
                    error_msg = f"体检号 {pe_no} 重传失败: {result.get('message', '未知错误')}"
                    errors.append(error_msg)
                    print(f"[FAIL] [{i}/{total_count}] {error_msg}")
                    
            except Exception as e:
                failed_count += 1
                error_msg = f"体检号 {pe_no} 处理异常: {str(e)}"
                errors.append(error_msg)
                print(f"[FAIL] [{i}/{total_count}] {error_msg}")
        
        return {
            'total': total_count,
            'success': success_count,
            'failed': failed_count,
            'errors': errors,
            'success_rate': f"{(success_count/total_count*100):.1f}%" if total_count > 0 else "0%"
        }


# API配置
API_CONFIG = {
    'base_url': 'http://203.83.237.114:9300',
    'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
    'mic_code': 'MIC1.001E',
    'misc_id': 'MISC1.00001A',
    'timeout': 30
}


def test_interface_09():
    """测试09号接口"""
    print("测试天健云09号接口 - 体检科室结果重传接口")
    print("=" * 60)
    
    # 创建接口实例
    interface = TianjianInterface09(API_CONFIG)
    
    # 测试场景1：重传指定体检号的所有科室结果
    print("\n[SYNC] 测试场景1：重传指定体检号的所有科室结果")
    result1 = interface.retransmit_dept_result(
        pe_no="0220000001",
        test_mode=True
    )
    print(f"结果: {json.dumps(result1, ensure_ascii=False, indent=2)}")
    
    # 测试场景2：重传指定体检号指定科室的结果
    print("\n[SYNC] 测试场景2：重传指定体检号指定科室的结果")
    result2 = interface.retransmit_dept_result(
        pe_no="0220000002",
        dept_code="01",
        test_mode=True
    )
    print(f"结果: {json.dumps(result2, ensure_ascii=False, indent=2)}")
    
    # 测试场景3：批量重传科室结果
    print("\n[SYNC] 测试场景3：批量重传科室结果")
    result3 = interface.batch_retransmit_dept_results(
        pe_no_list=["0220000001", "0220000002"],
        test_mode=True
    )
    print(f"结果: {json.dumps(result3, ensure_ascii=False, indent=2)}")
    
    print("\n[OK] 天健云09号接口测试完成")


if __name__ == "__main__":
    test_interface_09()