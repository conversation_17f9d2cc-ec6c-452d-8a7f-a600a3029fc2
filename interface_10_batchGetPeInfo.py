#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云10号接口实现 - 批量获取体检单信息
根据天健云提供的标准报文格式实现
"""

import json
import hashlib
import requests
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from database_service import get_database_service
from multi_org_config import get_current_org_config


class TianjianInterface10:
    """天健云10号接口 - 批量获取体检单信息"""
    
    def __init__(self, api_config: Dict[str, Any] = None, org_config: Dict[str, Any] = None):
        """
        初始化接口配置
        
        Args:
            api_config: API配置信息（可选，默认使用中心库配置）
            org_config: 机构配置信息（可选，默认使用当前机构配置）
        """
        # 获取当前机构配置
        self.org_config = org_config or get_current_org_config()

        # 使用机构配置中的天健云API配置
        if api_config:
            self.api_config = api_config
        else:
            self.api_config = {
                'base_url': self.org_config.get('tianjian_base_url', 'http://203.83.237.114:9300'),
                'api_key': self.org_config.get('tianjian_api_key', ''),
                'mic_code': self.org_config.get('tianjian_mic_code', ''),
                'misc_id': self.org_config.get('tianjian_misc_id', ''),
                'timeout': 30
            }

        self.db_service = get_database_service()

    def batch_get_pe_info_standard(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        按照天健云标准格式批量获取体检单信息

        请求格式:
        {
            "start": "2023-04-01 09:40:22",  // 查询时间起始点（包含）
            "end": "2023-11-07 03:59:58",    // 查询时间终止点（不包含）
            "peNo": "",                      // 体检号
            "hospitalCode": ""               // 医院编码，适用于多院区的情况
        }

        返回格式:
        {
            "code": 0,
            "msg": "",
            "data": [...],
            "reponseTime": 1715563223823
        }
        """
        try:
            # 解析请求参数
            start_time = request_data.get('start', '')
            end_time = request_data.get('end', '')
            pe_no = request_data.get('peNo', '')
            hospital_code = request_data.get('hospitalCode', '')

            print(f"10号接口请求参数: start={start_time}, end={end_time}, peNo={pe_no}, hospitalCode={hospital_code}")

            if not self.db_service.connect():
                return {
                    "code": -1,
                    "msg": "数据库连接失败",
                    "data": [],
                    "reponseTime": int(datetime.now().timestamp() * 1000)
                }

            try:
                # 构建SQL查询 - 使用正确的字段名
                sql = """
                SELECT
                    rm.cClientCode as archiveNo,
                    rm.cName as name,
                    rm.cIdCard as icCode,
                    rm.cSex as sex_code,
                    CASE rm.cSex
                        WHEN '1' THEN '男'
                        WHEN '2' THEN '女'
                        ELSE '未知'
                    END as sex_name,
                    CONVERT(varchar, rm.dBornDate, 112) as birthday,
                    rm.cClientCode as peno,
                    CONVERT(varchar, rm.dOperdate, 120) as peDate,
                    ISNULL(rm.cTel, '') as phone,
                    ISNULL(rm.cMarryFlag, '') as ms_code,
                    CASE rm.cMarryFlag
                        WHEN '1' THEN '已婚'
                        WHEN '0' THEN '未婚'
                        ELSE '未知'
                    END as ms_name,
                    '' as vipLevel_code,
                    '普通' as vipLevel_name,
                    CASE
                        WHEN (rm.cContractCode IS NULL OR rm.cContractCode = '') THEN '1'
                        ELSE '2'
                    END as medicalType_code,
                    CASE
                        WHEN (rm.cContractCode IS NULL OR rm.cContractCode = '') THEN '个人体检'
                        ELSE '团体体检'
                    END as medicalType_name,
                    CASE WHEN (rm.cContractCode IS NULL OR rm.cContractCode = '') THEN '0' ELSE '1' END as isGroup,
                    '' as company,
                    '' as workDept,
                    ISNULL(tc.cCode, '') as teamNo,
                    '' as professional,
                    '' as workAge,
                    rm.cStatus as peStates_code,
                    CASE rm.cStatus
                        WHEN '0' THEN '登记完成'
                        WHEN '1' THEN '分科未完成'
                        WHEN '2' THEN '分科完成'
                        WHEN '3' THEN '主检初审中'
                        WHEN '4' THEN '主检初审完成'
                        WHEN '5' THEN '主检终审中'
                        WHEN '6' THEN '主检终审完成'
                        WHEN '7' THEN '主检终审完成'
                        ELSE '未知状态'
                    END as peStates_name,
                    CONVERT(varchar, rm.dOperdate, 120) as deptFinishTime,
                    CONVERT(varchar, rm.dOperdate, 120) as firstCheckFinishTime,
                    ISNULL(rm.cOperCode, '') as firstCheckFinishDoctor_code,
                    ISNULL(rm.cOperName, '') as firstCheckFinishDoctor_name,
                    CONVERT(varchar, rm.dOperdate, 120) as mainCheckFinishTime,
                    ISNULL(rm.cOperCode, '') as mainCheckFinishDoctor_code,
                    ISNULL(rm.cOperName, '') as mainCheckFinishDoctor_name,
                    '0' as forbidGoCheck,
                    '0' as reportPrint,
                    '0' as reportGot,
                    '0' as replacementInspectionMark,
                    DATEDIFF(YEAR, rm.dBornDate, GETDATE()) as age,
                    CASE
                        WHEN rm.cStatus >= '4' THEN '4'
                        WHEN rm.cStatus >= '2' THEN '3'
                        WHEN rm.cStatus >= '1' THEN '2'
                        ELSE '1'
                    END as currentNodeType,
                    ISNULL(rm.cSuitCode, '') as pePackage_code,
                    ISNULL(us.cName, '') as pePackage_name
                FROM T_Register_Main rm
                LEFT JOIN T_Contract tc ON rm.cContractCode = tc.cCode
                LEFT JOIN T_UnitsSuit_Master us ON rm.cSuitCode = us.cSuitCode
                WHERE 1=1
                """

                params = []

                # 添加时间范围条件
                if start_time:
                    sql += " AND rm.dOperdate >= ?"
                    params.append(start_time)

                if end_time:
                    sql += " AND rm.dOperdate < ?"
                    params.append(end_time)

                # 添加体检号条件
                if pe_no:
                    sql += " AND rm.cClientCode = ?"
                    params.append(pe_no)

                # 添加医院编码条件（如果需要）
                if hospital_code:
                    sql += " AND rm.cShopCode = ?"
                    params.append(hospital_code)

                sql += " ORDER BY rm.dOperdate DESC"

                exam_records = self.db_service.execute_query(sql, tuple(params) if params else None)

                # 构建返回数据
                data_list = []
                for record in exam_records:
                    client_code = record['archiveNo']

                    # 暂时使用固定值，避免查询不存在的表
                    apply_items = []
                    dept_count = 0

                    # 获取档案下的体检号列表
                    pe_no_list = self._get_pe_no_list(client_code)

                    # 构建标准格式的体检单信息
                    exam_info = {
                        "peUserInfo": {
                            "archiveNo": record.get('archiveNo', ''),
                            "name": record.get('name', ''),
                            "icCode": record.get('icCode', ''),
                            "sex": {
                                "code": record.get('sex_code', '3'),
                                "name": record.get('sex_name', '未知')
                            },
                            "birthday": record.get('birthday', ''),
                            "peno": record.get('peno', ''),
                            "peDate": record.get('peDate', ''),
                            "phone": record.get('phone', ''),
                            "ms": {
                                "code": record.get('ms_code', ''),
                                "name": record.get('ms_name', '未知')
                            },
                            "pregnantState": {
                                "code": "",
                                "name": ""
                            },
                            "vipLevel": {
                                "code": record.get('vipLevel_code', ''),
                                "name": record.get('vipLevel_name', '普通')
                            },
                            "medicalType": {
                                "code": record.get('medicalType_code', ''),
                                "name": record.get('medicalType_name', '个人体检')
                            },
                            "isGroup": record.get('isGroup', '0'),
                            "company": record.get('company', ''),
                            "workDept": record.get('workDept', ''),
                            "teamNo": record.get('teamNo', ''),
                            "professional": record.get('professional', ''),
                            "workAge": record.get('workAge', ''),
                            "peStates": {
                                "code": record.get('peStates_code', '0'),
                                "name": record.get('peStates_name', '已登记')
                            },
                            "deptCount": dept_count,
                            "age": record.get('age', 0),
                            "deptFinishTime": record.get('deptFinishTime', ''),
                            "firstCheckFinishTime": record.get('firstCheckFinishTime', ''),
                            "firstCheckFinishDoctor": {
                                "code": record.get('firstCheckFinishDoctor_code', ''),
                                "name": record.get('firstCheckFinishDoctor_name', '')
                            },
                            "mainCheckFinishTime": record.get('mainCheckFinishTime', ''),
                            "mainCheckFinishDoctor": {
                                "code": record.get('mainCheckFinishDoctor_code', ''),
                                "name": record.get('mainCheckFinishDoctor_name', '')
                            },
                            "forbidGoCheck": record.get('forbidGoCheck', ''),
                            "reportPrint": record.get('reportPrint', ''),
                            "reportGot": record.get('reportGot', ''),
                            "replacementInspectionMark": record.get('replacementInspectionMark', ''),
                            "applyItemList": apply_items,
                            "currentNodeType": record.get('currentNodeType', ''),
                            "pePackage": {
                                "code": record.get('pePackage_code', ''),
                                "name": record.get('pePackage_name', '')
                            }
                        },
                        "archiveInfo": {
                            "name": record.get('name', ''),
                            "icCode": record.get('icCode', ''),
                            "sex": {
                                "code": record.get('sex_code', '3'),
                                "name": record.get('sex_name', '未知')
                            },
                            "birthday": record.get('birthday', ''),
                            "peNoList": pe_no_list
                        },
                        "hospital": {
                            "code": self.org_config.get('org_code', 'DEFAULT'),
                            "name": self.org_config.get('org_name', '默认医院')
                        }
                    }

                    data_list.append(exam_info)

                return {
                    "code": 0,
                    "msg": "",
                    "data": data_list,
                    "reponseTime": int(datetime.now().timestamp() * 1000)
                }

            finally:
                self.db_service.disconnect()

        except Exception as e:
            print(f"10号接口处理异常: {str(e)}")
            return {
                "code": -1,
                "msg": f"处理异常: {str(e)}",
                "data": [],
                "reponseTime": int(datetime.now().timestamp() * 1000)
            }

    def _get_apply_item_list(self, client_code: str) -> List[str]:
        """获取申请项目列表 - 简化版本"""
        # 暂时返回空列表，避免查询不存在的表
        return []

    def _get_dept_count(self, client_code: str) -> int:
        """获取科室数量 - 简化版本"""
        # 暂时返回0，避免查询不存在的表
        return 0

    def _get_pe_no_list(self, client_code: str) -> List[str]:
        """获取档案下的体检号列表"""
        try:
            # 这里假设一个档案对应一个体检号，实际情况可能需要调整
            return [client_code]
        except:
            return []

    def generate_signature(self, api_key: str, timestamp: str) -> str:
        """生成MD5签名"""
        sign_string = api_key + timestamp
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    def create_headers(self) -> Dict[str, str]:
        """创建请求头"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        nonce = str(uuid.uuid4())
        signature = self.generate_signature(self.api_config['api_key'], timestamp)
        
        return {
            'Content-Type': 'application/json',
            'sign': signature,
            'timestamp': timestamp,
            'nonce': nonce,
            'mic-code': self.api_config['mic_code'],
            'misc-id': self.api_config['misc_id']
        }
    
    def get_batch_exam_info(self, client_codes: List[str] = None,
                           pe_nos: List[str] = None,
                           start_date: str = None, end_date: str = None,
                           pe_status: str = None,
                           company: str = None,
                           limit: int = None) -> List[Dict[str, Any]]:
        """
        批量获取体检单信息（基于01号接口数据结构）
        
        Args:
            client_codes: 客户编号列表
            pe_nos: 体检号列表
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            pe_status: 体检状态
            company: 体检单位
            limit: 限制返回条数
            
        Returns:
            体检单信息列表
        """
        if not self.db_service.connect():
            raise Exception("数据库连接失败")
        
        try:
            # 构建SQL查询体检基本信息（与01号接口相同的数据结构）
            sql = """
            SELECT 
                rm.cClientCode as archiveNo,
                rm.cName as name,
                rm.cIdCard as icCode,
                rm.cSex as sex_code,
                CASE rm.cSex 
                    WHEN '1' THEN '男' 
                    WHEN '2' THEN '女' 
                    ELSE '未知' 
                END as sex_name,
                rm.dBornDate as dBornDate,
                rm.cClientCode as peno,
                rm.dOperdate as peDate,
                rm.cTel as phone,
                rm.cMarryFlag as ms_code,
                CASE rm.cMarryFlag
                    WHEN '1' THEN 'married'
                    WHEN '0' THEN 'unmarried'
                    ELSE 'unknown'
                END as ms_key,
                CASE rm.cMarryFlag
                    WHEN '1' THEN '已婚'
                    WHEN '0' THEN '未婚'
                    ELSE '未知'
                END as ms_name,
                '' as vipLevel_code,
                '普通' as vipLevel_name,
                rm.cType as medicalType_code,
                ISNULL(rm.cType, '个人体检') as medicalType_name,
                CASE WHEN (rm.cContractCode IS NULL OR rm.cContractCode = '') THEN 0 ELSE 1 END as isGroup,
                '' as company,
                '' as workDept,
                ISNULL(tc.cCode, '') as teamNo,
                '' as professional,
                '' as workAge,
                rm.cStatus as peStates_code,
                CASE rm.cStatus
                    WHEN '0' THEN '登记完成'
                    WHEN '1' THEN '分科未完成'
                    WHEN '2' THEN '分科完成'
                    WHEN '3' THEN '主检初审中'
                    WHEN '4' THEN '主检初审完成'
                    WHEN '5' THEN '主检终审中'
                    WHEN '6' THEN '主检终审完成'
                    WHEN '7' THEN '主检终审完成'
                    ELSE '未知状态'
                END as peStates_name,
                rm.dOperdate as deptFinishTime,
                rm.dOperdate as firstCheckFinishTime,
                rm.cOperName as firstCheckFinishDoctor_code,
                rm.cOperName as firstCheckFinishDoctor_name,
                rm.dOperdate as mainCheckFinishTime,
                rm.cOperName as mainCheckFinishDoctor_code,
                rm.cOperName as mainCheckFinishDoctor_name,
                0 as forbidGoCheck,
                0 as reportPrint,
                0 as reportGot,
                0 as replacementInspectionMark,
                DATEDIFF(YEAR, rm.dBornDate, GETDATE()) as age,
                CASE 
                    WHEN rm.cStatus >= '2' THEN 4
                    WHEN rm.cStatus >= '1' THEN 3
                    ELSE 1
                END as currentNodeType,
                rm.cSuitCode as pePackage_code,
                us.cName as pePackage_name,
                rm.cStatus as urgentStatus,
                rm.cContractCode as contractCode
            FROM T_Register_Main rm
            LEFT JOIN T_Contract tc ON rm.cContractCode = tc.cCode
            LEFT JOIN T_UnitsSuit_Master us ON rm.cSuitCode = us.cSuitCode
            WHERE 1=1
            """
            
            params = []
            
            # 添加客户编号条件
            if client_codes:
                placeholders = ','.join(['?' for _ in client_codes])
                sql += f" AND rm.cClientCode IN ({placeholders})"
                params.extend(client_codes)
            
            # 添加体检号条件
            if pe_nos:
                placeholders = ','.join(['?' for _ in pe_nos])
                sql += f" AND rm.cClientCode IN ({placeholders})"
                params.extend(pe_nos)
            
            # 添加日期范围条件
            if start_date:
                sql += " AND rm.dOperdate >= ?"
                params.append(start_date)
            
            if end_date:
                sql += " AND rm.dOperdate <= ?"
                params.append(end_date)
            
            # 添加体检状态条件
            if pe_status:
                sql += " AND rm.cStatus = ?"
                params.append(pe_status)
            
            # 添加体检单位条件
            if company:
                sql += " AND tc.cUnitsName LIKE ?"
                params.append(f'%{company}%')
            
            sql += " ORDER BY rm.dOperdate DESC, rm.cClientCode"
            
            if limit:
                # 直接在主查询中使用TOP
                sql = sql.replace("SELECT ", f"SELECT TOP {limit} ")
            
            exam_records = self.db_service.execute_query(sql, tuple(params) if params else None)
            
            # 为每条记录构建完整的体检单信息
            result = []
            for record in exam_records:
                client_code = record['archiveNo']
                
                # 获取申请项目列表
                apply_items = self.db_service.get_exam_apply_items(client_code)
                
                # 获取科室数量
                dept_count = self.db_service.get_dept_count(client_code)
                
                # 获取档案下的体检号列表
                pe_no_list = self.db_service.get_archive_pe_list(client_code)
                
                # 格式化生日
                birthday = record.get('dBornDate', '')
                if birthday and len(str(birthday)) >= 10:
                    birthday = str(birthday)[:10].replace('-', '')
                
                # 构建体检单信息
                exam_info = {
                    "peUserInfo": {
                        "archiveNo": record.get('archiveNo', ''),
                        "name": record.get('name', ''),
                        "icCode": record.get('icCode', ''),
                        "sex": {
                            "code": record.get('sex_code', '3'),
                            "name": record.get('sex_name', '未知')
                        },
                        "birthday": birthday,
                        "peno": record.get('peno', ''),
                        "peDate": record.get('peDate', ''),
                        "phone": record.get('phone', ''),
                        "ms": {
                            "code": record.get('ms_key', 'unknown'),
                            "name": record.get('ms_name', '未知')
                        },
                        "pregnantState": {
                            "code": "unknown",
                            "name": "未知"
                        },
                        "vipLevel": {
                            "code": record.get('vipLevel_code', 'NORMAL'),
                            "name": record.get('vipLevel_name', '普通')
                        },
                        "medicalType": {
                            "code": record.get('medicalType_code', 'PERSONAL'),
                            "name": record.get('medicalType_name', '个人体检')
                        },
                        "isGroup": bool(record.get('isGroup', 0)),
                        "company": record.get('company', ''),
                        "workDept": record.get('workDept', ''),
                        "teamNo": record.get('teamNo', ''),
                        "professional": record.get('professional', ''),
                        "workAge": record.get('workAge', ''),
                        "peStates": {
                            "code": record.get('peStates_code', '0'),
                            "name": record.get('peStates_name', '已登记')
                        },
                        "deptCount": dept_count,
                        "age": record.get('age', 0),
                        "deptFinishTime": record.get('deptFinishTime', ''),
                        "firstCheckFinishTime": record.get('firstCheckFinishTime', ''),
                        "firstCheckFinishDoctor": {
                            "code": record.get('firstCheckFinishDoctor_code', ''),
                            "name": record.get('firstCheckFinishDoctor_name', '')
                        },
                        "mainCheckFinishTime": record.get('mainCheckFinishTime', ''),
                        "mainCheckFinishDoctor": {
                            "code": record.get('mainCheckFinishDoctor_code', ''),
                            "name": record.get('mainCheckFinishDoctor_name', '')
                        },
                        "forbidGoCheck": bool(record.get('forbidGoCheck', 0)),
                        "reportPrint": bool(record.get('reportPrint', 0)),
                        "reportGot": bool(record.get('reportGot', 0)),
                        "replacementInspectionMark": bool(record.get('replacementInspectionMark', 0)),
                        "applyItemList": apply_items,
                        "currentNodeType": record.get('currentNodeType', 1),
                        "pePackage": {
                            "code": record.get('pePackage_code', ''),
                            "name": record.get('pePackage_name', '')
                        },
                        "urgentStatus": record.get('urgentStatus', '0')
                    },
                    "archiveInfo": {
                        "name": record.get('name', ''),
                        "icCode": record.get('icCode', ''),
                        "sex": {
                            "code": record.get('sex_code', '3'),
                            "name": record.get('sex_name', '未知')
                        },
                        "birthday": birthday,
                        "peNoList": pe_no_list
                    },
                    "hospital": {
                        "code": self.org_config.get('org_code', 'DEFAULT'),
                        "name": self.org_config.get('org_name', '默认医院')
                    },
                    "batchQuery": True,  # 标记为批量查询
                    "queryTime": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                
                result.append(exam_info)
            
            return result
            
        finally:
            self.db_service.disconnect()
    
    def batch_get_exam_info(self, client_codes: List[str] = None,
                           pe_nos: List[str] = None,
                           start_date: str = None, end_date: str = None,
                           pe_status: str = None,
                           company: str = None,
                           limit: int = None, test_mode: bool = False,
                           page_size: int = 50) -> Dict[str, Any]:
        """
        批量获取体检单信息
        
        Args:
            client_codes: 客户编号列表
            pe_nos: 体检号列表
            start_date: 开始日期
            end_date: 结束日期
            pe_status: 体检状态
            company: 体检单位
            limit: 限制返回条数
            test_mode: 测试模式
            page_size: 分页大小
            
        Returns:
            批量查询结果
        """
        try:
            # 获取体检单信息
            exam_info_list = self.get_batch_exam_info(
                client_codes, pe_nos, start_date, end_date, pe_status, company, limit
            )
            
            if not exam_info_list:
                return {
                    'success': True,
                    'message': '没有找到符合条件的体检单信息',
                    'total': 0,
                    'data': []
                }
            
            total = len(exam_info_list)
            
            print(f"批量查询到 {total} 条体检单信息")
            
            if test_mode:
                print("测试模式 - 显示前2条体检单信息的数据格式:")
                for i, item in enumerate(exam_info_list[:2], 1):
                    print(f"\n第 {i} 条体检单信息:")
                    print(json.dumps(item, ensure_ascii=False, indent=2))
                
                return {
                    'success': True,
                    'message': f"测试模式完成 - 共 {total} 条体检单信息格式正确",
                    'total': total,
                    'data': exam_info_list
                }
            
            # 分页返回数据
            pages = []
            for i in range(0, total, page_size):
                page_data = exam_info_list[i:i + page_size]
                page_num = i // page_size + 1
                total_pages = (total + page_size - 1) // page_size
                
                page_info = {
                    'pageNum': page_num,
                    'pageSize': len(page_data),
                    'totalPages': total_pages,
                    'total': total,
                    'data': page_data
                }
                pages.append(page_info)
                
                print(f"✓ 第 {page_num}/{total_pages} 页数据准备完成，包含 {len(page_data)} 条记录")
            
            # 返回结果
            return {
                'success': True,
                'message': f"批量查询完成 - 共 {total} 条体检单信息，分为 {len(pages)} 页",
                'total': total,
                'totalPages': len(pages),
                'pageSize': page_size,
                'pages': pages,
                'summary': {
                    'queryTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'queryConditions': {
                        'client_codes': client_codes,
                        'pe_nos': pe_nos,
                        'start_date': start_date,
                        'end_date': end_date,
                        'pe_status': pe_status,
                        'company': company,
                        'limit': limit
                    }
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"批量查询过程异常: {str(e)}",
                'total': 0,
                'data': []
            }
    
    def export_exam_info_to_api(self, query_result: Dict[str, Any], 
                               batch_size: int = 10) -> Dict[str, Any]:
        """
        将查询结果导出到天健云API
        
        Args:
            query_result: 批量查询结果
            batch_size: 批量发送大小
            
        Returns:
            导出结果
        """
        if not query_result.get('success') or not query_result.get('pages'):
            return {
                'success': False,
                'error': '没有有效的查询结果数据'
            }
        
        total_sent = 0
        total_failed = 0
        errors = []
        
        try:
            for page in query_result['pages']:
                page_data = page['data']
                page_num = page['pageNum']
                
                print(f"\n导出第 {page_num} 页数据到天健云...")
                
                # 分批发送当前页的数据
                for i in range(0, len(page_data), batch_size):
                    batch = page_data[i:i + batch_size]
                    batch_num = i // batch_size + 1
                    
                    try:
                        result = self._send_export_request(batch)
                        
                        if result['success']:
                            total_sent += len(batch)
                            print(f"✓ 第 {page_num} 页第 {batch_num} 批次导出成功")
                        else:
                            total_failed += len(batch)
                            error_msg = f"第 {page_num} 页第 {batch_num} 批次导出失败: {result.get('error', '未知错误')}"
                            print(f"✗ {error_msg}")
                            errors.append(error_msg)
                    
                    except Exception as e:
                        total_failed += len(batch)
                        error_msg = f"第 {page_num} 页第 {batch_num} 批次处理异常: {str(e)}"
                        print(f"✗ {error_msg}")
                        errors.append(error_msg)
            
            return {
                'success': total_failed == 0,
                'message': f"导出完成 - 成功: {total_sent}, 失败: {total_failed}",
                'total': query_result.get('total', 0),
                'sent': total_sent,
                'failed': total_failed,
                'errors': errors
            }
        
        except Exception as e:
            return {
                'success': False,
                'error': f"导出过程异常: {str(e)}",
                'sent': total_sent,
                'failed': total_failed
            }
    
    def _send_export_request(self, request_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        发送导出HTTP请求到天健云
        
        Args:
            request_data: 请求数据
            
        Returns:
            请求结果
        """
        # 使用01号接口的URL，但添加批量查询标识
        url = f"{self.api_config['base_url']}/dx/inter/sendPeInfo"
        headers = self.create_headers()
        
        # 在请求头中添加批量查询标识
        headers['X-Batch-Query'] = 'true'
        headers['X-Query-Type'] = 'batch_exam_info'
        
        try:
            response = requests.post(
                url,
                headers=headers,
                json=request_data,
                timeout=self.api_config.get('timeout', 30),
                verify=False
            )
            
            if response.status_code == 200:
                try:
                    response_json = response.json()
                    if response_json.get('code') == 0:
                        return {
                            'success': True,
                            'response': response_json
                        }
                    else:
                        return {
                            'success': False,
                            'error': f"API错误: code={response_json.get('code')}, msg={response_json.get('msg', '未知错误')}",
                            'response': response_json
                        }
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': f"响应解析失败: {response.text}",
                        'response': response.text
                    }
            else:
                return {
                    'success': False,
                    'error': f"HTTP错误: {response.status_code}",
                    'response': response.text
                }
        
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'error': "请求超时"
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'error': "连接错误"
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"请求异常: {str(e)}"
            }


# API配置
API_CONFIG = {
    'base_url': 'http://203.83.237.114:9300',
    'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
    'mic_code': 'MIC1.001E',
    'misc_id': 'MISC1.00001A',
    'timeout': 30
}


def main():
    """主函数 - 测试10号接口"""
    print("天健云10号接口测试 - 批量获取体检单信息")
    print("=" * 60)
    
    # 创建接口实例
    interface = TianjianInterface10(API_CONFIG)
    
    # 测试模式 - 批量查询最近30天的体检单
    print("\n1. 测试模式 - 批量查询最近30天内前5条体检单信息")
    from datetime import datetime, timedelta
    from config import Config

    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    
    result = interface.batch_get_exam_info(
        start_date=start_date,
        end_date=end_date,
        limit=5,
        test_mode=True,
        page_size=3
    )
    print(f"测试结果: {result}")
    
    # 测试批量查询特定状态的体检单
    print("\n2. 测试模式 - 批量查询已登记状态的体检单")
    result = interface.batch_get_exam_info(
        pe_status='0',  # 已登记状态
        limit=3,
        test_mode=True,
        page_size=2
    )
    print(f"特定状态批量查询测试结果: {result}")
    
    # 实际批量查询和导出
    print("\n3. 实际批量查询 - 最近30天内前3条体检单信息")
    confirm = input("是否继续实际批量查询并导出到天健云？(y/N): ")
    if confirm.lower() == 'y':
        # 先批量查询
        query_result = interface.batch_get_exam_info(
            start_date=start_date,
            end_date=end_date,
            limit=3,
            test_mode=False,
            page_size=2
        )
        print(f"批量查询结果: {query_result}")
        
        # 再导出到API
        if query_result.get('success'):
            export_result = interface.export_exam_info_to_api(query_result, batch_size=1)
            print(f"导出结果: {export_result}")
    else:
        print("已取消实际批量查询")


if __name__ == '__main__':
    main()