#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云11号接口实现 - 查询项目字典信息接口
基于02号接口的数据查询功能
"""

import json
import hashlib
import requests
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from database_service import get_database_service
from config import Config
from multi_org_config import get_current_org_config
from api_config_manager import get_tianjian_base_url


class TianjianInterface11:
    """天健云11号接口 - 查询项目字典信息接口"""
    
    def __init__(self, api_config: Dict[str, Any] = None, org_config: Dict[str, Any] = None):
        """
        初始化接口配置
        
        Args:
            api_config: API配置信息（可选，默认使用中心库配置）
            org_config: 机构配置信息（可选，默认使用当前机构配置）
        """
        # 获取当前机构配置
        self.org_config = org_config or get_current_org_config()

        # 使用机构配置中的天健云API配置
        if api_config:
            self.api_config = api_config
        else:
            self.api_config = {
                'base_url': self.org_config.get('tianjian_base_url', get_tianjian_base_url()),
                'api_key': self.org_config.get('tianjian_api_key', ''),
                'mic_code': self.org_config.get('tianjian_mic_code', ''),
                'misc_id': self.org_config.get('tianjian_misc_id', ''),
                'timeout': 30
            }

        self.db_service = get_database_service()
    
    def generate_signature(self, api_key: str, timestamp: str) -> str:
        """生成MD5签名"""
        sign_string = api_key + timestamp
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    def create_headers(self) -> Dict[str, str]:
        """创建请求头"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        nonce = str(uuid.uuid4())
        signature = self.generate_signature(self.api_config['api_key'], timestamp)
        
        return {
            'Content-Type': 'application/json',
            'sign': signature,
            'timestamp': timestamp,
            'nonce': nonce,
            'mic-code': self.api_config['mic_code'],
            'misc-id': self.api_config['misc_id']
        }
    
    def get_apply_items_dict_data(self, apply_item_codes: List[str] = None,
                                 dept_codes: List[str] = None,
                                 item_name_keyword: str = None,
                                 include_stopped: bool = False,
                                 limit: int = None) -> List[Dict[str, Any]]:
        """
        获取申请项目字典数据（基于02号接口数据结构）
        
        Args:
            apply_item_codes: 申请项目编码列表
            dept_codes: 科室编码列表
            item_name_keyword: 项目名称关键字
            include_stopped: 是否包含已停用项目
            limit: 限制返回条数
            
        Returns:
            申请项目字典列表
        """
        if not self.db_service.connect():
            raise Exception("数据库连接失败")
        
        try:
            # 构建SQL查询申请项目字典（与02号接口相同的数据结构）
            sql = """
            SELECT 
                im.cCode as applyItemId,
                im.cName as applyItemName,
                CAST(ROW_NUMBER() OVER (ORDER BY im.cCode) AS VARCHAR) as displaySequence,
                ISNULL(dm.cDeptCode, 'UNKNOWN') as deptId,
                dd.cName as deptName,
                im.cStopTag as stopTag,
                im.cPrice as price,
                im.cSpec as specification,
                im.cUnit as unit,
                im.cMemo as memo,
                im.cOperDate as createTime,
                im.cOperName as createUser
            FROM Code_Item_Main im
            LEFT JOIN Code_Dept_Main dm ON im.cCode = dm.cMainCode
            LEFT JOIN Code_Dept_dict dd ON dm.cDeptCode = dd.cCode
            WHERE 1=1
            """
            
            params = []
            
            # 添加申请项目编码条件
            if apply_item_codes:
                placeholders = ','.join(['?' for _ in apply_item_codes])
                sql += f" AND im.cCode IN ({placeholders})"
                params.extend(apply_item_codes)
            
            # 添加科室编码条件
            if dept_codes:
                placeholders = ','.join(['?' for _ in dept_codes])
                sql += f" AND dm.cDeptCode IN ({placeholders})"
                params.extend(dept_codes)
            
            # 添加项目名称关键字条件
            if item_name_keyword:
                sql += " AND im.cName LIKE ?"
                params.append(f'%{item_name_keyword}%')
            
            # 是否包含已停用项目
            if not include_stopped:
                sql += " AND im.cStopTag = '0'"
            
            sql += " ORDER BY im.cCode"
            
            if limit:
                # 直接在主查询中使用TOP
                sql = sql.replace("SELECT ", f"SELECT TOP {limit} ")
            
            apply_items = self.db_service.execute_query(sql, tuple(params) if params else None)
            
            # 为每个申请项目获取检查项目列表
            result = []
            for item in apply_items:
                apply_item_id = item['applyItemId']
                
                # 获取该申请项目下的检查项目
                check_items = self._get_check_items_for_apply_item(apply_item_id)
                
                apply_item_data = {
                    "applyItemId": apply_item_id,
                    "applyItemName": item['applyItemName'],
                    "displaySequence": item['displaySequence'],
                    "dept": {
                        "code": item['deptId'],
                        "name": item.get('deptName', '未知科室')
                    },
                    "hospital": {
                        "code": self.org_config.get('org_code', 'DEFAULT'),
                        "name": self.org_config.get('org_name', '默认医院')
                    },
                    "checkItemList": check_items,
                    "itemInfo": {
                        "stopTag": item.get('stopTag', '0'),
                        "price": item.get('price', ''),
                        "specification": item.get('specification', ''),
                        "unit": item.get('unit', ''),
                        "memo": item.get('memo', ''),
                        "createTime": item.get('createTime', ''),
                        "createUser": item.get('createUser', '')
                    },
                    "queryTime": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                
                result.append(apply_item_data)
            
            return result
            
        finally:
            self.db_service.disconnect()
    
    def _get_check_items_for_apply_item(self, apply_item_id: str) -> List[Dict[str, Any]]:
        """
        获取申请项目下的检查项目列表（与02号接口相同）
        
        Args:
            apply_item_id: 申请项目ID
            
        Returns:
            检查项目列表
        """
        sql = """
        SELECT 
            id.cCode as checkItemId,
            id.cName as checkItemName,
            CAST(ROW_NUMBER() OVER (ORDER BY id.cCode) AS VARCHAR) as displaySequence,
            id.cStopTag as stopTag,
            id.cUnit as unit,
            id.cConsult as reference,
            id.cSpec as specification,
            id.cMemo as memo,
            id.cPrice as price
        FROM Code_Item_Detail id
        WHERE id.cMainCode = ?
        AND id.cStopTag = '0'
        ORDER BY id.cCode
        """
        
        result = self.db_service.execute_query(sql, (apply_item_id,))
        
        # 格式化检查项目数据
        check_items = []
        for item in result:
            check_item_data = {
                "checkItemId": item['checkItemId'],
                "checkItemName": item['checkItemName'],
                "displaySequence": item['displaySequence'],
                "itemDetails": {
                    "stopTag": item.get('stopTag', '0'),
                    "unit": item.get('unit', ''),
                    "reference": item.get('reference', ''),
                    "specification": item.get('specification', ''),
                    "memo": item.get('memo', ''),
                    "price": item.get('price', '')
                }
            }
            check_items.append(check_item_data)
        
        return check_items
    
    def query_apply_item_dict(self, apply_item_codes: List[str] = None,
                             dept_codes: List[str] = None,
                             item_name_keyword: str = None,
                             include_stopped: bool = False,
                             limit: int = None, test_mode: bool = False,
                             page_size: int = 20) -> Dict[str, Any]:
        """
        查询申请项目字典信息
        
        Args:
            apply_item_codes: 申请项目编码列表
            dept_codes: 科室编码列表
            item_name_keyword: 项目名称关键字
            include_stopped: 是否包含已停用项目
            limit: 限制查询条数
            test_mode: 测试模式
            page_size: 分页大小
            
        Returns:
            查询结果
        """
        try:
            # 获取申请项目字典数据
            apply_items_data = self.get_apply_items_dict_data(
                apply_item_codes, dept_codes, item_name_keyword, include_stopped, limit
            )
            
            if not apply_items_data:
                return {
                    'success': True,
                    'message': '没有找到符合条件的申请项目字典数据',
                    'total': 0,
                    'data': []
                }
            
            total = len(apply_items_data)
            
            print(f"查询到 {total} 条申请项目字典信息")
            
            if test_mode:
                print("测试模式 - 显示前2条申请项目字典的数据格式:")
                for i, item in enumerate(apply_items_data[:2], 1):
                    print(f"\n第 {i} 条申请项目字典:")
                    print(json.dumps(item, ensure_ascii=False, indent=2))
                
                return {
                    'success': True,
                    'message': f"测试模式完成 - 共 {total} 条申请项目字典格式正确",
                    'total': total,
                    'data': apply_items_data
                }
            
            # 分页处理数据
            pages = []
            for i in range(0, total, page_size):
                page_data = apply_items_data[i:i + page_size]
                page_num = i // page_size + 1
                total_pages = (total + page_size - 1) // page_size
                
                page_info = {
                    'pageNum': page_num,
                    'pageSize': len(page_data),
                    'totalPages': total_pages,
                    'total': total,
                    'data': page_data
                }
                pages.append(page_info)
                
                print(f"✓ 第 {page_num}/{total_pages} 页数据准备完成，包含 {len(page_data)} 条申请项目")
            
            # 发送查询请求到天健云
            query_request = {
                'queryType': 'applyItemDict',
                'queryConditions': {
                    'apply_item_codes': apply_item_codes,
                    'dept_codes': dept_codes,
                    'item_name_keyword': item_name_keyword,
                    'include_stopped': include_stopped,
                    'limit': limit
                },
                'totalItems': total,
                'totalPages': len(pages)
            }
            
            result = self._send_query_request(query_request)
            
            if result['success']:
                print(f"✓ 申请项目字典查询成功")
                return {
                    'success': True,
                    'message': f"查询完成 - 共 {total} 条申请项目字典，分为 {len(pages)} 页",
                    'total': total,
                    'totalPages': len(pages),
                    'pageSize': page_size,
                    'pages': pages,
                    'response': result.get('response', {}),
                    'summary': {
                        'queryTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        'queryConditions': {
                            'apply_item_codes': apply_item_codes,
                            'dept_codes': dept_codes,
                            'item_name_keyword': item_name_keyword,
                            'include_stopped': include_stopped,
                            'limit': limit
                        }
                    }
                }
            else:
                print(f"✗ 申请项目字典查询失败: {result.get('error', '未知错误')}")
                return {
                    'success': False,
                    'error': f"查询失败: {result.get('error', '未知错误')}",
                    'total': total,
                    'data': apply_items_data
                }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"查询过程异常: {str(e)}",
                'total': 0,
                'data': []
            }
    
    def sync_apply_item_dict_to_api(self, query_result: Dict[str, Any],
                                   batch_size: int = 10) -> Dict[str, Any]:
        """
        将查询结果同步到天健云API
        
        Args:
            query_result: 查询结果
            batch_size: 批量发送大小
            
        Returns:
            同步结果
        """
        if not query_result.get('success') or not query_result.get('pages'):
            return {
                'success': False,
                'error': '没有有效的查询结果数据'
            }
        
        total_sent = 0
        total_failed = 0
        errors = []
        
        try:
            for page in query_result['pages']:
                page_data = page['data']
                page_num = page['pageNum']
                
                print(f"\n同步第 {page_num} 页申请项目字典到天健云...")
                
                # 分批发送当前页的数据
                for i in range(0, len(page_data), batch_size):
                    batch = page_data[i:i + batch_size]
                    batch_num = i // batch_size + 1
                    
                    try:
                        result = self._send_sync_request(batch)
                        
                        if result['success']:
                            total_sent += len(batch)
                            print(f"✓ 第 {page_num} 页第 {batch_num} 批次同步成功")
                        else:
                            total_failed += len(batch)
                            error_msg = f"第 {page_num} 页第 {batch_num} 批次同步失败: {result.get('error', '未知错误')}"
                            print(f"✗ {error_msg}")
                            errors.append(error_msg)
                    
                    except Exception as e:
                        total_failed += len(batch)
                        error_msg = f"第 {page_num} 页第 {batch_num} 批次处理异常: {str(e)}"
                        print(f"✗ {error_msg}")
                        errors.append(error_msg)
            
            return {
                'success': total_failed == 0,
                'message': f"同步完成 - 成功: {total_sent}, 失败: {total_failed}",
                'total': query_result.get('total', 0),
                'sent': total_sent,
                'failed': total_failed,
                'errors': errors
            }
        
        except Exception as e:
            return {
                'success': False,
                'error': f"同步过程异常: {str(e)}",
                'sent': total_sent,
                'failed': total_failed
            }
    
    def _send_query_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送查询HTTP请求到天健云
        
        Args:
            request_data: 请求数据
            
        Returns:
            请求结果
        """
        url = f"{self.api_config['base_url']}/dx/inter/getApplyItemDict"
        headers = self.create_headers()
        
        # 在请求头中添加查询标识
        headers['X-Query-Type'] = 'apply_item_dict'
        
        try:
            response = requests.post(
                url,
                headers=headers,
                json=request_data,
                timeout=self.api_config.get('timeout', 30),
                verify=False
            )
            
            if response.status_code == 200:
                try:
                    response_json = response.json()
                    if response_json.get('code') == 0:
                        return {
                            'success': True,
                            'response': response_json
                        }
                    else:
                        return {
                            'success': False,
                            'error': f"API错误: code={response_json.get('code')}, msg={response_json.get('msg', '未知错误')}",
                            'response': response_json
                        }
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': f"响应解析失败: {response.text}",
                        'response': response.text
                    }
            else:
                return {
                    'success': False,
                    'error': f"HTTP错误: {response.status_code}",
                    'response': response.text
                }
        
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'error': "请求超时"
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'error': "连接错误"
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"请求异常: {str(e)}"
            }
    
    def _send_sync_request(self, request_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        发送同步HTTP请求到天健云
        
        Args:
            request_data: 请求数据
            
        Returns:
            请求结果
        """
        # 使用02号接口的URL
        url = f"{self.api_config['base_url']}/dx/inter/syncApplyItem"
        headers = self.create_headers()
        
        # 在请求头中添加同步标识
        headers['X-Sync-Type'] = 'apply_item_dict_query'
        
        try:
            response = requests.post(
                url,
                headers=headers,
                json=request_data,
                timeout=self.api_config.get('timeout', 30),
                verify=False
            )
            
            if response.status_code == 200:
                try:
                    response_json = response.json()
                    if response_json.get('code') == 0:
                        return {
                            'success': True,
                            'response': response_json
                        }
                    else:
                        return {
                            'success': False,
                            'error': f"API错误: code={response_json.get('code')}, msg={response_json.get('msg', '未知错误')}",
                            'response': response_json
                        }
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': f"响应解析失败: {response.text}",
                        'response': response.text
                    }
            else:
                return {
                    'success': False,
                    'error': f"HTTP错误: {response.status_code}",
                    'response': response.text
                }
        
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'error': "请求超时"
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'error': "连接错误"
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"请求异常: {str(e)}"
            }


# API配置
API_CONFIG = {
    'base_url': get_tianjian_base_url(),
    'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
    'mic_code': 'MIC1.001E',
    'misc_id': 'MISC1.00001A',
    'timeout': 30
}


def main():
    """主函数 - 测试11号接口"""
    print("天健云11号接口测试 - 查询项目字典信息接口")
    print("=" * 60)
    
    # 创建接口实例
    interface = TianjianInterface11(API_CONFIG)
    
    # 测试模式 - 查询所有申请项目字典
    print("\n1. 测试模式 - 查询前5条申请项目字典")
    result = interface.query_apply_item_dict(
        limit=5,
        test_mode=True,
        page_size=3
    )
    print(f"测试结果: {result}")
    
    # 测试按科室查询申请项目字典
    print("\n2. 测试模式 - 按科室查询申请项目字典")
    result = interface.query_apply_item_dict(
        dept_codes=['001', '002'],  # 假设的科室编码
        limit=3,
        test_mode=True,
        page_size=2
    )
    print(f"按科室查询测试结果: {result}")
    
    # 测试按项目名称关键字查询
    print("\n3. 测试模式 - 按项目名称关键字查询")
    result = interface.query_apply_item_dict(
        item_name_keyword='血',  # 搜索包含"血"字的项目
        limit=3,
        test_mode=True,
        page_size=2
    )
    print(f"按关键字查询测试结果: {result}")
    
    # 实际查询和同步
    print("\n4. 实际查询 - 查询前10条申请项目字典")
    confirm = input("是否继续实际查询并同步到天健云？(y/N): ")
    if confirm.lower() == 'y':
        # 先查询
        query_result = interface.query_apply_item_dict(
            limit=10,
            test_mode=False,
            page_size=5
        )
        print(f"查询结果: {query_result}")
        
        # 再同步到API
        if query_result.get('success'):
            sync_result = interface.sync_apply_item_dict_to_api(query_result, batch_size=3)
            print(f"同步结果: {sync_result}")
    else:
        print("已取消实际查询")


if __name__ == '__main__':
    main()