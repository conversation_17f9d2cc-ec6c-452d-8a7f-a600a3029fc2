#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云11号接口实现 - 查询项目字典信息接口
严格按照天健云标准格式实现
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from database_service import get_database_service
from multi_org_config import get_current_org_config

# 设置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/interface_11.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('Interface11')


class TianjianInterface11:
    """天健云11号接口 - 查询项目字典信息接口"""
    
    def __init__(self):
        """初始化接口"""
        try:
            self.org_config = get_current_org_config()
            self.db_service = get_database_service()
            logger.info("天健云11号接口初始化成功")
        except Exception as e:
            logger.error(f"天健云11号接口初始化失败: {str(e)}")
            raise
    
    def _validate_request(self, request_data: Dict[str, Any]) -> Tuple[bool, str]:
        """
        验证请求参数
        
        Args:
            request_data: 请求数据
            
        Returns:
            (是否有效, 错误信息)
        """
        if not isinstance(request_data, dict):
            return False, "请求数据必须是字典格式"
        
        # 检查必需的键是否存在
        required_keys = ['id', 'hospitalCode']
        for key in required_keys:
            if key not in request_data:
                return False, f"缺少必需参数: {key}"
        
        # 验证id参数
        apply_id = request_data.get('id', '')
        if apply_id and not isinstance(apply_id, str):
            return False, "参数id必须是字符串类型"
        
        # 验证hospitalCode参数
        hospital_code = request_data.get('hospitalCode', '')
        if hospital_code and not isinstance(hospital_code, str):
            return False, "参数hospitalCode必须是字符串类型"
        
        return True, ""
    
    def get_apply_item_dict_standard(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        按照天健云标准格式查询项目字典信息
        
        请求格式:
        {
            "id": "",           // 申请ID，如果为空代表查询全部
            "hospitalCode": ""  // 医院编码，适用于多院区情况
        }
        
        响应格式:
        {
            "code": 0,
            "data": [
                {
                    "applyItemId": "申请ID",
                    "applyItemName": "申请名称", 
                    "displaySequence": "排序",
                    "deptId": "科室id",
                    "checkItemList": [
                        {
                            "checkItemId": "检查项目id",
                            "checkItemName": "检查项目名称",
                            "displaySequence": "排序"
                        }
                    ]
                }
            ],
            "msg": ""
        }
        """
        try:
            logger.info(f"开始处理11号接口请求: {request_data}")
            
            # 验证请求参数
            is_valid, error_msg = self._validate_request(request_data)
            if not is_valid:
                logger.warning(f"参数验证失败: {error_msg}")
                return {
                    "code": -1,
                    "msg": f"参数验证失败: {error_msg}",
                    "data": []
                }
            
            # 解析请求参数
            apply_item_id = request_data.get('id', '')
            hospital_code = request_data.get('hospitalCode', '')
            
            logger.info(f"11号接口请求参数: id={apply_item_id}, hospitalCode={hospital_code}")
            print(f"11号接口请求参数: id={apply_item_id}, hospitalCode={hospital_code}")
            
            if not self.db_service.connect():
                logger.error("数据库连接失败")
                return {
                    "code": -1,
                    "msg": "数据库连接失败",
                    "data": []
                }
            
            try:
                # 优化后的SQL查询 - 基于正确的表结构
                if apply_item_id:
                    # 查询特定申请项目
                    sql = """
                    SELECT DISTINCT
                        ip.cCode as applyItemId,
                        ip.cName as applyItemName,
                        CAST(ROW_NUMBER() OVER (ORDER BY ip.cCode) AS VARCHAR) as displaySequence,
                        ISNULL(dd.cCode, 'UNKNOWN') as deptId
                    FROM code_Item_Price ip
                    INNER JOIN Code_Item_Main im ON ip.cMainCode = im.cCode
                    LEFT JOIN Code_Dept_Main dm ON im.cCode = dm.cMainCode
                    LEFT JOIN Code_Dept_dict dd ON dm.cDeptCode = dd.cCode
                    WHERE (ip.cStopTag IS NULL OR ip.cStopTag = '0') 
                      AND (im.cStopTag IS NULL OR im.cStopTag = '0')
                      AND ip.cCode = ?
                    ORDER BY ip.cCode
                    """
                    params = [apply_item_id]
                else:
                    # 查询所有申请项目，使用TOP限制数量
                    sql = """
                    SELECT DISTINCT TOP 10
                        ip.cCode as applyItemId,
                        ip.cName as applyItemName,
                        CAST(ROW_NUMBER() OVER (ORDER BY ip.cCode) AS VARCHAR) as displaySequence,
                        ISNULL(dd.cCode, 'UNKNOWN') as deptId
                    FROM code_Item_Price ip
                    INNER JOIN Code_Item_Main im ON ip.cMainCode = im.cCode
                    LEFT JOIN Code_Dept_Main dm ON im.cCode = dm.cMainCode
                    LEFT JOIN Code_Dept_dict dd ON dm.cDeptCode = dd.cCode
                    WHERE (ip.cStopTag IS NULL OR ip.cStopTag = '0') 
                      AND (im.cStopTag IS NULL OR im.cStopTag = '0')
                    ORDER BY ip.cCode
                    """
                    params = []
                
                apply_items = self.db_service.execute_query(sql, tuple(params) if params else None)
                logger.info(f"数据库查询成功，返回 {len(apply_items) if apply_items else 0} 条申请项目记录")
                
                # 构建返回数据
                data_list = []
                for item in apply_items:
                    apply_item_id_val = item['applyItemId']
                    
                    # 获取该申请项目下的检查项目列表
                    check_items = self._get_check_items_for_apply_item(apply_item_id_val)
                    
                    # 构建标准格式的申请项目数据
                    apply_item_data = {
                        "applyItemId": apply_item_id_val,
                        "applyItemName": item['applyItemName'],
                        "displaySequence": item['displaySequence'],
                        "deptId": item['deptId'],
                        "checkItemList": check_items
                    }
                    
                    data_list.append(apply_item_data)
                
                logger.info(f"11号接口处理成功，返回 {len(data_list)} 条完整数据")
                return {
                    "code": 0,
                    "msg": "",
                    "data": data_list
                }
                
            except Exception as db_error:
                logger.error(f"数据库查询异常: {str(db_error)}")
                return {
                    "code": -1,
                    "msg": f"数据库查询异常: {str(db_error)}",
                    "data": []
                }
            finally:
                try:
                    self.db_service.disconnect()
                    logger.debug("数据库连接已关闭")
                except Exception as disconnect_error:
                    logger.warning(f"关闭数据库连接时出现异常: {str(disconnect_error)}")
                
        except Exception as e:
            logger.error(f"11号接口处理异常: {str(e)}")
            print(f"11号接口处理异常: {str(e)}")
            return {
                "code": -1,
                "msg": f"处理异常: {str(e)}",
                "data": []
            }
    
    def _get_check_items_for_apply_item(self, apply_item_id: str) -> List[Dict[str, Any]]:
        """
        获取申请项目下的检查项目列表
        
        Args:
            apply_item_id: 申请项目ID (code_Item_Price.cCode)
            
        Returns:
            检查项目列表
        """
        try:
            logger.debug(f"查询申请项目 {apply_item_id} 的检查项目列表")
            
            # 优化后的查询 - 直接通过价格表关联到主项目
            # 在医疗体检系统中，通常一个申请项目就是一个检查项目
            # 如果需要更细分的检查项目，可以通过其他表关联
            sql = """
            SELECT DISTINCT
                im.cCode as checkItemId,
                im.cName as checkItemName,
                CAST(ROW_NUMBER() OVER (ORDER BY im.cCode) AS VARCHAR) as displaySequence
            FROM code_Item_Price ip
            INNER JOIN Code_Item_Main im ON ip.cMainCode = im.cCode
            WHERE ip.cCode = ? 
              AND (im.cStopTag IS NULL OR im.cStopTag = '0')
            ORDER BY im.cCode
            """
            
            result = self.db_service.execute_query(sql, (apply_item_id,))
            logger.debug(f"查询到 {len(result) if result else 0} 条检查项目记录")
            
            # 格式化检查项目数据
            check_items = []
            for item in result:
                if item['checkItemId']:  # 确保检查项目ID存在
                    check_item_data = {
                        "checkItemId": item['checkItemId'],
                        "checkItemName": item['checkItemName'],
                        "displaySequence": item['displaySequence']
                    }
                    check_items.append(check_item_data)
            
            # 如果没有找到对应的检查项目，使用申请项目本身作为检查项目
            if not check_items:
                logger.warning(f"申请项目 {apply_item_id} 未找到对应的检查项目，使用申请项目本身")
                check_items = [{
                    "checkItemId": apply_item_id,
                    "checkItemName": "未找到具体检查项目",
                    "displaySequence": "1"
                }]
            
            return check_items
            
        except Exception as e:
            logger.error(f"获取申请项目 {apply_item_id} 的检查项目异常: {str(e)}")
            print(f"获取检查项目异常: {str(e)}")
            # 返回申请项目本身作为检查项目
            return [{
                "checkItemId": apply_item_id,
                "checkItemName": "查询异常",
                "displaySequence": "1"
            }]


def main():
    """主函数 - 测试11号接口"""
    print("天健云11号接口测试 - 查询项目字典信息接口")
    print("=" * 60)
    
    # 创建接口实例
    interface = TianjianInterface11()
    
    # 测试1: 查询所有申请项目
    print("\n1. 测试查询所有申请项目字典")
    request_data = {
        "id": "",
        "hospitalCode": ""
    }
    
    result = interface.get_apply_item_dict_standard(request_data)
    print(f"返回码: {result.get('code')}")
    print(f"消息: {result.get('msg')}")
    print(f"数据数量: {len(result.get('data', []))}")
    
    if result.get('data'):
        print("\n前2条申请项目示例:")
        for i, item in enumerate(result['data'][:2], 1):
            print(f"\n第 {i} 条申请项目:")
            print(f"  申请ID: {item.get('applyItemId')}")
            print(f"  申请名称: {item.get('applyItemName')}")
            print(f"  科室ID: {item.get('deptId')}")
            print(f"  检查项目数量: {len(item.get('checkItemList', []))}")
            
            # 显示前2个检查项目
            check_items = item.get('checkItemList', [])
            if check_items:
                print("  检查项目:")
                for j, check_item in enumerate(check_items[:2], 1):
                    print(f"    {j}. {check_item.get('checkItemName')} (ID: {check_item.get('checkItemId')})")
    
    # 测试2: 查询特定申请项目
    print(f"\n2. 测试查询特定申请项目")
    if result.get('data'):
        first_item_id = result['data'][0]['applyItemId']
        request_data_specific = {
            "id": first_item_id,
            "hospitalCode": ""
        }
        
        result_specific = interface.get_apply_item_dict_standard(request_data_specific)
        print(f"指定申请项目 {first_item_id} 查询结果:")
        print(f"  返回码: {result_specific.get('code')}")
        print(f"  数据数量: {len(result_specific.get('data', []))}")
        
        if result_specific.get('data'):
            item = result_specific['data'][0]
            print(f"  申请名称: {item.get('applyItemName')}")
            print(f"  检查项目数量: {len(item.get('checkItemList', []))}")


if __name__ == '__main__':
    main()