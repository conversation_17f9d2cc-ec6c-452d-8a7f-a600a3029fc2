#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云12号接口实现 - 主检锁定与解锁(可选)
主检系统，通过这个接口，按要求更改T_Diag_result表
支持多门店数据库切换和卡号到客户编码的转换
"""

import json
from datetime import datetime
from typing import Dict, Any, List, Optional
from multi_org_config import get_org_config_by_shop_code
from database_service import DatabaseService
from config import Config


class TianjianInterface12:
    """天健云12号接口 - 主检锁定与解锁(可选)"""
    
    def __init__(self, api_config: Dict[str, Any]):
        """
        初始化接口配置
        
        Args:
            api_config: API配置信息 (保留以兼容旧接口)
        """
        self.api_config = api_config
        self.db_service = None  # 将根据门店编码动态创建
    
    def get_db_service_by_shop_code(self, shop_code: str) -> DatabaseService:
        # 根据门店编码获取对应的数据库服务
        org_config = get_org_config_by_shop_code(shop_code)
        if not org_config:
            raise Exception(f'Shop code {shop_code} not found in configuration')
        
        # 构建数据库连接字符串
        connection_string = (
            f"DRIVER={{{org_config['db_driver']}}};"
            f"SERVER={org_config['db_host']},{org_config['db_port']};"
            f"DATABASE={org_config['db_name']};"
            f"UID={org_config['db_user']};"
            f"PWD={org_config['db_password']};"
            f"TrustServerCertificate=yes;"
        )
        
        print(f'Using database for shop {shop_code}: {org_config["db_host"]}:{org_config["db_port"]}/{org_config["db_name"]}')
        
        return DatabaseService(connection_string)
    
    # generate_signature 方法已移除，不再需要API签名
    # 现在直接操作本地数据库
    
    # send_request 方法已移除，不再向远程API发送请求
    # 现在直接操作本地数据库T_Diag_result表
    
    def lock_pe_info(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        主检锁定解锁接口 - GUI服务调用入口
        
        Args:
            request_data: 请求数据，包含operator和peInfoList（每个peInfo包含shopCode）
            
        Returns:
            操作结果
        """
        try:
            # 验证请求数据
            if not request_data:
                return {
                    'code': -1,
                    'msg': '请求数据不能为空',
                    'data': None
                }
            
            operator = request_data.get('operator', '')
            pe_info_list = request_data.get('peInfoList', [])
            
            if not operator:
                return {
                    'code': -1,
                    'msg': '操作人不能为空',
                    'data': None
                }
            
            if not pe_info_list:
                return {
                    'code': -1,
                    'msg': '体检信息列表不能为空',
                    'data': None
                }
            
            # 验证每个体检信息项的必要字段
            for i, pe_info in enumerate(pe_info_list):
                required_fields = ['accountId', 'currentNodeType', 'operationType', 'peNo', 'shopCode']
                missing_fields = [field for field in required_fields if field not in pe_info or not pe_info[field]]
                
                if missing_fields:
                    return {
                        'code': -1,
                        'msg': f'第{i+1}项体检信息缺少必要字段: {", ".join(missing_fields)}',
                        'data': None
                    }
                
                # 验证操作类型
                operation_type = pe_info.get('operationType')
                if operation_type not in [1, 2]:
                    return {
                        'code': -1,
                        'msg': f'第{i+1}项体检信息操作类型无效，应为1(锁定)或2(解锁)',
                        'data': None
                    }
            
            # 执行主检锁定解锁操作 - 每个peInfo包含自己的shopCode
            return self.process_pe_info_by_shop(operator, pe_info_list, test_mode=False)
            
        except Exception as e:
            return {
                'code': -1,
                'msg': f'主检锁定解锁操作失败: {str(e)}',
                'data': None
            }
    
    def process_pe_info_by_shop(self, operator: str, pe_info_list: List[Dict[str, Any]], test_mode: bool = False) -> Dict[str, Any]:
        """
        根据门店编码处理体检信息 - 每个peInfo包含自己的shopCode
        
        Args:
            operator: 操作人主键id
            pe_info_list: 体检信息列表，每个项目包含shopCode
            test_mode: 测试模式标志
            
        Returns:
            操作结果
        """
        if test_mode:
            print(f"主检锁定与解锁操作 - 测试模式")
            print(f"   操作人: {operator}")
            print(f"   操作数量: {len(pe_info_list)}")
            for i, pe_info in enumerate(pe_info_list, 1):
                print(f"   [{i}] 卡号: {pe_info.get('peNo')}, 门店: {pe_info.get('shopCode')}")
            return {
                'code': 0,
                'msg': '测试模式 - 主检锁定解锁操作成功',
                'data': None
            }
        
        try:
            success_count = 0
            failed_count = 0
            errors = []
            
            print(f"开始处理T_Diag_result表记录，共 {len(pe_info_list)} 条")
            
            for i, pe_info in enumerate(pe_info_list, 1):
                try:
                    card_no = pe_info.get('peNo', '')
                    account_id = pe_info.get('accountId', '')
                    operation_type = pe_info.get('operationType', 1)
                    shop_code = pe_info.get('shopCode', '09')
                    
                    operation_text = '锁定' if operation_type == 1 else '解锁'
                    print(f"   [{i}] 处理卡号: {card_no}, 门店: {shop_code}, 操作: {operation_text}")
                    
                    # 根据门店编码获取对应的数据库服务
                    db_service = self.get_db_service_by_shop_code(shop_code)
                    
                    if not db_service.connect():
                        failed_count += 1
                        error_msg = f"卡号 {card_no}: 门店 {shop_code} 数据库连接失败"
                        errors.append(error_msg)
                        print(f"   [FAIL] {error_msg}")
                        continue
                    
                    try:
                        # 通过卡号获取客户编码
                        client_code = self.get_client_code_by_card(db_service, card_no)
                        if not client_code:
                            failed_count += 1
                            error_msg = f"卡号 {card_no}: 客户编码获取失败"
                            errors.append(error_msg)
                            print(f"   [FAIL] {error_msg}")
                            continue
                        
                        # 根据操作类型处理T_Diag_result表
                        if operation_type == 1:  # 锁定 - 插入/更新记录
                            if self.update_diag_table(db_service, client_code, account_id, operator):
                                success_count += 1
                                print(f"   [OK] 卡号 {card_no} -> 客户编码 {client_code} (门店{shop_code}) 锁定成功")
                            else:
                                failed_count += 1
                                error_msg = f"卡号 {card_no}: T_Diag_result表锁定失败"
                                errors.append(error_msg)
                                print(f"   [FAIL] {error_msg}")
                        elif operation_type == 2:  # 解锁 - 删除记录
                            if self.delete_diag_table(db_service, client_code):
                                success_count += 1
                                print(f"   [OK] 卡号 {card_no} -> 客户编码 {client_code} (门店{shop_code}) 解锁成功")
                            else:
                                failed_count += 1
                                error_msg = f"卡号 {card_no}: T_Diag_result表解锁失败"
                                errors.append(error_msg)
                                print(f"   [FAIL] {error_msg}")
                    
                    finally:
                        db_service.disconnect()
                        
                except Exception as e:
                    failed_count += 1
                    error_msg = f"卡号 {pe_info.get('peNo', 'Unknown')} 处理异常: {str(e)}"
                    errors.append(error_msg)
                    print(f"   [FAIL] {error_msg}")
            
            # 返回结果
            if failed_count == 0:
                return {
                    'code': 0,
                    'msg': f'所有 {success_count} 条记录处理成功',
                    'data': {
                        'total': len(pe_info_list),
                        'success': success_count,
                        'failed': failed_count
                    }
                }
            else:
                return {
                    'code': -1,
                    'msg': f'部分记录处理失败: {success_count}成功, {failed_count}失败',
                    'data': {
                        'total': len(pe_info_list),
                        'success': success_count,
                        'failed': failed_count,
                        'errors': errors
                    }
                }
                
        except Exception as e:
            return {
                'code': -1,
                'msg': f'T_Diag_result表处理异常: {str(e)}',
                'data': None
            }
    
    def get_client_code_by_card(self, db_service: DatabaseService, card_no: str) -> str:
        """通过卡号获取客户编码"""
        try:
            sql = 'SELECT cClientCode FROM T_Register_Main WHERE cCardNo = ?'
            result = db_service.execute_query(sql, (card_no,))
            if result and len(result) > 0:
                client_code = result[0]['cClientCode']
                print('   [INFO] Card ' + card_no + ' -> ClientCode: ' + client_code)
                return client_code
            else:
                print('   [WARN] Card ' + card_no + ' not found in T_Register_Main')
                return None
        except Exception as e:
            print('   [ERROR] Query client code error: ' + str(e))
            return None
    
    def delete_diag_table(self, db_service: DatabaseService, client_code: str) -> bool:
        """
        从T_Diag_result表中删除记录 - 主检解锁操作
        
        Args:
            db_service: 数据库服务实例
            client_code: 客户编码
            
        Returns:
            bool: 是否成功
        """
        try:
            # 检查记录是否存在
            check_sql = """
            SELECT COUNT(*) as count
            FROM T_Diag_result
            WHERE cClientCode = ?
            """
            
            result = db_service.execute_query(check_sql, (client_code,))
            record_exists = result[0]['count'] > 0 if result else False
            
            if record_exists:
                # 删除记录
                delete_sql = """
                DELETE FROM T_Diag_result
                WHERE cClientCode = ?
                """
                
                db_service.execute_update(delete_sql, (client_code,))
                print(f"   [INFO] 删除T_Diag_result表记录: {client_code}")
                return True
            else:
                print(f"   [WARN] 客户编码 {client_code} 在T_Diag_result表中不存在，无需删除")
                return True  # 记录不存在也视为解锁成功
            
        except Exception as e:
            print(f"   [ERROR] T_Diag_result表删除操作异常: {str(e)}")
            return False
    
    def update_diag_table(self, db_service: DatabaseService, client_code: str, account_id: str, operator: str) -> bool:
        """
        按要求更改T_Diag_result表 - 支持多门店数据库
        
        Args:
            db_service: 数据库服务实例
            client_code: 客户编码
            account_id: 医生账号ID
            operator: 操作人
            
        Returns:
            bool: 是否成功
        """
        try:
            from datetime import datetime
            current_time = datetime.now()
            
            # 限制cOperCode字段长度为6位
            oper_code = account_id[:6] if len(account_id) > 6 else account_id
            if len(account_id) > 6:
                print(f"   [WARN] cOperCode {account_id} 超过6位，截取为: {oper_code}")
            
            # 检查记录是否存在
            check_sql = """
            SELECT COUNT(*) as count
            FROM T_Diag_result
            WHERE cClientCode = ?
            """
            
            result = db_service.execute_query(check_sql, (client_code,))
            record_exists = result[0]['count'] > 0 if result else False
            
            if record_exists:
                # 更新现有记录
                update_sql = """
                UPDATE T_Diag_result
                SET cOperCode = ?,
                    cOpername = ?,
                    dOperDate = ?
                WHERE cClientCode = ?
                """
                
                params = (oper_code, operator, current_time, client_code)
                db_service.execute_update(update_sql, params)
                print(f"   [INFO] 更新T_Diag_result表记录: {client_code}")
            else:
                # 插入新记录
                insert_sql = """
                INSERT INTO T_Diag_result (
                    cClientCode,
                    cOperCode,
                    cOpername,
                    dOperDate
                ) VALUES (?, ?, ?, ?)
                """
                
                params = (client_code, oper_code, operator, current_time)
                db_service.execute_update(insert_sql, params)
                print(f"   [INFO] 插入T_Diag_result表记录: {client_code}")
            
            return True
            
        except Exception as e:
            print(f"   [ERROR] T_Diag_result表操作异常: {str(e)}")
            return False
    
    def lock_or_unlock_pe_info(self, operator: str, pe_info_list: List[Dict[str, Any]], 
                               shop_code: str = '09', test_mode: bool = False) -> Dict[str, Any]:
        """
        主检锁定与解锁接口 - 兼容旧测试代码
        
        Args:
            operator: 操作人主键id
            pe_info_list: 体检信息列表
            shop_code: 门店编码（默认为'09'）
            test_mode: 测试模式标志
            
        Returns:
            操作结果
        """
        try:
            # 为每个pe_info添加shopCode（如果没有的话）
            for pe_info in pe_info_list:
                if 'shopCode' not in pe_info:
                    pe_info['shopCode'] = shop_code
            
            # 构建符合lock_pe_info方法要求的请求数据格式
            request_data = {
                'operator': operator,
                'peInfoList': pe_info_list
            }
            
            # 调用现有的lock_pe_info方法
            return self.lock_pe_info(request_data)
            
        except Exception as e:
            return {
                'code': -1,
                'msg': f'主检锁定解锁操作失败: {str(e)}',
                'data': None
            }

    
    def get_pe_info_from_db(self, pe_nos: List[str] = None, limit: int = None) -> List[Dict[str, Any]]:
        """
        从数据库获取体检信息
        
        Args:
            pe_nos: 体检号列表
            limit: 限制返回条数
            
        Returns:
            体检信息列表
        """
        if not self.db_service.connect():
            raise Exception("数据库连接失败")
        
        try:
            sql = """
            SELECT 
                trm.cRegNo as peNo,
                trm.cName as patientName,
                trm.cIDNo as idCard,
                trm.cSex as gender,
                trm.cBirthday as birthday,
                trm.cRegDate as regDate,
                trm.cCheckDate as checkDate,
                trm.cStatus as status,
                cod.cOperCode as operatorCode,
                cod.cOperName as operatorName
            FROM T_Register_Main trm
            LEFT JOIN Code_Operator_dict cod ON trm.cOperCode = cod.cOperCode
            WHERE 1=1
            """
            
            params = []
            
            if pe_nos:
                placeholders = ','.join(['?' for _ in pe_nos])
                sql += f" AND trm.cRegNo IN ({placeholders})"
                params.extend(pe_nos)
            
            sql += " ORDER BY trm.cRegDate DESC"
            
            if limit:
                sql = sql.replace("SELECT ", f"SELECT TOP {limit} ")
            
            result = self.db_service.execute_query(sql, tuple(params) if params else None)
            return result
            
        finally:
            self.db_service.disconnect()


def test_interface_12():
    """测试12号接口 - 新的数据库操作逻辑"""
    print("[TEST] 测试天健云12号接口 - T_Diag_result表操作")
    print("=" * 60)
    
    # API配置(保留以兼容旧接口)
    api_config = {
        'base_url': 'http://203.83.237.114:9300',
        'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
        'mic_code': 'MIC1.001E',
        'misc_id': 'MISC1.00001A',
        'timeout': 30
    }
    
    # 创建接口实例
    interface = TianjianInterface12(api_config)
    
    # 测试场景1：锁定主检任务 - 在T_Diag_result表中插入记录
    print("\\n[LOCK] 测试场景1：锁定主检任务 - 数据库操作")
    pe_info_list = [
        {
            "accountId": "09DOCTOR001",  # 门店编号09
            "currentNodeType": 3,  # 主检
            "force": False,
            "operationType": 1,  # 锁定
            "peNo": "5000003"  # 测试卡号
        }
    ]
    
    result1 = interface.lock_or_unlock_pe_info(
        operator="09ADMIN001",  # 门店编号09
        pe_info_list=pe_info_list,
        shop_code="09",  # 门店编码09
        test_mode=True  # 测试模式，不实际操作数据库
    )
    print(f"结果: {json.dumps(result1, ensure_ascii=False, indent=2)}")
    
    # 测试场景2：解锁主检任务 - 更新T_Diag_result表记录
    print("\\n[UNLOCK] 测试场景2：解锁主检任务 - 数据库操作")
    pe_info_list = [
        {
            "accountId": "09DOCTOR001",  # 门店编号09
            "currentNodeType": 3,  # 主检
            "force": True,
            "operationType": 2,  # 解锁
            "peNo": "5000006"  # 测试卡号
        }
    ]
    
    result2 = interface.lock_or_unlock_pe_info(
        operator="09ADMIN001",  # 门店编号09
        pe_info_list=pe_info_list,
        shop_code="09",  # 门店编码09
        test_mode=True  # 测试模式，不实际操作数据库
    )
    print(f"结果: {json.dumps(result2, ensure_ascii=False, indent=2)}")
    
    # 测试场景3：实际数据库操作演示（谨慎使用）
    print("\n数据库操作说明：")
    print("   - 按要求更改T_Diag_result表")
    print("   - 记录存在则更新，不存在则插入")
    print("   - 更新字段：cClientCode(体检号), cOperCode(医生账号), cOpername(操作人), dOperDate(操作时间)")
    
    print("\n[OK] 天健云12号接口测试完成 - 现在直接操作本地数据库")


if __name__ == "__main__":
    test_interface_12()