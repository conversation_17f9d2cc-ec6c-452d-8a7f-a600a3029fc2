#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云12号接口实现 - 主检锁定与解锁(修复版)
支持多门店数据库切换和卡号到客户编码的转换
"""

import json
from datetime import datetime
from typing import Dict, Any, List, Optional
from multi_org_config import get_org_config_by_shop_code
from database_service import DatabaseService
from config import Config
from api_config_manager import get_tianjian_base_url


class TianjianInterface12Fixed:
    """天健云12号接口 - 主检锁定与解锁(修复版)"""
    
    def __init__(self, api_config: Dict[str, Any]):
        self.api_config = api_config
        self.db_service = None
    
    def get_db_service_by_shop_code(self, shop_code: str) -> DatabaseService:
        """根据门店编码获取对应的数据库服务"""
        org_config = get_org_config_by_shop_code(shop_code)
        if not org_config:
            raise Exception(f'Shop code {shop_code} not found in configuration')
        
        connection_string = (
            f"DRIVER={{{org_config['db_driver']}}};"
            f"SERVER={org_config['db_host']},{org_config['db_port']};"
            f"DATABASE={org_config['db_name']};"
            f"UID={org_config['db_user']};"
            f"PWD={org_config['db_password']};"
            f"TrustServerCertificate=yes;"
        )
        
        print(f'Using database for shop {shop_code}: {org_config["db_host"]}:{org_config["db_port"]}/{org_config["db_name"]}')
        return DatabaseService(connection_string)
    
    def get_client_code_by_card(self, db_service: DatabaseService, card_no: str) -> str:
        """通过卡号获取客户编码"""
        try:
            sql = 'SELECT cClientCode FROM T_Register_Main WHERE cCardNo = ?'
            result = db_service.execute_query(sql, (card_no,))
            if result and len(result) > 0:
                client_code = result[0]['cClientCode']
                print(f'   [INFO] Card {card_no} -> ClientCode: {client_code}')
                return client_code
            else:
                print(f'   [WARN] Card {card_no} not found in T_Register_Main')
                return None
        except Exception as e:
            print(f'   [ERROR] Query client code error: {str(e)}')
            return None
    
    def update_diag_table(self, db_service: DatabaseService, client_code: str, account_id: str, operator: str) -> bool:
        """更新T_Diag_result表"""
        try:
            current_time = datetime.now()
            
            # 限制cOperCode字段长度为6位
            oper_code = account_id[:6] if len(account_id) > 6 else account_id
            
            # 检查记录是否存在
            check_sql = 'SELECT COUNT(*) as count FROM T_Diag_result WHERE cClientCode = ?'
            result = db_service.execute_query(check_sql, (client_code,))
            record_exists = result[0]['count'] > 0 if result else False
            
            if record_exists:
                # 更新现有记录
                update_sql = '''UPDATE T_Diag_result 
                               SET cOperCode = ?, cOpername = ?, dOperDate = ? 
                               WHERE cClientCode = ?'''
                params = (oper_code, operator, current_time, client_code)
                db_service.execute_update(update_sql, params)
                print(f'   [INFO] Updated T_Diag_result record: {client_code}')
            else:
                # 插入新记录
                insert_sql = '''INSERT INTO T_Diag_result (cClientCode, cOperCode, cOpername, dOperDate) 
                               VALUES (?, ?, ?, ?)'''
                params = (client_code, oper_code, operator, current_time)
                db_service.execute_update(insert_sql, params)
                print(f'   [INFO] Inserted T_Diag_result record: {client_code}')
            
            return True
        except Exception as e:
            print(f'   [ERROR] T_Diag_result operation error: {str(e)}')
            return False
    
    def lock_pe_info(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """主检锁定解锁接口 - GUI服务调用入口"""
        try:
            # 验证请求数据
            if not request_data:
                return {'code': -1, 'msg': '请求数据不能为空', 'data': None}
            
            operator = request_data.get('operator', '')
            pe_info_list = request_data.get('peInfoList', [])
            shop_code = request_data.get('shopCode', '09')  # 默认门店编码09
            
            if not operator:
                return {'code': -1, 'msg': '操作人不能为空', 'data': None}
            
            if not pe_info_list:
                return {'code': -1, 'msg': '体检信息列表不能为空', 'data': None}
            
            # 获取对应门店的数据库服务
            db_service = self.get_db_service_by_shop_code(shop_code)
            
            if not db_service.connect():
                return {'code': -1, 'msg': '数据库连接失败', 'data': None}
            
            try:
                success_count = 0
                failed_count = 0
                errors = []
                
                print(f'Processing {len(pe_info_list)} records for shop {shop_code}')
                
                for i, pe_info in enumerate(pe_info_list, 1):
                    try:
                        card_no = pe_info.get('peNo', '')
                        account_id = pe_info.get('accountId', '')
                        operation_type = pe_info.get('operationType', 1)
                        
                        operation_text = 'Lock' if operation_type == 1 else 'Unlock'
                        print(f'   [{i}] Processing card: {card_no}, operation: {operation_text}')
                        
                        # 通过卡号获取客户编码
                        client_code = self.get_client_code_by_card(db_service, card_no)
                        if not client_code:
                            failed_count += 1
                            error_msg = f'Card {card_no}: Client code not found'
                            errors.append(error_msg)
                            print(f'   [FAIL] {error_msg}')
                            continue
                        
                        # 更新T_Diag_result表
                        if self.update_diag_table(db_service, client_code, account_id, operator):
                            success_count += 1
                            print(f'   [OK] Card {card_no} -> ClientCode {client_code} processed successfully')
                        else:
                            failed_count += 1
                            error_msg = f'Card {card_no}: T_Diag_result update failed'
                            errors.append(error_msg)
                            print(f'   [FAIL] {error_msg}')
                    
                    except Exception as e:
                        failed_count += 1
                        error_msg = f'Card {pe_info.get("peNo", "Unknown")}: Processing error: {str(e)}'
                        errors.append(error_msg)
                        print(f'   [FAIL] {error_msg}')
                
                # 返回结果
                if failed_count == 0:
                    return {
                        'code': 0,
                        'msg': f'所有 {success_count} 条记录处理成功',
                        'data': {
                            'total': len(pe_info_list),
                            'success': success_count,
                            'failed': failed_count
                        }
                    }
                else:
                    return {
                        'code': -1,
                        'msg': f'部分记录处理失败: {success_count}成功, {failed_count}失败',
                        'data': {
                            'total': len(pe_info_list),
                            'success': success_count,
                            'failed': failed_count,
                            'errors': errors
                        }
                    }
            
            finally:
                db_service.disconnect()
        
        except Exception as e:
            return {
                'code': -1,
                'msg': f'主检锁定解锁操作失败: {str(e)}',
                'data': None
            }


def test_interface_12_fixed():
    """测试修复版12号接口"""
    print("Testing Fixed Interface 12")
    print("=" * 50)
    
    api_config = {
        'base_url': get_tianjian_base_url(),
        'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
        'mic_code': 'MIC1.001E',
        'misc_id': 'MISC1.00001A',
        'timeout': 30
    }
    
    interface = TianjianInterface12Fixed(api_config)
    
    # 测试数据 - 包含门店编码09
    request_data = {
        'operator': 'ADMIN001',
        'shopCode': '09',  # 门店编码09
        'peInfoList': [
            {
                'accountId': 'DO001',
                'currentNodeType': 3,
                'force': False,
                'operationType': 1,
                'peNo': '5000003'  # 卡号
            }
        ]
    }
    
    result = interface.lock_pe_info(request_data)
    print(f"Result: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
    return result


if __name__ == "__main__":
    test_interface_12_fixed()