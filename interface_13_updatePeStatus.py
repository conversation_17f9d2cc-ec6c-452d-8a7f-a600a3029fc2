#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云13号接口实现 - 体检报告状态更新
根据机构编码获取数据库连接，更新T_Register_Main.cstatus字段
"""

import json
from datetime import datetime
from typing import Dict, Any, List, Optional
from multi_org_config import get_org_config_by_shop_code
from database_service import DatabaseService


class TianjianInterface13:
    """天健云13号接口 - 体检报告状态更新"""
    
    def __init__(self, api_config: Dict[str, Any]):
        """
        初始化接口配置
        
        Args:
            api_config: API配置信息
        """
        self.api_config = api_config
    
    def get_db_service_by_shop_code(self, shop_code: str) -> DatabaseService:
        """根据门店编码获取对应的数据库服务"""
        org_config = get_org_config_by_shop_code(shop_code)
        if not org_config:
            raise Exception(f'Shop code {shop_code} not found in configuration')
        
        connection_string = (
            f"DRIVER={{{org_config['db_driver']}}};"
            f"SERVER={org_config['db_host']},{org_config['db_port']};"
            f"DATABASE={org_config['db_name']};"
            f"UID={org_config['db_user']};"
            f"PWD={org_config['db_password']};"
            f"TrustServerCertificate=yes;"
        )
        
        print(f'Using database for shop {shop_code}: {org_config["db_host"]}:{org_config["db_port"]}/{org_config["db_name"]}')
        return DatabaseService(connection_string)
    
    def get_client_code_by_card(self, db_service: DatabaseService, card_no: str) -> str:
        """通过卡号获取客户编码"""
        try:
            sql = 'SELECT cClientCode FROM T_Register_Main WHERE cCardNo = ?'
            result = db_service.execute_query(sql, (card_no,))
            if result and len(result) > 0:
                client_code = result[0]['cClientCode']
                print(f'   [INFO] Card {card_no} -> ClientCode: {client_code}')
                return client_code
            else:
                print(f'   [WARN] Card {card_no} not found in T_Register_Main')
                return None
        except Exception as e:
            print(f'   [ERROR] Query client code error: {str(e)}')
            return None
    
    def get_status_by_node_type(self, node_type: str) -> str:
        """
        根据nodeType获取对应的cstatus值
        
        Args:
            node_type: 节点类型 (1:登记, 2:检查, 3:总检, 4:总审)
            
        Returns:
            对应的cstatus值
        """
        status_mapping = {
            '1': '0',  # 登记 -> 0
            '2': '1',  # 检查(分科检查) -> 1  
            '3': '2',  # 总检 -> 2
            '4': '2'   # 总审 -> 2
        }
        return status_mapping.get(node_type, '0')
    
    def update_pe_status_service(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        体检报告状态更新接口 - GUI服务调用入口
        
        Args:
            request_data: 请求数据，包含nodeType、timestamp、doUser、peNo、cshopcode
            
        Returns:
            操作结果
        """
        try:
            # 验证请求数据
            if not request_data:
                return {
                    'code': -1,
                    'msg': '请求数据不能为空',
                    'data': None
                }
            
            node_type = request_data.get('nodeType', '')
            timestamp = request_data.get('timestamp', '')
            do_user = request_data.get('doUser', {})
            pe_no = request_data.get('peNo', '')
            shop_code = request_data.get('hospitalCode') or request_data.get('cshopcode') or request_data.get('shopcode', '09')  # 机构编码，默认09
            
            # 验证必要字段
            if not node_type:
                return {
                    'code': -1,
                    'msg': 'nodeType不能为空',
                    'data': None
                }
            
            if not pe_no:
                return {
                    'code': -1,
                    'msg': 'peNo不能为空',
                    'data': None
                }
            
            if not do_user or not do_user.get('code'):
                return {
                    'code': -1,
                    'msg': '操作人信息不完整',
                    'data': None
                }
            
            # 验证nodeType有效性
            if node_type not in ['1', '2', '3', '4']:
                return {
                    'code': -1,
                    'msg': 'nodeType无效，应为1(登记)、2(检查)、3(总检)、4(总审)',
                    'data': None
                }
            
            print(f"[UPDATE] 体检状态更新")
            print(f"   体检号: {pe_no}")
            print(f"   节点类型: {node_type} ({'登记' if node_type=='1' else '检查' if node_type=='2' else '总检' if node_type=='3' else '总审'})")
            print(f"   操作人: {do_user.get('name', '')} ({do_user.get('code', '')})")
            print(f"   机构编码: {shop_code}")
            print(f"   操作时间: {timestamp}")
            
            # 执行状态更新
            return self.update_register_status(pe_no, node_type, do_user, shop_code, timestamp)
            
        except Exception as e:
            return {
                'code': -1,
                'msg': f'体检状态更新失败: {str(e)}',
                'data': None
            }
    
    def update_register_status(self, pe_no: str, node_type: str, do_user: Dict[str, str], 
                             shop_code: str, timestamp: str = '') -> Dict[str, Any]:
        """
        更新T_Register_Main表的cstatus字段
        
        Args:
            pe_no: 体检号（卡号）
            node_type: 节点类型
            do_user: 操作人信息
            shop_code: 机构编码
            timestamp: 操作时间
            
        Returns:
            操作结果
        """
        try:
            # 根据机构编码获取数据库服务
            db_service = self.get_db_service_by_shop_code(shop_code)
            
            if not db_service.connect():
                return {
                    'code': -1,
                    'msg': f'机构 {shop_code} 数据库连接失败',
                    'data': None
                }
            
            try:
                # 通过卡号获取客户编码
                client_code = self.get_client_code_by_card(db_service, pe_no)
                if not client_code:
                    return {
                        'code': -1,
                        'msg': f'卡号 {pe_no} 对应的客户编码不存在',
                        'data': None
                    }
                
                # 获取新的状态值
                new_status = self.get_status_by_node_type(node_type)
                
                # 检查记录是否存在
                check_sql = 'SELECT cStatus FROM T_Register_Main WHERE cClientCode = ?'
                result = db_service.execute_query(check_sql, (client_code,))
                
                if not result or len(result) == 0:
                    return {
                        'code': -1,
                        'msg': f'客户编码 {client_code} 在T_Register_Main表中不存在',
                        'data': None
                    }
                
                current_status = result[0]['cStatus']
                print(f"   [INFO] 当前状态: {current_status} -> 新状态: {new_status}")
                
                # 更新状态
                update_sql = 'UPDATE T_Register_Main SET cStatus = ? WHERE cClientCode = ?'
                db_service.execute_update(update_sql, (new_status, client_code))
                
                print(f"   [OK] 卡号 {pe_no} -> 客户编码 {client_code} 状态更新成功: {current_status} -> {new_status}")
                
                return {
                    'code': 0,
                    'msg': '成功',
                    'data': None
                }
                
            finally:
                db_service.disconnect()
                
        except Exception as e:
            return {
                'code': -1,
                'msg': f'状态更新异常: {str(e)}',
                'data': None
            }


def test_interface_13():
    """测试13号接口 - 体检状态更新"""
    print("[TEST] 测试天健云13号接口 - 体检状态更新")
    print("=" * 60)
    
    # API配置
    api_config = {
        'base_url': 'http://203.83.237.114:9300',
        'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
        'mic_code': 'MIC1.001E',
        'misc_id': 'MISC1.00001A',
        'timeout': 30
    }
    
    # 创建接口实例
    interface = TianjianInterface13(api_config)
    
    # 测试数据
    request_data = {
        'nodeType': '3',  # 总检
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'doUser': {
            'code': 'DO001',
            'name': '张医生'
        },
        'peNo': '5000003',  # 卡号
        'cshopcode': '09'   # 机构编码
    }
    
    print("\\n[UPDATE] 测试状态更新")
    print(f"请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
    
    # 执行状态更新测试
    result = interface.update_pe_status_service(request_data)
    print(f"结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
    # 测试不同nodeType
    print("\\n[MAPPING] 测试状态映射")
    test_mappings = [
        ('1', '登记'),
        ('2', '检查'),
        ('3', '总检'),
        ('4', '总审')
    ]
    
    for node_type, desc in test_mappings:
        status = interface.get_status_by_node_type(node_type)
        print(f"   nodeType {node_type} ({desc}) -> cstatus {status}")
    
    print("\\n[OK] 天健云13号接口测试完成")


if __name__ == "__main__":
    test_interface_13()