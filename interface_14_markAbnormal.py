#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云14号接口实现 - 重要异常标注接口(可选)
主检系统通过此接口，将发现的重要异常通知给体检系统
"""

import json
import hashlib
import requests
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from database_service import get_database_service
from config import Config
from api_config_manager import get_tianjian_base_url


class TianjianInterface14:
    """天健云14号接口 - 重要异常标注接口(可选)"""
    
    def __init__(self, api_config: Dict[str, Any]):
        """
        初始化接口配置
        
        Args:
            api_config: API配置信息
        """
        self.api_config = api_config
        self.db_service = get_database_service()
        self.endpoint = "/dx/inter/markAbnormal"  # 根据实际情况确定
    
    def generate_signature(self, api_key: str, timestamp: str) -> str:
        """
        生成API签名
        
        Args:
            api_key: API密钥
            timestamp: 时间戳
            
        Returns:
            MD5签名字符串
        """
        sign_string = f"{api_key}{timestamp}"
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    def mark_abnormal_service(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        重要异常标注接口 - GUI服务调用入口
        
        Args:
            request_data: 请求数据，包含abnormalList
            
        Returns:
            操作结果
        """
        try:
            # 验证请求数据
            if not request_data:
                return {
                    'code': -1,
                    'msg': '请求数据不能为空',
                    'data': None
                }
            
            abnormal_list = request_data.get('abnormalList', [])
            
            if not abnormal_list:
                return {
                    'code': -1,
                    'msg': '异常标注列表不能为空',
                    'data': None
                }
            
            # 验证每个异常标注项的必要字段
            for i, abnormal_item in enumerate(abnormal_list):
                required_fields = ['peNo', 'abnormalType', 'abnormalContent', 'deptCode', 'doctorCode']
                missing_fields = [field for field in required_fields if field not in abnormal_item or not abnormal_item[field]]
                
                if missing_fields:
                    return {
                        'code': -1,
                        'msg': f'第{i+1}项异常标注缺少必要字段: {", ".join(missing_fields)}',
                        'data': None
                    }
                
                # 验证异常类型
                abnormal_type = abnormal_item.get('abnormalType')
                valid_types = ['URGENT', 'SERIOUS', 'IMPORTANT', 'NORMAL']
                if abnormal_type not in valid_types:
                    return {
                        'code': -1,
                        'msg': f'第{i+1}项异常类型无效，应为: {", ".join(valid_types)}',
                        'data': None
                    }
            
            # 执行异常标注
            return self.mark_abnormal_batch(abnormal_list, test_mode=False)
            
        except Exception as e:
            return {
                'code': -1,
                'msg': f'重要异常标注操作失败: {str(e)}',
                'data': None
            }
    
    def mark_abnormal_batch(self, abnormal_list: List[Dict[str, Any]], 
                           test_mode: bool = False) -> Dict[str, Any]:
        """
        批量标注重要异常 - 适配GUI服务调用格式
        
        Args:
            abnormal_list: 异常标注列表，包含peNo, abnormalType, abnormalContent等
            test_mode: 测试模式标志
            
        Returns:
            批量标注结果
        """
        # 构建请求数据
        request_data = {
            "abnormalList": abnormal_list
        }
        
        print(f"[WARN] 批量标注重要异常，共 {len(abnormal_list)} 项")
        for i, abnormal in enumerate(abnormal_list, 1):
            print(f"   [{i}] 体检号: {abnormal.get('peNo')}, 类型: {abnormal.get('abnormalType')}")
        
        # 发送请求
        result = self.send_request(request_data, test_mode)
        
        if result['code'] == 0:
            print(f"[OK] 批量异常标注成功")
        else:
            print(f"[FAIL] 批量异常标注失败: {result['msg']}")
        
        return result
    
    def send_request(self, data: Dict[str, Any], test_mode: bool = False) -> Dict[str, Any]:
        """
        发送重要异常标注请求
        
        Args:
            data: 请求数据
            test_mode: 测试模式标志
            
        Returns:
            API响应结果
        """
        try:
            # 生成时间戳和签名
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            signature = self.generate_signature(self.api_config['api_key'], timestamp)
            
            # 构建请求头
            headers = {
                'Content-Type': 'application/json',
                'sign': signature,
                'timestamp': timestamp,
                'mic-code': self.api_config['mic_code'],
                'misc-id': self.api_config['misc_id']
            }
            
            # 构建请求URL
            url = f"{self.api_config['base_url']}{self.endpoint}"
            
            if test_mode:
                print(f"[WARN] 重要异常标注接口 - 测试模式")
                print(f"URL: {url}")
                print(f"Headers: {json.dumps(headers, ensure_ascii=False, indent=2)}")
                print(f"Request Data: {json.dumps(data, ensure_ascii=False, indent=2)}")
                return {
                    'code': 0,
                    'msg': '测试模式 - 重要异常标注接口调用成功',
                    'data': None
                }
            
            # 发送请求 - 尝试GET方法
            response = requests.get(
                url=url,
                headers=headers,
                params=data,
                timeout=self.api_config.get('timeout', 30)
            )
            
            # 处理响应
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    'code': -1,
                    'msg': f'HTTP请求失败，状态码: {response.status_code}',
                    'data': None
                }
                
        except Exception as e:
            return {
                'code': -1,
                'msg': f'重要异常标注失败: {str(e)}',
                'data': None
            }
    
    def mark_abnormal(self, pe_no: str, level: str, importance_code: str,
                     mark_doctor: Dict[str, str], mark_dept: Dict[str, str],
                     relate_item: List[str], relate_conclusion: List[str],
                     remark: str, is_notice_user: bool = False,
                     notice_remark: str = "", test_mode: bool = False) -> Dict[str, Any]:
        """
        标注重要异常
        
        Args:
            pe_no: 体检号
            level: 级别：1-A;2-B;3-C;4-D;9-其他
            importance_code: 重要异常编码
            mark_doctor: 报告医生信息 {"code": "医生编码", "name": "医生姓名"}
            mark_dept: 报告科室信息 {"code": "科室编码", "name": "科室名称"}
            relate_item: 关联申请项目
            relate_conclusion: 关联结论
            remark: 异常备注
            is_notice_user: 是否已经通知体检者
            notice_remark: 通知结果备注
            test_mode: 测试模式标志
            
        Returns:
            标注结果
        """
        # 构建请求数据
        request_data = {
            "pe_no": pe_no,
            "level": level,
            "importanceCode": importance_code,
            "mark_doctor": mark_doctor,
            "mark_dept": mark_dept,
            "relateItem": relate_item,
            "relateConclusion": relate_conclusion,
            "remark": remark,
            "isNoticeUser": is_notice_user,
            "noticeRemark": notice_remark
        }
        
        # 级别描述
        level_descriptions = {
            "1": "A级",
            "2": "B级", 
            "3": "C级",
            "4": "D级",
            "9": "其他"
        }
        
        print(f"[WARN] 标注重要异常")
        print(f"   体检号: {pe_no}")
        print(f"   异常级别: {level} ({level_descriptions.get(level, '未知')})")
        print(f"   异常编码: {importance_code}")
        print(f"   报告医生: {mark_doctor.get('name', '')} ({mark_doctor.get('code', '')})")
        print(f"   报告科室: {mark_dept.get('name', '')} ({mark_dept.get('code', '')})")
        print(f"   关联项目: {', '.join(relate_item)}")
        print(f"   是否已通知: {'是' if is_notice_user else '否'}")
        
        # 发送请求
        result = self.send_request(request_data, test_mode)
        
        if result['code'] == 0:
            print(f"[OK] 重要异常标注成功")
        else:
            print(f"[FAIL] 重要异常标注失败: {result['msg']}")
        
        return result
    
    def batch_mark_abnormal(self, abnormal_list: List[Dict[str, Any]], 
                           test_mode: bool = False) -> Dict[str, Any]:
        """
        批量标注重要异常
        
        Args:
            abnormal_list: 异常标注列表
            test_mode: 测试模式标志
            
        Returns:
            批量标注结果
        """
        total_count = len(abnormal_list)
        success_count = 0
        failed_count = 0
        errors = []
        
        print(f"[WARN] 开始批量标注重要异常，共 {total_count} 条记录")
        
        for i, abnormal_data in enumerate(abnormal_list, 1):
            try:
                result = self.mark_abnormal(
                    pe_no=abnormal_data['pe_no'],
                    level=abnormal_data['level'],
                    importance_code=abnormal_data['importance_code'],
                    mark_doctor=abnormal_data['mark_doctor'],
                    mark_dept=abnormal_data['mark_dept'],
                    relate_item=abnormal_data['relate_item'],
                    relate_conclusion=abnormal_data['relate_conclusion'],
                    remark=abnormal_data['remark'],
                    is_notice_user=abnormal_data.get('is_notice_user', False),
                    notice_remark=abnormal_data.get('notice_remark', ''),
                    test_mode=test_mode
                )
                
                if result['code'] == 0:
                    success_count += 1
                    print(f"[OK] [{i}/{total_count}] 体检号 {abnormal_data['pe_no']} 异常标注成功")
                else:
                    failed_count += 1
                    error_msg = f"体检号 {abnormal_data['pe_no']} 标注失败: {result['msg']}"
                    errors.append(error_msg)
                    print(f"[FAIL] [{i}/{total_count}] {error_msg}")
                    
            except Exception as e:
                failed_count += 1
                error_msg = f"体检号 {abnormal_data.get('pe_no', 'Unknown')} 处理异常: {str(e)}"
                errors.append(error_msg)
                print(f"[FAIL] [{i}/{total_count}] {error_msg}")
        
        return {
            'total': total_count,
            'success': success_count,
            'failed': failed_count,
            'errors': errors,
            'success_rate': f"{(success_count/total_count*100):.1f}%" if total_count > 0 else "0%"
        }
    
    def get_abnormal_data_from_db(self, pe_nos: List[str] = None, limit: int = None) -> List[Dict[str, Any]]:
        """
        从数据库获取异常数据信息（示例查询）
        
        Args:
            pe_nos: 体检号列表
            limit: 限制返回条数
            
        Returns:
            异常数据信息列表
        """
        if not self.db_service.connect():
            raise Exception("数据库连接失败")
        
        try:
            # 示例查询，实际应根据业务需求调整
            sql = """
            SELECT 
                trm.cRegNo as pe_no,
                trm.cName as patient_name,
                trm.cIDNo as id_card,
                trm.cMemo as memo,
                cod.cOperCode as doctor_code,
                cod.cOperName as doctor_name,
                'UNKNOWN' as dept_code,
                '未知科室' as dept_name
            FROM T_Register_Main trm
            LEFT JOIN Code_Operator_dict cod ON trm.cOperCode = cod.cOperCode
            WHERE 1=1
            """
            
            params = []
            
            if pe_nos:
                placeholders = ','.join(['?' for _ in pe_nos])
                sql += f" AND trm.cRegNo IN ({placeholders})"
                params.extend(pe_nos)
            
            # 筛选可能有异常的记录（这里只是示例条件）
            sql += " AND trm.cMemo IS NOT NULL AND trm.cMemo <> ''"
            sql += " ORDER BY trm.cRegDate DESC"
            
            if limit:
                sql = sql.replace("SELECT ", f"SELECT TOP {limit} ")
            
            result = self.db_service.execute_query(sql, tuple(params) if params else None)
            return result
            
        finally:
            self.db_service.disconnect()


def test_interface_14():
    """测试14号接口"""
    print("[TEST] 测试天健云14号接口 - 重要异常标注接口")
    print("=" * 50)
    
    # API配置
    api_config = {
        'base_url': get_tianjian_base_url(),
        'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
        'mic_code': 'MIC1.001E',
        'misc_id': 'MISC1.00001A',
        'timeout': 30
    }
    
    # 创建接口实例
    interface = TianjianInterface14(api_config)
    
    # 测试场景1：单个异常标注
    print("\n[WARN] 测试场景1：单个重要异常标注")
    result1 = interface.mark_abnormal(
        pe_no="5000003",  # 测试卡号
        level="1",  # A级
        importance_code="ABN001",
        mark_doctor={"code": "09DOCTOR001", "name": "张医生"},  # 门店编号09
        mark_dept={"code": "09DEPT001", "name": "内科"},
        relate_item=["ITEM001", "ITEM002"],
        relate_conclusion=["血压偏高", "心率异常"],
        remark="建议进一步检查",
        is_notice_user=True,
        notice_remark="已电话通知患者",
        test_mode=True
    )
    print(f"结果: {json.dumps(result1, ensure_ascii=False, indent=2)}")
    
    # 测试场景2：批量异常标注
    print("\n[WARN] 测试场景2：批量重要异常标注")
    abnormal_list = [
        {
            "pe_no": "5000003",  # 测试卡号
            "level": "2",  # B级
            "importance_code": "ABN002",
            "mark_doctor": {"code": "09DOCTOR001", "name": "张医生"},  # 门店编号09
            "mark_dept": {"code": "09DEPT001", "name": "内科"},
            "relate_item": ["ITEM003"],
            "relate_conclusion": ["血糖异常"],
            "remark": "需要控制饮食",
            "is_notice_user": False,
            "notice_remark": ""
        },
        {
            "pe_no": "5000006",  # 测试卡号
            "level": "3",  # C级
            "importance_code": "ABN003",
            "mark_doctor": {"code": "09DOCTOR002", "name": "李医生"},  # 门店编号09
            "mark_dept": {"code": "09DEPT002", "name": "外科"},
            "relate_item": ["ITEM004"],
            "relate_conclusion": ["肝功能轻度异常"],
            "remark": "建议复查",
            "is_notice_user": True,
            "notice_remark": "短信通知已发送"
        }
    ]
    
    result2 = interface.batch_mark_abnormal(abnormal_list, test_mode=True)
    print(f"批量标注结果: {json.dumps(result2, ensure_ascii=False, indent=2)}")
    
    print("\n[OK] 天健云14号接口测试完成")


if __name__ == "__main__":
    test_interface_14()