#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云16号接口实现 - 查询图片接口
根据机构编码获取数据库连接，查询ExamDB_Pacs库的t_check_result_main_pic表获取图片
"""

import json
import base64
from datetime import datetime
from typing import Dict, Any, List, Optional
from multi_org_config import get_org_config_by_shop_code
from database_service import DatabaseService


class TianjianInterface16:
    """天健云16号接口 - 查询图片接口"""
    
    def __init__(self, api_config: Dict[str, Any]):
        """
        初始化接口配置
        
        Args:
            api_config: API配置信息
        """
        self.api_config = api_config
    
    def get_pacs_db_service_by_shop_code(self, shop_code: str) -> DatabaseService:
        """根据门店编码获取对应的PACS数据库服务"""
        org_config = get_org_config_by_shop_code(shop_code)
        if not org_config:
            raise Exception(f'Shop code {shop_code} not found in configuration')
        
        # PACS数据库连接字符串 - 连接到ExamDB_Pacs库
        connection_string = (
            f"DRIVER={{{org_config['db_driver']}}};"
            f"SERVER={org_config['db_host']},{org_config['db_port']};"
            f"DATABASE=ExamDB_Pacs;"  # 固定连接到PACS库
            f"UID={org_config['db_user']};"
            f"PWD={org_config['db_password']};"
            f"TrustServerCertificate=yes;"
        )
        
        print(f'Using PACS database for shop {shop_code}: {org_config["db_host"]}:{org_config["db_port"]}/ExamDB_Pacs')
        return DatabaseService(connection_string)
    
    def get_main_db_service_by_shop_code(self, shop_code: str) -> DatabaseService:
        """根据门店编码获取对应的主数据库服务"""
        org_config = get_org_config_by_shop_code(shop_code)
        if not org_config:
            raise Exception(f'Shop code {shop_code} not found in configuration')
        
        connection_string = (
            f"DRIVER={{{org_config['db_driver']}}};"
            f"SERVER={org_config['db_host']},{org_config['db_port']};"
            f"DATABASE={org_config['db_name']};"
            f"UID={org_config['db_user']};"
            f"PWD={org_config['db_password']};"
            f"TrustServerCertificate=yes;"
        )
        
        return DatabaseService(connection_string)
    
    def get_client_code_by_card(self, db_service: DatabaseService, card_no: str) -> str:
        """通过卡号获取客户编码"""
        try:
            sql = 'SELECT cClientCode FROM T_Register_Main WHERE cCardNo = ?'
            result = db_service.execute_query(sql, (card_no,))
            if result and len(result) > 0:
                client_code = result[0]['cClientCode']
                print(f'   [INFO] Card {card_no} -> ClientCode: {client_code}')
                return client_code
            else:
                print(f'   [WARN] Card {card_no} not found in T_Register_Main')
                return None
        except Exception as e:
            print(f'   [ERROR] Query client code error: {str(e)}')
            return None
    
    def get_images(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        查询图片接口 - GUI服务调用入口（别名）
        """
        return self.query_images_service(request_data)
    
    def query_images_service(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        查询图片接口 - GUI服务调用入口
        
        Args:
            request_data: 请求数据，包含peNo、deptId、applyItemId、cshopcode
            
        Returns:
            图片查询结果
        """
        try:
            # 验证请求数据
            if not request_data:
                return {
                    'code': -1,
                    'msg': '请求数据不能为空',
                    'data': []
                }
            
            pe_no = request_data.get('peNo', '')
            dept_id = request_data.get('deptId', '')
            apply_item_ids = request_data.get('applyItemId', [])
            # 支持多种字段名：hospitalCode（新标准）、cshopcode（标准）和shopcode（兼容）
            shop_code = request_data.get('hospitalCode') or request_data.get('cshopcode') or request_data.get('shopcode', '08')  # 机构编码，默认08
            
            # 验证必要字段
            if not pe_no:
                return {
                    'code': -1,
                    'msg': 'peNo不能为空',
                    'data': []
                }
            
            print(f"[QUERY] 查询图片")
            print(f"   体检号: {pe_no}")
            print(f"   科室编号: {dept_id}")
            print(f"   申请项目: {apply_item_ids}")
            print(f"   机构编码: {shop_code}")
            
            # 执行图片查询
            return self.query_images_from_pacs(pe_no, dept_id, apply_item_ids, shop_code)
            
        except Exception as e:
            return {
                'code': -1,
                'msg': f'查询图片失败: {str(e)}',
                'data': []
            }
    
    def query_images_from_pacs(self, pe_no: str, dept_id: str = '', 
                              apply_item_ids: List[str] = None, 
                              shop_code: str = '09') -> Dict[str, Any]:
        """
        从PACS数据库查询图片数据
        
        Args:
            pe_no: 体检号（卡号）
            dept_id: 科室编号
            apply_item_ids: 申请项目ID列表
            shop_code: 机构编码
            
        Returns:
            图片查询结果
        """
        try:
            # 首先获取主数据库连接，将卡号转换为客户编码
            main_db_service = self.get_main_db_service_by_shop_code(shop_code)
            
            if not main_db_service.connect():
                return {
                    'code': -1,
                    'msg': f'机构 {shop_code} 主数据库连接失败',
                    'data': []
                }
            
            try:
                # 通过卡号获取客户编码
                client_code = self.get_client_code_by_card(main_db_service, pe_no)
                if not client_code:
                    return {
                        'code': -1,
                        'msg': f'卡号 {pe_no} 对应的客户编码不存在',
                        'data': []
                    }
            finally:
                main_db_service.disconnect()
            
            # 获取PACS数据库连接
            pacs_db_service = self.get_pacs_db_service_by_shop_code(shop_code)
            
            if not pacs_db_service.connect():
                return {
                    'code': -1,
                    'msg': f'机构 {shop_code} PACS数据库连接失败',
                    'data': []
                }
            
            try:
                # 构建查询SQL
                sql = '''
                SELECT 
                    cClientCode,
                    cMainCode,
                    cpicName,
                    pImage,
                    cPosition,
                    ccardno
                FROM t_check_result_main_pic
                WHERE cClientCode = ?
                '''
                
                params = [client_code]
                
                # 如果指定了科室，添加科室过滤条件
                if dept_id:
                    sql += ' AND cMainCode = ?'
                    params.append(dept_id)
                
                # 如果指定了申请项目ID，添加过滤条件
                # 注意：这里假设cMainCode对应申请项目ID，实际可能需要调整
                if apply_item_ids:
                    placeholders = ','.join(['?' for _ in apply_item_ids])
                    sql += f' AND cMainCode IN ({placeholders})'
                    params.extend(apply_item_ids)
                
                sql += ' ORDER BY iIndex'
                
                print(f"   [SQL] {sql}")
                print(f"   [PARAMS] {params}")
                
                # 执行查询
                result = pacs_db_service.execute_query(sql, tuple(params))
                
                if not result:
                    return {
                        'code': 0,
                        'msg': '未找到相关图片',
                        'data': []
                    }
                
                # 处理查询结果，转换图片为base64
                images_data = []
                for row in result:
                    try:
                        image_info = {
                            'fileName': row['cpicName'],
                            'fileUri': f"/pacs/images/{row['cClientCode']}/{row['cpicName']}",  # 文件绝对路径
                            'applyItemId': row['cMainCode'],  # 文件归属项目id
                            'deptId': row['cMainCode'],       # 文件归属科室
                            'base64Url': ''
                        }
                        
                        # 将image字段转换为base64
                        if row['pImage']:
                            try:
                                # 处理SQL Server的image类型数据
                                image_bytes = bytes(row['pImage'])
                                base64_string = base64.b64encode(image_bytes).decode('utf-8')
                                image_info['base64Url'] = f"data:image/jpeg;base64,{base64_string}"
                                print(f"   [OK] 图片转换成功: {row['cpicName']} ({len(image_bytes)} bytes)")
                            except Exception as e:
                                print(f"   [WARN] 图片转换失败: {row['cpicName']} - {str(e)}")
                                image_info['base64Url'] = ''
                        
                        images_data.append(image_info)
                        
                    except Exception as e:
                        print(f"   [ERROR] 处理图片记录失败: {str(e)}")
                        continue
                
                print(f"   [INFO] 成功查询到 {len(images_data)} 张图片")
                
                return {
                    'code': 0,
                    'msg': '查询成功',
                    'data': images_data
                }
                
            finally:
                pacs_db_service.disconnect()
                
        except Exception as e:
            return {
                'code': -1,
                'msg': f'PACS图片查询异常: {str(e)}',
                'data': []
            }


def test_interface_16():
    """测试16号接口 - 查询图片"""
    print("[TEST] 测试天健云16号接口 - 查询图片")
    print("=" * 60)
    
    # API配置
    api_config = {
        'base_url': 'http://203.83.237.114:9300',
        'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
        'mic_code': 'MIC1.001E',
        'misc_id': 'MISC1.00001A',
        'timeout': 30
    }
    
    # 创建接口实例
    interface = TianjianInterface16(api_config)
    
    # 测试数据1：查询指定体检号的所有图片
    print("\\n[QUERY1] 测试查询所有图片")
    request_data1 = {
        'peNo': '5000003',  # 卡号
        'deptId': '',       # 空表示所有科室
        'applyItemId': [],  # 空表示所有项目
        'cshopcode': '09'   # 机构编码
    }
    
    print(f"请求数据: {json.dumps(request_data1, ensure_ascii=False, indent=2)}")
    result1 = interface.query_images_service(request_data1)
    print(f"结果: {json.dumps(result1, ensure_ascii=False, indent=2)}")
    
    # 测试数据2：查询指定科室的图片
    print("\\n[QUERY2] 测试查询指定科室图片")
    request_data2 = {
        'peNo': '5000003',     # 卡号
        'deptId': 'XRAY001',   # 指定科室
        'applyItemId': [],     # 空表示该科室所有项目
        'cshopcode': '09'      # 机构编码
    }
    
    print(f"请求数据: {json.dumps(request_data2, ensure_ascii=False, indent=2)}")
    result2 = interface.query_images_service(request_data2)
    print(f"结果: {json.dumps(result2, ensure_ascii=False, indent=2)}")
    
    # 测试数据3：查询指定项目的图片
    print("\\n[QUERY3] 测试查询指定项目图片")
    request_data3 = {
        'peNo': '5000003',        # 卡号
        'deptId': '',             # 空表示所有科室
        'applyItemId': ['ITEM001', 'ITEM002'],  # 指定项目
        'cshopcode': '09'         # 机构编码
    }
    
    print(f"请求数据: {json.dumps(request_data3, ensure_ascii=False, indent=2)}")
    result3 = interface.query_images_service(request_data3)
    print(f"结果: {json.dumps(result3, ensure_ascii=False, indent=2)}")
    
    print("\\n[OK] 天健云16号接口测试完成")


if __name__ == "__main__":
    test_interface_16()