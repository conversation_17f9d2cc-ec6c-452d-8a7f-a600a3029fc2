#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云17号接口实现 - 重要异常删除接口(可选)
删除已标注的重要异常信息
"""

import json
import hashlib
import requests
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from database_service import get_database_service
from config import Config
from api_config_manager import get_tianjian_base_url


class TianjianInterface17:
    """天健云17号接口 - 重要异常删除接口(可选)"""
    
    def __init__(self, api_config: Dict[str, Any]):
        """
        初始化接口配置
        
        Args:
            api_config: API配置信息
        """
        self.api_config = api_config
        self.db_service = get_database_service()
        self.endpoint = "/dx/inter/deleteAbnormal"  # 根据实际情况确定
    
    def generate_signature(self, api_key: str, timestamp: str) -> str:
        """
        生成API签名
        
        Args:
            api_key: API密钥
            timestamp: 时间戳
            
        Returns:
            MD5签名字符串
        """
        sign_string = f"{api_key}{timestamp}"
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    def send_request(self, data: Dict[str, Any], test_mode: bool = False) -> Dict[str, Any]:
        """
        发送重要异常删除请求
        
        Args:
            data: 请求数据
            test_mode: 测试模式标志
            
        Returns:
            API响应结果
        """
        try:
            # 生成时间戳和签名
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            signature = self.generate_signature(self.api_config['api_key'], timestamp)
            
            # 构建请求头
            headers = {
                'Content-Type': 'application/json',
                'sign': signature,
                'timestamp': timestamp,
                'mic-code': self.api_config['mic_code'],
                'misc-id': self.api_config['misc_id']
            }
            
            # 构建请求URL
            url = f"{self.api_config['base_url']}{self.endpoint}"
            
            if test_mode:
                print(f"重要异常删除接口 - 测试模式")
                print(f"URL: {url}")
                print(f"Headers: {json.dumps(headers, ensure_ascii=False, indent=2)}")
                print(f"Request Data: {json.dumps(data, ensure_ascii=False, indent=2)}")
                return {
                    'code': 0,
                    'msg': '测试模式 - 重要异常删除接口调用成功',
                    'data': None
                }
            
            # 发送请求
            response = requests.post(
                url=url,
                headers=headers,
                json=data,
                timeout=self.api_config.get('timeout', 30)
            )
            
            # 处理响应
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    'code': -1,
                    'msg': f'HTTP请求失败，状态码: {response.status_code}',
                    'data': None
                }
                
        except Exception as e:
            return {
                'code': -1,
                'msg': f'重要异常删除失败: {str(e)}',
                'data': None
            }
    
    def delete_abnormal(self, pe_no: str, importance_code: str, 
                       test_mode: bool = False) -> Dict[str, Any]:
        """
        删除重要异常
        
        Args:
            pe_no: 体检号
            importance_code: 重要异常编码
            test_mode: 测试模式标志
            
        Returns:
            删除结果
        """
        # 构建请求数据
        request_data = {
            "pe_no": pe_no,
            "importanceCode": importance_code
        }
        
        print(f"删除重要异常")
        print(f"   体检号: {pe_no}")
        print(f"   异常编码: {importance_code}")
        
        # 发送请求
        result = self.send_request(request_data, test_mode)
        
        if result['code'] == 0:
            print(f"[OK] 重要异常删除成功")
        else:
            print(f"[FAIL] 重要异常删除失败: {result['msg']}")
        
        return result
    
    def batch_delete_abnormal(self, delete_list: List[Dict[str, str]], 
                             test_mode: bool = False) -> Dict[str, Any]:
        """
        批量删除重要异常
        
        Args:
            delete_list: 删除信息列表，每项包含 pe_no 和 importance_code
            test_mode: 测试模式标志
            
        Returns:
            批量删除结果
        """
        total_count = len(delete_list)
        success_count = 0
        failed_count = 0
        errors = []
        
        print(f"开始批量删除重要异常，共 {total_count} 条记录")
        
        for i, delete_data in enumerate(delete_list, 1):
            try:
                result = self.delete_abnormal(
                    pe_no=delete_data['pe_no'],
                    importance_code=delete_data['importance_code'],
                    test_mode=test_mode
                )
                
                if result['code'] == 0:
                    success_count += 1
                    print(f"[OK] [{i}/{total_count}] 体检号 {delete_data['pe_no']} 异常删除成功")
                else:
                    failed_count += 1
                    error_msg = f"体检号 {delete_data['pe_no']} 删除失败: {result['msg']}"
                    errors.append(error_msg)
                    print(f"[FAIL] [{i}/{total_count}] {error_msg}")
                    
            except Exception as e:
                failed_count += 1
                error_msg = f"体检号 {delete_data.get('pe_no', 'Unknown')} 处理异常: {str(e)}"
                errors.append(error_msg)
                print(f"[FAIL] [{i}/{total_count}] {error_msg}")
        
        return {
            'total': total_count,
            'success': success_count,
            'failed': failed_count,
            'errors': errors,
            'success_rate': f"{(success_count/total_count*100):.1f}%" if total_count > 0 else "0%"
        }
    
    def get_abnormal_records_from_db(self, pe_nos: List[str] = None, 
                                    importance_codes: List[str] = None,
                                    limit: int = None) -> List[Dict[str, Any]]:
        """
        从数据库获取异常记录信息（示例查询）
        
        Args:
            pe_nos: 体检号列表
            importance_codes: 异常编码列表
            limit: 限制返回条数
            
        Returns:
            异常记录信息列表
        """
        if not self.db_service.connect():
            raise Exception("数据库连接失败")
        
        try:
            # 示例查询，实际应根据异常记录表结构调整
            sql = """
            SELECT 
                trm.cRegNo as pe_no,
                trm.cName as patient_name,
                trm.cIDNo as id_card,
                'ABN' + RIGHT('000' + CAST(ROW_NUMBER() OVER (ORDER BY trm.cRegNo) AS VARCHAR), 3) as importance_code,
                trm.cMemo as remark,
                trm.cOperCode as operator_code,
                cod.cOperName as operator_name,
                trm.cRegDate as create_time
            FROM T_Register_Main trm
            LEFT JOIN Code_Operator_dict cod ON trm.cOperCode = cod.cOperCode
            WHERE 1=1
            """
            
            params = []
            
            if pe_nos:
                placeholders = ','.join(['?' for _ in pe_nos])
                sql += f" AND trm.cRegNo IN ({placeholders})"
                params.extend(pe_nos)
            
            # 筛选可能有异常记录的数据（这里只是示例条件）
            sql += " AND trm.cMemo IS NOT NULL AND trm.cMemo <> ''"
            sql += " ORDER BY trm.cRegDate DESC"
            
            if limit:
                sql = sql.replace("SELECT ", f"SELECT TOP {limit} ")
            
            result = self.db_service.execute_query(sql, tuple(params) if params else None)
            return result
            
        finally:
            self.db_service.disconnect()
    
    def list_abnormal_records(self, pe_no: str = "", limit: int = 10) -> List[Dict[str, Any]]:
        """
        列出异常记录，用于确认删除操作
        
        Args:
            pe_no: 体检号（可选）
            limit: 限制返回条数
            
        Returns:
            异常记录列表
        """
        try:
            pe_nos = [pe_no] if pe_no else None
            records = self.get_abnormal_records_from_db(pe_nos=pe_nos, limit=limit)
            
            print(f"[LIST] 异常记录列表 (前{len(records)}条):")
            for i, record in enumerate(records, 1):
                print(f"   {i}. 体检号: {record['pe_no']}, "
                      f"患者: {record['patient_name']}, "
                      f"异常编码: {record['importance_code']}, "
                      f"备注: {record.get('remark', '')[:30]}...")
            
            return records
            
        except Exception as e:
            print(f"[FAIL] 获取异常记录失败: {str(e)}")
            return []


def test_interface_17():
    """测试17号接口"""
    print("[TEST] 测试天健云17号接口 - 重要异常删除接口")
    print("=" * 50)
    
    # API配置
    api_config = {
        'base_url': get_tianjian_base_url(),
        'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
        'mic_code': 'MIC1.001E',
        'misc_id': 'MISC1.00001A',
        'timeout': 30
    }
    
    # 创建接口实例
    interface = TianjianInterface17(api_config)
    
    # 测试场景1：单个异常删除
    print("\n测试场景1：单个重要异常删除")
    result1 = interface.delete_abnormal(
        pe_no="PE202501010001",
        importance_code="ABN001",
        test_mode=True
    )
    print(f"结果: {json.dumps(result1, ensure_ascii=False, indent=2)}")
    
    # 测试场景2：批量异常删除
    print("\n测试场景2：批量重要异常删除")
    delete_list = [
        {
            "pe_no": "PE202501010001",
            "importance_code": "ABN001"
        },
        {
            "pe_no": "PE202501010001",
            "importance_code": "ABN002"
        },
        {
            "pe_no": "PE202501010002",
            "importance_code": "ABN003"
        }
    ]
    
    result2 = interface.batch_delete_abnormal(delete_list, test_mode=True)
    print(f"批量删除结果: {json.dumps(result2, ensure_ascii=False, indent=2)}")
    
    # 测试场景3：列出异常记录
    print("\n[LIST] 测试场景3：列出异常记录")
    try:
        records = interface.list_abnormal_records(limit=5)
        print(f"找到 {len(records)} 条异常记录")
    except Exception as e:
        print(f"列出异常记录时出错: {str(e)}")
    
    print("\n[OK] 天健云17号接口测试完成")


if __name__ == "__main__":
    test_interface_17()