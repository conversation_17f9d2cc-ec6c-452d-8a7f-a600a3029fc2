#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云18号接口实现 - 查询医生信息接口
查询医生基本信息
"""

import json
import hashlib
import requests
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from database_service import get_database_service, DatabaseService
from multi_org_config import get_org_config_by_shop_code
from config import Config


class TianjianInterface18:
    """天健云18号接口 - 查询医生信息接口"""
    
    def __init__(self, api_config: Dict[str, Any]):
        """
        初始化接口配置
        
        Args:
            api_config: API配置信息
        """
        self.api_config = api_config
        self.db_service = None  # 延迟初始化
        self.endpoint = "/dx/inter/getDoctorInfo"  # 18号接口正确端点

    def _get_database_service_by_hospital_code(self, hospital_code: str = None) -> DatabaseService:
        """
        根据hospitalCode获取对应的数据库服务
        
        Args:
            hospital_code: 医院编码
            
        Returns:
            DatabaseService实例
        """
        if hospital_code:
            # 根据hospitalCode获取对应的机构配置
            org_config = get_org_config_by_shop_code(hospital_code)
            if org_config:
                # 构建连接字符串
                connection_string = (
                    f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                    f"SERVER={org_config['db_host']},{org_config['db_port']};"
                    f"DATABASE={org_config['db_name']};"
                    f"UID={org_config['db_user']};"
                    f"PWD={org_config['db_password']};"
                    f"TrustServerCertificate=yes;"
                )
                return DatabaseService(connection_string)
        
        # 如果没有指定hospitalCode或找不到配置，使用默认数据库服务
        if self.db_service is None:
            self.db_service = get_database_service()
        return self.db_service
    
    def generate_signature(self, api_key: str, timestamp: str) -> str:
        """
        生成API签名
        
        Args:
            api_key: API密钥
            timestamp: 时间戳
            
        Returns:
            MD5签名字符串
        """
        sign_string = f"{api_key}{timestamp}"
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    def send_request(self, data: Dict[str, Any], test_mode: bool = False) -> Dict[str, Any]:
        """
        发送查询医生信息请求
        
        Args:
            data: 请求数据
            test_mode: 测试模式标志
            
        Returns:
            API响应结果
        """
        try:
            # 生成时间戳和签名
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            signature = self.generate_signature(self.api_config['api_key'], timestamp)
            
            # 构建请求头
            headers = {
                'Content-Type': 'application/json',
                'sign': signature,
                'timestamp': timestamp,
                'mic-code': self.api_config['mic_code'],
                'misc-id': self.api_config['misc_id']
            }
            
            # 构建请求URL
            url = f"{self.api_config['base_url']}{self.endpoint}"
            
            if test_mode:
                print(f"[DOCTOR] 查询医生信息接口 - 测试模式")
                print(f"URL: {url}")
                print(f"Headers: {json.dumps(headers, ensure_ascii=False, indent=2)}")
                print(f"Request Data: {json.dumps(data, ensure_ascii=False, indent=2)}")
                
                # 模拟返回医生数据
                mock_data = [
                    {
                        "name": "张医生",
                        "icCode": "110101198001011234",
                        "phoneNo": "***********",
                        "sex": {"code": "1", "name": "男性"},
                        "accountCode": "DOCTOR001",
                        "accountId": "DOC001"
                    },
                    {
                        "name": "李医生",
                        "icCode": "110101198501011234",
                        "phoneNo": "***********",
                        "sex": {"code": "2", "name": "女性"},
                        "accountCode": "DOCTOR002",
                        "accountId": "DOC002"
                    }
                ]
                
                return {
                    'code': 0,
                    'msg': '测试模式 - 查询医生信息接口调用成功',
                    'data': mock_data
                }
            
            # 发送请求（使用GET方法）
            response = requests.get(
                url=url,
                headers=headers,
                params=data,  # GET请求使用params而不是json
                timeout=self.api_config.get('timeout', 30)
            )
            
            # 处理响应
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    'code': -1,
                    'msg': f'HTTP请求失败，状态码: {response.status_code}',
                    'data': None
                }
                
        except Exception as e:
            return {
                'code': -1,
                'msg': f'查询医生信息失败: {str(e)}',
                'data': None
            }
    
    def query_doctor_info(self, doctor_id: str = "", hospital_code: str = "",
                         test_mode: bool = False) -> Dict[str, Any]:
        """
        查询医生信息
        
        Args:
            doctor_id: 医生主键，如果不传，则查询所有的数据
            hospital_code: 医院编码，适用于多院区的情况
            test_mode: 测试模式标志
            
        Returns:
            查询结果
        """
        print(f"[DOCTOR] 查询医生信息")
        print(f"   医生ID: {doctor_id or '全部'}")
        print(f"   医院编码: {hospital_code or '默认'}")
        
        try:
            # 18号接口应该从数据库查询医生信息，而不是调用外部API
            if test_mode:
                # 测试模式返回模拟数据
                mock_data = [
                    {
                        "name": "张医生",
                        "icCode": "110101198001011234",
                        "phoneNo": "***********",
                        "sex": {"code": "1", "name": "男性"},
                        "accountCode": "DOCTOR001",
                        "accountId": "DOC001"
                    },
                    {
                        "name": "李医生",
                        "icCode": "110101198501011234",
                        "phoneNo": "***********",
                        "sex": {"code": "2", "name": "女性"},
                        "accountCode": "DOCTOR002",
                        "accountId": "DOC002"
                    }
                ]
                
                print(f"[OK] 查询成功（测试模式）")
                print(f"[DATA] 返回 {len(mock_data)} 条医生信息")
                for i, doctor in enumerate(mock_data, 1):
                    print(f"   医生{i}: {doctor.get('name', '')} ({doctor.get('accountCode', '')})")
                
                return {
                    'code': 0,
                    'msg': '测试模式 - 查询医生信息接口调用成功',
                    'data': mock_data
                }
            else:
                # 从数据库查询实际医生信息
                doctors = self.get_doctor_info_from_db(
                    doctor_codes=[doctor_id] if doctor_id else None,
                    limit=None,  # 不限制返回数量
                    hospital_code=hospital_code  # 传递医院编码
                )
                
                print(f"[OK] 查询成功")
                if doctors and len(doctors) > 0:
                    print(f"[DATA] 返回 {len(doctors)} 条医生信息")
                    for i, doctor in enumerate(doctors[:3], 1):  # 只显示前3条
                        print(f"   医生{i}: {doctor.get('name', '')} ({doctor.get('accountCode', '')})")
                    if len(doctors) > 3:
                        print(f"   ... 共 {len(doctors)} 条记录")
                else:
                    print(f"[DATA] 没有找到医生信息数据")
                
                return {
                    'code': 0,
                    'msg': '查询医生信息成功',
                    'data': doctors or []
                }
                
        except Exception as e:
            print(f"[FAIL] 查询失败: {str(e)}")
            return {
                'code': -1,
                'msg': f'查询失败: {str(e)}',
                'data': []
            }

    def get_doctor_info(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        查询医生信息（路由接口方法）

        Args:
            data: 请求数据，包含id和hospitalCode字段

        Returns:
            查询结果
        """
        try:
            # 从请求数据中提取参数
            doctor_id = data.get('id', '')
            # 支持多种字段名：hospitalCode（新标准）、shopcode（兼容）、cshopcode（兼容）
            hospital_code = data.get('hospitalCode') or data.get('shopcode') or data.get('cshopcode', '')

            # 调用查询方法
            return self.query_doctor_info(doctor_id, hospital_code, test_mode=False)

        except Exception as e:
            return {
                'code': -1,
                'msg': f'查询失败: {str(e)}',
                'data': []
            }

    def get_doctor_info_from_db(self, doctor_codes: List[str] = None,
                               name_keyword: str = None, 
                               limit: int = None,
                               hospital_code: str = None) -> List[Dict[str, Any]]:
        """
        从数据库获取医生信息
        
        Args:
            doctor_codes: 医生编码列表
            name_keyword: 姓名关键字
            limit: 限制返回条数
            hospital_code: 医院编码
            
        Returns:
            医生信息列表
        """
        # 根据hospital_code获取对应的数据库服务
        db_service = self._get_database_service_by_hospital_code(hospital_code)

        if not db_service.connect():
            raise Exception("数据库连接失败")
        
        try:
            sql = """
            SELECT 
                cod.cCode as accountCode,
                cod.cName as name,
                cod.cCode as accountId,
                cod.cCardNo as icCode,
                cod.cMobil as phoneNo,
                '3' as sexCode,
                '未知' as sexName,
                cod.cDepartmentCode as deptCode,
                dd.cName as deptName,
                cod.cMemo as memo,
                GETDATE() as createTime
            FROM Code_Operator_dict cod
            LEFT JOIN Code_Dept_dict dd ON cod.cDepartmentCode = dd.cCode
            WHERE cod.cStopTag = '0' AND cod.cDoctorTag = '1'
            """
            
            params = []
            
            # 添加医生编码条件
            if doctor_codes:
                placeholders = ','.join(['?' for _ in doctor_codes])
                sql += f" AND cod.cCode IN ({placeholders})"
                params.extend(doctor_codes)
            
            # 添加姓名关键字条件
            if name_keyword:
                sql += " AND cod.cName LIKE ?"
                params.append(f'%{name_keyword}%')
            
            sql += " ORDER BY cod.cCode"
            
            # 不再使用TOP限制，返回所有符合条件的记录
            # if limit:
            #     sql = sql.replace("SELECT ", f"SELECT TOP {limit} ")
            
            result = db_service.execute_query(sql, tuple(params) if params else None)
            
            # 格式化数据结构
            formatted_result = []
            for row in result:
                doctor_info = {
                    "name": row['name'],
                    "icCode": row.get('icCode', ''),
                    "phoneNo": row.get('phoneNo', ''),
                    "sex": {
                        "code": row.get('sexCode', '3'),
                        "name": row.get('sexName', '未知')
                    },
                    "accountCode": row['accountCode'],
                    "accountId": row['accountId'],
                    "dept": {
                        "code": row.get('deptCode', ''),
                        "name": row.get('deptName', '')
                    },
                    "memo": row.get('memo', ''),
                    "createTime": row.get('createTime', '')
                }
                formatted_result.append(doctor_info)
            
            return formatted_result
            
        finally:
            db_service.disconnect()
    
    def sync_doctor_info(self, doctor_codes: List[str] = None, 
                        name_keyword: str = None, limit: int = None,
                        test_mode: bool = False, hospital_code: str = None) -> Dict[str, Any]:
        """
        同步医生信息到天健云
        
        Args:
            doctor_codes: 医生编码列表
            name_keyword: 姓名关键字
            limit: 限制同步条数
            test_mode: 测试模式标志
            hospital_code: 医院编码
            
        Returns:
            同步结果
        """
        try:
            # 从数据库获取医生信息
            doctors = self.get_doctor_info_from_db(doctor_codes, name_keyword, limit, hospital_code)
            
            if not doctors:
                return {
                    'success': True,
                    'message': '没有找到符合条件的医生信息',
                    'total': 0,
                    'data': []
                }
            
            total = len(doctors)
            print(f"[DATA] 准备同步 {total} 条医生信息")
            
            if test_mode:
                print("测试模式 - 显示前2条医生信息的数据格式:")
                for i, doctor in enumerate(doctors[:2], 1):
                    print(f"\n第 {i} 条医生信息:")
                    print(json.dumps(doctor, ensure_ascii=False, indent=2))
                
                return {
                    'success': True,
                    'message': f"测试模式完成 - 共 {total} 条医生信息格式正确",
                    'total': total,
                    'data': doctors
                }
            
            # 实际同步逻辑
            # 这里可以调用查询接口或者使用04号接口同步医生信息
            sync_request = {
                'type': 'doctor_info_sync',
                'data': doctors,
                'total': total
            }
            
            result = self.send_request(sync_request, test_mode=False)
            
            if result['code'] == 0:
                return {
                    'success': True,
                    'message': f"医生信息同步成功 - 共 {total} 条",
                    'total': total,
                    'data': doctors,
                    'response': result
                }
            else:
                return {
                    'success': False,
                    'error': f"医生信息同步失败: {result['msg']}",
                    'total': total,
                    'data': doctors
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"医生信息同步异常: {str(e)}",
                'total': 0,
                'data': []
            }


def test_interface_18():
    """测试18号接口"""
    print("[TEST] 测试天健云18号接口 - 查询医生信息接口")
    print("=" * 50)
    
    # API配置
    api_config = {
        'base_url': 'http://203.83.237.114:9300',
        'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
        'mic_code': 'MIC1.001E',
        'misc_id': 'MISC1.00001A',
        'timeout': 30
    }
    
    # 创建接口实例
    interface = TianjianInterface18(api_config)
    
    # 测试场景1：查询所有医生信息
    print("\n[DOCTOR] 测试场景1：查询所有医生信息")
    result1 = interface.query_doctor_info(test_mode=True)
    print(f"结果: {json.dumps(result1, ensure_ascii=False, indent=2)}")
    
    # 测试场景2：查询特定医生信息
    print("\n[DOCTOR] 测试场景2：查询特定医生信息")
    result2 = interface.query_doctor_info(
        doctor_id="DOCTOR001",
        hospital_code="0350001",
        test_mode=True
    )
    print(f"结果: {json.dumps(result2, ensure_ascii=False, indent=2)}")
    
    # 测试场景3：从数据库同步医生信息
    print("\n[DOCTOR] 测试场景3：从数据库同步医生信息")
    try:
        result3 = interface.sync_doctor_info(
            test_mode=True
        )
        print(f"同步结果: {json.dumps(result3, ensure_ascii=False, indent=2)}")
    except Exception as e:
        print(f"同步测试时出错: {str(e)}")
    
    # 测试场景4：按关键字查询医生
    print("\n[DOCTOR] 测试场景4：按关键字查询医生")
    try:
        result4 = interface.sync_doctor_info(
            name_keyword="医生",
            test_mode=True
        )
        print(f"关键字查询结果: {json.dumps(result4, ensure_ascii=False, indent=2)}")
    except Exception as e:
        print(f"关键字查询测试时出错: {str(e)}")
    
    print("\n[OK] 天健云18号接口测试完成")


if __name__ == "__main__":
    test_interface_18()