#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云19号接口实现 - 查询科室信息接口
查询科室基本信息
"""

import json
import hashlib
import requests
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from database_service import get_database_service
from config import Config
from multi_org_config import get_org_config_by_shop_code
from api_config_manager import get_tianjian_base_url


class TianjianInterface19:
    """天健云19号接口 - 查询科室信息接口"""
    
    def __init__(self, api_config: Dict[str, Any]):
        """
        初始化接口配置
        
        Args:
            api_config: API配置信息
        """
        self.api_config = api_config
        self.db_service = None  # 延迟初始化
        self.endpoint = "/dx/inter/getDeptInfo"
    
    def generate_signature(self, api_key: str, timestamp: str) -> str:
        """
        生成API签名
        
        Args:
            api_key: API密钥
            timestamp: 时间戳
            
        Returns:
            MD5签名字符串
        """
        sign_string = f"{api_key}{timestamp}"
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    def send_request(self, data: Dict[str, Any], test_mode: bool = False) -> Dict[str, Any]:
        """
        发送查询科室信息请求
        
        Args:
            data: 请求数据
            test_mode: 测试模式标志
            
        Returns:
            API响应结果
        """
        try:
            # 生成时间戳和签名
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            signature = self.generate_signature(self.api_config['api_key'], timestamp)
            
            # 构建请求头
            headers = {
                'Content-Type': 'application/json',
                'sign': signature,
                'timestamp': timestamp,
                'mic-code': self.api_config['mic_code'],
                'misc-id': self.api_config['misc_id']
            }
            
            # 构建请求URL
            url = f"{self.api_config['base_url']}{self.endpoint}"
            
            if test_mode:
                print(f"[HOSPITAL] 查询科室信息接口 - 测试模式")
                print(f"URL: {url}")
                print(f"Headers: {json.dumps(headers, ensure_ascii=False, indent=2)}")
                print(f"Request Data: {json.dumps(data, ensure_ascii=False, indent=2)}")
                
                # 模拟返回科室数据 - 按照API文档格式
                mock_data = [
                    {
                        "name": "内科",
                        "id": "DEPT001",
                        "displaySequence": "1",
                        "shopcode": "08"
                    },
                    {
                        "name": "外科",
                        "id": "DEPT002", 
                        "displaySequence": "2",
                        "shopcode": "08"
                    },
                    {
                        "name": "妇科",
                        "id": "DEPT003",
                        "displaySequence": "3",
                        "shopcode": "08"
                    }
                ]
                
                return {
                    'code': 0,
                    'msg': '测试模式 - 查询科室信息接口调用成功',
                    'data': mock_data,
                    'reponseTime': int(datetime.now().timestamp() * 1000)
                }
            
            # 发送请求
            response = requests.post(
                url=url,
                headers=headers,
                json=data,
                timeout=self.api_config.get('timeout', 30)
            )
            
            # 处理响应
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    'code': -1,
                    'msg': f'HTTP请求失败，状态码: {response.status_code}',
                    'data': None
                }
                
        except Exception as e:
            return {
                'code': -1,
                'msg': f'查询科室信息失败: {str(e)}',
                'data': None
            }
    
    def query_dept_info(self, dept_id: str = "", hospital_code: str = "",
                       test_mode: bool = False) -> Dict[str, Any]:
        """
        查询科室信息
        
        Args:
            dept_id: 科室主键，如果不传，则查询所有的数据
            hospital_code: 医院编码，适用于多院区的情况
            test_mode: 测试模式标志
            
        Returns:
            查询结果
        """
        print(f"[HOSPITAL] 查询科室信息")
        print(f"   科室ID: {dept_id or '全部'}")
        print(f"   医院编码: {hospital_code or '默认'}")
        
        try:
            # 19号接口应该从数据库查询科室信息，而不是调用外部API
            if test_mode:
                # 测试模式返回模拟数据 - 按照API文档格式
                mock_data = [
                    {
                        "name": "内科",
                        "id": "DEPT001",
                        "displaySequence": "1",
                        "shopcode": "08"
                    },
                    {
                        "name": "外科",
                        "id": "DEPT002", 
                        "displaySequence": "2",
                        "shopcode": "08"
                    },
                    {
                        "name": "妇科",
                        "id": "DEPT003",
                        "displaySequence": "3",
                        "shopcode": "08"
                    }
                ]
                
                print(f"[OK] 查询成功（测试模式）")
                print(f"[DATA] 返回 {len(mock_data)} 条科室信息")
                for i, dept in enumerate(mock_data, 1):
                    print(f"   科室{i}: {dept.get('name', '')} ({dept.get('id', '')})")
                
                return {
                    'code': 0,
                    'msg': '测试模式 - 查询科室信息接口调用成功',
                    'data': mock_data,
                    'reponseTime': int(datetime.now().timestamp() * 1000)
                }
            else:
                # 从数据库查询实际科室信息
                depts = self.get_dept_info_from_db(
                    dept_codes=[dept_id] if dept_id else None,
                    limit=None,  # 不限制返回数量
                    shopcode=hospital_code  # 传入机构编码
                )
                
                print(f"[OK] 查询成功")
                if depts and len(depts) > 0:
                    print(f"[DATA] 返回 {len(depts)} 条科室信息")
                    for i, dept in enumerate(depts[:3], 1):  # 只显示前3条
                        print(f"   科室{i}: {dept.get('name', '')} ({dept.get('id', '')})")
                    if len(depts) > 3:
                        print(f"   ... 共 {len(depts)} 条记录")
                else:
                    print(f"[DATA] 没有找到科室信息数据")
                
                return {
                    'code': 0,
                    'msg': '查询科室信息成功',
                    'data': depts or []
                }
                
        except Exception as e:
            print(f"[FAIL] 查询失败: {str(e)}")
            return {
                'code': -1,
                'msg': f'查询失败: {str(e)}',
                'data': []
            }

    def get_dept_info(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        查询科室信息（路由接口方法）

        Args:
            data: 请求数据，包含id和hospitalCode字段

        Returns:
            查询结果
        """
        try:
            # 从请求数据中提取参数
            dept_id = data.get('id', '')
            # 支持多种字段名：hospitalCode（新标准）、shopcode（兼容）、cshopcode（兼容）
            hospital_code = data.get('hospitalCode') or data.get('shopcode') or data.get('cshopcode', '')

            # 调用查询方法
            return self.query_dept_info(dept_id, hospital_code, test_mode=False)

        except Exception as e:
            return {
                'code': -1,
                'msg': f'查询失败: {str(e)}',
                'data': []
            }
    
    def get_dept_info_from_db(self, dept_codes: List[str] = None, 
                             name_keyword: str = None,
                             limit: int = None,
                             shopcode: str = None) -> List[Dict[str, Any]]:
        """
        从数据库获取科室信息
        
        Args:
            dept_codes: 科室编码列表
            name_keyword: 科室名称关键字
            limit: 限制返回条数
            shopcode: 机构编码
            
        Returns:
            科室信息列表
        """
        # 根据shopcode获取对应的机构配置
        if shopcode:
            org_config = get_org_config_by_shop_code(shopcode)
            if not org_config:
                raise Exception(f"未找到门店编码 {shopcode} 对应的机构配置")
            
            # 构建连接字符串
            connection_string = (
                f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                f"SERVER={org_config['db_host']},{org_config['db_port']};"
                f"DATABASE={org_config['db_name']};"
                f"UID={org_config['db_user']};"
                f"PWD={org_config['db_password']};"
                f"TrustServerCertificate=yes;"
            )
            
            # 创建专用的数据库服务实例
            from database_service import DatabaseService
            db_service = DatabaseService(connection_string)
        else:
            # 如果没有指定shopcode，使用默认数据库服务
            if self.db_service is None:
                self.db_service = get_database_service()
            db_service = self.db_service

        if not db_service.connect():
            raise Exception("数据库连接失败")
        
        try:
            sql = """
            SELECT 
                dd.cCode as id,
                dd.cName as name,
                CAST(ROW_NUMBER() OVER (ORDER BY dd.cCode) AS VARCHAR) as displaySequence,
                dd.cStopTag as stopTag,
                dd.cMemo as memo,
                '' as createTime,
                '' as createUser,
                COUNT(cod.cCode) as doctorCount
            FROM Code_Dept_dict dd
            LEFT JOIN Code_Operator_dict cod ON dd.cCode = cod.cDepartmentCode
            WHERE 1=1
            """
            
            params = []
            
            # 添加科室编码条件
            if dept_codes:
                placeholders = ','.join(['?' for _ in dept_codes])
                sql += f" AND dd.cCode IN ({placeholders})"
                params.extend(dept_codes)
            
            # 添加科室名称关键字条件
            if name_keyword:
                sql += " AND dd.cName LIKE ?"
                params.append(f'%{name_keyword}%')
            
            sql += " GROUP BY dd.cCode, dd.cName, dd.cStopTag, dd.cMemo"
            sql += " ORDER BY dd.cCode"
            
            result = db_service.execute_query(sql, tuple(params) if params else None)
            
            # 格式化数据结构 - 按照API文档格式
            formatted_result = []
            for row in result:
                dept_info = {
                    "name": row['name'],
                    "id": row['id'], 
                    "displaySequence": row['displaySequence'],
                    "shopcode": shopcode or "08"  # 使用传入的shopcode参数，默认为08
                }
                formatted_result.append(dept_info)
            
            return formatted_result
            
        finally:
            db_service.disconnect()
    
    def sync_dept_info(self, dept_codes: List[str] = None,
                      name_keyword: str = None, limit: int = None,
                      test_mode: bool = False, shopcode: str = None) -> Dict[str, Any]:
        """
        同步科室信息到天健云
        
        Args:
            dept_codes: 科室编码列表
            name_keyword: 科室名称关键字
            limit: 限制同步条数
            test_mode: 测试模式标志
            shopcode: 机构编码
            
        Returns:
            同步结果
        """
        try:
            # 从数据库获取科室信息
            depts = self.get_dept_info_from_db(dept_codes, name_keyword, limit, shopcode)
            
            if not depts:
                return {
                    'success': True,
                    'message': '没有找到符合条件的科室信息',
                    'total': 0,
                    'data': []
                }
            
            total = len(depts)
            print(f"[DATA] 准备同步 {total} 条科室信息")
            
            if test_mode:
                print("测试模式 - 显示前3条科室信息的数据格式:")
                for i, dept in enumerate(depts[:3], 1):
                    print(f"\n第 {i} 条科室信息:")
                    print(json.dumps(dept, ensure_ascii=False, indent=2))
                
                return {
                    'success': True,
                    'message': f"测试模式完成 - 共 {total} 条科室信息格式正确",
                    'total': total,
                    'data': depts
                }
            
            # 实际同步逻辑
            # 这里可以调用查询接口或者使用05号接口同步科室信息
            sync_request = {
                'type': 'dept_info_sync',
                'data': depts,
                'total': total
            }
            
            result = self.send_request(sync_request, test_mode=False)
            
            if result['code'] == 0:
                return {
                    'success': True,
                    'message': f"科室信息同步成功 - 共 {total} 条",
                    'total': total,
                    'data': depts,
                    'response': result
                }
            else:
                return {
                    'success': False,
                    'error': f"科室信息同步失败: {result['msg']}",
                    'total': total,
                    'data': depts
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"科室信息同步异常: {str(e)}",
                'total': 0,
                'data': []
            }
    
    def get_dept_statistics(self, shopcode: str = None) -> Dict[str, Any]:
        """
        获取科室统计信息
        
        Args:
            shopcode: 机构编码
        
        Returns:
            科室统计结果
        """
        try:
            depts = self.get_dept_info_from_db(shopcode=shopcode)
            
            # 统计信息
            total_depts = len(depts)
            active_depts = len([d for d in depts if d.get('stopTag') == '0'])
            inactive_depts = total_depts - active_depts
            total_doctors = sum(d.get('doctorCount', 0) for d in depts)
            
            # 按科室统计医生数量
            dept_doctor_stats = []
            for dept in depts:
                if dept.get('doctorCount', 0) > 0:
                    dept_doctor_stats.append({
                        'deptName': dept['name'],
                        'deptCode': dept['id'],
                        'doctorCount': dept['doctorCount']
                    })
            
            # 按医生数量排序
            dept_doctor_stats.sort(key=lambda x: x['doctorCount'], reverse=True)
            
            return {
                'success': True,
                'statistics': {
                    'totalDepts': total_depts,
                    'activeDepts': active_depts,
                    'inactiveDepts': inactive_depts,
                    'totalDoctors': total_doctors,
                    'avgDoctorsPerDept': round(total_doctors / total_depts, 2) if total_depts > 0 else 0
                },
                'deptDoctorStats': dept_doctor_stats[:10],  # 前10名
                'queryTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'shopcode': shopcode or "08"
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"获取科室统计信息失败: {str(e)}"
            }


def test_interface_19():
    """测试19号接口"""
    print("[TEST] 测试天健云19号接口 - 查询科室信息接口")
    print("=" * 50)
    
    # API配置
    api_config = {
        'base_url': get_tianjian_base_url(),
        'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
        'mic_code': 'MIC1.001E',
        'misc_id': 'MISC1.00001A',
        'timeout': 30
    }
    
    # 创建接口实例
    interface = TianjianInterface19(api_config)
    
    # 测试场景1：查询所有科室信息
    print("\n[HOSPITAL] 测试场景1：查询所有科室信息")
    result1 = interface.query_dept_info(test_mode=True)
    print(f"结果: {json.dumps(result1, ensure_ascii=False, indent=2)}")
    
    # 测试场景2：查询特定科室信息
    print("\n[HOSPITAL] 测试场景2：查询特定科室信息")
    result2 = interface.query_dept_info(
        dept_id="DEPT001",
        hospital_code="0350001",
        test_mode=True
    )
    print(f"结果: {json.dumps(result2, ensure_ascii=False, indent=2)}")
    
    # 测试场景3：从数据库同步科室信息
    print("\n[HOSPITAL] 测试场景3：从数据库同步科室信息")
    try:
        result3 = interface.sync_dept_info(
            test_mode=True
        )
        print(f"同步结果: {json.dumps(result3, ensure_ascii=False, indent=2)}")
    except Exception as e:
        print(f"同步测试时出错: {str(e)}")
    
    # 测试场景4：按关键字查询科室
    print("\n[HOSPITAL] 测试场景4：按关键字查询科室")
    try:
        result4 = interface.sync_dept_info(
            name_keyword="科",
            test_mode=True
        )
        print(f"关键字查询结果: {json.dumps(result4, ensure_ascii=False, indent=2)}")
    except Exception as e:
        print(f"关键字查询测试时出错: {str(e)}")
    
    # 测试场景5：科室统计信息
    print("\n[DATA] 测试场景5：获取科室统计信息")
    try:
        stats = interface.get_dept_statistics()
        print(f"统计结果: {json.dumps(stats, ensure_ascii=False, indent=2)}")
    except Exception as e:
        print(f"统计信息获取时出错: {str(e)}")
    
    print("\n[OK] 天健云19号接口测试完成")


if __name__ == "__main__":
    test_interface_19()