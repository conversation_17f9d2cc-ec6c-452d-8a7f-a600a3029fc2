#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云20号接口实现 - 查询个人开单情况(可选)
查询对应体检号下面的开单信息，只包含检查项目信息，不包含耗材类的数据
"""

import json
import hashlib
import requests
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from database_service import get_database_service, DatabaseService
from multi_org_config import get_org_config_by_shop_code
from config import Config
from api_config_manager import get_tianjian_base_url


class TianjianInterface20:
    """天健云20号接口 - 查询个人开单情况(可选)"""
    
    def __init__(self, api_config: Dict[str, Any]):
        """
        初始化接口配置
        
        Args:
            api_config: API配置信息
        """
        self.api_config = api_config
        self.db_service = None  # 延迟初始化
        self.endpoint = "/dx/inter/getPersonalOrders"  # 根据实际情况确定

    def _get_database_service_by_hospital_code(self, hospital_code: str = None) -> DatabaseService:
        """
        根据hospitalCode获取对应的数据库服务
        
        Args:
            hospital_code: 医院编码
            
        Returns:
            DatabaseService实例
        """
        if hospital_code:
            # 根据hospitalCode获取对应的机构配置
            org_config = get_org_config_by_shop_code(hospital_code)
            if org_config:
                # 构建连接字符串
                connection_string = (
                    f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                    f"SERVER={org_config['db_host']},{org_config['db_port']};"
                    f"DATABASE={org_config['db_name']};"
                    f"UID={org_config['db_user']};"
                    f"PWD={org_config['db_password']};"
                    f"TrustServerCertificate=yes;"
                )
                return DatabaseService(connection_string)
        
        # 如果没有指定hospitalCode或找不到配置，使用默认数据库服务
        if self.db_service is None:
            self.db_service = get_database_service()
        return self.db_service
    
    def generate_signature(self, api_key: str, timestamp: str) -> str:
        """
        生成API签名
        
        Args:
            api_key: API密钥
            timestamp: 时间戳
            
        Returns:
            MD5签名字符串
        """
        sign_string = f"{api_key}{timestamp}"
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    def send_request(self, data: Dict[str, Any], test_mode: bool = False) -> Dict[str, Any]:
        """
        发送查询个人开单情况请求
        
        Args:
            data: 请求数据
            test_mode: 测试模式标志
            
        Returns:
            API响应结果
        """
        try:
            # 生成时间戳和签名
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            signature = self.generate_signature(self.api_config['api_key'], timestamp)
            
            # 构建请求头
            headers = {
                'Content-Type': 'application/json',
                'sign': signature,
                'timestamp': timestamp,
                'mic-code': self.api_config['mic_code'],
                'misc-id': self.api_config['misc_id']
            }
            
            # 构建请求URL
            url = f"{self.api_config['base_url']}{self.endpoint}"
            
            if test_mode:
                print(f"[LIST] 查询个人开单情况接口 - 测试模式")
                print(f"URL: {url}")
                print(f"Headers: {json.dumps(headers, ensure_ascii=False, indent=2)}")
                print(f"Request Data: {json.dumps(data, ensure_ascii=False, indent=2)}")
                
                # 模拟返回开单数据
                mock_data = []
                pe_no_list = data.get('peNoList', [])
                
                for pe_no in pe_no_list:
                    mock_data.extend([
                        {
                            "peNo": pe_no,
                            "applyItemId": "ITEM001",
                            "applyItemName": "血常规",
                            "registrationFlag": True,
                            "refusedInspectionFlag": False,
                            "checkedFlag": True,
                            "orderTime": "2025-01-12 08:30:00",
                            "medicalOrderDoctor": {
                                "code": "DOCTOR001",
                                "name": "张医生"
                            },
                            "patientResponsibility": "25.00"
                        },
                        {
                            "peNo": pe_no,
                            "applyItemId": "ITEM002", 
                            "applyItemName": "肝功能",
                            "registrationFlag": True,
                            "refusedInspectionFlag": False,
                            "checkedFlag": False,
                            "orderTime": "2025-01-12 08:35:00",
                            "medicalOrderDoctor": {
                                "code": "DOCTOR002",
                                "name": "李医生"
                            },
                            "patientResponsibility": "45.00"
                        }
                    ])
                
                return {
                    'code': 0,
                    'msg': '测试模式 - 查询个人开单情况接口调用成功',
                    'data': mock_data,
                    'reponseTime': int(datetime.now().timestamp() * 1000)
                }
            
            # 发送请求
            response = requests.post(
                url=url,
                headers=headers,
                json=data,
                timeout=self.api_config.get('timeout', 30)
            )
            
            # 处理响应
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    'code': -1,
                    'msg': f'HTTP请求失败，状态码: {response.status_code}',
                    'data': None
                }
                
        except Exception as e:
            return {
                'code': -1,
                'msg': f'查询个人开单情况失败: {str(e)}',
                'data': None
            }
    
    def query_personal_orders(self, pe_no_list: List[str], hospital_code: str = "",
                             test_mode: bool = False) -> Dict[str, Any]:
        """
        查询个人开单情况
        
        Args:
            pe_no_list: 体检号列表
            hospital_code: 医院编码
            test_mode: 测试模式标志
            
        Returns:
            查询结果
        """
        print(f"[LIST] 查询个人开单情况")
        print(f"   体检号数量: {len(pe_no_list)}")
        print(f"   体检号列表: {', '.join(pe_no_list[:5])}{'...' if len(pe_no_list) > 5 else ''}")
        print(f"   医院编码: {hospital_code or '默认'}")
        
        try:
            # 20号接口应该从数据库查询开单信息，而不是调用外部API
            if test_mode:
                # 测试模式返回模拟数据
                mock_data = []
                for pe_no in pe_no_list:
                    mock_data.extend([
                        {
                            "peNo": pe_no,
                            "applyItemId": "ITEM001",
                            "applyItemName": "血常规",
                            "registrationFlag": True,
                            "refusedInspectionFlag": False,
                            "checkedFlag": True,
                            "orderTime": "2025-01-12 08:30:00",
                            "medicalOrderDoctor": {
                                "code": "DOCTOR001",
                                "name": "张医生"
                            },
                            "patientResponsibility": "25.00"
                        },
                        {
                            "peNo": pe_no,
                            "applyItemId": "ITEM002", 
                            "applyItemName": "肝功能",
                            "registrationFlag": True,
                            "refusedInspectionFlag": False,
                            "checkedFlag": False,
                            "orderTime": "2025-01-12 08:35:00",
                            "medicalOrderDoctor": {
                                "code": "DOCTOR002",
                                "name": "李医生"
                            },
                            "patientResponsibility": "45.00"
                        }
                    ])
                
                print(f"[OK] 查询成功（测试模式）")
                print(f"[DATA] 返回 {len(mock_data)} 条开单信息")
                # 按体检号统计
                pe_stats = {}
                for order in mock_data:
                    pe_no = order.get('peNo')
                    if pe_no not in pe_stats:
                        pe_stats[pe_no] = 0
                    pe_stats[pe_no] += 1
                
                for pe_no, count in pe_stats.items():
                    print(f"   体检号 {pe_no}: {count} 项检查")
                
                return {
                    'code': 0,
                    'msg': '测试模式 - 查询个人开单情况接口调用成功',
                    'data': mock_data,
                    'reponseTime': int(datetime.now().timestamp() * 1000)
                }
            else:
                # 从数据库查询实际开单信息
                orders = self.get_personal_orders_from_db(
                    pe_nos=pe_no_list,
                    limit=None,  # 不限制返回数量
                    hospital_code=hospital_code  # 传递医院编码
                )
                
                print(f"[OK] 查询成功")
                if orders and len(orders) > 0:
                    print(f"[DATA] 返回 {len(orders)} 条开单信息")
                    # 按体检号统计并显示前几条
                    pe_stats = {}
                    for order in orders:
                        pe_no = order.get('peNo')
                        if pe_no not in pe_stats:
                            pe_stats[pe_no] = 0
                        pe_stats[pe_no] += 1
                    
                    for i, (pe_no, count) in enumerate(list(pe_stats.items())[:3], 1):
                        print(f"   体检号{i} {pe_no}: {count} 项检查")
                    if len(pe_stats) > 3:
                        print(f"   ... 共涉及 {len(pe_stats)} 个体检号")
                else:
                    print(f"[DATA] 没有找到开单信息数据")
                
                return {
                    'code': 0,
                    'msg': '查询个人开单情况成功',
                    'data': orders or []
                }
                
        except Exception as e:
            print(f"[FAIL] 查询失败: {str(e)}")
            return {
                'code': -1,
                'msg': f'查询失败: {str(e)}',
                'data': []
            }

    def get_personal_orders(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        查询个人开单情况（路由接口方法）

        Args:
            data: 请求数据，包含peNoList和hospitalCode字段

        Returns:
            查询结果
        """
        try:
            # 从请求数据中提取参数
            pe_no_list = data.get('peNoList', [])
            # 支持多种字段名：hospitalCode（新标准）、shopcode（兼容）、cshopcode（兼容）
            hospital_code = data.get('hospitalCode') or data.get('shopcode') or data.get('cshopcode', '')

            # 调用查询方法
            return self.query_personal_orders(pe_no_list, hospital_code, test_mode=False)

        except Exception as e:
            return {
                'code': -1,
                'msg': f'查询失败: {str(e)}',
                'data': []
            }
    
    def get_personal_orders_from_db(self, pe_nos: List[str] = None,
                                   start_date: str = None, end_date: str = None,
                                   limit: int = None,
                                   hospital_code: str = None) -> List[Dict[str, Any]]:
        """
        从数据库获取个人开单信息
        
        Args:
            pe_nos: 体检号列表
            start_date: 开始日期
            end_date: 结束日期
            limit: 限制返回条数
            hospital_code: 医院编码
            
        Returns:
            开单信息列表
        """
        # 根据hospital_code获取对应的数据库服务
        db_service = self._get_database_service_by_hospital_code(hospital_code)

        if not db_service.connect():
            raise Exception("数据库连接失败")
        
        try:
            sql = """
            SELECT 
                rm.cClientCode as peNo,
                rm.cName as patientName,
                cip.cCode as applyItemId,
                cip.cName as applyItemName,
                CASE WHEN rm.cStatus >= '1' THEN 1 ELSE 0 END as registrationFlag,
                0 as refusedInspectionFlag,
                CASE WHEN rm.cStatus >= '2' THEN 1 ELSE 0 END as checkedFlag,
                ISNULL(rm.dOperdate, rm.dAffirmdate) as orderTime,
                cod.cCode as doctorCode,
                cod.cName as doctorName,
                ISNULL(cip.fPrice, 0) as patientResponsibility,
                ISNULL(dm.cDeptCode, 'UNKNOWN') as deptCode,
                ISNULL(dd.cName, '未知科室') as deptName
            FROM T_Register_Main rm
            LEFT JOIN T_Register_Detail rd ON rm.cClientCode = rd.cClientCode
            LEFT JOIN code_Item_Price cip ON rd.cPriceCode = cip.cCode
            LEFT JOIN Code_Item_Main cim ON cip.cMainCode = cim.cCode
            LEFT JOIN Code_Dept_Main dm ON cim.cCode = dm.cMainCode
            LEFT JOIN Code_Dept_dict dd ON dm.cDeptCode = dd.cCode
            LEFT JOIN Code_Operator_dict cod ON rm.cOperCode = cod.cCode
            WHERE 1=1 AND cip.cCode IS NOT NULL
            """
            
            params = []
            
            # 添加体检号条件
            if pe_nos:
                placeholders = ','.join(['?' for _ in pe_nos])
                sql += f" AND rm.cClientCode IN ({placeholders})"
                params.extend(pe_nos)
            
            # 添加日期条件
            if start_date:
                sql += " AND rm.dOperdate >= ?"
                params.append(start_date)
            
            if end_date:
                sql += " AND rm.dOperdate <= ?"
                params.append(end_date)
            
            sql += " ORDER BY rm.cClientCode, cip.cCode"
            
            # 不再使用TOP限制，返回所有符合条件的记录
            # if limit:
            #     sql = sql.replace("SELECT ", f"SELECT TOP {limit} ")
            
            result = db_service.execute_query(sql, tuple(params) if params else None)
            
            # 格式化数据结构
            formatted_result = []
            for row in result:
                order_info = {
                    "peNo": row['peNo'],
                    "applyItemId": row['applyItemId'],
                    "applyItemName": row['applyItemName'],
                    "registrationFlag": bool(row.get('registrationFlag', 0)),
                    "refusedInspectionFlag": bool(row.get('refusedInspectionFlag', 0)),
                    "checkedFlag": bool(row.get('checkedFlag', 0)),
                    "orderTime": str(row.get('orderTime', '')),
                    "medicalOrderDoctor": {
                        "code": row.get('doctorCode', ''),
                        "name": row.get('doctorName', '')
                    },
                    "patientResponsibility": str(row.get('patientResponsibility', '0.00')),
                    "dept": {
                        "code": row.get('deptCode', ''),
                        "name": row.get('deptName', '')
                    },
                    "patient": {
                        "name": row.get('patientName', '')
                    }
                }
                formatted_result.append(order_info)
            
            return formatted_result
            
        finally:
            db_service.disconnect()
    
    def sync_personal_orders(self, pe_nos: List[str] = None,
                           start_date: str = None, end_date: str = None,
                           limit: int = None, test_mode: bool = False,
                           hospital_code: str = None) -> Dict[str, Any]:
        """
        同步个人开单信息到天健云
        
        Args:
            pe_nos: 体检号列表
            start_date: 开始日期
            end_date: 结束日期
            limit: 限制同步条数
            test_mode: 测试模式标志
            hospital_code: 医院编码
            
        Returns:
            同步结果
        """
        try:
            # 从数据库获取开单信息
            orders = self.get_personal_orders_from_db(pe_nos, start_date, end_date, limit, hospital_code)
            
            if not orders:
                return {
                    'success': True,
                    'message': '没有找到符合条件的开单信息',
                    'total': 0,
                    'data': []
                }
            
            # 按体检号分组
            pe_no_list = list(set(order['peNo'] for order in orders))
            total = len(orders)
            
            print(f"[DATA] 准备同步 {total} 条开单信息，涉及 {len(pe_no_list)} 个体检号")
            
            if test_mode:
                print("测试模式 - 显示前3条开单信息的数据格式:")
                for i, order in enumerate(orders[:3], 1):
                    print(f"\n第 {i} 条开单信息:")
                    print(json.dumps(order, ensure_ascii=False, indent=2))
                
                # 统计信息
                pe_stats = {}
                for order in orders:
                    pe_no = order['peNo']
                    if pe_no not in pe_stats:
                        pe_stats[pe_no] = []
                    pe_stats[pe_no].append(order['applyItemName'])
                
                print(f"\n[DATA] 按体检号统计:")
                for pe_no, items in list(pe_stats.items())[:5]:
                    print(f"   {pe_no}: {len(items)} 项 ({', '.join(items[:3])}{'...' if len(items) > 3 else ''})")
                
                return {
                    'success': True,
                    'message': f"测试模式完成 - 共 {total} 条开单信息格式正确",
                    'total': total,
                    'pe_count': len(pe_no_list),
                    'pe_stats': pe_stats,
                    'data': orders
                }
            
            # 实际查询开单信息
            result = self.query_personal_orders(pe_no_list, test_mode=False)
            
            if result['code'] == 0:
                return {
                    'success': True,
                    'message': f"开单信息查询成功 - 共 {total} 条",
                    'total': total,
                    'pe_count': len(pe_no_list),
                    'local_data': orders,
                    'response': result
                }
            else:
                return {
                    'success': False,
                    'error': f"开单信息查询失败: {result['msg']}",
                    'total': total,
                    'data': orders
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"开单信息同步异常: {str(e)}",
                'total': 0,
                'data': []
            }


def test_interface_20():
    """测试20号接口"""
    print("[TEST] 测试天健云20号接口 - 查询个人开单情况接口")
    print("=" * 50)
    
    # API配置
    api_config = {
        'base_url': get_tianjian_base_url(),
        'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
        'mic_code': 'MIC1.001E',
        'misc_id': 'MISC1.00001A',
        'timeout': 30
    }
    
    # 创建接口实例
    interface = TianjianInterface20(api_config)
    
    # 测试场景1：查询单个体检号开单情况
    print("\n[LIST] 测试场景1：查询单个体检号开单情况")
    result1 = interface.query_personal_orders(
        pe_no_list=["PE202501010001"],
        test_mode=True
    )
    print(f"结果: {json.dumps(result1, ensure_ascii=False, indent=2)}")
    
    # 测试场景2：查询多个体检号开单情况
    print("\n[LIST] 测试场景2：查询多个体检号开单情况")
    result2 = interface.query_personal_orders(
        pe_no_list=["PE202501010001", "PE202501010002", "PE202501010003"],
        hospital_code="0350001",
        test_mode=True
    )
    print(f"结果: {json.dumps(result2, ensure_ascii=False, indent=2)}")
    
    # 测试场景3：从数据库同步开单信息
    print("\n[LIST] 测试场景3：从数据库同步开单信息")
    try:
        result3 = interface.sync_personal_orders(
            test_mode=True
        )
        print(f"同步结果: {json.dumps(result3, ensure_ascii=False, indent=2)}")
    except Exception as e:
        print(f"同步测试时出错: {str(e)}")
    
    # 测试场景4：按日期范围查询开单信息
    print("\n[LIST] 测试场景4：按日期范围查询开单信息")
    try:
        result4 = interface.sync_personal_orders(
            start_date="2025-01-01",
            end_date="2025-01-12",
            test_mode=True
        )
        print(f"按日期查询结果: {json.dumps(result4, ensure_ascii=False, indent=2)}")
    except Exception as e:
        print(f"按日期查询测试时出错: {str(e)}")
    
    print("\n[OK] 天健云20号接口测试完成")


if __name__ == "__main__":
    test_interface_20()