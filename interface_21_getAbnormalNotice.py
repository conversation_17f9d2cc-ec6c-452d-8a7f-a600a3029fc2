#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云21号接口实现 - 查询重要异常通知数据(可选)
查询院区健管系统的重要异常是否通知
"""

import json
import hashlib
import requests
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from database_service import get_database_service, DatabaseService
from multi_org_config import get_org_config_by_shop_code
from config import Config


class TianjianInterface21:
    """天健云21号接口 - 查询重要异常通知数据(可选)"""
    
    def __init__(self, api_config: Dict[str, Any]):
        """
        初始化接口配置
        
        Args:
            api_config: API配置信息
        """
        self.api_config = api_config
        self.db_service = None  # 延迟初始化
        self.endpoint = "/data-external-gw/getAbnormal"
    
    def _get_database_service_by_hospital_code(self, hospital_code: str = None) -> DatabaseService:
        """
        根据hospitalCode获取对应的数据库服务
        
        Args:
            hospital_code: 医院编码
            
        Returns:
            DatabaseService实例
        """
        if hospital_code:
            # 根据hospitalCode获取对应的机构配置
            org_config = get_org_config_by_shop_code(hospital_code)
            if org_config:
                # 构建连接字符串
                connection_string = (
                    f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                    f"SERVER={org_config['db_host']},{org_config['db_port']};"
                    f"DATABASE={org_config['db_name']};"
                    f"UID={org_config['db_user']};"
                    f"PWD={org_config['db_password']};"
                    f"TrustServerCertificate=yes;"
                )
                return DatabaseService(connection_string)
        
        # 如果没有指定hospitalCode或找不到配置，使用默认数据库服务
        return get_database_service()
    
    def generate_signature(self, api_key: str, timestamp: str) -> str:
        """
        生成API签名
        
        Args:
            api_key: API密钥
            timestamp: 时间戳
            
        Returns:
            MD5签名字符串
        """
        sign_string = f"{api_key}{timestamp}"
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    def send_request(self, data: Dict[str, Any], test_mode: bool = False) -> Dict[str, Any]:
        """
        发送查询重要异常通知数据请求
        
        Args:
            data: 请求数据
            test_mode: 测试模式标志
            
        Returns:
            API响应结果
        """
        try:
            # 生成时间戳和签名
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            signature = self.generate_signature(self.api_config['api_key'], timestamp)
            
            # 构建请求头
            headers = {
                'Content-Type': 'application/json',
                'sign': signature,
                'timestamp': timestamp,
                'mic-code': self.api_config['mic_code'],
                'misc-id': self.api_config['misc_id']
            }
            
            # 构建请求URL
            url = f"{self.api_config['base_url']}{self.endpoint}"
            
            if test_mode:
                print(f"[TEST] 查询重要异常通知数据接口 - 测试模式")
                print(f"URL: {url}")
                print(f"Headers: {json.dumps(headers, ensure_ascii=False, indent=2)}")
                print(f"Request Data: {json.dumps(data, ensure_ascii=False, indent=2)}")
                
                # 模拟返回异常通知数据
                pe_no = data.get('peNo', 'PE202501010001')
                mock_data = {
                    "peNo": pe_no,
                    "abnormalType": 1,  # 1-重要异常, 2-一般异常
                    "abnormalName": "血压异常",
                    "discoveryTime": "2025-01-12 10:30:00",
                    "discoveryUserId": "DOCTOR001",
                    "deptId": "DEPT001",
                    "deptName": "内科",
                    "hasNotice": "1"  # 1-已通知, 0-未通知
                }
                
                return {
                    'code': 0,
                    'msg': '测试模式 - 查询重要异常通知数据接口调用成功',
                    'data': mock_data,
                    'reponseTime': int(datetime.now().timestamp() * 1000)
                }
            
            # 发送请求
            response = requests.post(
                url=url,
                headers=headers,
                json=data,
                timeout=self.api_config.get('timeout', 30)
            )
            
            # 处理响应
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    'code': -1,
                    'msg': f'HTTP请求失败，状态码: {response.status_code}',
                    'data': None
                }
                
        except Exception as e:
            return {
                'code': -1,
                'msg': f'查询重要异常通知数据失败: {str(e)}',
                'data': None
            }
    
    def query_abnormal_notice(self, pe_no: str, hospital_code: str = "", 
                             hospital_name: str = "", test_mode: bool = False) -> Dict[str, Any]:
        """
        查询重要异常通知数据
        
        Args:
            pe_no: 体检号
            hospital_code: 院区编码
            hospital_name: 院区名称
            test_mode: 测试模式标志
            
        Returns:
            查询结果
        """
        # 使用默认值（如果未提供）
        if not hospital_code:
            hospital_code = 'DEFAULT'
        if not hospital_name:
            hospital_name = '默认医院'
            
        print(f"[INFO] 查询重要异常通知数据")
        print(f"   体检号: {pe_no}")
        print(f"   院区编码: {hospital_code or '默认'}")
        print(f"   院区名称: {hospital_name or '默认'}")
        
        try:
            # 21号接口应该从数据库查询异常通知信息，而不是调用外部API
            if test_mode:
                # 测试模式返回模拟数据
                mock_data = {
                    "peNo": pe_no,
                    "abnormalType": 1,  # 1-重要异常, 2-一般异常
                    "abnormalName": "血压异常",
                    "discoveryTime": "2025-01-12 10:30:00",
                    "discoveryUserId": "DOCTOR001",
                    "deptId": "DEPT001",
                    "deptName": "内科",
                    "hasNotice": "1"  # 1-已通知, 0-未通知
                }
                
                print(f"[OK] 查询成功（测试模式）")
                abnormal_type_desc = {
                    1: "重要异常",
                    2: "一般异常"
                }.get(mock_data.get('abnormalType'), "未知")
                
                has_notice_desc = {
                    "1": "已通知",
                    "0": "未通知"
                }.get(str(mock_data.get('hasNotice', '')), "未知")
                
                print(f"[DATA] 异常信息: {mock_data.get('abnormalName', '')} ({abnormal_type_desc})")
                print(f"   发现时间: {mock_data.get('discoveryTime', '')}")
                print(f"   所属科室: {mock_data.get('deptName', '')} ({mock_data.get('deptId', '')})")
                print(f"   通知状态: {has_notice_desc}")
                
                return {
                    'code': 0,
                    'msg': '测试模式 - 查询重要异常通知数据接口调用成功',
                    'data': mock_data,
                    'reponseTime': int(datetime.now().timestamp() * 1000)
                }
            else:
                # 从数据库查询实际异常通知信息（使用动态数据库连接）
                abnormal_notices = self.get_abnormal_notice_from_db(
                    pe_nos=[pe_no],
                    hospital_code=hospital_code,  # 传递医院编码
                    limit=None  # 不限制返回数量
                )
                
                print(f"[OK] 查询成功")
                if abnormal_notices and len(abnormal_notices) > 0:
                    # 返回第一条匹配的记录（通常一个体检号对应一条异常记录）
                    data = abnormal_notices[0]
                    
                    abnormal_type_desc = {
                        1: "重要异常",
                        2: "一般异常"
                    }.get(data.get('abnormalType'), "未知")
                    
                    has_notice_desc = {
                        "1": "已通知",
                        "0": "未通知"
                    }.get(str(data.get('hasNotice', '')), "未知")
                    
                    print(f"[DATA] 异常信息: {data.get('abnormalName', '')} ({abnormal_type_desc})")
                    print(f"   发现时间: {data.get('discoveryTime', '')}")
                    print(f"   所属科室: {data.get('deptName', '')} ({data.get('deptId', '')})")
                    print(f"   通知状态: {has_notice_desc}")
                    
                    return {
                        'code': 0,
                        'msg': '查询重要异常通知数据成功',
                        'data': data
                    }
                else:
                    print(f"[DATA] 没有找到异常通知数据")
                    return {
                        'code': 0,
                        'msg': '查询成功，但未找到异常通知数据',
                        'data': None
                    }
                
        except Exception as e:
            print(f"[FAIL] 查询失败: {str(e)}")
            return {
                'code': -1,
                'msg': f'查询失败: {str(e)}',
                'data': None
            }

    def get_abnormal_notice(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        查询重要异常通知数据（路由接口方法）

        Args:
            data: 请求数据，包含peNo和shopcode字段

        Returns:
            查询结果
        """
        try:
            # 从请求数据中提取参数
            pe_no = data.get('peNo', '')
            # 支持多种字段名：hospital.code（标准）、shopcode（兼容）、cshopcode（兼容）
            hospital_data = data.get('hospital', {})
            hospital_code = hospital_data.get('code', '') or data.get('hospitalCode', '') or data.get('shopcode', '') or data.get('cshopcode', '')

            # 调用查询方法
            return self.query_abnormal_notice(pe_no, hospital_code, test_mode=False)

        except Exception as e:
            return {
                'code': -1,
                'msg': f'查询失败: {str(e)}',
                'data': None
            }
    
    def batch_query_abnormal_notice(self, pe_no_list: List[str],
                                   hospital_code: str = "", hospital_name: str = "",
                                   test_mode: bool = False) -> Dict[str, Any]:
        """
        批量查询重要异常通知数据
        
        Args:
            pe_no_list: 体检号列表
            hospital_code: 院区编码
            hospital_name: 院区名称
            test_mode: 测试模式标志
            
        Returns:
            批量查询结果
        """
        total_count = len(pe_no_list)
        success_count = 0
        failed_count = 0
        errors = []
        all_data = []
        
        print(f"[INFO] 开始批量查询重要异常通知数据，共 {total_count} 条记录")
        
        for i, pe_no in enumerate(pe_no_list, 1):
            try:
                result = self.query_abnormal_notice(
                    pe_no=pe_no,
                    hospital_code=hospital_code,
                    hospital_name=hospital_name,
                    test_mode=test_mode
                )
                
                if result['code'] == 0:
                    success_count += 1
                    if result.get('data'):
                        all_data.append(result['data'])
                    print(f"[OK] [{i}/{total_count}] 体检号 {pe_no} 查询成功")
                else:
                    failed_count += 1
                    error_msg = f"体检号 {pe_no} 查询失败: {result['msg']}"
                    errors.append(error_msg)
                    print(f"[FAIL] [{i}/{total_count}] {error_msg}")
                    
            except Exception as e:
                failed_count += 1
                error_msg = f"体检号 {pe_no} 处理异常: {str(e)}"
                errors.append(error_msg)
                print(f"[FAIL] [{i}/{total_count}] {error_msg}")
        
        # 统计信息
        notice_stats = {"已通知": 0, "未通知": 0, "未知": 0}
        abnormal_type_stats = {"重要异常": 0, "一般异常": 0, "未知": 0}
        
        for data in all_data:
            # 通知状态统计
            has_notice = str(data.get('hasNotice', ''))
            notice_status = {"1": "已通知", "0": "未通知"}.get(has_notice, "未知")
            notice_stats[notice_status] += 1
            
            # 异常类型统计
            abnormal_type = data.get('abnormalType')
            abnormal_type_desc = {1: "重要异常", 2: "一般异常"}.get(abnormal_type, "未知")
            abnormal_type_stats[abnormal_type_desc] += 1
        
        return {
            'total': total_count,
            'success': success_count,
            'failed': failed_count,
            'total_abnormal': len(all_data),
            'data': all_data,
            'errors': errors,
            'statistics': {
                'notice_stats': notice_stats,
                'abnormal_type_stats': abnormal_type_stats
            },
            'success_rate': f"{(success_count/total_count*100):.1f}%" if total_count > 0 else "0%"
        }
    
    def get_abnormal_notice_from_db(self, pe_nos: List[str] = None,
                                   start_date: str = None, end_date: str = None,
                                   hospital_code: str = None, limit: int = None) -> List[Dict[str, Any]]:
        """
        从数据库获取异常通知数据（支持动态数据库连接）
        
        Args:
            pe_nos: 体检号列表
            start_date: 开始日期
            end_date: 结束日期
            hospital_code: 医院编码
            limit: 限制返回条数
            
        Returns:
            异常通知数据列表
        """
        # 根据hospital_code获取对应的数据库服务
        db_service = self._get_database_service_by_hospital_code(hospital_code)

        if not db_service.connect():
            raise Exception("数据库连接失败")
        
        try:
            # 示例查询，实际应根据异常通知表结构调整
            sql = """
            SELECT 
                rm.cClientCode as peNo,
                rm.cName as patientName,
                rm.cIdCard as idCard,
                CASE 
                    WHEN rm.cMemo LIKE '%重要%' OR rm.cMemo LIKE '%严重%' THEN 1
                    ELSE 2
                END as abnormalType,
                ISNULL(rm.cMemo, '一般异常') as abnormalName,
                ISNULL(rm.dAffirmdate, rm.dOperdate) as discoveryTime,
                rm.cOperCode as discoveryUserId,
                ISNULL(dm.cDeptCode, 'UNKNOWN') as deptId,
                ISNULL(dd.cName, '未知科室') as deptName,
                CASE 
                    WHEN rm.cStatus >= '3' THEN '1'
                    ELSE '0'
                END as hasNotice
            FROM T_Register_Main rm
            LEFT JOIN T_Register_Detail rd ON rm.cClientCode = rd.cClientCode
            LEFT JOIN code_Item_Price cip ON rd.cPriceCode = cip.cCode
            LEFT JOIN Code_Item_Main cim ON cip.cMainCode = cim.cCode
            LEFT JOIN Code_Dept_Main dm ON cim.cCode = dm.cMainCode
            LEFT JOIN Code_Dept_dict dd ON dm.cDeptCode = dd.cCode
            WHERE 1=1
            """
            
            params = []
            
            # 添加体检号条件
            if pe_nos:
                placeholders = ','.join(['?' for _ in pe_nos])
                sql += f" AND rm.cClientCode IN ({placeholders})"
                params.extend(pe_nos)
            
            # 添加日期条件
            if start_date:
                sql += " AND rm.dOperdate >= ?"
                params.append(start_date)
            
            if end_date:
                sql += " AND rm.dOperdate <= ?"
                params.append(end_date)
            
            # 筛选可能有异常的记录
            sql += " AND (rm.cMemo IS NOT NULL AND rm.cMemo <> '')"
            sql += " ORDER BY rm.dOperdate DESC"
            
            # 不再使用TOP限制，返回所有符合条件的记录
            # if limit:
            #     sql = sql.replace("SELECT ", f"SELECT TOP {limit} ")
            
            result = db_service.execute_query(sql, tuple(params) if params else None)
            
            # 格式化数据结构
            formatted_result = []
            for row in result:
                abnormal_data = {
                    "peNo": row['peNo'],
                    "abnormalType": row.get('abnormalType', 2),
                    "abnormalName": row.get('abnormalName', ''),
                    "discoveryTime": str(row.get('discoveryTime', '')),
                    "discoveryUserId": row.get('discoveryUserId', ''),
                    "deptId": row.get('deptId', ''),
                    "deptName": row.get('deptName', ''),
                    "hasNotice": str(row.get('hasNotice', '0')),
                    "patient": {
                        "name": row.get('patientName', ''),
                        "idCard": row.get('idCard', '')
                    }
                }
                formatted_result.append(abnormal_data)
            
            return formatted_result
            
        finally:
            db_service.disconnect()


def test_interface_21():
    """测试21号接口"""
    print("[TEST] 测试天健云21号接口 - 查询重要异常通知数据接口")
    print("=" * 50)
    
    # API配置
    api_config = {
        'base_url': 'http://203.83.237.114:9300',
        'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
        'mic_code': 'MIC1.001E',
        'misc_id': 'MISC1.00001A',
        'timeout': 30
    }
    
    # 创建接口实例
    interface = TianjianInterface21(api_config)
    
    # 测试场景1：查询单个体检号异常通知
    print("\n[TEST] 测试场景1：查询单个体检号异常通知")
    result1 = interface.query_abnormal_notice(
        pe_no="PE202501010001",
        hospital_code="0350001",
        hospital_name=None,
        test_mode=True
    )
    print(f"结果: {json.dumps(result1, ensure_ascii=False, indent=2)}")
    
    # 测试场景2：批量查询异常通知
    print("\n[TEST] 测试场景2：批量查询异常通知")
    pe_no_list = ["PE202501010001", "PE202501010002", "PE202501010003"]
    result2 = interface.batch_query_abnormal_notice(
        pe_no_list=pe_no_list,
        hospital_code="0350001",
        hospital_name=None,
        test_mode=True
    )
    print(f"批量查询结果: {json.dumps(result2, ensure_ascii=False, indent=2)}")
    
    # 测试场景3：从数据库获取异常通知数据
    print("\n[TEST] 测试场景3：从数据库获取异常通知数据")
    try:
        db_data = interface.get_abnormal_notice_from_db()
        print(f"数据库查询结果: {len(db_data)} 条记录")
        for i, data in enumerate(db_data[:3], 1):
            print(f"   {i}. 体检号: {data['peNo']}, "
                  f"异常: {data['abnormalName']}, "
                  f"通知状态: {'已通知' if data['hasNotice'] == '1' else '未通知'}")
    except Exception as e:
        print(f"数据库查询测试时出错: {str(e)}")
    
    # 测试场景4：按日期范围查询异常通知
    print("\n[TEST] 测试场景4：按日期范围查询异常通知")
    try:
        db_data_by_date = interface.get_abnormal_notice_from_db(
            start_date="2025-01-01",
            end_date="2025-01-12"
        )
        print(f"按日期查询结果: {len(db_data_by_date)} 条记录")
        
        if db_data_by_date:
            # 使用第一条记录的体检号进行API查询
            first_pe_no = db_data_by_date[0]['peNo']
            result4 = interface.query_abnormal_notice(
                pe_no=first_pe_no,
                test_mode=True
            )
            print(f"API查询结果: {json.dumps(result4, ensure_ascii=False, indent=2)}")
        
    except Exception as e:
        print(f"按日期查询测试时出错: {str(e)}")
    
    print("\n[OK] 天健云21号接口测试完成")


if __name__ == "__main__":
    test_interface_21()