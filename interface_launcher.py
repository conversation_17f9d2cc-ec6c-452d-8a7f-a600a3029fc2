#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云接口启动器
专用于在打包环境下安全启动接口脚本，防止GUI重复启动
"""

import os
import sys
import subprocess
import json
from pathlib import Path


def main():
    """接口启动器主函数"""
    if len(sys.argv) < 2:
        print("Usage: interface_launcher.py <interface_num> [args...]")
        sys.exit(1)
    
    interface_num = sys.argv[1]
    extra_args = sys.argv[2:] if len(sys.argv) > 2 else []
    
    # 接口文件映射
    interface_mapping = {
        "01": "interface_01_sendPeInfo.py",
        "02": "interface_02_syncApplyItem.py",
        "03": "interface_03_deptInfo.py",
        "04": "interface_04_syncUser.py",
        "05": "interface_05_syncDept.py",
        "06": "interface_06_syncDict.py"
    }
    
    if interface_num not in interface_mapping:
        print(f"[ERROR] Unsupported interface number: {interface_num}")
        sys.exit(1)
    
    script_file = interface_mapping[interface_num]
    
    # 检测运行环境
    is_packaged = getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')
    
    if is_packaged:
        # 打包环境：使用绝对路径
        script_path = os.path.join(sys._MEIPASS, script_file)
    else:
        # 开发环境：使用相对路径
        script_path = script_file
    
    # 检查脚本文件是否存在
    if not os.path.exists(script_path):
        print(f"[ERROR] Interface script not found: {script_path}")
        sys.exit(1)
    
    # 构建命令
    cmd = [sys.executable, script_path] + extra_args
    
    # 设置环境变量
    env = os.environ.copy()
    env['GUI_SUBPROCESS'] = '1'
    env['TIANJIAN_NO_GUI'] = '1'
    env['NO_GUI_MODE'] = '1'
    env['PYTHONIOENCODING'] = 'utf-8'
    
    # 在打包环境下添加模块路径
    if is_packaged:
        env['PYTHONPATH'] = sys._MEIPASS + os.pathsep + env.get('PYTHONPATH', '')
    
    print(f"[LAUNCH] Interface {interface_num}: {script_path}")
    print(f"[CMD] Command: {' '.join(cmd)}")
    print(f"[ENV] Environment: {'Packaged' if is_packaged else 'Development'}")
    
    try:
        # 执行接口脚本
        result = subprocess.run(
            cmd,
            env=env,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            timeout=600
        )
        
        # 输出结果
        if result.stdout:
            print("[STDOUT]:")
            print(result.stdout)
        
        if result.stderr:
            print("[STDERR]:")
            print(result.stderr)
        
        # 返回退出码
        sys.exit(result.returncode)
        
    except subprocess.TimeoutExpired:
        print(f"[TIMEOUT] Interface {interface_num} execution timeout")
        sys.exit(1)
    except Exception as e:
        print(f"[ERROR] Interface {interface_num} execution failed: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()