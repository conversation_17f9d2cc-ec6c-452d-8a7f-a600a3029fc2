#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云数据同步系统 - 简化启动器
根据可用的GUI库自动选择合适的界面
"""

import sys
import importlib.util

def check_pyside6():
    """检查PySide6是否可用"""
    return importlib.util.find_spec("PySide6") is not None

def start_advanced_gui():
    """启动PySide6高级GUI"""
    try:
        print("启动PySide6图形界面...")
        from gui_main import main
        main()
        return True
    except Exception as e:
        print(f"PySide6 GUI启动失败: {e}")
        return False

def start_simple_gui():
    """启动tkinter简化GUI"""
    try:
        print("启动tkinter图形界面...")
        from gui_simple import main
        main()
        return True
    except Exception as e:
        print(f"tkinter GUI启动失败: {e}")
        return False

def show_status():
    """显示当前状态"""
    print("=== 天健云数据同步系统 v1.3.0 ===")
    print("开发: 福能AI对接项目组")
    print()
    
    pyside6_ok = check_pyside6()
    print(f"PySide6高级界面: {'[可用]' if pyside6_ok else '[安装中...]'}")
    print(f"tkinter简化界面: [可用]")
    print()
    
    return pyside6_ok

def main():
    """主函数"""
    pyside6_available = show_status()
    
    if pyside6_available:
        print("检测到PySide6，启动高级图形界面...")
        if start_advanced_gui():
            return
        
        print("高级界面启动失败，切换到简化界面...")
    else:
        print("PySide6未安装，使用简化界面...")
    
    print("启动tkinter简化图形界面...")
    if start_simple_gui():
        return
    
    print("图形界面启动失败，请使用以下选项:")
    print("python tests/run_all_tests.py  # 运行测试")
    print("python interface_01_sendPeInfo.py --test-mode  # 测试接口")

if __name__ == "__main__":
    main()