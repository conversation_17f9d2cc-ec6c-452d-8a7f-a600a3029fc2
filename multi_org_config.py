#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多机构配置管理 - 完整版
基于中心库提供完整的多机构配置管理功能
支持机构配置的增删改查、机构切换、动态配置等
"""

import logging
from typing import Dict, Any, Optional, List, Tuple
from config import Config
from center_organization_service import get_center_organization_service, CenterOrganizationConfig


class MultiOrgManager:
    """多机构管理器 - 完整版"""

    def __init__(self):
        """初始化多机构管理器（延迟初始化）"""
        self.logger = logging.getLogger(__name__)
        self.config = Config()

        # 延迟初始化的组件
        self.center_service = None
        self._initialized = False

        # 当前机构配置 - 使用中心库中的真实机构
        self.current_org_code = getattr(self.config, 'ORG_CODE', '08')
        self.current_org_config = None

        # 机构配置缓存
        self._org_cache = {}
        self._cache_valid = False

        self.logger.info(f"多机构管理器创建完成，当前机构代码: {self.current_org_code}（延迟初始化）")

    def _ensure_initialized(self):
        """确保管理器已初始化"""
        if self._initialized:
            return

        try:
            # 获取中心库机构服务
            self.center_service = get_center_organization_service()

            # 初始化当前机构配置
            self._load_current_org_config()

            self._initialized = True
            self.logger.info(f"多机构管理器初始化完成，当前机构: {self.current_org_code}")

        except Exception as e:
            self.logger.error(f"多机构管理器初始化失败: {e}")
            # 创建默认配置以确保系统可以继续运行
            self.current_org_config = self._create_default_org_config()
            self._initialized = True

    def _load_current_org_config(self):
        """加载当前机构配置"""
        try:
            # 从中心库获取当前机构配置
            org_config = self.center_service.get_organization_by_code(self.current_org_code)
            if org_config:
                self.current_org_config = org_config
                self.logger.info(f"成功加载机构配置: {org_config.org_name}")
            else:
                # 如果中心库中没有配置，使用默认配置
                self.current_org_config = self._create_default_org_config()
                self.logger.warning(f"未找到机构配置 {self.current_org_code}，使用默认配置")
        except Exception as e:
            self.logger.error(f"加载机构配置失败: {e}")
            self.current_org_config = self._create_default_org_config()

    def _create_default_org_config(self) -> CenterOrganizationConfig:
        """创建默认机构配置"""
        return CenterOrganizationConfig(
            org_code=self.current_org_code,
            org_name="默认机构",
            org_type="CENTER",
            status="1",
            tianjian_mic_code=getattr(self.config, 'TIANJIAN_MIC_CODE', 'MIC1.001E'),
            tianjian_misc_id=getattr(self.config, 'TIANJIAN_MISC_ID', 'MISC1.00001A'),
            tianjian_api_key=getattr(self.config, 'TIANJIAN_API_KEY', '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM'),
            tianjian_base_url=getattr(self.config, 'TIANJIAN_BASE_URL', 'http://**************:9300'),
            db_host=getattr(self.config, 'DB_HOST', '************'),
            db_port=getattr(self.config, 'DB_PORT', 1433),
            db_name=getattr(self.config, 'DB_NAME', 'Examdb'),
            db_user=getattr(self.config, 'DB_USER', 'znzj'),
            db_password=getattr(self.config, 'DB_PASSWORD', '2025znzj/888'),
            shop_code=getattr(self.config, 'SHOP_CODE', '01'),
            default_dept_code=getattr(self.config, 'DEFAULT_DEPT_CODE', '01')
        )


    def get_current_org_config(self) -> Dict[str, Any]:
        """获取当前机构配置"""
        self._ensure_initialized()
        if self.current_org_config:
            return self.current_org_config.to_dict()
        return {}

    def get_org_config_by_code(self, org_code: str) -> Optional[Dict[str, Any]]:
        """根据机构编码获取配置"""
        self._ensure_initialized()
        try:
            # 如果是当前机构，直接返回
            if org_code == self.current_org_code and self.current_org_config:
                return self.current_org_config.to_dict()

            # 从缓存获取
            if org_code in self._org_cache and self._cache_valid:
                return self._org_cache[org_code].to_dict()

            # 从中心库获取
            org_config = self.center_service.get_organization_by_code(org_code)
            if org_config:
                # 更新缓存
                self._org_cache[org_code] = org_config
                return org_config.to_dict()

            return None

        except Exception as e:
            self.logger.error(f"获取机构配置失败 {org_code}: {e}")
            return None

    def get_all_org_codes(self) -> List[str]:
        """获取所有机构编码"""
        self._ensure_initialized()
        try:
            organizations = self.center_service.get_all_organizations()
            return [org.org_code for org in organizations if org.status == '1']
        except Exception as e:
            self.logger.error(f"获取机构编码列表失败: {e}")
            return [self.current_org_code]

    def get_all_organizations(self) -> List[Dict[str, Any]]:
        """获取所有机构配置"""
        self._ensure_initialized()
        try:
            organizations = self.center_service.get_all_organizations()
            return [org.to_dict() for org in organizations]
        except Exception as e:
            self.logger.error(f"获取机构配置列表失败: {e}")
            return []

    def switch_org(self, org_code: str) -> Tuple[bool, str]:
        """切换机构"""
        self._ensure_initialized()
        try:
            # 检查机构是否存在
            org_config = self.center_service.get_organization_by_code(org_code)
            if not org_config:
                return False, f"机构 {org_code} 不存在"

            if org_config.status != '1':
                return False, f"机构 {org_code} 已停用"

            # 切换机构
            old_org_code = self.current_org_code
            self.current_org_code = org_code
            self.current_org_config = org_config

            self.logger.info(f"机构切换成功: {old_org_code} -> {org_code}")
            return True, f"已切换到机构: {org_config.org_name}"

        except Exception as e:
            error_msg = f"机构切换失败: {e}"
            self.logger.error(error_msg)
            return False, error_msg


    def save_organization(self, org_config: Dict[str, Any]) -> Tuple[bool, str]:
        """保存机构配置"""
        self._ensure_initialized()
        try:
            # 转换为数据类
            org = CenterOrganizationConfig(**org_config)

            # 保存到中心库
            success, message = self.center_service.save_organization(org)

            if success:
                # 清除缓存
                self._cache_valid = False
                self._org_cache.clear()

                # 如果是当前机构，重新加载配置
                if org.org_code == self.current_org_code:
                    self._load_current_org_config()

                self.logger.info(f"机构配置保存成功: {org.org_code}")

            return success, message

        except Exception as e:
            error_msg = f"保存机构配置失败: {e}"
            self.logger.error(error_msg)
            return False, error_msg

    def delete_organization(self, org_code: str) -> Tuple[bool, str]:
        """删除机构配置"""
        self._ensure_initialized()
        try:
            # 不能删除当前机构
            if org_code == self.current_org_code:
                return False, "不能删除当前使用的机构"

            # 删除机构配置
            success, message = self.center_service.delete_organization(org_code)

            if success:
                # 清除缓存
                self._cache_valid = False
                if org_code in self._org_cache:
                    del self._org_cache[org_code]

                self.logger.info(f"机构配置删除成功: {org_code}")

            return success, message

        except Exception as e:
            error_msg = f"删除机构配置失败: {e}"
            self.logger.error(error_msg)
            return False, error_msg

    def test_organization_connection(self, org_code: str) -> Tuple[bool, str]:
        """测试机构连接"""
        self._ensure_initialized()
        try:
            org_config = self.center_service.get_organization_by_code(org_code)
            if not org_config:
                return False, f"机构 {org_code} 不存在"

            return self.center_service.test_organization_connection(org_config)

        except Exception as e:
            return False, f"连接测试失败: {e}"

    def get_organization_summary(self) -> List[Dict[str, Any]]:
        """获取机构统计摘要"""
        self._ensure_initialized()
        try:
            return self.center_service.get_organization_summary()
        except Exception as e:
            self.logger.error(f"获取机构统计摘要失败: {e}")
            return []

    def refresh_cache(self):
        """刷新缓存"""
        self._cache_valid = False
        self._org_cache.clear()
        self._load_current_org_config()
        self.logger.info("机构配置缓存已刷新")

    def get_current_org_db_config(self) -> Dict[str, Any]:
        """获取当前机构的数据库配置"""
        if self.current_org_config:
            return {
                'host': self.current_org_config.db_host,
                'port': self.current_org_config.db_port,
                'database': self.current_org_config.db_name,
                'username': self.current_org_config.db_user,
                'password': self.current_org_config.db_password,
                'driver': self.current_org_config.db_driver
            }
        return {}

    def get_current_org_api_config(self) -> Dict[str, Any]:
        """获取当前机构的API配置"""
        if self.current_org_config:
            return self.current_org_config.get_tianjian_api_config()
        return {}

    def get_org_config_by_hospital_code(self, hospital_code: str) -> Optional[Dict[str, Any]]:
        """
        根据医院编码（天健云MIC代码）获取机构配置

        Args:
            hospital_code: 医院编码（天健云MIC代码）

        Returns:
            机构配置字典，如果未找到返回None
        """
        try:
            # 获取所有机构配置
            all_orgs = self.get_all_organizations()

            # 查找匹配的机构
            for org in all_orgs:
                if org.get('tianjian_mic_code') == hospital_code:
                    return org

            self.logger.warning(f"未找到医院编码 {hospital_code} 对应的机构配置")
            return None

        except Exception as e:
            self.logger.error(f"根据医院编码获取机构配置失败: {e}")
            return None

    def get_org_config_by_shop_code(self, shop_code: str) -> Optional[Dict[str, Any]]:
        """
        根据门店编码获取机构配置

        Args:
            shop_code: 门店编码

        Returns:
            机构配置字典，如果未找到返回None
        """
        try:
            # 获取所有机构配置
            all_orgs = self.get_all_organizations()

            # 查找匹配的机构
            for org in all_orgs:
                if org.get('shop_code') == shop_code:
                    return org

            self.logger.warning(f"未找到门店编码 {shop_code} 对应的机构配置")
            return None

        except Exception as e:
            self.logger.error(f"根据门店编码获取机构配置失败: {e}")
            return None


# 全局实例
_multi_org_manager = None


def get_multi_org_manager() -> MultiOrgManager:
    """获取多机构管理器实例（单例模式）"""
    global _multi_org_manager
    if _multi_org_manager is None:
        _multi_org_manager = MultiOrgManager()
    return _multi_org_manager


def get_current_org_config() -> Dict[str, Any]:
    """获取当前机构配置"""
    manager = get_multi_org_manager()
    return manager.get_current_org_config()


def get_org_config_by_code(org_code: str) -> Optional[Dict[str, Any]]:
    """根据机构编码获取配置"""
    manager = get_multi_org_manager()
    return manager.get_org_config_by_code(org_code)


def switch_organization(org_code: str) -> Tuple[bool, str]:
    """切换机构"""
    manager = get_multi_org_manager()
    return manager.switch_org(org_code)


def get_all_organizations() -> List[Dict[str, Any]]:
    """获取所有机构配置"""
    manager = get_multi_org_manager()
    return manager.get_all_organizations()


def get_org_config_by_hospital_code(hospital_code: str) -> Optional[Dict[str, Any]]:
    """根据医院编码（天健云MIC代码）获取机构配置"""
    manager = get_multi_org_manager()
    return manager.get_org_config_by_hospital_code(hospital_code)


def get_org_config_by_shop_code(shop_code: str) -> Optional[Dict[str, Any]]:
    """根据门店编码获取机构配置"""
    manager = get_multi_org_manager()
    return manager.get_org_config_by_shop_code(shop_code)


def test_multi_org_config():
    """测试多机构配置"""
    print("=== 多机构配置测试 ===")

    try:
        # 测试获取管理器
        manager = get_multi_org_manager()
        print(f"✅ 多机构管理器创建成功")

        # 测试获取当前机构配置
        current_config = get_current_org_config()
        print(f"✅ 当前机构: {current_config.get('org_code', 'N/A')} - {current_config.get('org_name', 'N/A')}")

        # 测试获取所有机构编码
        org_codes = manager.get_all_org_codes()
        print(f"✅ 机构数量: {len(org_codes)}")

        # 测试获取所有机构配置
        all_orgs = get_all_organizations()
        print(f"✅ 机构配置数量: {len(all_orgs)}")

        # 显示配置信息
        if current_config:
            print(f"\n📋 当前机构配置:")
            print(f"  机构编码: {current_config.get('org_code', 'N/A')}")
            print(f"  机构名称: {current_config.get('org_name', 'N/A')}")
            print(f"  机构类型: {current_config.get('org_type', 'N/A')}")
            print(f"  天健云MIC: {current_config.get('tianjian_mic_code', 'N/A')}")
            print(f"  天健云MISC: {current_config.get('tianjian_misc_id', 'N/A')}")
            print(f"  API地址: {current_config.get('tianjian_base_url', 'N/A')}")
            print(f"  数据库: {current_config.get('db_host', 'N/A')}:{current_config.get('db_port', 'N/A')}/{current_config.get('db_name', 'N/A')}")

        # 显示所有机构
        if all_orgs:
            print(f"\n📋 所有机构列表:")
            for org in all_orgs:
                status_text = "启用" if org.get('status') == '1' else "停用"
                print(f"  {org.get('org_code', 'N/A')}: {org.get('org_name', 'N/A')} ({org.get('org_type', 'N/A')}) - {status_text}")

        print("\n✅ 多机构配置测试完成")
        return True

    except Exception as e:
        print(f"\n❌ 多机构配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    test_multi_org_config()
