#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的数据库服务
基于性能优化器的高效数据库访问层
"""

import time
from typing import Dict, List, Any, Optional, Generator
from datetime import datetime, timedelta
from config import Config
from performance_optimizer import (
    get_connection_manager,
    get_batch_processor,
    get_stream_processor
)


class OptimizedDatabaseService:
    """优化的数据库服务"""
    
    def __init__(self, connection_string: str):
        self.connection_string = connection_string
        self.connection_manager = get_connection_manager()
        self.batch_processor = get_batch_processor()
        self.stream_processor = get_stream_processor()
        self._connection_warmed_up = False

        # 不在初始化时预热连接池，改为延迟预热
    
    def _warm_up_connection_pool(self):
        """预热连接池（延迟执行）"""
        if self._connection_warmed_up:
            return

        try:
            self.connection_manager.execute_query_with_cache(
                self.connection_string,
                "SELECT 1 as test",
                cache_key="connection_test",
                use_cache=False
            )
            print("[OK] 数据库连接池预热完成")
            self._connection_warmed_up = True
        except Exception as e:
            print(f"[WARN] 连接池预热失败: {e}")

    def ensure_connection_ready(self):
        """确保连接已准备就绪"""
        if not self._connection_warmed_up:
            self._warm_up_connection_pool()
    
    def get_exam_data_optimized(
        self,
        days: int = 30,
        limit: Optional[int] = None,
        use_cache: bool = True
    ) -> List[Dict[str, Any]]:
        """优化的体检数据获取"""

        # 确保连接已准备就绪
        self.ensure_connection_ready()

        cache_key = f"exam_data_{days}_{limit}" if use_cache else None
        
        sql = """
        SELECT TOP ({limit})
            cClientCode as reg_code,
            cName as name, 
            cSex as sex,
            dBornDate as birthday,
            cIdCard as cert_id,
            cTel as mobile,
            dOperdate as exam_date,
            'UNKNOWN' as dept_name,
            'NORMAL' as reg_type
        FROM T_Register_Main 
        WHERE dOperdate >= DATEADD(day, -{days}, GETDATE())
            AND cClientCode IS NOT NULL 
            AND cName IS NOT NULL
        ORDER BY dOperdate DESC
        """.format(
            limit=limit or 100000,  # 如果没有限制，设置一个较大的数字
            days=days
        )
        
        try:
            results = self.connection_manager.execute_query_with_cache(
                self.connection_string,
                sql,
                cache_key=cache_key,
                use_cache=use_cache
            )
            
            print(f"[OK] 获取体检数据: {len(results)} 条记录")
            return results
            
        except Exception as e:
            print(f"[FAIL] 获取体检数据失败: {e}")
            return []
    
    def get_apply_items_with_check_items_optimized(
        self, 
        limit: Optional[int] = None,
        use_cache: bool = True
    ) -> List[Dict[str, Any]]:
        """优化的申请项目获取（解决N+1查询问题）"""
        
        cache_key = f"apply_items_with_checks_{limit}" if use_cache else None
        
        # 一次性查询所有数据，避免N+1问题
        sql = """
        SELECT 
            im.cCode as apply_item_id,
            im.cName as apply_item_name,
            im.cStopTag as stop_tag,
            COALESCE(dm.cDeptCode, 'UNKNOWN') as dept_code,
            COALESCE(ip.cCode, '') as check_item_id,
            COALESCE(ip.cName, '') as check_item_name,
            COALESCE(ip.fPrice, 0) as price,
            COALESCE(dm.cDeptCode, 'UNKNOWN') as check_dept_code
        FROM Code_Item_Main im
        LEFT JOIN Code_Dept_Main dm ON im.cCode = dm.cMainCode
        LEFT JOIN code_Item_Price ip ON im.cCode = ip.cMainCode
        WHERE im.cStopTag = '0'
        {limit_clause}
        ORDER BY im.cCode, ip.cCode
        """.format(
            limit_clause=f"AND im.cCode IN (SELECT TOP {limit} cCode FROM Code_Item_Main WHERE cStopTag = '0' ORDER BY cCode)" if limit else ""
        )
        
        try:
            start_time = time.time()
            raw_results = self.connection_manager.execute_query_with_cache(
                self.connection_string,
                sql,
                cache_key=cache_key,
                use_cache=use_cache
            )
            
            # 在应用层组装层次结构，避免多次数据库查询
            grouped_results = self._group_apply_items(raw_results)
            
            query_time = time.time() - start_time
            print(f"[OK] 获取申请项目: {len(grouped_results)} 个项目，耗时 {query_time:.3f}s")
            
            return grouped_results
            
        except Exception as e:
            print(f"[FAIL] 获取申请项目失败: {e}")
            return []
    
    def _group_apply_items(self, raw_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """将平铺的查询结果组装成层次结构"""
        items_dict = {}
        
        for row in raw_results:
            item_id = row['apply_item_id']
            
            # 如果是新的申请项目，创建基础结构
            if item_id not in items_dict:
                items_dict[item_id] = {
                    'applyItemId': item_id,
                    'applyItemName': row['apply_item_name'],
                    'deptId': row['dept_code'] or '',
                    'checkItemList': []
                }
            
            # 如果有检查项目，添加到列表中
            if row['check_item_id']:
                items_dict[item_id]['checkItemList'].append({
                    'checkItemId': row['check_item_id'],
                    'checkItemName': row['check_item_name'],
                    'price': float(row['price']) if row['price'] else 0.0,
                    'deptCode': row['check_dept_code'] or ''
                })
        
        return list(items_dict.values())
    
    def get_dict_data_optimized(
        self, 
        dict_type: str,
        use_cache: bool = True
    ) -> List[Dict[str, Any]]:
        """优化的字典数据获取"""
        
        cache_key = f"dict_data_{dict_type}" if use_cache else None
        
        # 根据字典类型选择合适的查询
        sql_map = {
            'items': """
                SELECT cCode as code, cName as name, cStopTag as stop_tag,
                       cDeptCode as dept_code, cPYCode as py_code
                FROM Code_Item_Main 
                WHERE cStopTag = '0'
                ORDER BY cCode
            """,
            'departments': """
                SELECT cCode as code, cName as name, cStopTag as stop_tag,
                       cShotName as short_name, cMemo as memo
                FROM Code_Dept_dict 
                WHERE cStopTag = '0'
                ORDER BY cCode
            """,
            'operators': """
                SELECT cCode as code, cName as name, cStopTag as stop_tag,
                       cDepartmentCode as dept_code, cDoctorTag as is_doctor
                FROM Code_Operator_dict 
                WHERE cStopTag = '0'
                ORDER BY cCode
            """
        }
        
        sql = sql_map.get(dict_type)
        if not sql:
            print(f"[WARN] 未知的字典类型: {dict_type}")
            return []
        
        try:
            results = self.connection_manager.execute_query_with_cache(
                self.connection_string,
                sql,
                cache_key=cache_key,
                use_cache=use_cache
            )
            
            print(f"[OK] 获取{dict_type}字典数据: {len(results)} 条记录")
            return results
            
        except Exception as e:
            print(f"[FAIL] 获取{dict_type}字典数据失败: {e}")
            return []
    
    def stream_large_dataset(
        self, 
        base_sql: str, 
        batch_size: int = 1000,
        max_records: Optional[int] = None
    ) -> Generator[List[Dict[str, Any]], None, None]:
        """流式处理大数据集"""
        
        print(f"[STREAM] 开始流式处理，批量大小: {batch_size}")
        
        total_processed = 0
        for batch in self.stream_processor.stream_query_results(
            self.connection_string,
            base_sql,
            batch_size=batch_size,
            total_limit=max_records
        ):
            total_processed += len(batch)
            print(f"[STREAM] 处理批次: {len(batch)} 条记录 (总计: {total_processed})")
            yield batch
    
    def execute_batch_operation(
        self, 
        operation_func,
        data: List[Any],
        batch_size: int = 100,
        auto_adjust: bool = True
    ) -> Dict[str, Any]:
        """执行批量操作"""
        
        # 配置批量处理器
        self.batch_processor.batch_size = batch_size
        
        return self.batch_processor.process_in_batches(
            data=data,
            processor_func=operation_func,
            auto_adjust=auto_adjust
        )
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        
        metrics = self.connection_manager.get_performance_metrics()
        pool_status = self.connection_manager.get_connection_pool_status()
        
        return {
            'timestamp': datetime.now(),
            'query_metrics': {
                'total_queries': metrics.total_queries,
                'avg_response_time': metrics.avg_time,
                'slow_queries': metrics.slow_queries,
                'slow_query_rate': (metrics.slow_queries / metrics.total_queries * 100) if metrics.total_queries > 0 else 0
            },
            'cache_metrics': {
                'cache_hits': metrics.cache_hits,
                'cache_misses': metrics.cache_misses,
                'cache_hit_rate': (metrics.cache_hits / (metrics.cache_hits + metrics.cache_misses) * 100) if (metrics.cache_hits + metrics.cache_misses) > 0 else 0
            },
            'connection_pool': pool_status,
            'batch_processing': {
                'current_batch_size': self.batch_processor.batch_size,
                'success_rate': self.batch_processor.success_rate * 100,
                'avg_response_time': self.batch_processor.avg_response_time,
                'adjustments_count': len(self.batch_processor.adjustment_history)
            }
        }
    
    def optimize_for_large_dataset(self):
        """为大数据集处理进行优化"""
        
        print("[OPTIMIZE] 启用大数据集优化模式")
        
        # 增加批量大小
        self.batch_processor.batch_size = 500
        self.batch_processor.max_batch_size = 1000
        
        # 清空缓存以释放内存
        self.connection_manager.clear_cache()
        
        print("[OK] 大数据集优化配置完成")
    
    def close(self):
        """关闭连接和清理资源"""
        try:
            # 清空缓存
            self.connection_manager.clear_cache()
            
            # 获取最终性能报告
            final_report = self.get_performance_report()
            print(f"[FINAL_REPORT] 性能统计:")
            print(f"  总查询数: {final_report['query_metrics']['total_queries']}")
            print(f"  平均响应时间: {final_report['query_metrics']['avg_response_time']:.3f}s")
            print(f"  缓存命中率: {final_report['cache_metrics']['cache_hit_rate']:.1f}%")
            
        except Exception as e:
            print(f"[WARN] 关闭数据库服务时出现警告: {e}")


# 便利函数
def create_optimized_db_service(connection_string: str) -> OptimizedDatabaseService:
    """创建优化的数据库服务实例"""
    return OptimizedDatabaseService(connection_string)


# 性能测试函数
def performance_comparison_test():
    """性能对比测试"""
    print("="*60)
    print("数据库性能对比测试")
    print("="*60)
    
    # 使用统一配置
    connection_string = Config.get_interface_db_connection_string()
    
    # 创建优化的数据库服务
    db_service = create_optimized_db_service(connection_string)
    
    try:
        # 测试1: 体检数据查询
        print("\n[TEST 1] 体检数据查询性能测试")
        start_time = time.time()
        exam_data = db_service.get_exam_data_optimized(days=7, limit=100)
        test1_time = time.time() - start_time
        print(f"  结果: 获取 {len(exam_data)} 条记录，耗时 {test1_time:.3f}s")
        
        # 测试2: 申请项目查询（优化的N+1查询解决方案）
        print("\n[TEST 2] 申请项目查询性能测试（解决N+1查询）")
        start_time = time.time()
        apply_items = db_service.get_apply_items_with_check_items_optimized(limit=20)
        test2_time = time.time() - start_time
        total_check_items = sum(len(item.get('checkItemList', [])) for item in apply_items)
        print(f"  结果: 获取 {len(apply_items)} 个申请项目，{total_check_items} 个检查项目，耗时 {test2_time:.3f}s")
        
        # 测试3: 字典数据查询（带缓存）
        print("\n[TEST 3] 字典数据查询性能测试（缓存测试）")
        
        # 第一次查询
        start_time = time.time()
        depts1 = db_service.get_dict_data_optimized('departments', use_cache=True)
        first_query_time = time.time() - start_time
        
        # 第二次查询（应该命中缓存）
        start_time = time.time()
        depts2 = db_service.get_dict_data_optimized('departments', use_cache=True)
        cached_query_time = time.time() - start_time
        
        print(f"  第一次查询: {len(depts1)} 条记录，耗时 {first_query_time:.3f}s")
        print(f"  缓存查询: {len(depts2)} 条记录，耗时 {cached_query_time:.3f}s")
        print(f"  缓存加速: {first_query_time/cached_query_time:.1f}x" if cached_query_time > 0 else "  缓存加速: 无穷大")
        
        # 性能报告
        print("\n[PERFORMANCE REPORT]")
        report = db_service.get_performance_report()
        print(f"  总查询数: {report['query_metrics']['total_queries']}")
        print(f"  平均响应时间: {report['query_metrics']['avg_response_time']:.3f}s")
        print(f"  慢查询率: {report['query_metrics']['slow_query_rate']:.1f}%")
        print(f"  缓存命中率: {report['cache_metrics']['cache_hit_rate']:.1f}%")
        
    except Exception as e:
        print(f"[ERROR] 性能测试失败: {e}")
    
    finally:
        db_service.close()


if __name__ == "__main__":
    performance_comparison_test()