#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机构编码前缀管理器
为发送到天健云的数据自动添加机构编码前缀，用于区分不同机构的数据
"""

import re
from typing import Dict, Any, List, Optional, Union
import logging


class OrgCodePrefixManager:
    """机构编码前缀管理器"""
    
    # 需要添加机构编码前缀的字段配置
    PREFIX_FIELDS_CONFIG = {
        # 01号接口 - 体检基本信息传输
        'sendPeInfo': {
            'peUserInfo': {
                # 'archiveNo': True,    # 档案号 - 不需要前缀
                # 'peno': True,         # 体检号 - 不需要前缀
                'applyItemList': True,  # 申请项目列表（字符串数组）
                # 'pePackage': {
                #     'code': True        # 套餐编码 - 不需要前缀
                # },
                'firstCheckFinishDoctor': {
                    'code': True        # 初检医生编码
                },
                'mainCheckFinishDoctor': {
                    'code': True        # 主检医生编码
                }
            },
            'archiveInfo': {
                # 'peNoList': True      # 体检号列表 - 不需要前缀
            }
        },
        
        # 02号接口 - 申请项目字典数据传输
        'syncApplyItem': {
            'applyItemId': True,        # 申请项目ID
            'deptId': True,             # 科室ID
            'checkItemList': {
                'checkItemId': True     # 检查项目ID
            }
        },
        
        # 03号接口 - 体检科室结果传输
        'deptInfo': {
            # 'peNo': False,            # 体检号 - 不需要前缀
            'dept': {
                'code': True            # 科室编码
            },
            'checkDoctor': {
                'code': True            # 检查医生编码
            },
            'auditDoctor': {
                'code': True            # 审核医生编码
            },
            'itemDesc': {
                'applyItem': {
                    'code': True        # 申请项目编码
                },
                'checkItem': {
                    'code': True        # 检查项目编码
                }
            }
        },
        
        # 04号接口 - 医生信息传输
        'syncUser': {
            'doctorId': True,           # 医生ID
            'deptId': True              # 科室ID
        },
        
        # 05号接口 - 科室信息传输
        'syncDept': {
            'deptId': True              # 科室ID
        },
        
        # 06号接口 - 字典信息传输
        'syncDict': {
            'dictId': True,             # 字典ID
            'parentId': True            # 父级ID
        }
    }
    
    def __init__(self, org_code: str, separator: str = "_"):
        """
        初始化机构编码前缀管理器
        
        Args:
            org_code: 机构编码
            separator: 分隔符，默认为下划线
        """
        self.org_code = org_code
        self.separator = separator
        self.logger = logging.getLogger(__name__)
        
        # 验证机构编码
        if not org_code or not isinstance(org_code, str):
            raise ValueError("机构编码不能为空且必须为字符串")
        
        # 清理机构编码（移除特殊字符）
        self.org_code = re.sub(r'[^\w]', '', org_code.upper())
        
        self.logger.info(f"初始化机构编码前缀管理器: {self.org_code}")
    
    def add_prefix(self, value: str, field_name: str = None) -> str:
        """
        为单个值添加机构编码前缀
        
        Args:
            value: 原始值
            field_name: 字段名（用于日志）
            
        Returns:
            添加前缀后的值
        """
        if not value or not isinstance(value, str):
            return value
        
        # 检查是否已经有前缀
        if value.startswith(f"{self.org_code}{self.separator}"):
            return value
        
        # 添加前缀
        prefixed_value = f"{self.org_code}{self.separator}{value}"
        
        if field_name:
            self.logger.debug(f"添加前缀 {field_name}: {value} -> {prefixed_value}")
        
        return prefixed_value
    
    def remove_prefix(self, value: str) -> str:
        """
        移除机构编码前缀
        
        Args:
            value: 带前缀的值
            
        Returns:
            移除前缀后的原始值
        """
        if not value or not isinstance(value, str):
            return value
        
        prefix = f"{self.org_code}{self.separator}"
        if value.startswith(prefix):
            return value[len(prefix):]
        
        return value
    
    def process_data(self, data: Dict[str, Any], interface_name: str) -> Dict[str, Any]:
        """
        处理接口数据，为指定字段添加机构编码前缀
        
        Args:
            data: 接口数据
            interface_name: 接口名称
            
        Returns:
            处理后的数据
        """
        if interface_name not in self.PREFIX_FIELDS_CONFIG:
            self.logger.warning(f"未找到接口 {interface_name} 的前缀配置")
            return data
        
        config = self.PREFIX_FIELDS_CONFIG[interface_name]
        processed_data = self._process_dict(data, config)
        
        self.logger.info(f"完成 {interface_name} 接口数据前缀处理")
        return processed_data
    
    def _process_dict(self, data: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """递归处理字典数据"""
        if not isinstance(data, dict):
            return data
        
        result = {}
        
        for key, value in data.items():
            if key in config:
                field_config = config[key]
                
                if field_config is True:
                    # 直接添加前缀
                    if isinstance(value, str):
                        result[key] = self.add_prefix(value, key)
                    elif isinstance(value, list):
                        result[key] = [self.add_prefix(item, key) if isinstance(item, str) else item for item in value]
                    else:
                        result[key] = value
                        
                elif isinstance(field_config, dict):
                    # 递归处理嵌套对象
                    if isinstance(value, dict):
                        result[key] = self._process_dict(value, field_config)
                    elif isinstance(value, list):
                        result[key] = [
                            self._process_dict(item, field_config) if isinstance(item, dict) else item
                            for item in value
                        ]
                    else:
                        result[key] = value
                else:
                    result[key] = value
            else:
                result[key] = value
        
        return result
    
    def get_prefix(self) -> str:
        """获取完整前缀（包含分隔符）"""
        return f"{self.org_code}{self.separator}"
    
    def validate_prefixed_data(self, data: Dict[str, Any], interface_name: str) -> List[str]:
        """
        验证数据中的前缀是否正确
        
        Args:
            data: 要验证的数据
            interface_name: 接口名称
            
        Returns:
            验证错误列表
        """
        errors = []
        
        if interface_name not in self.PREFIX_FIELDS_CONFIG:
            return errors
        
        config = self.PREFIX_FIELDS_CONFIG[interface_name]
        self._validate_dict(data, config, "", errors)
        
        return errors
    
    def _validate_dict(self, data: Dict[str, Any], config: Dict[str, Any], path: str, errors: List[str]):
        """递归验证字典数据"""
        if not isinstance(data, dict):
            return
        
        for key, value in data.items():
            current_path = f"{path}.{key}" if path else key
            
            if key in config:
                field_config = config[key]
                
                if field_config is True:
                    # 验证前缀
                    if isinstance(value, str) and value and not value.startswith(self.get_prefix()):
                        errors.append(f"字段 {current_path} 缺少机构编码前缀: {value}")
                    elif isinstance(value, list):
                        for i, item in enumerate(value):
                            if isinstance(item, str) and item and not item.startswith(self.get_prefix()):
                                errors.append(f"字段 {current_path}[{i}] 缺少机构编码前缀: {item}")
                                
                elif isinstance(field_config, dict):
                    # 递归验证嵌套对象
                    if isinstance(value, dict):
                        self._validate_dict(value, field_config, current_path, errors)
                    elif isinstance(value, list):
                        for i, item in enumerate(value):
                            if isinstance(item, dict):
                                self._validate_dict(item, field_config, f"{current_path}[{i}]", errors)


def create_org_prefix_manager(org_code: str) -> OrgCodePrefixManager:
    """
    创建机构编码前缀管理器
    
    Args:
        org_code: 机构编码
        
    Returns:
        机构编码前缀管理器实例
    """
    return OrgCodePrefixManager(org_code)


def add_org_prefix_to_interface_data(data: Dict[str, Any], interface_name: str, org_code: str) -> Dict[str, Any]:
    """
    便捷函数：为接口数据添加机构编码前缀
    
    Args:
        data: 接口数据
        interface_name: 接口名称
        org_code: 机构编码
        
    Returns:
        处理后的数据
    """
    manager = create_org_prefix_manager(org_code)
    return manager.process_data(data, interface_name)
