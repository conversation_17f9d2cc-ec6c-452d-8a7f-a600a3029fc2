#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机构配置服务
用于管理多个体检机构的配置信息
"""

import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass, asdict
from config import Config
from optimized_database_service import create_optimized_db_service


@dataclass
class OrganizationConfig:
    """机构配置数据类"""
    id: int = 0
    org_code: str = ""
    org_name: str = ""
    org_type: str = "HOSPITAL"
    status: str = "1"
    
    # 天健云API配置
    tianjian_mic_code: str = ""
    tianjian_misc_id: str = ""
    tianjian_api_key: str = ""
    tianjian_base_url: str = "http://**************:9300"
    
    # 数据库配置
    db_host: str = ""
    db_port: int = 1433
    db_name: str = ""
    db_user: str = ""
    db_password: str = ""
    db_driver: str = "ODBC Driver 17 for SQL Server"
    
    # 业务配置
    shop_code: str = ""
    default_dept_code: str = ""
    sync_enabled: str = "1"
    sync_intervals: int = 60
    
    # 联系信息
    contact_person: str = ""
    contact_phone: str = ""
    contact_email: str = ""
    address: str = ""
    
    # 系统字段
    create_time: str = ""
    update_time: str = ""
    create_user: str = "SYSTEM"
    update_user: str = "SYSTEM"
    remark: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    def get_db_connection_string(self) -> str:
        """获取数据库连接字符串"""
        return (
            f"DRIVER={{{self.db_driver}}};"
            f"SERVER={self.db_host},{self.db_port};"
            f"DATABASE={self.db_name};"
            f"UID={self.db_user};"
            f"PWD={self.db_password}"
        )
    
    def get_tianjian_api_config(self) -> Dict[str, Any]:
        """获取天健云API配置"""
        return {
            'base_url': self.tianjian_base_url,
            'api_key': self.tianjian_api_key,
            'mic_code': self.tianjian_mic_code,
            'misc_id': self.tianjian_misc_id,
            'timeout': 30
        }


class OrganizationConfigService:
    """机构配置服务类"""
    
    def __init__(self):
        """初始化服务"""
        self.logger = logging.getLogger(__name__)
        connection_string = Config.get_interface_db_connection_string()
        self.db_service = create_optimized_db_service(connection_string)
    
    def get_all_organizations(self) -> List[OrganizationConfig]:
        """获取所有机构配置"""
        try:
            sql = """
            SELECT 
                id, cOrgCode, cOrgName, cOrgType, cStatus,
                cTianjianMicCode, cTianjianMiscId, cTianjianApiKey, cTianjianBaseUrl,
                cDbHost, cDbPort, cDbName, cDbUser, cDbPassword, cDbDriver,
                cShopCode, cDefaultDeptCode, cSyncEnabled, cSyncIntervals,
                cContactPerson, cContactPhone, cContactEmail, cAddress,
                dCreateTime, dUpdateTime, cCreateUser, cUpdateUser, cRemark
            FROM T_Organization_Config 
            ORDER BY cOrgCode
            """
            
            result = self.db_service.connection_manager.execute_query_with_cache(
                self.db_service.connection_string, sql, 
                cache_key='all_organizations', use_cache=False
            )
            
            organizations = []
            for row in result:
                org = OrganizationConfig(
                    id=row.get('id', 0),
                    org_code=row.get('cOrgCode', ''),
                    org_name=row.get('cOrgName', ''),
                    org_type=row.get('cOrgType', 'HOSPITAL'),
                    status=row.get('cStatus', '1'),
                    tianjian_mic_code=row.get('cTianjianMicCode', ''),
                    tianjian_misc_id=row.get('cTianjianMiscId', ''),
                    tianjian_api_key=row.get('cTianjianApiKey', ''),
                    tianjian_base_url=row.get('cTianjianBaseUrl', 'http://**************:9300'),
                    db_host=row.get('cDbHost', ''),
                    db_port=row.get('cDbPort', 1433),
                    db_name=row.get('cDbName', ''),
                    db_user=row.get('cDbUser', ''),
                    db_password=row.get('cDbPassword', ''),
                    db_driver=row.get('cDbDriver', 'ODBC Driver 17 for SQL Server'),
                    shop_code=row.get('cShopCode', ''),
                    default_dept_code=row.get('cDefaultDeptCode', ''),
                    sync_enabled=row.get('cSyncEnabled', '1'),
                    sync_intervals=row.get('cSyncIntervals', 60),
                    contact_person=row.get('cContactPerson', ''),
                    contact_phone=row.get('cContactPhone', ''),
                    contact_email=row.get('cContactEmail', ''),
                    address=row.get('cAddress', ''),
                    create_time=str(row.get('dCreateTime', '')),
                    update_time=str(row.get('dUpdateTime', '')),
                    create_user=row.get('cCreateUser', 'SYSTEM'),
                    update_user=row.get('cUpdateUser', 'SYSTEM'),
                    remark=row.get('cRemark', '')
                )
                organizations.append(org)
            
            return organizations
            
        except Exception as e:
            self.logger.error(f"获取机构配置失败: {e}")
            return []
    
    def get_organization_by_code(self, org_code: str) -> Optional[OrganizationConfig]:
        """根据机构编码获取配置"""
        try:
            sql = """
            SELECT 
                id, cOrgCode, cOrgName, cOrgType, cStatus,
                cTianjianMicCode, cTianjianMiscId, cTianjianApiKey, cTianjianBaseUrl,
                cDbHost, cDbPort, cDbName, cDbUser, cDbPassword, cDbDriver,
                cShopCode, cDefaultDeptCode, cSyncEnabled, cSyncIntervals,
                cContactPerson, cContactPhone, cContactEmail, cAddress,
                dCreateTime, dUpdateTime, cCreateUser, cUpdateUser, cRemark
            FROM T_Organization_Config 
            WHERE cOrgCode = ?
            """
            
            result = self.db_service.connection_manager.execute_query_with_cache(
                self.db_service.connection_string, sql, (org_code,),
                cache_key=f'organization_{org_code}', use_cache=True
            )
            
            if result:
                row = result[0]
                return OrganizationConfig(
                    id=row.get('id', 0),
                    org_code=row.get('cOrgCode', ''),
                    org_name=row.get('cOrgName', ''),
                    org_type=row.get('cOrgType', 'HOSPITAL'),
                    status=row.get('cStatus', '1'),
                    tianjian_mic_code=row.get('cTianjianMicCode', ''),
                    tianjian_misc_id=row.get('cTianjianMiscId', ''),
                    tianjian_api_key=row.get('cTianjianApiKey', ''),
                    tianjian_base_url=row.get('cTianjianBaseUrl', 'http://**************:9300'),
                    db_host=row.get('cDbHost', ''),
                    db_port=row.get('cDbPort', 1433),
                    db_name=row.get('cDbName', ''),
                    db_user=row.get('cDbUser', ''),
                    db_password=row.get('cDbPassword', ''),
                    db_driver=row.get('cDbDriver', 'ODBC Driver 17 for SQL Server'),
                    shop_code=row.get('cShopCode', ''),
                    default_dept_code=row.get('cDefaultDeptCode', ''),
                    sync_enabled=row.get('cSyncEnabled', '1'),
                    sync_intervals=row.get('cSyncIntervals', 60),
                    contact_person=row.get('cContactPerson', ''),
                    contact_phone=row.get('cContactPhone', ''),
                    contact_email=row.get('cContactEmail', ''),
                    address=row.get('cAddress', ''),
                    create_time=str(row.get('dCreateTime', '')),
                    update_time=str(row.get('dUpdateTime', '')),
                    create_user=row.get('cCreateUser', 'SYSTEM'),
                    update_user=row.get('cUpdateUser', 'SYSTEM'),
                    remark=row.get('cRemark', '')
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取机构配置失败: {e}")
            return None
    
    def save_organization(self, org: OrganizationConfig) -> Tuple[bool, str]:
        """保存机构配置"""
        try:
            if org.id > 0:
                # 更新现有记录
                sql = """
                UPDATE T_Organization_Config SET
                    cOrgName = ?, cOrgType = ?, cStatus = ?,
                    cTianjianMicCode = ?, cTianjianMiscId = ?, cTianjianApiKey = ?, cTianjianBaseUrl = ?,
                    cDbHost = ?, cDbPort = ?, cDbName = ?, cDbUser = ?, cDbPassword = ?, cDbDriver = ?,
                    cShopCode = ?, cDefaultDeptCode = ?, cSyncEnabled = ?, cSyncIntervals = ?,
                    cContactPerson = ?, cContactPhone = ?, cContactEmail = ?, cAddress = ?,
                    dUpdateTime = GETDATE(), cUpdateUser = ?, cRemark = ?
                WHERE id = ?
                """
                
                params = (
                    org.org_name, org.org_type, org.status,
                    org.tianjian_mic_code, org.tianjian_misc_id, org.tianjian_api_key, org.tianjian_base_url,
                    org.db_host, org.db_port, org.db_name, org.db_user, org.db_password, org.db_driver,
                    org.shop_code, org.default_dept_code, org.sync_enabled, org.sync_intervals,
                    org.contact_person, org.contact_phone, org.contact_email, org.address,
                    org.update_user, org.remark, org.id
                )
            else:
                # 插入新记录
                sql = """
                INSERT INTO T_Organization_Config (
                    cOrgCode, cOrgName, cOrgType, cStatus,
                    cTianjianMicCode, cTianjianMiscId, cTianjianApiKey, cTianjianBaseUrl,
                    cDbHost, cDbPort, cDbName, cDbUser, cDbPassword, cDbDriver,
                    cShopCode, cDefaultDeptCode, cSyncEnabled, cSyncIntervals,
                    cContactPerson, cContactPhone, cContactEmail, cAddress,
                    cCreateUser, cUpdateUser, cRemark
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                params = (
                    org.org_code, org.org_name, org.org_type, org.status,
                    org.tianjian_mic_code, org.tianjian_misc_id, org.tianjian_api_key, org.tianjian_base_url,
                    org.db_host, org.db_port, org.db_name, org.db_user, org.db_password, org.db_driver,
                    org.shop_code, org.default_dept_code, org.sync_enabled, org.sync_intervals,
                    org.contact_person, org.contact_phone, org.contact_email, org.address,
                    org.create_user, org.update_user, org.remark
                )
            
            self.db_service.connection_manager.execute_non_query(
                self.db_service.connection_string, sql, params
            )
            
            return True, "保存成功"
            
        except Exception as e:
            error_msg = f"保存机构配置失败: {e}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def delete_organization(self, org_code: str) -> Tuple[bool, str]:
        """删除机构配置"""
        try:
            # 检查是否有关联数据
            check_sql = "SELECT COUNT(*) as count FROM T_Organization_Interface_Config WHERE cOrgCode = ?"
            result = self.db_service.connection_manager.execute_query_with_cache(
                self.db_service.connection_string, check_sql, (org_code,),
                cache_key=f'check_org_{org_code}', use_cache=False
            )
            
            if result and result[0]['count'] > 0:
                return False, "该机构存在关联的接口配置，无法删除"
            
            # 删除机构配置
            sql = "DELETE FROM T_Organization_Config WHERE cOrgCode = ?"
            self.db_service.connection_manager.execute_non_query(
                self.db_service.connection_string, sql, (org_code,)
            )
            
            return True, "删除成功"
            
        except Exception as e:
            error_msg = f"删除机构配置失败: {e}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def test_organization_connection(self, org: OrganizationConfig) -> Tuple[bool, str]:
        """测试机构连接"""
        try:
            # 测试数据库连接
            connection_string = org.get_db_connection_string()
            test_db_service = create_optimized_db_service(connection_string)
            
            # 执行测试查询
            test_sql = "SELECT COUNT(*) as count FROM T_Register_Main"
            result = test_db_service.connection_manager.execute_query_with_cache(
                connection_string, test_sql, 
                cache_key='test_connection', use_cache=False
            )
            
            if result:
                count = result[0]['count']
                return True, f"连接成功，共有 {count} 条体检记录"
            else:
                return False, "连接失败，无法获取数据"
                
        except Exception as e:
            return False, f"连接测试失败: {e}"


# 全局服务实例
_org_config_service = None

def get_organization_config_service() -> OrganizationConfigService:
    """获取机构配置服务实例（单例模式）"""
    global _org_config_service
    if _org_config_service is None:
        _org_config_service = OrganizationConfigService()
    return _org_config_service
