from api_config_manager import get_tianjian_base_url
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机构配置管理窗口
独立的机构配置管理界面，连接到中心库
"""

import sys
import traceback
from datetime import datetime
from typing import Optional

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QTableWidget, QTableWidgetItem, QGroupBox, QGridLayout,
    QLabel, QPushButton, QLineEdit, QComboBox, QTextEdit,
    QMessageBox, QHeaderView, QSplitter, QDateEdit, QStatusBar
)
from PySide6.QtCore import Qt, QDate, QTimer
from PySide6.QtGui import QFont, QIcon

from center_organization_service import get_center_organization_service, CenterOrganizationConfig


class OrganizationConfigWindow(QMainWindow):
    """机构配置管理主窗口"""
    
    def __init__(self):
        super().__init__()
        self.org_service = get_center_organization_service()
        self.current_org = None
        self.setup_ui()
        self.load_organizations()
        
        # 设置定时器用于状态更新
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(30000)  # 30秒更新一次
    
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("天健云机构配置管理系统")
        # 设置窗口大小为800x400，在屏幕中间打开
        self.resize(800, 400)
        self.center_window()

    def center_window(self):
        """将窗口居中显示"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
        
        # 设置窗口图标和样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #4CAF50;
                border: none;
                color: white;
                padding: 8px 16px;
                text-align: center;
                font-size: 14px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧：机构列表
        left_widget = self.create_organization_list()
        splitter.addWidget(left_widget)
        
        # 右侧：机构配置详情
        right_widget = self.create_organization_detail()
        splitter.addWidget(right_widget)
        
        # 设置分割器比例
        splitter.setSizes([400, 1000])
        
        # 状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪 - 连接到中心库(81.70.17.88/Examdb)")
        
        # 菜单栏
        self.create_menu_bar()
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')
        
        refresh_action = file_menu.addAction('刷新(&R)')
        refresh_action.setShortcut('F5')
        refresh_action.triggered.connect(self.load_organizations)
        
        file_menu.addSeparator()
        
        exit_action = file_menu.addAction('退出(&X)')
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        
        # 编辑菜单
        edit_menu = menubar.addMenu('编辑(&E)')
        
        add_action = edit_menu.addAction('新增机构(&N)')
        add_action.setShortcut('Ctrl+N')
        add_action.triggered.connect(self.add_organization)
        
        save_action = edit_menu.addAction('保存(&S)')
        save_action.setShortcut('Ctrl+S')
        save_action.triggered.connect(self.save_organization)
        
        delete_action = edit_menu.addAction('删除(&D)')
        delete_action.setShortcut('Delete')
        delete_action.triggered.connect(self.delete_organization)
        
        # 工具菜单
        tools_menu = menubar.addMenu('工具(&T)')
        
        test_action = tools_menu.addAction('测试连接(&T)')
        test_action.triggered.connect(self.test_connection)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')
        
        about_action = help_menu.addAction('关于(&A)')
        about_action.triggered.connect(self.show_about)
    
    def create_organization_list(self) -> QWidget:
        """创建机构列表部件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 标题和工具栏
        title_layout = QHBoxLayout()
        title_label = QLabel("机构列表")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        # 工具按钮
        add_btn = QPushButton("新增")
        add_btn.clicked.connect(self.add_organization)
        title_layout.addWidget(add_btn)

        edit_btn = QPushButton("修改")
        edit_btn.clicked.connect(self.edit_organization)
        title_layout.addWidget(edit_btn)

        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.load_organizations)
        title_layout.addWidget(refresh_btn)
        
        layout.addLayout(title_layout)
        
        # 机构列表表格
        self.org_table = QTableWidget()
        self.org_table.setColumnCount(4)
        self.org_table.setHorizontalHeaderLabels(["机构编码", "机构名称", "类型", "状态"])
        self.org_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.org_table.setAlternatingRowColors(True)
        self.org_table.itemSelectionChanged.connect(self.on_org_selected)
        
        # 设置列宽
        header = self.org_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        
        layout.addWidget(self.org_table)
        
        return widget
    
    def create_organization_detail(self) -> QWidget:
        """创建机构配置详情部件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 标题
        title_label = QLabel("机构配置详情")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(title_label)
        
        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QGridLayout(basic_group)
        
        basic_layout.addWidget(QLabel("机构编码:"), 0, 0)
        self.org_code_edit = QLineEdit()
        basic_layout.addWidget(self.org_code_edit, 0, 1)
        
        basic_layout.addWidget(QLabel("机构名称:"), 0, 2)
        self.org_name_edit = QLineEdit()
        basic_layout.addWidget(self.org_name_edit, 0, 3)
        
        basic_layout.addWidget(QLabel("机构类型:"), 1, 0)
        self.org_type_combo = QComboBox()
        self.org_type_combo.addItems(["HOSPITAL", "CLINIC", "CENTER", "BRANCH"])
        basic_layout.addWidget(self.org_type_combo, 1, 1)
        
        basic_layout.addWidget(QLabel("状态:"), 1, 2)
        self.status_combo = QComboBox()
        self.status_combo.addItems(["1-启用", "0-停用"])
        basic_layout.addWidget(self.status_combo, 1, 3)
        
        basic_layout.addWidget(QLabel("门店编码:"), 2, 0)
        self.shop_code_edit = QLineEdit()
        basic_layout.addWidget(self.shop_code_edit, 2, 1)
        
        layout.addWidget(basic_group)
        
        # 天健云API配置组
        api_group = QGroupBox("天健云API配置")
        api_layout = QGridLayout(api_group)
        
        api_layout.addWidget(QLabel("机构代码(mic-code):"), 0, 0)
        self.mic_code_edit = QLineEdit()
        api_layout.addWidget(self.mic_code_edit, 0, 1)
        
        api_layout.addWidget(QLabel("系统ID(misc-id):"), 0, 2)
        self.misc_id_edit = QLineEdit()
        api_layout.addWidget(self.misc_id_edit, 0, 3)
        
        api_layout.addWidget(QLabel("API密钥:"), 1, 0)
        self.api_key_edit = QLineEdit()
        self.api_key_edit.setEchoMode(QLineEdit.Password)
        api_layout.addWidget(self.api_key_edit, 1, 1, 1, 3)
        
        api_layout.addWidget(QLabel("API地址:"), 2, 0)
        self.api_url_edit = QLineEdit()
        self.api_url_edit.setText(get_tianjian_base_url())
        api_layout.addWidget(self.api_url_edit, 2, 1, 1, 3)
        
        layout.addWidget(api_group)
        
        # 数据库配置组
        db_group = QGroupBox("数据库配置")
        db_layout = QGridLayout(db_group)
        
        db_layout.addWidget(QLabel("服务器:"), 0, 0)
        self.db_host_edit = QLineEdit()
        db_layout.addWidget(self.db_host_edit, 0, 1)
        
        db_layout.addWidget(QLabel("端口:"), 0, 2)
        self.db_port_edit = QLineEdit()
        self.db_port_edit.setText("1433")
        db_layout.addWidget(self.db_port_edit, 0, 3)
        
        db_layout.addWidget(QLabel("数据库:"), 1, 0)
        self.db_name_edit = QLineEdit()
        db_layout.addWidget(self.db_name_edit, 1, 1)
        
        db_layout.addWidget(QLabel("用户名:"), 1, 2)
        self.db_user_edit = QLineEdit()
        db_layout.addWidget(self.db_user_edit, 1, 3)
        
        db_layout.addWidget(QLabel("密码:"), 2, 0)
        self.db_password_edit = QLineEdit()
        self.db_password_edit.setEchoMode(QLineEdit.Password)
        db_layout.addWidget(self.db_password_edit, 2, 1, 1, 3)
        
        layout.addWidget(db_group)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        save_btn = QPushButton("保存")
        save_btn.clicked.connect(self.save_organization)
        button_layout.addWidget(save_btn)
        
        test_btn = QPushButton("测试连接")
        test_btn.clicked.connect(self.test_connection)
        button_layout.addWidget(test_btn)
        
        delete_btn = QPushButton("删除")
        delete_btn.setStyleSheet("QPushButton { background-color: #f44336; }")
        delete_btn.clicked.connect(self.delete_organization)
        button_layout.addWidget(delete_btn)
        
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        layout.addStretch()
        
        return widget


    def load_organizations(self):
        """加载机构列表"""
        try:
            organizations = self.org_service.get_all_organizations()

            self.org_table.setRowCount(len(organizations))

            for i, org in enumerate(organizations):
                self.org_table.setItem(i, 0, QTableWidgetItem(org.org_code))
                self.org_table.setItem(i, 1, QTableWidgetItem(org.org_name))
                self.org_table.setItem(i, 2, QTableWidgetItem(org.org_type))
                status_text = "启用" if org.status == "1" else "停用"
                self.org_table.setItem(i, 3, QTableWidgetItem(status_text))

                # 存储完整的机构对象
                self.org_table.item(i, 0).setData(Qt.UserRole, org)

            self.status_bar.showMessage(f"已加载 {len(organizations)} 个机构配置")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载机构列表失败: {e}")
            self.status_bar.showMessage("加载机构列表失败")

    def on_org_selected(self):
        """机构选择事件"""
        current_row = self.org_table.currentRow()
        if current_row >= 0:
            item = self.org_table.item(current_row, 0)
            if item:
                org = item.data(Qt.UserRole)
                if org:
                    self.load_organization_detail(org)

    def load_organization_detail(self, org: CenterOrganizationConfig):
        """加载机构详情到表单"""
        self.current_org = org

        # 基本信息
        self.org_code_edit.setText(org.org_code)
        self.org_name_edit.setText(org.org_name)
        self.org_type_combo.setCurrentText(org.org_type)
        self.status_combo.setCurrentText(f"{org.status}-{'启用' if org.status == '1' else '停用'}")
        self.shop_code_edit.setText(org.shop_code)

        # 天健云API配置
        self.mic_code_edit.setText(org.tianjian_mic_code)
        self.misc_id_edit.setText(org.tianjian_misc_id)
        self.api_key_edit.setText(org.tianjian_api_key)
        self.api_url_edit.setText(org.tianjian_base_url)

        # 数据库配置
        self.db_host_edit.setText(org.db_host)
        self.db_port_edit.setText(str(org.db_port))
        self.db_name_edit.setText(org.db_name)
        self.db_user_edit.setText(org.db_user)
        self.db_password_edit.setText(org.db_password)

        # 机构编码字段始终可编辑
        self.org_code_edit.setReadOnly(False)

        self.status_bar.showMessage(f"已加载机构: {org.org_name}")

    def add_organization(self):
        """新增机构"""
        self.current_org = CenterOrganizationConfig()
        self.clear_form()
        self.org_code_edit.setReadOnly(False)
        self.status_bar.showMessage("新增机构模式")

    def edit_organization(self):
        """修改机构"""
        # 获取当前选中的机构
        current_row = self.org_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请先选择要修改的机构")
            return

        # 获取选中机构的数据
        item = self.org_table.item(current_row, 0)
        if item:
            org = item.data(Qt.UserRole)
            if org:
                self.load_organization_detail(org)
                self.org_code_edit.setReadOnly(False)  # 修改模式下机构编码也可以编辑
                self.status_bar.showMessage(f"修改机构模式: {org.org_name}")
            else:
                QMessageBox.warning(self, "警告", "无法获取机构数据")
        else:
            QMessageBox.warning(self, "警告", "无法获取机构数据")

    def clear_form(self):
        """清空表单"""
        self.org_code_edit.clear()
        self.org_name_edit.clear()
        self.org_type_combo.setCurrentIndex(0)
        self.status_combo.setCurrentIndex(0)
        self.shop_code_edit.clear()

        self.mic_code_edit.clear()
        self.misc_id_edit.clear()
        self.api_key_edit.clear()
        self.api_url_edit.setText(get_tianjian_base_url())

        self.db_host_edit.clear()
        self.db_port_edit.setText("1433")
        self.db_name_edit.clear()
        self.db_user_edit.clear()
        self.db_password_edit.clear()

    def save_organization(self):
        """保存机构配置"""
        try:
            if not self.current_org:
                self.current_org = CenterOrganizationConfig()

            # 验证必填字段
            if not self.org_code_edit.text().strip():
                QMessageBox.warning(self, "警告", "机构编码不能为空")
                return

            if not self.org_name_edit.text().strip():
                QMessageBox.warning(self, "警告", "机构名称不能为空")
                return

            # 检查机构编码是否重复（仅在新增或修改编码时检查）
            new_org_code = self.org_code_edit.text().strip()
            if not self.current_org.id or self.current_org.org_code != new_org_code:
                existing_org = self.org_service.get_organization_by_code(new_org_code)
                if existing_org and existing_org.id != self.current_org.id:
                    QMessageBox.warning(self, "警告", f"机构编码 '{new_org_code}' 已存在，请使用其他编码")
                    return

            # 更新机构对象
            self.current_org.org_code = self.org_code_edit.text().strip()
            self.current_org.org_name = self.org_name_edit.text().strip()
            self.current_org.org_type = self.org_type_combo.currentText()
            self.current_org.status = self.status_combo.currentText().split('-')[0]
            self.current_org.shop_code = self.shop_code_edit.text().strip()

            # 天健云API配置
            self.current_org.tianjian_mic_code = self.mic_code_edit.text().strip()
            self.current_org.tianjian_misc_id = self.misc_id_edit.text().strip()
            self.current_org.tianjian_api_key = self.api_key_edit.text().strip()
            self.current_org.tianjian_base_url = self.api_url_edit.text().strip()

            # 数据库配置
            self.current_org.db_host = self.db_host_edit.text().strip()
            self.current_org.db_port = int(self.db_port_edit.text() or 1433)
            self.current_org.db_name = self.db_name_edit.text().strip()
            self.current_org.db_user = self.db_user_edit.text().strip()
            self.current_org.db_password = self.db_password_edit.text().strip()

            # 保存到数据库
            success, message = self.org_service.save_organization(self.current_org)

            if success:
                QMessageBox.information(self, "成功", "机构配置保存成功")
                self.load_organizations()  # 刷新列表
                self.status_bar.showMessage("机构配置保存成功")
            else:
                QMessageBox.warning(self, "错误", f"保存失败: {message}")
                self.status_bar.showMessage(f"保存失败: {message}")

        except Exception as e:
            error_msg = f"保存机构配置时发生错误: {e}"
            QMessageBox.critical(self, "错误", error_msg)
            self.status_bar.showMessage("保存失败")

    def test_connection(self):
        """测试连接"""
        if not self.current_org:
            QMessageBox.warning(self, "警告", "请先选择或创建机构配置")
            return

        try:
            # 创建临时机构对象用于测试
            test_org = CenterOrganizationConfig()
            test_org.db_host = self.db_host_edit.text().strip()
            test_org.db_port = int(self.db_port_edit.text() or 1433)
            test_org.db_name = self.db_name_edit.text().strip()
            test_org.db_user = self.db_user_edit.text().strip()
            test_org.db_password = self.db_password_edit.text().strip()
            test_org.db_driver = "ODBC Driver 17 for SQL Server"

            self.status_bar.showMessage("正在测试连接...")

            success, message = self.org_service.test_organization_connection(test_org)

            if success:
                QMessageBox.information(self, "测试成功", f"连接测试成功\n{message}")
                self.status_bar.showMessage(f"连接测试成功: {message}")
            else:
                QMessageBox.warning(self, "测试失败", f"连接测试失败\n{message}")
                self.status_bar.showMessage(f"连接测试失败: {message}")

        except Exception as e:
            error_msg = f"连接测试时发生错误: {e}"
            QMessageBox.critical(self, "错误", error_msg)
            self.status_bar.showMessage("连接测试失败")

    def delete_organization(self):
        """删除机构配置"""
        if not self.current_org or not self.current_org.org_code:
            QMessageBox.warning(self, "警告", "请先选择要删除的机构")
            return

        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除机构 '{self.current_org.org_name}' 吗？\n此操作不可撤销！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                success, message = self.org_service.delete_organization(self.current_org.org_code)

                if success:
                    QMessageBox.information(self, "成功", "机构配置删除成功")
                    self.load_organizations()  # 刷新列表
                    self.clear_form()  # 清空表单
                    self.current_org = None
                    self.status_bar.showMessage("机构配置删除成功")
                else:
                    QMessageBox.warning(self, "错误", f"删除失败: {message}")
                    self.status_bar.showMessage(f"删除失败: {message}")

            except Exception as e:
                error_msg = f"删除机构配置时发生错误: {e}"
                QMessageBox.critical(self, "错误", error_msg)
                self.status_bar.showMessage("删除失败")

    def update_status(self):
        """更新状态栏信息"""
        try:
            org_count = self.org_table.rowCount()
            current_time = datetime.now().strftime("%H:%M:%S")
            self.status_bar.showMessage(f"机构数量: {org_count} | 中心库连接正常 | {current_time}")
        except:
            pass

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于",
            "天健云机构配置管理系统\n\n"
            "版本: v1.0.0\n"
            "日期: 2025-07-20\n\n"
            "连接到中心库: 81.70.17.88/Examdb\n"
            "用于管理多个体检机构的配置信息")

    def closeEvent(self, event):
        """窗口关闭事件"""
        reply = QMessageBox.question(
            self, "确认退出",
            "确定要退出机构配置管理系统吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            event.accept()
        else:
            event.ignore()


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("天健云机构配置管理系统")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("天健云")

    try:
        # 创建主窗口
        window = OrganizationConfigWindow()
        window.show()

        # 运行应用程序
        sys.exit(app.exec())

    except Exception as e:
        QMessageBox.critical(None, "启动失败", f"应用程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
