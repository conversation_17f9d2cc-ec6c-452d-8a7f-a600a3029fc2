#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高性能数据库连接池管理器
解决数据库连接效率和N+1查询问题
"""

import threading
import time
from typing import Dict, List, Any, Optional, Generator, Tuple
from contextlib import contextmanager
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass

from sqlalchemy import create_engine, text, event
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.engine import Engine
from config import Config


@dataclass
class PerformanceMetrics:
    """性能指标"""
    total_queries: int = 0
    total_time: float = 0.0
    avg_time: float = 0.0
    slow_queries: int = 0
    connection_pool_hits: int = 0
    connection_pool_misses: int = 0
    cache_hits: int = 0
    cache_misses: int = 0


class ConnectionPoolManager:
    """高性能数据库连接池管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self._engines: Dict[str, Engine] = {}
        self._metrics = PerformanceMetrics()
        self._query_cache: Dict[str, Tuple[Any, datetime]] = {}
        self._cache_ttl = 300  # 5分钟缓存TTL
        self._lock = threading.Lock()
        self._initialized = True
        
        # 设置性能监控
        self._setup_performance_monitoring()
    
    def _setup_performance_monitoring(self):
        """设置性能监控"""
        @event.listens_for(Engine, "before_cursor_execute")
        def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            context._query_start_time = time.time()
        
        @event.listens_for(Engine, "after_cursor_execute")
        def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            total = time.time() - context._query_start_time
            
            with self._lock:
                self._metrics.total_queries += 1
                self._metrics.total_time += total
                self._metrics.avg_time = self._metrics.total_time / self._metrics.total_queries
                
                if total > 1.0:  # 超过1秒认为是慢查询
                    self._metrics.slow_queries += 1
    
    def get_engine(self, connection_string: str, engine_name: str = "default") -> Engine:
        """获取数据库引擎（带连接池）"""
        # 使用连接字符串的哈希作为引擎的唯一标识，避免不同连接复用同一引擎
        import hashlib
        connection_hash = hashlib.md5(connection_string.encode()).hexdigest()[:8]
        unique_engine_name = f"{engine_name}_{connection_hash}"
        
        if unique_engine_name not in self._engines:
            with self._lock:
                if unique_engine_name not in self._engines:
                    self._engines[unique_engine_name] = self._create_optimized_engine(connection_string)
        
        return self._engines[unique_engine_name]
    
    def _create_optimized_engine(self, connection_string: str) -> Engine:
        """创建优化的数据库引擎"""
        # 解析连接字符串，确定是否为SQL Server
        if "mssql" not in connection_string.lower() and "pyodbc" not in connection_string.lower():
            # 转换为SQLAlchemy格式
            if connection_string.startswith("DRIVER="):
                connection_string = f"mssql+pyodbc:///?odbc_connect={connection_string}"
        
        engine = create_engine(
            connection_string,
            poolclass=QueuePool,
            pool_size=20,                    # 增加连接池大小
            max_overflow=40,                 # 增加溢出连接数
            pool_pre_ping=True,              # 连接前测试
            pool_recycle=1800,               # 30分钟回收连接
            pool_timeout=30,                 # 获取连接超时时间
            echo=False,                      # 生产环境关闭SQL回显
            isolation_level="READ_COMMITTED", # 设置隔离级别
            connect_args={
                "timeout": 30,               # 连接超时
                "autocommit": True           # 自动提交
            }
        )
        
        return engine
    
    @contextmanager
    def get_connection(self, connection_string: str, engine_name: str = "default"):
        """获取数据库连接的上下文管理器"""
        engine = self.get_engine(connection_string, engine_name)
        connection = None
        
        try:
            connection = engine.connect()
            with self._lock:
                self._metrics.connection_pool_hits += 1
            yield connection
        except Exception as e:
            with self._lock:
                self._metrics.connection_pool_misses += 1
            raise e
        finally:
            if connection:
                connection.close()
    
    def execute_query_with_cache(
        self, 
        connection_string: str, 
        sql: str, 
        params: Dict[str, Any] = None,
        cache_key: str = None,
        use_cache: bool = True
    ) -> List[Dict[str, Any]]:
        """执行查询（带缓存）"""
        
        # 检查缓存
        if use_cache and cache_key:
            cached_result = self._get_from_cache(cache_key)
            if cached_result is not None:
                with self._lock:
                    self._metrics.cache_hits += 1
                return cached_result
        
        # 执行查询
        with self.get_connection(connection_string) as conn:
            try:
                if params:
                    result = conn.execute(text(sql), params)
                else:
                    result = conn.execute(text(sql))
                
                # 转换结果为字典列表
                rows = []
                for row in result:
                    rows.append(dict(row._mapping))
                
                # 缓存结果
                if use_cache and cache_key:
                    self._set_cache(cache_key, rows)
                    with self._lock:
                        self._metrics.cache_misses += 1
                
                return rows
                
            except SQLAlchemyError as e:
                logging.error(f"数据库查询失败: {e}")
                raise
    
    def execute_batch_query(
        self, 
        connection_string: str, 
        sql: str, 
        batch_params: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """批量执行查询"""
        with self.get_connection(connection_string) as conn:
            try:
                results = []
                for params in batch_params:
                    result = conn.execute(text(sql), params)
                    for row in result:
                        results.append(dict(row._mapping))
                return results
            except SQLAlchemyError as e:
                logging.error(f"批量查询失败: {e}")
                raise
    
    def _get_from_cache(self, cache_key: str) -> Optional[List[Dict[str, Any]]]:
        """从缓存获取数据"""
        with self._lock:
            if cache_key in self._query_cache:
                data, timestamp = self._query_cache[cache_key]
                if datetime.now() - timestamp < timedelta(seconds=self._cache_ttl):
                    return data
                else:
                    # 缓存过期，删除
                    del self._query_cache[cache_key]
        return None
    
    def _set_cache(self, cache_key: str, data: List[Dict[str, Any]]):
        """设置缓存"""
        with self._lock:
            self._query_cache[cache_key] = (data, datetime.now())
            
            # 清理过期缓存
            current_time = datetime.now()
            expired_keys = [
                key for key, (_, timestamp) in self._query_cache.items()
                if current_time - timestamp > timedelta(seconds=self._cache_ttl)
            ]
            for key in expired_keys:
                del self._query_cache[key]
    
    def get_performance_metrics(self) -> PerformanceMetrics:
        """获取性能指标"""
        with self._lock:
            return PerformanceMetrics(
                total_queries=self._metrics.total_queries,
                total_time=self._metrics.total_time,
                avg_time=self._metrics.avg_time,
                slow_queries=self._metrics.slow_queries,
                connection_pool_hits=self._metrics.connection_pool_hits,
                connection_pool_misses=self._metrics.connection_pool_misses,
                cache_hits=self._metrics.cache_hits,
                cache_misses=self._metrics.cache_misses
            )
    
    def clear_cache(self):
        """清空缓存"""
        with self._lock:
            self._query_cache.clear()
    
    def get_connection_pool_status(self) -> Dict[str, Any]:
        """获取连接池状态"""
        status = {}
        for name, engine in self._engines.items():
            pool = engine.pool
            status[name] = {
                "pool_size": pool.size(),
                "checked_in": pool.checkedin(),
                "checked_out": pool.checkedout(),
                "overflow": pool.overflow()
            }
        return status


class BatchProcessor:
    """自适应批量处理器"""
    
    def __init__(self, initial_batch_size: int = 100, min_batch_size: int = 10, max_batch_size: int = 500):
        self.batch_size = initial_batch_size
        self.min_batch_size = min_batch_size
        self.max_batch_size = max_batch_size
        self.success_rate = 1.0
        self.avg_response_time = 0.0
        self.adjustment_history = []
    
    def process_in_batches(
        self, 
        data: List[Any], 
        processor_func, 
        auto_adjust: bool = True
    ) -> Dict[str, Any]:
        """批量处理数据"""
        
        total_count = len(data)
        processed_count = 0
        success_count = 0
        failed_items = []
        start_time = time.time()
        
        print(f"[BATCH] 开始批量处理 {total_count} 条记录，批量大小: {self.batch_size}")
        
        # 分批处理
        for i in range(0, total_count, self.batch_size):
            batch = data[i:i + self.batch_size]
            batch_start_time = time.time()
            
            try:
                # 处理批次
                result = processor_func(batch)
                batch_time = time.time() - batch_start_time
                
                if result.get('success', False):
                    success_count += len(batch)
                    print(f"[OK] 批次 {i//self.batch_size + 1}: {len(batch)} 条记录处理成功，耗时 {batch_time:.2f}s")
                else:
                    failed_items.extend([item.get('id', f'item_{i+j}') for j, item in enumerate(batch)])
                    print(f"[FAIL] 批次 {i//self.batch_size + 1}: 处理失败")
                
                processed_count += len(batch)
                
                # 自适应调整批量大小
                if auto_adjust and processed_count > 0:
                    self._adjust_batch_size(success_count, processed_count, batch_time)
                
            except Exception as e:
                batch_time = time.time() - batch_start_time
                failed_items.extend([f'batch_{i//self.batch_size + 1}_item_{j}' for j in range(len(batch))])
                print(f"[ERROR] 批次 {i//self.batch_size + 1} 处理异常: {e}")
                
                # 出现异常时减小批量大小
                if auto_adjust:
                    self.batch_size = max(self.batch_size // 2, self.min_batch_size)
                    print(f"[ADJUST] 因异常调整批量大小为: {self.batch_size}")
        
        total_time = time.time() - start_time
        success_rate = (success_count / total_count) * 100 if total_count > 0 else 0
        
        result_summary = {
            'total_count': total_count,
            'success_count': success_count,
            'failed_count': total_count - success_count,
            'success_rate': success_rate,
            'total_time': total_time,
            'avg_time_per_item': total_time / total_count if total_count > 0 else 0,
            'final_batch_size': self.batch_size,
            'failed_items': failed_items
        }
        
        print(f"[SUMMARY] 批量处理完成:")
        print(f"  总数: {total_count}, 成功: {success_count}, 失败: {total_count - success_count}")
        print(f"  成功率: {success_rate:.1f}%, 总耗时: {total_time:.2f}s")
        print(f"  最终批量大小: {self.batch_size}")
        
        return result_summary
    
    def _adjust_batch_size(self, success_count: int, total_count: int, response_time: float):
        """自适应调整批量大小"""
        current_success_rate = success_count / total_count if total_count > 0 else 0
        
        # 更新历史平均值
        self.success_rate = (self.success_rate * 0.7) + (current_success_rate * 0.3)
        self.avg_response_time = (self.avg_response_time * 0.7) + (response_time * 0.3)
        
        old_batch_size = self.batch_size
        
        # 调整逻辑
        if self.success_rate > 0.95 and self.avg_response_time < 2.0:
            # 成功率高且响应快，增加批量大小
            self.batch_size = min(int(self.batch_size * 1.2), self.max_batch_size)
        elif self.success_rate < 0.8 or self.avg_response_time > 5.0:
            # 成功率低或响应慢，减少批量大小
            self.batch_size = max(int(self.batch_size * 0.8), self.min_batch_size)
        
        # 记录调整历史
        if self.batch_size != old_batch_size:
            self.adjustment_history.append({
                'timestamp': datetime.now(),
                'old_size': old_batch_size,
                'new_size': self.batch_size,
                'success_rate': self.success_rate,
                'response_time': self.avg_response_time,
                'reason': 'performance_based'
            })
            print(f"[ADJUST] 批量大小调整: {old_batch_size} -> {self.batch_size} "
                  f"(成功率: {self.success_rate:.1%}, 响应时间: {self.avg_response_time:.2f}s)")


class StreamProcessor:
    """流式数据处理器"""
    
    def __init__(self, connection_manager: ConnectionPoolManager):
        self.connection_manager = connection_manager
    
    def stream_query_results(
        self, 
        connection_string: str,
        base_sql: str, 
        batch_size: int = 1000,
        total_limit: int = None
    ) -> Generator[List[Dict[str, Any]], None, None]:
        """流式查询大数据集"""
        
        offset = 0
        total_fetched = 0
        
        print(f"[STREAM] 开始流式查询，批量大小: {batch_size}")
        
        while True:
            # 构建分页查询SQL
            if "ORDER BY" not in base_sql.upper():
                # 如果没有ORDER BY，添加一个默认排序
                paged_sql = f"""
                SELECT * FROM (
                    SELECT ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) as rn, sub.*
                    FROM ({base_sql}) sub
                ) paged
                WHERE rn BETWEEN {offset + 1} AND {offset + batch_size}
                """
            else:
                # 如果有ORDER BY，使用OFFSET和FETCH
                paged_sql = f"{base_sql} OFFSET {offset} ROWS FETCH NEXT {batch_size} ROWS ONLY"
            
            try:
                batch_data = self.connection_manager.execute_query_with_cache(
                    connection_string, 
                    paged_sql,
                    use_cache=False  # 流式查询不使用缓存
                )
                
                if not batch_data:
                    print(f"[STREAM] 流式查询完成，总共获取 {total_fetched} 条记录")
                    break
                
                total_fetched += len(batch_data)
                print(f"[STREAM] 获取批次数据: {len(batch_data)} 条 (总计: {total_fetched})")
                
                yield batch_data
                
                offset += batch_size
                
                # 检查总数限制
                if total_limit and total_fetched >= total_limit:
                    print(f"[STREAM] 达到总数限制 {total_limit}，停止查询")
                    break
                    
            except Exception as e:
                print(f"[ERROR] 流式查询异常: {e}")
                break


# 全局实例
connection_pool_manager = ConnectionPoolManager()
batch_processor = BatchProcessor()
stream_processor = StreamProcessor(connection_pool_manager)


def get_connection_manager() -> ConnectionPoolManager:
    """获取连接管理器实例"""
    return connection_pool_manager


def get_batch_processor() -> BatchProcessor:
    """获取批量处理器实例"""
    return batch_processor


def get_stream_processor() -> StreamProcessor:
    """获取流式处理器实例"""
    return stream_processor


# 使用示例
if __name__ == "__main__":
    # 连接字符串示例
    connection_string = Config.get_interface_db_connection_string()