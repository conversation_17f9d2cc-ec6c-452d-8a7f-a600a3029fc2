#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打印10号接口返回的完整报文
"""

import requests
import json
from datetime import datetime, timedelta

def print_full_response():
    """打印完整的10号接口响应报文"""
    print("10号接口完整报文展示")
    print("=" * 60)
    
    base_url = "http://localhost:5007"
    
    # 构建测试请求
    end_time = datetime.now()
    start_time = end_time - timedelta(days=7)
    
    request_data = {
        "start": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "peNo": "",
        "hospitalCode": ""
    }
    
    print("请求参数:")
    print(json.dumps(request_data, ensure_ascii=False, indent=2))
    print("\n" + "=" * 60)
    
    try:
        response = requests.post(
            f"{base_url}/dx/inter/batchGetPeInfo",
            json=request_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            # 只展示第一条记录的完整结构
            if result.get('data') and len(result['data']) > 0:
                # 构建展示用的响应（只包含第一条记录）
                display_response = {
                    "code": result.get('code'),
                    "msg": result.get('msg'),
                    "data": [result['data'][0]],  # 只取第一条记录
                    "reponseTime": result.get('reponseTime')
                }
                
                print("响应报文 (第一条记录):")
                print(json.dumps(display_response, ensure_ascii=False, indent=2))
                
                print(f"\n总记录数: {len(result.get('data', []))}")
                print("注意: 为了便于查看，这里只显示了第一条记录的完整结构")
                
            else:
                print("响应报文:")
                print(json.dumps(result, ensure_ascii=False, indent=2))
                print("注意: 返回数据为空")
                
        else:
            print(f"请求失败: HTTP {response.status_code}")
            print("响应内容:")
            print(response.text)
            
    except Exception as e:
        print(f"请求异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print_full_response()