#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复04-05号接口的HTTP日志记录功能
"""

import re
import os


def fix_interface_04():
    """修复04号接口的HTTP日志记录功能"""
    file_path = "interface_04_syncUser.py"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复请求部分
    old_request = '''        # 打印完整的HTTP请求报文到控制台
        print("=" * 80)
        print("【04号接口】HTTP请求报文")
        print("=" * 80)
        print(f"请求URL: {url}")
        print(f"请求方法: POST")
        print("请求头:")
        for key, value in headers.items():
            print(f"  {key}: {value}")
        print("请求体:")
        print(json.dumps(request_data, ensure_ascii=False, indent=2))
        print("=" * 80)'''
    
    new_request = '''        # 生成请求ID用于日志关联
        request_id = headers.get('x-request-id', str(uuid.uuid4()))
        
        # 打印完整的HTTP请求报文到控制台
        print("=" * 80)
        print("【04号接口】HTTP请求报文")
        print("=" * 80)
        print(f"请求URL: {url}")
        print(f"请求方法: POST")
        print("请求头:")
        for key, value in headers.items():
            print(f"  {key}: {value}")
        print("请求体:")
        print(json.dumps(request_data, ensure_ascii=False, indent=2))
        print("=" * 80)
        
        # 记录HTTP请求报文到日志文件
        self.http_logger.log_request(
            url=url,
            method="POST",
            headers=headers,
            request_data=request_data,
            request_id=request_id
        )'''
    
    content = content.replace(old_request, new_request)
    
    # 修复响应部分
    old_response = '''            # 打印完整的HTTP响应报文到控制台
            print("【04号接口】HTTP响应报文")
            print("=" * 80)
            print(f"响应状态: HTTP {response.status_code}")
            print("响应头:")
            for key, value in response.headers.items():
                print(f"  {key}: {value}")
            print("响应体:")

            # 尝试解析响应内容
            try:
                response_json = response.json()
                print(json.dumps(response_json, ensure_ascii=False, indent=2))
            except:
                print(response.text)
            print("=" * 80)'''
    
    new_response = '''            # 打印完整的HTTP响应报文到控制台
            print("【04号接口】HTTP响应报文")
            print("=" * 80)
            print(f"响应状态: HTTP {response.status_code}")
            print("响应头:")
            for key, value in response.headers.items():
                print(f"  {key}: {value}")
            print("响应体:")

            # 尝试解析响应内容
            response_data = None
            try:
                response_data = response.json()
                print(json.dumps(response_data, ensure_ascii=False, indent=2))
            except:
                response_data = response.text
                print(response_data)
            print("=" * 80)
            
            # 记录HTTP响应报文到日志文件
            self.http_logger.log_response(
                status_code=response.status_code,
                headers=dict(response.headers),
                response_data=response_data,
                request_id=request_id
            )'''
    
    content = content.replace(old_response, new_response)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ {file_path} HTTP日志记录功能修复完成")


def fix_interface_05():
    """修复05号接口的HTTP日志记录功能"""
    file_path = "interface_05_syncDept.py"
    
    # 添加导入
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    if 'from http_message_logger import get_http_logger' not in content:
        # 添加导入
        import_pattern = r'(from org_code_prefix_manager import create_org_prefix_manager\n)'
        replacement = r'\1from http_message_logger import get_http_logger\n'
        content = re.sub(import_pattern, replacement, content)
        
        # 添加初始化
        init_pattern = r'(self\.db_service = get_database_service\(\)\n\n        # 创建机构编码前缀管理器)'
        init_replacement = r'self.db_service = get_database_service()\n\n        # 创建HTTP报文日志记录器\n        self.http_logger = get_http_logger("05")\n\n        # 创建机构编码前缀管理器'
        content = re.sub(init_pattern, init_replacement, content)
    
    # 修复请求部分
    old_request = '''        # 打印完整的HTTP请求报文到控制台
        print("=" * 80)
        print("【05号接口】HTTP请求报文")
        print("=" * 80)
        print(f"请求URL: {url}")
        print(f"请求方法: POST")
        print("请求头:")
        for key, value in headers.items():
            print(f"  {key}: {value}")
        print("请求体:")
        print(json.dumps(request_data, ensure_ascii=False, indent=2))
        print("=" * 80)'''
    
    new_request = '''        # 生成请求ID用于日志关联
        request_id = headers.get('x-request-id', str(uuid.uuid4()))
        
        # 打印完整的HTTP请求报文到控制台
        print("=" * 80)
        print("【05号接口】HTTP请求报文")
        print("=" * 80)
        print(f"请求URL: {url}")
        print(f"请求方法: POST")
        print("请求头:")
        for key, value in headers.items():
            print(f"  {key}: {value}")
        print("请求体:")
        print(json.dumps(request_data, ensure_ascii=False, indent=2))
        print("=" * 80)
        
        # 记录HTTP请求报文到日志文件
        self.http_logger.log_request(
            url=url,
            method="POST",
            headers=headers,
            request_data=request_data,
            request_id=request_id
        )'''
    
    content = content.replace(old_request, new_request)
    
    # 修复响应部分
    old_response = '''            # 打印完整的HTTP响应报文到控制台
            print("【05号接口】HTTP响应报文")
            print("=" * 80)
            print(f"响应状态: HTTP {response.status_code}")
            print("响应头:")
            for key, value in response.headers.items():
                print(f"  {key}: {value}")
            print("响应体:")

            # 尝试解析响应内容
            try:
                response_json = response.json()
                print(json.dumps(response_json, ensure_ascii=False, indent=2))
            except:
                print(response.text)
            print("=" * 80)'''
    
    new_response = '''            # 打印完整的HTTP响应报文到控制台
            print("【05号接口】HTTP响应报文")
            print("=" * 80)
            print(f"响应状态: HTTP {response.status_code}")
            print("响应头:")
            for key, value in response.headers.items():
                print(f"  {key}: {value}")
            print("响应体:")

            # 尝试解析响应内容
            response_data = None
            try:
                response_data = response.json()
                print(json.dumps(response_data, ensure_ascii=False, indent=2))
            except:
                response_data = response.text
                print(response_data)
            print("=" * 80)
            
            # 记录HTTP响应报文到日志文件
            self.http_logger.log_response(
                status_code=response.status_code,
                headers=dict(response.headers),
                response_data=response_data,
                request_id=request_id
            )'''
    
    content = content.replace(old_response, new_response)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ {file_path} HTTP日志记录功能修复完成")


def main():
    """主函数"""
    print("开始修复04-05号接口的HTTP日志记录功能...")
    
    try:
        fix_interface_04()
        fix_interface_05()
        print("\n🎉 所有接口HTTP日志记录功能修复完成！")
        print("现在02-06号接口都支持HTTP报文日志记录到logs目录")
    except Exception as e:
        print(f"❌ 修复过程中出现错误: {e}")


if __name__ == '__main__':
    main()
