#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实天健云API测试
基于天健云接口文档进行真实API调用测试
"""

import hashlib
import json
import requests
import uuid
from datetime import datetime
from test_config import TestConfig

class RealTianjianAPITest:
    """真实天健云API测试类"""
    
    def __init__(self):
        self.api_config = TestConfig.API_CONFIG
        self.auth_config = TestConfig.API_AUTH_CONFIG
        self.base_url = self.api_config['base_url']
        
    def generate_timestamp(self):
        """生成时间戳 - YYYYmmDDHHMMSS格式"""
        return datetime.now().strftime('%Y%m%d%H%M%S')
    
    def generate_nonce(self):
        """生成UUID"""
        return str(uuid.uuid4())
    
    def generate_signature(self, timestamp):
        """生成MD5签名 - MD5(key+timestamp)"""
        sign_string = self.auth_config['api_key'] + timestamp
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    def create_headers(self):
        """创建请求头"""
        timestamp = self.generate_timestamp()
        nonce = self.generate_nonce()
        signature = self.generate_signature(timestamp)
        
        headers = {
            'Content-Type': 'application/json',
            'sign': signature,
            'timestamp': timestamp,
            'nonce': nonce,
            'mic-code': self.auth_config['mic_code'],
            'misc-id': self.auth_config['misc_id']
        }
        
        return headers, timestamp, nonce, signature
    
    def test_pe_info_api(self, pe_data):
        """测试体检信息传输接口"""
        print("\n" + "="*60)
        print("测试体检信息传输接口")
        print("="*60)
        
        url = f"{self.base_url}/dx/inter/sendPeInfo"
        headers, timestamp, nonce, signature = self.create_headers()
        
        print(f"接口地址: {url}")
        print(f"时间戳: {timestamp}")
        print(f"随机数: {nonce}")
        print(f"签名: {signature}")
        print(f"机构代码: {self.auth_config['mic_code']}")
        print(f"系统ID: {self.auth_config['misc_id']}")
        
        try:
            response = requests.post(
                url=url,
                headers=headers,
                json=pe_data,
                timeout=30
            )
            
            print(f"\n响应状态码: {response.status_code}")
            print(f"响应时间: {response.elapsed.total_seconds():.3f}s")
            
            # 打印响应内容
            try:
                response_json = response.json()
                print(f"响应JSON: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
                return True, response_json
            except json.JSONDecodeError:
                print(f"响应文本: {response.text[:500]}")
                return False, response.text
                
        except Exception as e:
            print(f"请求失败: {e}")
            return False, str(e)
    
    def test_dict_sync_api(self):
        """测试字典同步接口"""
        print("\n" + "="*60)
        print("测试字典同步接口")
        print("="*60)
        
        url = f"{self.base_url}/dx/inter/syncDict"
        headers, timestamp, nonce, signature = self.create_headers()
        
        # 同步字典信息的请求体（简化测试数据）
        query_data = [
            {
                "id": "VIP001",
                "name": "普通会员",
                "type": "OPEVIP",
                "hospitalCode": ""
            }
        ]
        
        print(f"接口地址: {url}")
        print(f"同步数据: {len(query_data)} 条字典记录")
        
        try:
            response = requests.post(
                url=url,
                headers=headers,
                json=query_data,
                timeout=30
            )
            
            print(f"\n响应状态码: {response.status_code}")
            print(f"响应时间: {response.elapsed.total_seconds():.3f}s")
            
            # 打印响应内容
            try:
                response_json = response.json()
                print(f"响应JSON: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
                return True, response_json
            except json.JSONDecodeError:
                print(f"响应文本: {response.text[:500]}")
                return False, response.text
                
        except Exception as e:
            print(f"请求失败: {e}")
            return False, str(e)

def create_test_pe_data():
    """创建测试体检数据"""
    return {
        "peUserInfo": {
            "archiveNo": "TEST2025001",
            "name": "测试用户",
            "icCode": "510823198705127845",
            "sex": {
                "code": "1",
                "name": "男"
            },
            "birthday": "19870512",
            "peno": "TEST2025001",
            "peDate": "2025-07-10 15:30:00",
            "phone": "13800138000",
            "ms": {
                "code": "married",
                "name": "已婚"
            },
            "pregnantState": {
                "code": "unconception",
                "name": "未怀孕"
            },
            "vipLevel": {
                "code": "VIP001",
                "name": "普通会员"
            },
            "medicalType": {
                "code": "NORMAL",
                "name": "常规体检"
            },
            "isGroup": False,
            "company": "",
            "workDept": "",
            "teamNo": "",
            "professional": "软件工程师",
            "workAge": "5",
            "peStates": {
                "code": "1",
                "name": "登记完成"
            },
            "deptCount": 5,
            "age": 37,
            "deptFinishTime": "2025-07-10 16:00:00",
            "firstCheckFinishTime": "2025-07-10 16:30:00",
            "firstCheckFinishDoctor": {
                "code": "DOC001",
                "name": "测试医生"
            },
            "mainCheckFinishTime": "2025-07-10 17:00:00",
            "mainCheckFinishDoctor": {
                "code": "DOC002",
                "name": "主检医生"
            },
            "forbidGoCheck": False,
            "reportPrint": False,
            "reportGot": False,
            "replacementInspectionMark": False,
            "applyItemList": ["ITEM001", "ITEM002", "ITEM003"],
            "currentNodeType": 1,
            "pePackage": {
                "code": "PKG001",
                "name": "基础体检套餐"
            }
        }
    }

def main():
    """主测试函数"""
    print("="*60)
    print("真实天健云API测试")
    print("="*60)
    
    # 检查配置
    auth_config = TestConfig.API_AUTH_CONFIG
    if not auth_config['api_key']:
        print("[FAIL] API密钥未配置")
        return
    
    print(f"机构代码: {auth_config['mic_code']}")
    print(f"系统ID: {auth_config['misc_id']}")
    print(f"API密钥: {'*' * len(auth_config['api_key'])}")
    
    # 创建测试对象
    api_test = RealTianjianAPITest()
    
    # 测试字典同步接口
    success1, result1 = api_test.test_dict_sync_api()
    
    # 测试体检信息传输接口
    pe_data = create_test_pe_data()
    success2, result2 = api_test.test_pe_info_api(pe_data)
    
    # 总结
    print("\n" + "="*60)
    print("测试总结")
    print("="*60)
    
    print(f"字典同步接口: {'[OK] 成功' if success1 else '[FAIL] 失败'}")
    print(f"体检信息接口: {'[OK] 成功' if success2 else '[FAIL] 失败'}")
    
    if success1 or success2:
        print("\n🎉 API连接测试成功！可以开始正式对接。")
    else:
        print("\n[WARN] API连接测试失败，请检查配置或联系技术支持。")

if __name__ == '__main__':
    main() 