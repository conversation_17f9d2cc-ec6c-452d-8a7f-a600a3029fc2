# 天健云AI对接项目 - 统一依赖管理
# 福能AI对接系统 - 完整依赖包列表

# ===== 核心依赖 =====
sqlalchemy>=1.4.53,<2.0.0
pyodbc>=5.0.1
requests>=2.31.0
urllib3>=2.0.7,<3.0.0

# ===== 数据处理 =====
pandas>=2.0.3
pydantic>=1.10.15,<2.0.0

# ===== 配置管理 =====
PyYAML>=6.0.1
python-dotenv>=1.0.0

# ===== 日志系统 =====
loguru>=0.7.2

# ===== 测试框架 =====
pytest>=7.4.4
pytest-cov>=4.1.0
pytest-mock>=3.12.0

# ===== 开发工具 =====
black>=23.12.1
isort>=5.13.2
flake8>=6.1.0,<8.0.0

# ===== 命令行工具 =====
click>=8.1.7
tqdm>=4.66.1

# ===== 调度和网络 =====
APScheduler>=3.10.4
certifi>=2023.11.17
cryptography>=41.0.8

# ===== 工具库 =====
python-dateutil>=2.8.2
typing-extensions>=4.9.0
colorama>=0.4.6

# ===== GUI框架 =====
PySide6>=6.4.3              # GUI框架

# ===== 可选依赖 (手动安装) =====
# PyInstaller>=6.3.0          # 打包工具

# Python版本要求: 3.8+
# ODBC驱动要求: ODBC Driver 17 for SQL Server
