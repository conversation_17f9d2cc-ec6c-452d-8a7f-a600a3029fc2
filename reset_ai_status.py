#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重置AI诊断状态，方便重新测试
"""

from multi_org_config import switch_organization
from optimized_database_service import create_optimized_db_service
from config import Config

def reset_ai_diagnosis_status():
    """重置AI诊断状态"""
    
    print("=" * 60)
    print("重置AI诊断状态")
    print("=" * 60)
    
    # 切换到09机构
    print("\n1. 切换到09机构")
    success, message = switch_organization('09')
    print(f"   切换结果: {message}")
    
    if success:
        # 获取当前机构配置
        from multi_org_config import get_current_org_config
        org_config = get_current_org_config()
        
        # 创建数据库连接
        if org_config.get('db_host'):
            connection_string = (
                f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                f"SERVER={org_config['db_host']},{org_config.get('db_port', 1433)};"
                f"DATABASE={org_config['db_name']};"
                f"UID={org_config['db_user']};"
                f"PWD={org_config['db_password']};TrustServerCertificate=yes;"
            )
        else:
            connection_string = Config.get_interface_db_connection_string()
        
        db_service = create_optimized_db_service(connection_string)
        
        try:
            # 重置测试客户的AI诊断状态
            reset_sql = """
            UPDATE T_Register_Main 
            SET AIDiagnosisStatus = 1,
                cAICanDiagnosisTime = NULL
            WHERE cClientCode IN ('0825749344', '0825749350')
            AND CONVERT(date, cCanDiagDate) = CONVERT(date, GETDATE())
            """
            
            print("\n2. 重置AI诊断状态...")
            print(f"   SQL: {reset_sql.strip()}")
            
            # 执行更新
            from database_service import DatabaseService
            local_db_service = DatabaseService(connection_string)
            
            if local_db_service.connect():
                try:
                    affected_rows = local_db_service.execute_update(reset_sql)
                    print(f"   更新了 {affected_rows} 条记录")
                    
                    if affected_rows > 0:
                        print("   ✅ AI诊断状态重置成功")
                        print("   现在可以重新测试轮询功能了")
                    else:
                        print("   ⚠️ 没有符合条件的记录被更新")
                        print("   可能记录已经是正确状态，或者不存在")
                        
                finally:
                    local_db_service.disconnect()
            else:
                print("   ❌ 数据库连接失败")
                
        except Exception as e:
            print(f"   ❌ 重置失败: {e}")
    else:
        print("   机构切换失败，无法重置")
    
    print("\n" + "=" * 60)
    print("重置完成")
    print("=" * 60)

if __name__ == '__main__':
    reset_ai_diagnosis_status()