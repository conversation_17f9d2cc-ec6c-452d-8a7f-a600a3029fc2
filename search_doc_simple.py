#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接读取HTML文件并查找18号接口相关内容
"""

def search_interface_18():
    try:
        with open(r"D:\python\福能AI对接\接口文档.html", "r", encoding="utf-8") as f:
            lines = f.readlines()
        
        # 查找包含18号接口的行
        for i, line in enumerate(lines):
            if ('18' in line and '接口' in line) or 'getDoctor' in line:
                print(f"Line {i+1}: {line.strip()}")
                # 打印上下文
                start = max(0, i-5)
                end = min(len(lines), i+6)
                for j in range(start, end):
                    marker = ">>> " if j == i else "    "
                    print(f"{marker}Line {j+1}: {lines[j].strip()}")
                print("=" * 60)
                
    except FileNotFoundError:
        print("文件未找到")
    except Exception as e:
        print(f"读取文件时出错: {e}")

if __name__ == "__main__":
    search_interface_18()