#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找接口文档中关于18号接口的内容
"""

import json
import re

def search_interface_18():
    try:
        with open(r"D:\python\福能AI对接\接口文档.html", "r", encoding="utf-8") as f:
            content = f.read()
            
        # 查找JSON数据部分
        json_match = re.search(r'apipostData\s*=\s*({.*?});', content, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
            try:
                data = json.loads(json_str)
                
                # 递归搜索包含"18"或"getDoctor"的接口
                def search_interfaces(obj, path=""):
                    if isinstance(obj, dict):
                        # 检查当前对象是否是接口并且名称包含18或getDoctor
                        if obj.get("type") == "api" and (
                            "18" in obj.get("name", "") or 
                            "getDoctor" in obj.get("name", "") or
                            "18" in obj.get("url", "") or
                            "getDoctor" in obj.get("url", "")
                        ):
                            print(f"找到18号接口: {obj.get('name', '')}")
                            print(f"URL: {obj.get('url', '')}")
                            print(f"请求方法: {obj.get('method', '')}")
                            print(f"路径: {path}")
                            print("=" * 50)
                            
                            # 打印请求参数
                            if "request" in obj:
                                request = obj["request"]
                                if "params" in request:
                                    print("请求参数:")
                                    for param in request["params"]:
                                        print(f"  {param.get('name', '')}: {param.get('example', '')} ({param.get('description', '')})")
                                    print()
                                    
                                if "headers" in request:
                                    print("请求头:")
                                    for header in request["headers"]:
                                        print(f"  {header.get('name', '')}: {header.get('value', '')}")
                                    print()
                                    
                                if "body" in request and "raw" in request["body"]:
                                    print("请求体:")
                                    print(request["body"]["raw"])
                                    print()
                            
                            # 打印响应示例
                            if "response" in obj and obj["response"]:
                                response = obj["response"][0] if isinstance(obj["response"], list) else obj["response"]
                                if "body" in response:
                                    print("响应示例:")
                                    print(response["body"])
                                    print()
                        
                        # 递归搜索子对象
                        for key, value in obj.items():
                            search_interfaces(value, f"{path}.{key}" if path else key)
                    elif isinstance(obj, list):
                        for i, item in enumerate(obj):
                            search_interfaces(item, f"{path}[{i}]")
                
                search_interfaces(data)
            except json.JSONDecodeError as e:
                print(f"解析JSON时出错: {e}")
        else:
            print("未找到apipostData JSON数据")
            
    except Exception as e:
        print(f"读取文件时出错: {e}")

if __name__ == "__main__":
    search_interface_18()