from api_config_manager import get_tianjian_base_url
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
健康同步系统配置向导
帮助用户一步步配置数据库连接和天健云API参数
"""

import os
import sys
import getpass
import json
from datetime import datetime

class SetupWizard:
    """配置向导类"""
    
    def __init__(self):
        self.config = {}
        self.env_file = '.env'
        
    def print_header(self, title):
        """打印标题"""
        print("\n" + "="*60)
        print(f" {title}")
        print("="*60)
    
    def print_section(self, title):
        """打印章节标题"""
        print(f"\n[{title}]")
        print("-" * 40)
    
    def ask_input(self, prompt, default=None, required=True, password=False):
        """询问用户输入"""
        if default:
            prompt += f" (默认: {default})"
        prompt += ": "
        
        while True:
            if password:
                value = getpass.getpass(prompt)
            else:
                value = input(prompt).strip()
            
            if not value and default:
                return default
            elif not value and required:
                print("[FAIL] 此项为必填项，请输入有效值")
                continue
            elif not value and not required:
                return ""
            else:
                return value
    
    def ask_yes_no(self, prompt, default="y"):
        """询问是否确认"""
        prompt += f" (y/n, 默认: {default}): "
        
        while True:
            answer = input(prompt).strip().lower()
            if not answer:
                answer = default.lower()
            
            if answer in ['y', 'yes', '是']:
                return True
            elif answer in ['n', 'no', '否']:
                return False
            else:
                print("请输入 y 或 n")
    
    def setup_database_config(self):
        """配置数据库连接"""
        self.print_section("主数据库配置")
        print("请配置体检系统主数据库连接参数:")
        
        self.config['MAIN_DB_HOST'] = self.ask_input(
            "数据库服务器地址", 
            default="localhost"
        )
        
        self.config['MAIN_DB_PORT'] = self.ask_input(
            "数据库端口", 
            default="1433"
        )
        
        self.config['MAIN_DB_NAME'] = self.ask_input(
            "数据库名称", 
            default=${INTERFACE_DB_NAME:-examdb_center}
        )
        
        self.config['MAIN_DB_USER'] = self.ask_input(
            "数据库用户名", 
            default="sa"
        )
        
        self.config['MAIN_DB_PASSWORD'] = self.ask_input(
            "数据库密码", 
            required=True,
            password=True
        )
        
        self.config['MAIN_DB_DRIVER'] = self.ask_input(
            "ODBC驱动名称", 
            default="ODBC Driver 17 for SQL Server"
        )
        
        # PACS数据库配置
        self.print_section("PACS数据库配置")
        print("PACS数据库用于存储影像相关数据，如果没有独立的PACS系统，可以与主数据库设置相同。")
        
        use_same_pacs = self.ask_yes_no("PACS数据库是否与主数据库相同", "y")
        
        if use_same_pacs:
            self.config['PACS_DB_HOST'] = self.config['MAIN_DB_HOST']
            self.config['PACS_DB_PORT'] = self.config['MAIN_DB_PORT']
            self.config['PACS_DB_USER'] = self.config['MAIN_DB_USER']
            self.config['PACS_DB_PASSWORD'] = self.config['MAIN_DB_PASSWORD']
            self.config['PACS_DB_DRIVER'] = self.config['MAIN_DB_DRIVER']
            self.config['PACS_DB_NAME'] = self.ask_input(
                "PACS数据库名称", 
                default="ExamDB_Pacs"
            )
        else:
            self.config['PACS_DB_HOST'] = self.ask_input(
                "PACS数据库服务器地址", 
                default=self.config['MAIN_DB_HOST']
            )
            
            self.config['PACS_DB_PORT'] = self.ask_input(
                "PACS数据库端口", 
                default=self.config['MAIN_DB_PORT']
            )
            
            self.config['PACS_DB_NAME'] = self.ask_input(
                "PACS数据库名称", 
                default="ExamDB_Pacs"
            )
            
            self.config['PACS_DB_USER'] = self.ask_input(
                "PACS数据库用户名", 
                default=self.config['MAIN_DB_USER']
            )
            
            self.config['PACS_DB_PASSWORD'] = self.ask_input(
                "PACS数据库密码", 
                required=True,
                password=True
            )
            
            self.config['PACS_DB_DRIVER'] = self.ask_input(
                "PACS ODBC驱动名称", 
                default=self.config['MAIN_DB_DRIVER']
            )
    
    def setup_api_config(self):
        """配置天健云API"""
        self.print_section("天健云API配置")
        print("请配置天健云API连接参数，这些参数由天健云平台分配:")
        
        self.config['API_BASE_URL'] = self.ask_input(
            "API基础地址", 
            default=get_tianjian_base_url()
        )
        
        self.config['API_MIC_CODE'] = self.ask_input(
            "机构代码(mic-code)", 
            default="MIC1.0001"
        )
        
        self.config['API_MISC_ID'] = self.ask_input(
            "系统ID(misc-id)", 
            default="MISC1.000001"
        )
        
        self.config['API_KEY'] = self.ask_input(
            "API密钥(key)", 
            required=True,
            password=True
        )
        
        # API高级配置
        print("\n可选的API高级配置:")
        
        self.config['API_TIMEOUT'] = self.ask_input(
            "请求超时时间(秒)", 
            default="30",
            required=False
        )
        
        self.config['API_RETRY_TIMES'] = self.ask_input(
            "重试次数", 
            default="3",
            required=False
        )
        
        self.config['API_RETRY_DELAY'] = self.ask_input(
            "重试延迟(秒)", 
            default="1",
            required=False
        )
    
    def setup_system_config(self):
        """配置系统参数"""
        self.print_section("系统配置")
        print("请配置系统运行参数:")
        
        log_levels = ["DEBUG", "INFO", "WARNING", "ERROR"]
        print(f"可选日志级别: {', '.join(log_levels)}")
        
        while True:
            log_level = self.ask_input(
                "日志级别", 
                default="INFO",
                required=False
            ).upper()
            
            if log_level in log_levels or not log_level:
                self.config['LOG_LEVEL'] = log_level or "INFO"
                break
            else:
                print(f"[FAIL] 无效的日志级别，请选择: {', '.join(log_levels)}")
        
        self.config['SYNC_BATCH_SIZE'] = self.ask_input(
            "同步批次大小", 
            default="100",
            required=False
        )
        
        self.config['SYNC_INTERVAL'] = self.ask_input(
            "同步间隔(秒)", 
            default="300",
            required=False
        )
        
        enable_auto_sync = self.ask_yes_no("是否启用自动同步", "y")
        self.config['ENABLE_AUTO_SYNC'] = "true" if enable_auto_sync else "false"
    
    def preview_config(self):
        """预览配置"""
        self.print_section("配置预览")
        print("以下是您的配置信息:")
        
        print("\n[主数据库]")
        print(f"  服务器: {self.config['MAIN_DB_HOST']}:{self.config['MAIN_DB_PORT']}")
        print(f"  数据库: {self.config['MAIN_DB_NAME']}")
        print(f"  用户名: {self.config['MAIN_DB_USER']}")
        print(f"  密码: {'*' * len(self.config['MAIN_DB_PASSWORD'])}")
        
        print("\n[PACS数据库]")
        print(f"  服务器: {self.config['PACS_DB_HOST']}:{self.config['PACS_DB_PORT']}")
        print(f"  数据库: {self.config['PACS_DB_NAME']}")
        print(f"  用户名: {self.config['PACS_DB_USER']}")
        print(f"  密码: {'*' * len(self.config['PACS_DB_PASSWORD'])}")
        
        print("\n[天健云API]")
        print(f"  API地址: {self.config['API_BASE_URL']}")
        print(f"  机构代码: {self.config['API_MIC_CODE']}")
        print(f"  系统ID: {self.config['API_MISC_ID']}")
        print(f"  API密钥: {'*' * len(self.config['API_KEY'])}")
        
        print("\n[系统配置]")
        print(f"  日志级别: {self.config['LOG_LEVEL']}")
        print(f"  同步批次: {self.config['SYNC_BATCH_SIZE']}")
        print(f"  同步间隔: {self.config['SYNC_INTERVAL']}秒")
        print(f"  自动同步: {self.config['ENABLE_AUTO_SYNC']}")
    
    def save_config(self):
        """保存配置到.env文件"""
        self.print_section("保存配置")
        
        # 检查是否已存在.env文件
        if os.path.exists(self.env_file):
            if not self.ask_yes_no(f"文件 {self.env_file} 已存在，是否覆盖", "n"):
                backup_file = f"{self.env_file}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                os.rename(self.env_file, backup_file)
                print(f"原配置文件已备份为: {backup_file}")
        
        try:
            with open(self.env_file, 'w', encoding='utf-8') as f:
                f.write("# =========================================================================\n")
                f.write("# 健康同步系统 - 环境变量配置文件\n")
                f.write(f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("# =========================================================================\n\n")
                
                f.write("# 主数据库配置\n")
                f.write(f"MAIN_DB_HOST={self.config['MAIN_DB_HOST']}\n")
                f.write(f"MAIN_DB_PORT={self.config['MAIN_DB_PORT']}\n")
                f.write(f"MAIN_DB_NAME={self.config['MAIN_DB_NAME']}\n")
                f.write(f"MAIN_DB_USER={self.config['MAIN_DB_USER']}\n")
                f.write(f"MAIN_DB_PASSWORD={self.config['MAIN_DB_PASSWORD']}\n")
                f.write(f"MAIN_DB_DRIVER={self.config['MAIN_DB_DRIVER']}\n\n")
                
                f.write("# PACS数据库配置\n")
                f.write(f"PACS_DB_HOST={self.config['PACS_DB_HOST']}\n")
                f.write(f"PACS_DB_PORT={self.config['PACS_DB_PORT']}\n")
                f.write(f"PACS_DB_NAME={self.config['PACS_DB_NAME']}\n")
                f.write(f"PACS_DB_USER={self.config['PACS_DB_USER']}\n")
                f.write(f"PACS_DB_PASSWORD={self.config['PACS_DB_PASSWORD']}\n")
                f.write(f"PACS_DB_DRIVER={self.config['PACS_DB_DRIVER']}\n\n")
                
                f.write("# 天健云API配置\n")
                f.write(f"API_BASE_URL={self.config['API_BASE_URL']}\n")
                f.write(f"API_MIC_CODE={self.config['API_MIC_CODE']}\n")
                f.write(f"API_MISC_ID={self.config['API_MISC_ID']}\n")
                f.write(f"API_KEY={self.config['API_KEY']}\n")
                f.write(f"API_TIMEOUT={self.config.get('API_TIMEOUT', '30')}\n")
                f.write(f"API_RETRY_TIMES={self.config.get('API_RETRY_TIMES', '3')}\n")
                f.write(f"API_RETRY_DELAY={self.config.get('API_RETRY_DELAY', '1')}\n\n")
                
                f.write("# 系统配置\n")
                f.write(f"LOG_LEVEL={self.config['LOG_LEVEL']}\n")
                f.write(f"SYNC_BATCH_SIZE={self.config.get('SYNC_BATCH_SIZE', '100')}\n")
                f.write(f"SYNC_INTERVAL={self.config.get('SYNC_INTERVAL', '300')}\n")
                f.write(f"ENABLE_AUTO_SYNC={self.config['ENABLE_AUTO_SYNC']}\n")
            
            print(f"[OK] 配置已保存到 {self.env_file}")
            return True
            
        except Exception as e:
            print(f"[FAIL] 保存配置失败: {e}")
            return False
    
    def run_tests(self):
        """运行连接测试"""
        self.print_section("连接测试")
        print("配置完成！现在可以运行连接测试来验证配置是否正确。")
        
        if self.ask_yes_no("是否立即运行数据库连接测试", "y"):
            print("\n执行数据库连接测试...")
            os.system("python test_db_connection.py")
        
        if self.ask_yes_no("是否立即运行API连接测试", "y"):
            print("\n执行API连接测试...")
            os.system("python test_api_connection.py")
    
    def run(self):
        """运行配置向导"""
        self.print_header("健康同步系统配置向导")
        print("欢迎使用健康同步系统配置向导!")
        print("此向导将帮助您配置数据库连接和天健云API参数。")
        print("\n注意事项:")
        print("- 请确保您已获得数据库访问权限")
        print("- 请准备好天健云分配的API认证信息")
        print("- 敏感信息将安全保存在.env文件中")
        
        if not self.ask_yes_no("\n是否继续配置", "y"):
            print("配置已取消。")
            return
        
        try:
            # 步骤1: 配置数据库
            self.setup_database_config()
            
            # 步骤2: 配置API
            self.setup_api_config()
            
            # 步骤3: 配置系统参数
            self.setup_system_config()
            
            # 步骤4: 预览配置
            self.preview_config()
            
            if not self.ask_yes_no("\n配置是否正确", "y"):
                print("配置已取消，您可以重新运行此向导。")
                return
            
            # 步骤5: 保存配置
            if self.save_config():
                # 步骤6: 运行测试
                self.run_tests()
                
                print("\n" + "="*60)
                print("🎉 配置向导完成!")
                print("="*60)
                print("接下来您可以:")
                print("1. 运行 'python test_db_connection.py' 测试数据库连接")
                print("2. 运行 'python test_api_connection.py' 测试API连接")
                print("3. 查看 README.md 了解更多使用方法")
                print("4. 开始使用健康同步系统")
                print("="*60)
            
        except KeyboardInterrupt:
            print("\n\n配置已中断。")
        except Exception as e:
            print(f"\n配置过程中发生错误: {e}")
            import traceback
    from config import Config
            traceback.print_exc()

def main():
    """主函数"""
    wizard = SetupWizard()
    wizard.run()

if __name__ == '__main__':
    main() 