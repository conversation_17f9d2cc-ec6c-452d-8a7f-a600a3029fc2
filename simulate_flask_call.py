#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟Flask环境中的21号接口调用
"""

from interface_21_getAbnormalNotice import TianjianInterface21
from config import Config

def simulate_flask_call():
    """模拟Flask调用"""
    print("模拟Flask环境中的21号接口调用")
    print("=" * 50)
    
    try:
        # 模拟Flask路由中的调用方式
        print("1. 获取API配置...")
        api_config = Config.get_tianjian_api_config()
        print(f"API配置: {api_config.get('base_url', 'N/A')}")
        
        print("\n2. 创建接口实例...")
        interface = TianjianInterface21(api_config)
        print("接口实例创建成功")
        
        print("\n3. 模拟请求数据...")
        # 模拟Flask request.get_json()返回的数据
        data = {
            'peNo': '0825751692',
            'hospital': {
                'code': '08',
                'name': 'test'
            }
        }
        print(f"请求数据: {data}")
        
        print("\n4. 调用接口方法...")
        result = interface.get_abnormal_notice(data)
        print(f"调用结果: {result}")
        
        if result.get('code') == 0:
            print("[OK] 接口调用成功")
            if result.get('data'):
                print(f"找到数据: {result['data']['abnormalName']}")
            else:
                print("未找到数据（正常）")
        else:
            print(f"[FAIL] 接口调用失败: {result.get('msg')}")
            
    except Exception as e:
        print(f"[ERROR] 模拟调用失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simulate_flask_call()