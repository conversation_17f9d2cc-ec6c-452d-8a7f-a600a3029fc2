#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云同步日志服务
用于记录所有接口的同步操作日志到中心库
"""

import json
import time
import uuid
import socket
from datetime import datetime
from typing import Dict, Any, Optional, List
from database_service import get_database_service


class TianJianSyncLogger:
    """天健云同步日志记录器"""
    
    def __init__(self, shop_code: str = None, org_code: str = None):
        """
        初始化同步日志记录器
        
        Args:
            shop_code: 门店编码
            org_code: 机构编码
        """
        self.shop_code = shop_code or "DEFAULT"
        self.org_code = org_code or "DEFAULT"
        self.batch_id = str(uuid.uuid4())[:8]  # 生成批次ID
        self.source_ip = self._get_local_ip()
        self.user_agent = "TianJian-Sync-Client/1.0"
        
        # 使用中心库连接
        self.db_service = get_database_service()
    
    def _get_local_ip(self) -> str:
        """获取本机IP地址"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except:
            return "127.0.0.1"
    
    def _ensure_table_exists(self) -> bool:
        """确保日志表存在"""
        if not self.db_service.connect():
            return False
        
        try:
            # 检查表是否存在
            check_sql = """
            SELECT COUNT(*) as table_exists
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_NAME = 'T_TianJian_Sync_Log'
            """
            result = self.db_service.execute_query(check_sql)
            
            if result and result[0]['table_exists'] > 0:
                return True
            else:
                print("[WARNING] T_TianJian_Sync_Log 表不存在，请先执行建表脚本")
                return False
                
        except Exception as e:
            print(f"[ERROR] 检查日志表失败: {e}")
            return False
        finally:
            self.db_service.disconnect()
    
    def log_sync_start(self, interface_type: str, interface_name: str, 
                      client_code: str = None, client_name: str = None, 
                      card_no: str = None, dept_status_type: str = None,
                      sync_type: str = "AUTO", request_data: Dict[str, Any] = None) -> Optional[int]:
        """
        记录同步开始
        
        Args:
            interface_type: 接口类型 (01, 02, 03等)
            interface_name: 接口名称
            client_code: 客户编码
            client_name: 客户姓名
            card_no: 卡号
            dept_status_type: 分科状态类型
            sync_type: 同步类型 (AUTO/MANUAL/TEST)
            request_data: 请求数据
            
        Returns:
            日志记录ID，失败返回None
        """
        if not self._ensure_table_exists():
            return None
        
        if not self.db_service.connect():
            return None
        
        try:
            insert_sql = """
            INSERT INTO T_TianJian_Sync_Log (
                LogTime, ShopCode, OrgCode, InterfaceType, InterfaceName, SyncType,
                ClientCode, ClientName, CardNo, DeptStatusType, SyncStatus,
                RequestData, BatchId, SourceIP, UserAgent
            ) VALUES (
                GETDATE(), ?, ?, ?, ?, ?,
                ?, ?, ?, ?, 'PROCESSING',
                ?, ?, ?, ?
            )
            """
            
            request_json = json.dumps(request_data, ensure_ascii=False) if request_data else None
            
            params = (
                self.shop_code, self.org_code, interface_type, interface_name, sync_type,
                client_code, client_name, card_no, dept_status_type,
                request_json, self.batch_id, self.source_ip, self.user_agent
            )
            
            # 执行插入并获取ID
            cursor = self.db_service.execute_update_with_cursor(insert_sql, params)
            if cursor:
                # 获取插入的ID
                cursor.execute("SELECT @@IDENTITY as log_id")
                result = cursor.fetchone()
                log_id = result[0] if result else None
                cursor.close()
                return int(log_id) if log_id else None
            
            return None
            
        except Exception as e:
            print(f"[ERROR] 记录同步开始日志失败: {e}")
            return None
        finally:
            self.db_service.disconnect()
    
    def log_sync_success(self, log_id: int, response_data: Dict[str, Any] = None,
                        interface_01_status: str = None, interface_03_status: str = None,
                        status_updated: bool = False, processing_time: int = None,
                        remarks: str = None):
        """
        记录同步成功
        
        Args:
            log_id: 日志记录ID
            response_data: 响应数据
            interface_01_status: 01接口状态
            interface_03_status: 03接口状态
            status_updated: 是否更新了本地状态
            processing_time: 处理时间(毫秒)
            remarks: 备注信息
        """
        if not log_id:
            return
        
        if not self.db_service.connect():
            return
        
        try:
            update_sql = """
            UPDATE T_TianJian_Sync_Log 
            SET SyncStatus = 'SUCCESS',
                ResponseData = ?,
                Interface01Status = ?,
                Interface03Status = ?,
                StatusUpdated = ?,
                ProcessingTime = ?,
                Remarks = ?
            WHERE ID = ?
            """
            
            response_json = json.dumps(response_data, ensure_ascii=False) if response_data else None
            
            params = (
                response_json, interface_01_status, interface_03_status,
                1 if status_updated else 0, processing_time, remarks, log_id
            )
            
            self.db_service.execute_update(update_sql, params)
            
        except Exception as e:
            print(f"[ERROR] 记录同步成功日志失败: {e}")
        finally:
            self.db_service.disconnect()
    
    def log_sync_failed(self, log_id: int, error_message: str,
                       interface_01_status: str = None, interface_03_status: str = None,
                       processing_time: int = None, retry_count: int = 0):
        """
        记录同步失败
        
        Args:
            log_id: 日志记录ID
            error_message: 错误信息
            interface_01_status: 01接口状态
            interface_03_status: 03接口状态
            processing_time: 处理时间(毫秒)
            retry_count: 重试次数
        """
        if not log_id:
            return
        
        if not self.db_service.connect():
            return
        
        try:
            update_sql = """
            UPDATE T_TianJian_Sync_Log 
            SET SyncStatus = 'FAILED',
                ErrorMessage = ?,
                Interface01Status = ?,
                Interface03Status = ?,
                ProcessingTime = ?,
                RetryCount = ?
            WHERE ID = ?
            """
            
            params = (
                error_message, interface_01_status, interface_03_status,
                processing_time, retry_count, log_id
            )
            
            self.db_service.execute_update(update_sql, params)
            
        except Exception as e:
            print(f"[ERROR] 记录同步失败日志失败: {e}")
        finally:
            self.db_service.disconnect()
    
    def log_batch_summary(self, total_count: int, success_count: int, failed_count: int,
                         interface_type: str, interface_name: str):
        """
        记录批次汇总信息
        
        Args:
            total_count: 总数
            success_count: 成功数
            failed_count: 失败数
            interface_type: 接口类型
            interface_name: 接口名称
        """
        if not self._ensure_table_exists():
            return
        
        if not self.db_service.connect():
            return
        
        try:
            summary_data = {
                "batch_id": self.batch_id,
                "total_count": total_count,
                "success_count": success_count,
                "failed_count": failed_count,
                "success_rate": round(success_count / total_count * 100, 2) if total_count > 0 else 0
            }
            
            insert_sql = """
            INSERT INTO T_TianJian_Sync_Log (
                LogTime, ShopCode, OrgCode, InterfaceType, InterfaceName, SyncType,
                SyncStatus, RequestData, BatchId, SourceIP, UserAgent, Remarks
            ) VALUES (
                GETDATE(), ?, ?, ?, ?, 'AUTO',
                'SUCCESS', ?, ?, ?, ?, ?
            )
            """
            
            request_json = json.dumps(summary_data, ensure_ascii=False)
            remarks = f"批次汇总 - 总计:{total_count} 成功:{success_count} 失败:{failed_count}"
            
            params = (
                self.shop_code, self.org_code, interface_type, interface_name,
                request_json, self.batch_id, self.source_ip, self.user_agent, remarks
            )
            
            self.db_service.execute_update(insert_sql, params)
            
        except Exception as e:
            print(f"[ERROR] 记录批次汇总失败: {e}")
        finally:
            self.db_service.disconnect()
    
    def query_sync_logs(self, days: int = 7, interface_type: str = None, 
                       sync_status: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """
        查询同步日志
        
        Args:
            days: 查询最近N天的数据
            interface_type: 接口类型过滤
            sync_status: 同步状态过滤
            limit: 限制返回条数
            
        Returns:
            日志记录列表
        """
        if not self._ensure_table_exists():
            return []
        
        if not self.db_service.connect():
            return []
        
        try:
            sql = f"""
            SELECT TOP ({limit})
                ID, LogTime, ShopCode, OrgCode, InterfaceType, InterfaceName,
                SyncType, ClientCode, ClientName, CardNo, DeptStatusType,
                SyncStatus, Interface01Status, Interface03Status, StatusUpdated,
                ProcessingTime, RetryCount, BatchId, ErrorMessage, Remarks
            FROM T_TianJian_Sync_Log
            WHERE LogTime >= DATEADD(day, -{days}, GETDATE())
            """
            
            params = []
            
            if interface_type:
                sql += " AND InterfaceType = ?"
                params.append(interface_type)
            
            if sync_status:
                sql += " AND SyncStatus = ?"
                params.append(sync_status)
            
            sql += " ORDER BY LogTime DESC"
            
            result = self.db_service.execute_query(sql, tuple(params) if params else None)
            return result or []
            
        except Exception as e:
            print(f"[ERROR] 查询同步日志失败: {e}")
            return []
        finally:
            self.db_service.disconnect()
    
    def get_sync_statistics(self, days: int = 7) -> Dict[str, Any]:
        """
        获取同步统计信息
        
        Args:
            days: 统计最近N天的数据
            
        Returns:
            统计信息
        """
        if not self._ensure_table_exists():
            return {}
        
        if not self.db_service.connect():
            return {}
        
        try:
            sql = f"""
            SELECT 
                COUNT(*) as total_count,
                SUM(CASE WHEN SyncStatus = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
                SUM(CASE WHEN SyncStatus = 'FAILED' THEN 1 ELSE 0 END) as failed_count,
                AVG(CAST(ProcessingTime AS FLOAT)) as avg_processing_time,
                MAX(LogTime) as last_sync_time
            FROM T_TianJian_Sync_Log
            WHERE LogTime >= DATEADD(day, -{days}, GETDATE())
            AND ShopCode = ?
            """
            
            result = self.db_service.execute_query(sql, (self.shop_code,))
            
            if result:
                stats = result[0]
                return {
                    "shop_code": self.shop_code,
                    "total_count": stats['total_count'] or 0,
                    "success_count": stats['success_count'] or 0,
                    "failed_count": stats['failed_count'] or 0,
                    "success_rate": round((stats['success_count'] or 0) / (stats['total_count'] or 1) * 100, 2),
                    "avg_processing_time": round(stats['avg_processing_time'] or 0, 2),
                    "last_sync_time": stats['last_sync_time']
                }
            
            return {}
            
        except Exception as e:
            print(f"[ERROR] 获取同步统计失败: {e}")
            return {}
        finally:
            self.db_service.disconnect()


def create_sync_logger(shop_code: str = None, org_code: str = None) -> TianJianSyncLogger:
    """
    创建同步日志记录器
    
    Args:
        shop_code: 门店编码
        org_code: 机构编码
        
    Returns:
        同步日志记录器实例
    """
    return TianJianSyncLogger(shop_code, org_code)