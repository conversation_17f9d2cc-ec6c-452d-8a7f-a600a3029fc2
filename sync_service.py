#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
体检信息同步服务
用于将体检系统数据同步到天健云平台
"""

import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from sqlalchemy import create_engine, text
from test_config import TestConfig, get_connection_string
from test_api_connection import APITester

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HealthSyncService:
    """健康同步服务类"""
    
    def __init__(self):
        """初始化同步服务"""
        self.db_config = TestConfig.MAIN_DB_CONFIG
        self.api_tester = APITester()
        self.engine = create_engine(get_connection_string(self.db_config))
        
        # 性别映射 (数据库中: 1=男, 2=女)
        self.gender_mapping = {
            '1': '男', 
            '2': '女',
            '男': '男',
            '女': '女'
        }
        
        # 婚姻状况映射
        self.marriage_mapping = {
            '1': '未婚',
            '2': '已婚', 
            '3': '离异',
            '4': '丧偶',
            '未婚': '未婚',
            '已婚': '已婚',
            '离异': '离异',
            '丧偶': '丧偶'
        }
    
    def validate_id_card(self, id_card: str) -> bool:
        """验证身份证号码格式"""
        if not id_card:
            return False
        
        # 18位身份证验证
        if len(id_card) == 18:
            return id_card[:17].isdigit() and (id_card[-1].isdigit() or id_card[-1].upper() == 'X')
        
        # 15位身份证验证
        if len(id_card) == 15:
            return id_card.isdigit()
        
        return False
    
    def get_recent_examinations(self, days: int = 7) -> List[Dict]:
        """获取最近N天的体检数据"""
        logger.info(f"获取最近{days}天的体检数据...")
        
        try:
            with self.engine.connect() as conn:
                # 查询最近N天的体检数据
                query = text(f"""
                    SELECT TOP 100
                        cClientCode,           -- 客户编号
                        cName,                 -- 姓名  
                        cSex,                  -- 性别
                        dBornDate,             -- 出生日期
                        iAges,                 -- 年龄
                        cMarryFlag,            -- 婚姻状况
                        cIdCard,               -- 身份证号
                        cTel,                  -- 电话
                        cOfficeAdrr,           -- 地址
                        dOperdate,             -- 登记日期
                        cOperName,             -- 登记人
                        cStatus,               -- 状态
                        cShopCode,             -- 门店代码
                        fTotal,                -- 总费用
                        cHealthNo,             -- 健康档案号
                        cWorkNumber,           -- 工作证号
                        iWorkYears,            -- 工作年限
                        cAddress,              -- 详细地址
                        cNationCode,           -- 民族代码
                        cCommanSuitName        -- 套餐名称
                    FROM T_Register_Main
                    WHERE dOperdate >= DATEADD(day, -{days}, GETDATE())
                        AND cName IS NOT NULL 
                        AND cName != ''
                    ORDER BY dOperdate DESC
                """)
                
                result = conn.execute(query)
                examinations = []
                
                for row in result:
                    examination = {
                        'client_code': row[0],
                        'name': row[1] or '',
                        'sex': self.gender_mapping.get(str(row[2] or ''), '未知'),
                        'birth_date': row[3].strftime('%Y-%m-%d') if row[3] else '',
                        'age': int(row[4]) if row[4] else 0,
                        'marriage': self.marriage_mapping.get(str(row[5] or ''), '未知'),
                        'id_card': row[6] or '',
                        'phone': row[7] or '',
                        'office_address': row[8] or '',
                        'register_date': row[9].strftime('%Y-%m-%d %H:%M:%S') if row[9] else '',
                        'operator': row[10] or '',
                        'status': row[11] or '',
                        'shop_code': row[12] or '',
                        'total_fee': float(row[13]) if row[13] else 0.0,
                        'health_no': row[14] or '',
                        'work_number': row[15] or '',
                        'work_years': int(row[16]) if row[16] else 0,
                        'address': row[17] or '',
                        'nation_code': row[18] or '',
                        'package_name': row[19] or ''
                    }
                    
                    # 验证必填字段
                    if examination['name'] and examination['id_card']:
                        if self.validate_id_card(examination['id_card']):
                            examinations.append(examination)
                        else:
                            logger.warning(f"身份证号码格式错误: {examination['client_code']} - {examination['id_card']}")
                
                logger.info(f"成功获取 {len(examinations)} 条有效体检数据")
                return examinations
                
        except Exception as e:
            logger.error(f"获取体检数据失败: {e}")
            return []
    
    def get_examination_details(self, client_code: str) -> Dict:
        """获取指定客户的详细体检信息"""
        logger.info(f"获取客户 {client_code} 的详细体检信息...")
        
        try:
            with self.engine.connect() as conn:
                # 获取基本信息
                basic_query = text("""
                    SELECT * FROM T_Register_Main WHERE cClientCode = :client_code
                """)
                
                basic_result = conn.execute(basic_query, {'client_code': client_code}).fetchone()
                if not basic_result:
                    logger.warning(f"未找到客户 {client_code} 的基本信息")
                    return {}
                
                # 获取体检项目详情
                detail_query = text("""
                    SELECT 
                        rd.cMainCode,          -- 项目代码
                        rd.cMainName,          -- 项目名称
                        rd.fPrice,             -- 价格
                        rd.cChargeNo,          -- 收费编号
                        im.cName as item_name, -- 项目标准名称
                        im.cSetType            -- 项目类型
                    FROM T_Register_Detail rd
                    LEFT JOIN Code_Item_Main im ON rd.cMainCode = im.cCode
                    WHERE rd.cClientCode = :client_code
                    ORDER BY rd.iNo
                """)
                
                detail_result = conn.execute(detail_query, {'client_code': client_code})
                
                # 获取检查结果
                result_query = text("""
                    SELECT 
                        cr.cDeptCode,          -- 科室代码
                        cr.cMainCode,          -- 项目代码
                        cr.cMainName,          -- 项目名称
                        cr.cResult,            -- 检查结果
                        cr.cAdvice,            -- 建议
                        cr.cOperCode,          -- 检查医生代码
                        cr.cOperName,          -- 检查医生姓名
                        cr.dCheckDate,         -- 检查日期
                        cr.cStatus,            -- 状态
                        dd.cName as dept_name  -- 科室名称
                    FROM T_Check_result cr
                    LEFT JOIN Code_Dept_dict dd ON cr.cDeptCode = dd.cCode
                    WHERE cr.cClientCode = :client_code
                    ORDER BY cr.dCheckDate
                """)
                
                result_data = conn.execute(result_query, {'client_code': client_code})
                
                # 组装详细信息
                details = {
                    'basic_info': dict(basic_result._asdict()),
                    'exam_items': [dict(row._asdict()) for row in detail_result],
                    'check_results': [dict(row._asdict()) for row in result_data]
                }
                
                logger.info(f"成功获取客户 {client_code} 的详细信息")
                return details
                
        except Exception as e:
            logger.error(f"获取客户详细信息失败: {e}")
            return {}
    
    def format_for_tianjian_api(self, examination: Dict) -> Dict:
        """将体检数据格式化为天健云API格式 - sendPeInfo接口"""
        
        # 处理性别
        sex_code = "3"  # 默认未知
        sex_name = "未知"
        if examination['sex']:
            if examination['sex'] in ['男', 'M', '1']:
                sex_code = "1"
                sex_name = "男"
            elif examination['sex'] in ['女', 'F', '2']:
                sex_code = "2" 
                sex_name = "女"
        
        # 处理生日格式 - 转换为yyyyMMdd
        birthday = ""
        if examination['birth_date']:
            try:
                if isinstance(examination['birth_date'], str):
                    birthday = examination['birth_date'].replace('-', '').replace('/', '')[:8]
                else:
                    birthday = examination['birth_date'].strftime('%Y%m%d')
            except:
                birthday = "19800101"  # 默认生日
        
        # 处理体检日期格式
        pe_date = ""
        if examination['register_date']:
            try:
                pe_date = examination['register_date'][:19]  # yyyy-MM-dd HH:mm:ss
            except:
                pe_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 计算年龄
        age = examination.get('age', 30)
        if not age and birthday:
            try:
                birth_year = int(birthday[:4])
                current_year = datetime.now().year
                age = current_year - birth_year
            except:
                age = 30
        
        # 按照天健云接口文档格式
        api_data = {
            "peUserInfo": {
                "archiveNo": examination['client_code'],  # 使用客户编号作为档案号
                "name": examination['name'],
                "icCode": examination['id_card'] or "",
                "sex": {
                    "code": sex_code,
                    "name": sex_name
                },
                "birthday": birthday,
                "peno": examination['client_code'],  # 体检号
                "peDate": pe_date,
                "phone": examination['phone'] or "",
                "vipLevel": {
                    "code": "NORMAL",
                    "name": "普通"
                },
                "medicalType": {
                    "code": "GENERAL",
                    "name": "常规体检"
                },
                "isGroup": False,
                "company": examination.get('work_unit', '') or "",
                "workDept": examination.get('work_dept', '') or "",
                "professional": examination.get('profession', '') or "",
                "workAge": str(examination.get('work_years', '') or ""),
                "peStates": {
                    "code": str(examination.get('status', '1')),
                    "name": "登记完成"
                },
                "deptCount": 1,  # 默认1个科室
                "age": int(age),
                "deptFinishTime": pe_date,
                "applyItemList": ["GENERAL_EXAM"],  # 默认体检项目
                "currentNodeType": 1,
                "urgentStatus": "0"
            },
            "archiveInfo": {
                "name": examination['name'],
                "icCode": examination['id_card'] or "",
                "sex": {
                    "code": sex_code,
                    "name": sex_name
                },
                "birthday": birthday,
                "peNoList": [examination['client_code']]
            }
        }
        
        return api_data
    
    def sync_to_tianjian(self, examinations: List[Dict]) -> Dict:
        """同步数据到天健云"""
        logger.info(f"开始同步 {len(examinations)} 条体检数据到天健云...")
        
        success_count = 0
        error_count = 0
        errors = []
        
        for exam in examinations:
            try:
                # 格式化数据为天健云API格式
                api_data = self.format_for_tianjian_api(exam)
                
                # 调用真实的天健云API - sendPeInfo接口
                response = self.api_tester.test_send_pe_info_api(api_data)
                
                if response and response.get('success'):
                    logger.info(f"真实同步成功: {exam['name']} ({exam['client_code']})")
                    success_count += 1
                else:
                    error_msg = response.get('error', '未知错误') if response else 'API调用失败'
                    logger.error(f"同步失败 - {exam['client_code']}: {error_msg}")
                    error_count += 1
                    errors.append(f"{exam['client_code']}: {error_msg}")
                
            except Exception as e:
                error_count += 1
                error_msg = f"同步失败 - {exam['client_code']}: {e}"
                logger.error(error_msg)
                errors.append(error_msg)
        
        result = {
            'total': len(examinations),
            'success': success_count,
            'error': error_count,
            'errors': errors,
            'sync_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        logger.info(f"同步完成 - 成功: {success_count}, 失败: {error_count}")
        return result
    
    def auto_sync_recent_data(self, days: int = 1) -> Dict:
        """自动同步最近的数据"""
        logger.info(f"开始自动同步最近{days}天的数据...")
        
        # 获取最近数据
        examinations = self.get_recent_examinations(days)
        
        if not examinations:
            logger.info("没有需要同步的数据")
            return {'message': '没有需要同步的数据', 'total': 0}
        
        # 执行同步
        result = self.sync_to_tianjian(examinations)
        return result

def main():
    """主函数 - 演示同步服务的使用"""
    print("="*60)
    print("健康同步服务测试")
    print("="*60)
    
    # 创建同步服务
    sync_service = HealthSyncService()
    
    # 测试获取最近7天的数据
    print("\n1. 获取最近7天的体检数据:")
    examinations = sync_service.get_recent_examinations(7)
    
    if examinations:
        print(f"找到 {len(examinations)} 条体检数据:")
        for i, exam in enumerate(examinations[:5], 1):  # 只显示前5条
            print(f"  {i}. {exam['name']} - {exam['id_card']} - {exam['register_date']}")
        
        if len(examinations) > 5:
            print(f"  ... 还有 {len(examinations) - 5} 条数据")
        
        # 测试获取详细信息
        if examinations:
            client_code = examinations[0]['client_code']
            print(f"\n2. 获取客户 {client_code} 的详细信息:")
            details = sync_service.get_examination_details(client_code)
            
            if details:
                print(f"  基本信息: {details['basic_info']['cName']}")
                print(f"  体检项目数: {len(details['exam_items'])}")
                print(f"  检查结果数: {len(details['check_results'])}")
        
        # 测试数据格式化
        print("\n3. 测试数据格式化:")
        api_data = sync_service.format_for_tianjian_api(examinations[0])
        print(f"  格式化后的数据结构: {list(api_data.keys())}")
        
        # 测试模拟同步
        print("\n4. 测试模拟同步(前3条数据):")
        test_data = examinations[:3]
        sync_result = sync_service.sync_to_tianjian(test_data)
        print(f"  同步结果: {sync_result}")
        
    else:
        print("  未找到体检数据")
    
    print("\n" + "="*60)
    print("测试完成")

if __name__ == '__main__':
    main() 