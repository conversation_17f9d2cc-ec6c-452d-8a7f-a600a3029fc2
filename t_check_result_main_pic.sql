/*
 Navicat Premium Dump SQL

 Source Server         : WIN云服务器
 Source Server Type    : SQL Server
 Source Server Version : 10504000 (10.50.4000)
 Source Host           : ***********:1433
 Source Catalog        : ExamDB_Pacs
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 10504000 (10.50.4000)
 File Encoding         : 65001

 Date: 24/07/2025 23:38:40
*/


-- ----------------------------
-- Table structure for t_check_result_main_pic
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[t_check_result_main_pic]') AND type IN ('U'))
	DROP TABLE [dbo].[t_check_result_main_pic]
GO

CREATE TABLE [dbo].[t_check_result_main_pic] (
  [cClientCode] varchar(10) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [cMainCode] varchar(6) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [cpicName] varchar(50) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [pImage] image  NULL,
  [iSelectTag] int  NULL,
  [iReportTag] int  NULL,
  [iIndex] int  NULL,
  [cPosition] varchar(100) COLLATE Chinese_PRC_CI_AS  NULL,
  [ccardno] varchar(20) COLLATE Chinese_PRC_CI_AS  NULL,
  [status] int DEFAULT 0 NOT NULL
)
GO

ALTER TABLE [dbo].[t_check_result_main_pic] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Primary Key structure for table t_check_result_main_pic
-- ----------------------------
ALTER TABLE [dbo].[t_check_result_main_pic] ADD CONSTRAINT [PK_t_check_result_main_pic] PRIMARY KEY CLUSTERED ([cClientCode], [cMainCode], [cpicName])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

