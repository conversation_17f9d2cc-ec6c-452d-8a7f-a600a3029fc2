<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv='X-UA-Compatible' content='IE=edge,chrome=1' />
    <meta name="renderer" content="webkit">
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache">
    <meta http-equiv="Cache" content="no-cache">
    <title>Apipost接口文档</title>
    <link href="https://img.cdn.apipost.cn/docs/css7/style.css?20220909" rel="stylesheet">
    <link href="https://img.cdn.apipost.cn/docs/css/font-awesome-4.7.0/css/font-awesome.min.css?20220909" rel="stylesheet">
    <link href="https://img.cdn.apipost.cn/docs/css7/github.css?20220909" rel="stylesheet">
    <link rel="stylesheet" href="https://img.cdn.apipost.cn/docs/css7/content-v7.css?20220909">
    <link href="https://img.cdn.apipost.cn/docs/css7/json-viewer.css?20220909" rel="stylesheet">
    <script type="text/javascript" src="https://img.cdn.apipost.cn/docs/js/jquery-3.4.1.min.js?20220909"></script>
    <script type="text/javascript" src="https://img.cdn.apipost.cn/docs/js7/marked.min.js?20220909"></script>
    <script type="text/javascript" src="https://img.cdn.apipost.cn/docs/js7/template-web.js?20220909"></script>
    <script type="text/javascript" src="https://img.cdn.apipost.cn/docs/js7/highlight.js?20220909"></script>
    <script type="text/javascript" src="https://img.cdn.apipost.cn/docs/js7/clipboard.min.js?20220909"></script>
    <script type="text/javascript" src="https://img.cdn.apipost.cn/docs/js7/crypto-js.min.js?20220909" charset="utf-8"></script>
    <script type="text/javascript" src="https://img.cdn.apipost.cn/docs/js/json5.min.js?20220909"></script>
    <script type="text/javascript" src="https://img.cdn.apipost.cn/docs/js/dayjs.min.js?20220909"></script>
    <script type="text/javascript" src="https://img.cdn.apipost.cn/docs/js/url-join.js?20220909"></script>
    <!-- <script type="text/javascript" src="./js/enc.min.js"></script> -->
    <script type="text/javascript" src="https://img.cdn.apipost.cn/docs/js/json-viewer.min.js?20220909"></script>
    <script type="text/javascript" src="https://img.cdn.apipost.cn/docs/js/lodash.min.js?20220909"></script>
    <script type="text/javascript">
        consoleLog = console.log; // 重写 consoleLog
        $(function () {

        })
    </script>
</head>

<body>
<div class="apipost-doc-body">
    <div class="apipost-doc-body-main">
        <div class="apipost-doc-body-nav">
            <div class="logo">
                <a href="https://www.apipost.cn/"><img class="project-logo" src="https://img.cdn.apipost.cn/docs/images7/logo.svg" /></a>
            </div>
            <div class="apipost-doc-body-nav-search">
                <img src="https://img.cdn.apipost.cn/docs/images7/search-icon.svg">
                <input type="text" name="search-input" placeholder="搜索目录、接口。支持 名称、URL、Method 搜索">
            </div>
            <div class="apipost-doc-body-nav-title"> 项目名 </div>
            <div class="apipost-doc-body-nav-list">
                <ul>
                    <li data-type="global" data-parent_id="0" data-target_id="001" data-indent="0"
                        class="global active">
                        <a class="load-data-btn" href="javascript:;" data-type="global" data-parent_id="0"
                           data-target_id="001" data-indent="0">全局公共参数</a>
                    </li>
                </ul>
                <ul class="apipost-left-nav-template">
                    <script type="text/html" id="apipost-left-nav-template-tpl">
                        {{if _.isArray(list)}}
                        {{each list item}}
                        {{if !item.indent}}
                        {{set indent = item.indent = 0}}
                        {{/if}}
                        <li data-type="{{item.target_type}}" data-parent_id="{{item.parent_id}}" data-target_id="{{item.target_id}}" data-indent="{{item.indent}}" class="{{item.target_type}} {{if target_id == item.target_id}}active{{/if}} {{if item.indent != 0}}is-hidden{{/if}}" style="padding-left: {{item.indent?item.indent:0}}em;">
                            {{if item.target_type == 'folder' || item.example_type == 'folder'}}
                            <a class="load-data-btn" href="javascript:;" data-type="{{item.target_type}}" data-parent_id="{{item.parent_id}}" data-target_id="{{item.local_target_id}}" data-indent="{{item.indent}}"><i class="fa fa-caret-down"></i>{{item.name}}</a>
                            {{else if item.target_type == 'websocket' || item.example_type == 'websocket'}}
                            <a class="load-data-btn" href="javascript:;" data-type="{{item.target_type}}" data-parent_id="{{item.parent_id}}" data-target_id="{{item.local_target_id}}" data-indent="{{item.indent}}"><em class="method websocket">WS</em><span>{{item.name}}</span></a>
                            {{else if item.target_type == 'doc' || item.example_type == 'doc'}}
                            <a class="load-data-btn" href="javascript:;" data-type="{{item.target_type}}" data-parent_id="{{item.parent_id}}" data-target_id="{{item.local_target_id}}" data-indent="{{item.indent}}"><em class="method doc">文本</em><span>{{item.name}}</span></a>
                            {{else if item.target_type == 'grpc' || item.example_type == 'grpc'}}
                            <a class="load-data-btn" href="javascript:;" data-type="{{item.target_type}}" data-parent_id="{{item.parent_id}}" data-target_id="{{item.local_target_id}}" data-indent="{{item.indent}}"><em class="method grpc">GRPC</em><span>{{item.name}}</span></a>
                            {{else}}
                            <a class="load-data-btn" href="javascript:;" data-type="{{item.target_type}}" data-parent_id="{{item.parent_id}}" data-target_id="{{item.local_target_id}}" data-indent="{{item.indent}}"><em class="method {{item.method}}">{{item.method}}</em><span>{{item.name}}</span></a>
                            {{/if}}
                            {{if item.target_type != 'folder' && item.color}}
                            <span style="margin-left:5px;display:inline-block;width:6px;height:6px;border-radius:50%;background-color: {{item.color}};"></span>



                            {{/if}}
                            {{if item.target_type == 'example'}}
                            <svg style="margin-left:5px;" width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M4.91033 6.68207H3.30033C3.09013 6.68207 2.94513 6.4768 3.01993 6.28529L4.61973 2.18849C4.64137 2.13306 4.67976 2.08536 4.72982 2.0517C4.77987 2.01804 4.83923 2.00001 4.90003 2H7.59962C7.81262 2 7.95782 2.21054 7.87742 2.40303L6.94592 4.63367H8.69951C8.95741 4.63367 9.09501 4.93026 8.92491 5.11933L4.62553 9.89887C4.41653 10.1313 4.02903 9.92989 4.11013 9.63106L4.91033 6.68207Z" fill="#FFC01E"/>
                            </svg>
                            {{/if}}
                        </li>
                        {{if _.isArray(item.children)}}
                        {{each item.children as childrenItem key}}
                        {{if item.children[key].indent}}
                        {{set indent = item.children[key].indent = item.children[key].indent+2}}
                        {{else}}
                        {{set indent = item.children[key].indent = item.indent + 2}}
                        {{/if}}
                        {{/each}}

                        {{set _recursiveList ={list:item.children}; }}
                        {{include 'apipost-left-nav-template-tpl' _recursiveList}}
                        {{/if}}
                        {{/each}}
                        {{/if}}
                    </script>
                </ul>
            </div>
            <div class="apipost-doc-body-nav-drag-bar"></div>
        </div>
        <div class="apipost-doc-body-content">
            <div class="apipost-doc-wrap">
                <script type="text/html" id="apipost-doc-wrap-tpl">
                    <div class="apipost-doc-wrap-title">
                        <h2 class="title" data-text="{{data.name}}" id="{{data.name}}">
                            <div class="mobile-nav-toggle-btn">
                                <a href="javascript:;" class="mobile-nav-toggle-link-btn"><i class="fa fa-reorder"></i></a>
                            </div>
                            <div class="title-text">{{data.name}}</div>
                            {{if data.target_type == 'api' || data.example_type == 'api'}}
                            <button type="button" class="copy-clipboard" data-clipboard-text="{{JSON.stringify(data.copyData)}}"><i class="fa fa-copy"></i> 复制接口 </button>
                            {{/if}}
                            {{if data.target_type == 'api' || data.target_type == 'grpc' || data.target_type == 'websocket' || data.example_type == 'api' || data.example_type == 'grpc' || data.example_type == 'websocket'}}
                            <a type="button" class="copy-clipboard" data-clipboard-text="{{JSON.stringify(data)}}" href="https://v7.apipost.cn/#/apis/run?target_id={{data.target_id}}" target="_blank">去调试</a>
                            {{/if}}
                        </h2>
                    </div>
                    <div class="apipost-doc-wrap-desc">
                        <!-- 创建人：哈利厄 最后更新：哈利厄 &nbsp;&nbsp; -->
                        创建时间：{{data.format_create_dtime}} &nbsp;&nbsp;更新时间：{{data.format_update_dtime}}
                    </div>
                    {{if _.has(data, 'request.description')}}
                    <div class="apipost-doc-wrap-para-markdown">
                        <div class="markdown-section">{{@marked(data.request.description)}}</div>
                    </div>
                    {{/if}}
                    {{if data.target_type == 'grpc' || data.example_type == 'grpc'}}
                    <h3 class="apipost-doc-wrap-para-title" data-text="基本信息" id="基本信息">
                        <i class="fa fa-wpforms"></i>
                        <span>基本信息</span>
                    </h3>
                    <ul class="apipost-doc-wrap-base-para">
                        {{if _.has(data, 'formatMark') && _.isObject(data.formatMark)}}
                        <li><span>接口状态： </span><span style="color: {{data.formatMark.color}};">{{data.formatMark.name}}</span> </li>
                        {{/if}}
                    </ul>
                    {{if _.has(data, 'protos') && _.isObject(data.protos) && Object.values(data.protos).length > 0}}
                    <h3 class="apipost-doc-wrap-para-title" data-text="方法列表" id="方法列表">
                        <i class="fa fa-wpforms"></i>
                        <span>方法列表</span>
                    </h3>
                    {{each data.protos item key}}
                    {{if _.has(item, 'services') && _.isObject(item.services) && !_.isEmpty(item.services)}}
                    <h4 class="apipost-doc-wrap-para-title" data-text="{{key}}" id="{{key}}">
                        <span>{{key}}</span>
                    </h4>
                    <div class="apipost-doc-paras" for-id="{{key}}">
                        {{each item.services service namespace}}
                        {{if _.isObject(service)}}
                        {{each service method methodName}}
                        <h5 data-text="{{namespace}}/{{methodName}}" id="{{namespace}}/{{methodName}}"><a name="{{key}}/{{namespace}}/{{methodName}}">[method]</a> {{key}}/{{namespace}}/{{methodName}}</h5>
                        <ul class="apipost-doc-wrap-base-para">
                            <li><span>方法名： </span><span><i class="copy-clipboard"  data-clipboard-text="{{methodName}}">{{methodName}}</i></span> </li>
                            <li><span>服务地址： </span><span><i class="copy-clipboard"  data-clipboard-text="{{method.url}}">{{method.url}}</i></span> </li>
                        </ul>
                        {{if _.has(method, 'request.body.raw') && method.request.body.raw != ''}}
                        <h6 data-text="请求参数" id="请求参数">请求参数</h6>
                        <div class="apipost-raw-json request-body-json-view">
                            <button type="button" class="copy-clipboard" data-clipboard-text="{{method.request.body.raw}}"><i class="fa fa-clipboard"></i>复制</button>
                            <input type="hidden" id="request-body-raw-editor-input-{{methodName}}" value='{{method.request.body.raw}}'>
                            <pre class="request-body-raw-editor-json" id="request-body-raw-editor-json-{{methodName}}"><code class="json">{{method.request.body.raw}}</code></pre>
                        </div>
                        {{if _.has(method, 'request.body.parameter') && _.isArray(method.request.body.parameter) && method.request.body.parameter.length > 0}}
                        <table>
                            <thead>
                            <tr>
                                <th>参数名</th>
                                <th>参数值</th>
                                <th>是否必填</th>
                                <th>参数类型</th>
                                <th>描述说明</th>
                            </tr>
                            </thead>
                            <tbody>
                            {{each method.request.body.parameter item}}
                            <tr>
                                <td><i class="copy-clipboard"  data-clipboard-text="{{item.key}}">{{item.key}}</i></td>
                                <td style="position: relative;">
                                    {{if item.value_var}}
                                    {{item.value_var}}
                                    <img src="https://img.cdn.apipost.cn/v6/docs/img/vars.svg" alt="">
                                    <div class="vars_title">
                                        变量{{item.value}} 生成
                                    </div>
                                    {{else}}
                                    <pre>{{item.value}}</pre>
                                    {{/if}}
                                </td>
                                <td>{{(item.not_null >0) ? '是' : '否'}}</td>
                                <td>{{item.field_type}}</td>
                                <td>
                                    <pre>{{item.description}}</pre>
                                </td>
                            </tr>
                            {{/each}}
                            </tbody>
                        </table>
                        {{/if}}
                        {{/if}}
                        {{if _.has(method, 'response') && _.isObject(method.response) && !_.isEmpty(method.response)}}
                        {{each method.response response name}}
                        {{if response.raw != ''}}
                        <h6 data-text="{{name == 'success' ? '成功响应示例':'错误响应示例'}}" id="{{name == 'success' ? '成功响应示例':'错误响应示例'}}">{{name == 'success' ? '成功响应示例':'错误响应示例'}}</h6>
                        <div class="apipost-raw-json response-body-json-view">
                            <button type="button" class="copy-clipboard" data-clipboard-text="{{response.raw}}"><i class="fa fa-clipboard"></i>复制</button>
                            <input type="hidden" id="response-body-raw-editor-input-{{name}}" value='{{response.raw}}'>
                            <pre class="request-body-raw-editor-json" id="response-body-raw-editor-json-{{name}}"><code class="json">{{response.raw}}</code></pre>
                        </div>
                        {{if _.has(response, 'parameter') && _.isArray(response.parameter) && response.parameter.length > 0}}
                        <table>
                            <thead>
                            <tr>
                                <th>参数名</th>
                                <th>参数值</th>
                                <th>是否必填</th>
                                <th>参数类型</th>
                                <th>描述说明</th>
                            </tr>
                            </thead>
                            <tbody>
                            {{each response.parameter item}}
                            <tr>
                                <td><i class="copy-clipboard"  data-clipboard-text="{{item.key}}">{{item.key}}</i></td>
                                <td>
                                    <pre>{{item.value}}</pre>
                                </td>
                                <td>{{(item.not_null >0) ? '是' : '否'}}</td>
                                <td>{{item.field_type}}</td>
                                <td>
                                    <pre>{{item.description}}</pre>
                                </td>
                            </tr>
                            {{/each}}
                            </tbody>
                        </table>
                        {{/if}}
                        {{/if}}
                        {{/each}}
                        {{/if}}
                        <hr>
                        {{/each}}
                        {{/if}}
                        {{/each}}
                    </div>
                    {{/if}}
                    {{/each}}
                    {{/if}}
                    {{/if}}
                    {{if data.target_type == 'folder'}}
                    <h3 class="apipost-doc-wrap-para-title" data-text="目录参数" id="目录参数">
                        <i class="fa fa-wpforms"></i>
                        <span>目录参数</span>
                    </h3>
                    <div class="apipost-doc-paras" for-id="目录参数">
                        {{if _.has(data, 'request.header') && _.isArray(data.request.header) && data.request.header.length > 0}}
                        <h4 data-text="Header 请求参数" id="Header 请求参数">Header 请求参数</h4>
                        <table>
                            <thead>
                            <tr>
                                <th>参数名</th>
                                <th>参数值</th>
                                <th>是否必填</th>
                                <th>参数类型</th>
                                <th>描述说明</th>
                            </tr>
                            </thead>
                            <tbody>
                            {{each data.request.header item}}
                            <tr>
                                <td><i class="copy-clipboard"  data-clipboard-text="{{item.key}}">{{item.key}}</i></td>
                                <td style="position: relative;">
                                    {{if item.value_var}}
                                    {{item.value_var}}
                                    <img src="https://img.cdn.apipost.cn/v6/docs/img/vars.svg" alt="">
                                    <div class="vars_title">
                                        变量{{item.value}} 生成
                                    </div>
                                    {{else}}
                                    <pre>{{item.value}}</pre>
                                    {{/if}}
                                </td>
                                <td>{{(item.not_null >0) ? '是' : '否'}}</td>
                                <td>{{item.field_type}}</td>
                                <td>
                                    <pre>{{item.description}}</pre>
                                </td>
                            </tr>
                            {{/each}}
                            </tbody>
                        </table>
                        {{/if}}
                        {{if _.has(data, 'request.query') && _.isArray(data.request.query) && data.request.query.length > 0}}
                        <h4 data-text="Query 请求参数" id="Query 请求参数">Query 请求参数</h4>
                        <table>
                            <thead>
                            <tr>
                                <th>参数名</th>
                                <th>参数值</th>
                                <th>是否必填</th>
                                <th>参数类型</th>
                                <th>描述说明</th>
                            </tr>
                            </thead>
                            <tbody>
                            {{each data.request.query item}}
                            <tr>
                                <td><i class="copy-clipboard"  data-clipboard-text="{{item.key}}">{{item.key}}</i></td>
                                <td style="position: relative;">
                                    {{if item.value_var}}
                                    {{item.value_var}}
                                    <img src="https://img.cdn.apipost.cn/v6/docs/img/vars.svg" alt="">
                                    <div class="vars_title">
                                        变量{{item.value}} 生成
                                    </div>
                                    {{else}}
                                    <pre>{{item.value}}</pre>
                                    {{/if}}
                                </td>
                                <td>{{(item.not_null >0) ? '是' : '否'}}</td>
                                <td>{{item.field_type}}</td>
                                <td>
                                    <pre>{{item.description}}</pre>
                                </td>
                            </tr>
                            {{/each}}
                            </tbody>
                        </table>
                        {{/if}}
                        {{if _.has(data, 'request.body') && _.isArray(data.request.body) && data.request.body.length > 0}}
                        <h4 data-text="Body 请求参数" id="Body 请求参数">Body 请求参数</h4>
                        <table>
                            <thead>
                            <tr>
                                <th>参数名</th>
                                <th>参数值</th>
                                <th>是否必填</th>
                                <th>参数类型</th>
                                <th>描述说明</th>
                            </tr>
                            </thead>
                            <tbody>
                            {{each data.request.body item}}
                            <tr>
                                <td><i class="copy-clipboard"  data-clipboard-text="{{item.key}}">{{item.key}}</i></td>
                                <td style="position: relative;">
                                    {{if item.value_var}}
                                    {{item.value_var}}
                                    <img src="https://img.cdn.apipost.cn/v6/docs/img/vars.svg" alt="">
                                    <div class="vars_title">
                                        变量{{item.value}} 生成
                                    </div>
                                    {{else}}
                                    <pre>{{item.type == 'File' ? '[文件路径]':item.value}}</pre>
                                    {{/if}}
                                </td>
                                <td>{{(item.not_null >0) ? '是' : '否'}}</td>
                                <td>{{item.type == 'File' ? '[文件类型]':item.field_type}}</td>
                                <td>
                                    <pre>{{item.description}}</pre>
                                </td>
                            </tr>
                            {{/each}}
                            </tbody>
                        </table>
                        {{/if}}
                        {{if _.has(data, 'request.authType')}}
                        <h4 data-text="认证方式" id="认证方式">认证方式</h4>
                        <ul class="apipost-doc-wrap-base-para">
                            <li>{{data.request.authType}}</li>
                        </ul>
                        {{/if}}
                    </div>
                    {{/if}}
                    {{if data.target_type == 'global'}}
                    <div class="apipost-global-tips">
                        说明：全局公共参数是针对项目而言的，所有请求的 HTTP 类型的接口都需要携带此参数。
                    </div>
                    <h3 data-text="全局公共参数" id="全局公共参数" class="apipost-doc-wrap-para-title">
                        <i class="fa fa-wpforms"></i>
                        <span>全局公共参数</span>
                    </h3>
                    <div class="apipost-doc-paras" for-id="全局公共参数">
                        {{if _.has(data, 'request.header') && _.isArray(data.request.header) && data.request.header.length > 0}}
                        <h4 data-text="Header 请求参数" id="Header 请求参数" >Header 请求参数</h4>
                        <table>
                            <thead>
                            <tr>
                                <th>参数名</th>
                                <th>参数值</th>


                                <th>描述说明</th>
                            </tr>
                            </thead>
                            <tbody>
                            {{each data.request.header item}}
                            <tr>
                                <td><i class="copy-clipboard"  data-clipboard-text="{{item.key}}">{{item.key}}</i></td>
                                <td style="position: relative;">
                                    {{if item.value_var}}
                                    {{item.value_var}}
                                    <img src="https://img.cdn.apipost.cn/v6/docs/img/vars.svg" alt="">
                                    <div class="vars_title">
                                        变量{{item.value}} 生成
                                    </div>
                                    {{else}}
                                    <pre>{{item.value}}</pre>
                                    {{/if}}
                                </td>


                                <td>
                                    <pre>{{item.description}}</pre>
                                </td>
                            </tr>
                            {{/each}}
                            </tbody>
                        </table>
                        {{/if}}
                        {{if _.has(data, 'request.query') && _.isArray(data.request.query) && data.request.query.length > 0}}
                        <h4 data-text="Query 请求参数" id="Query 请求参数">Query 请求参数</h4>
                        <table>
                            <thead>
                            <tr>
                                <th>参数名</th>
                                <th>参数值</th>


                                <th>描述说明</th>
                            </tr>
                            </thead>
                            <tbody>
                            {{each data.request.query item}}
                            <tr>
                                <td><i class="copy-clipboard"  data-clipboard-text="{{item.key}}">{{item.key}}</i></td>
                                <td style="position: relative;">
                                    {{if item.value_var}}
                                    {{item.value_var}}
                                    <img src="https://img.cdn.apipost.cn/v6/docs/img/vars.svg" alt="">
                                    <div class="vars_title">
                                        变量{{item.value}} 生成
                                    </div>
                                    {{else}}
                                    <pre>{{item.value}}</pre>
                                    {{/if}}
                                </td>


                                <td>
                                    <pre>{{item.description}}</pre>
                                </td>
                            </tr>
                            {{/each}}
                            </tbody>
                        </table>
                        {{/if}}
                        {{if _.has(data, 'request.body') && _.isArray(data.request.body) && data.request.body.length > 0}}
                        <h4 data-text="Body 请求参数" id="Body 请求参数">Body 请求参数</h4>
                        <table>
                            <thead>
                            <tr>
                                <th>参数名</th>
                                <th>参数值</th>


                                <th>描述说明</th>
                            </tr>
                            </thead>
                            <tbody>
                            {{each data.request.body item}}
                            <tr>
                                <td><i class="copy-clipboard"  data-clipboard-text="{{item.key}}">{{item.key}}</i></td>
                                <td style="position: relative;">
                                    {{if item.value_var}}
                                    {{item.value_var}}
                                    <img src="https://img.cdn.apipost.cn/v6/docs/img/vars.svg" alt="">
                                    <div class="vars_title">
                                        变量{{item.value}} 生成
                                    </div>
                                    {{else}}
                                    <pre>{{item.type == 'File' ? '[文件路径]':item.value}}</pre>
                                    {{/if}}
                                </td>


                                <td>
                                    <pre>{{item.description}}</pre>
                                </td>
                            </tr>
                            {{/each}}
                            </tbody>
                        </table>
                        {{/if}}
                        {{if _.has(data, 'script.auth.type')}}
                        <h4 data-text="认证方式" id="认证方式">认证方式</h4>
                        <ul class="apipost-doc-wrap-base-para">
                            <li>{{data.script.auth.type}}</li>
                        </ul>
                        {{/if}}
                    </div>
                    {{/if}}
                    {{if data.target_type == 'websocket' || data.example_type == 'websocket'}}
                    <h3 data-text="基本信息" id="基本信息" class="apipost-doc-wrap-para-title">
                        <i class="fa fa-wpforms"></i>
                        <span>基本信息</span>
                    </h3>
                    <ul class="apipost-doc-wrap-base-para">
                        {{if _.has(data, 'formatMark') && _.isObject(data.formatMark)}}
                        <li><span>接口状态： </span><span style="color: {{data.formatMark.color}};">{{data.formatMark.name}}</span> </li>
                        {{/if}}
                        <li><span>接口URL： </span><span><em class="method {{data.method}}">{{data.method}}</em>
                                <i class="copy-clipboard"  data-clipboard-text="{{data.url}}">{{data.url}}</i></span> </li>
                    </ul>
                    {{/if}}
                    {{if data.target_type == 'api' || data.example_type == 'api'}}
                    <h3 data-text="基本信息" id="基本信息" class="apipost-doc-wrap-para-title">
                        <i class="fa fa-wpforms"></i>
                        <span>基本信息</span>
                    </h3>
                    <ul class="apipost-doc-wrap-base-para">
                        {{if _.has(data, 'formatMark') && _.isObject(data.formatMark)}}
                        <li><span>接口状态： </span><span style="color: {{data.formatMark.color}};">{{data.formatMark.name}}</span> </li>
                        {{/if}}
                        <li><span>接口URL： </span><span><em class="method {{data.method}}">{{data.method}}</em>
                                <i class="copy-clipboard"  data-clipboard-text="{{data.url}}">{{data.url}}</i></span> </li>
                        {{if data.mock_url != ''}}
                        <li><span>本地 Mock： </span><span><i class="copy-clipboard"  data-clipboard-text="{{urlJoin('http://127.0.0.1:10393/mock/',data.project_id, data.mock_url,'?apipost_id='+data.target_id.toString().substr(0,6))}}">{{urlJoin('http://127.0.0.1:10393/mock/',data.project_id, data.mock_url,'?apipost_id='+data.target_id.toString().substr(0,6))}}</i><br><a href="https://www.apipost.cn/download.html" target="_blank">需安装 Apipost v7 客户端</a> </span> </li>
                        {{if data.enable_server_mock > 0}}
                        <li><span>云端 Mock： </span><span><i class="copy-clipboard"  data-clipboard-text="{{urlJoin('https://console-mock.apipost.cn/mock/',data.project_id, data.mock_url,'?apipost_id='+data.target_id.toString().substr(0,6))}}">{{urlJoin('https://console-mock.apipost.cn/mock/',data.project_id, data.mock_url,'?apipost_id='+data.target_id.toString().substr(0,6))}}</i></span> </li>
                        {{/if}}
                        {{/if}}
                        {{if _.has(data, 'request.body.formatMode')}}
                        <li><span>Content-Type：</span><span><i class="copy-clipboard" data-clipboard-text="{{data.request.body.formatMode}}">{{data.request.body.formatMode}}</i></span></li>
                        {{/if}}
                        {{if _.has(data, 'request.authType')}}
                        <li><span>认证方式：</span><span><i class="copy-clipboard" data-clipboard-text="{{data.request.authType}}">{{data.request.authType}}</i></span></li>
                        {{/if}}
                    </ul>
                    <h3 data-text="请求参数" id="请求参数" class="apipost-doc-wrap-para-title">
                        <i class="fa fa-wpforms"></i>
                        <span>请求参数</span>
                    </h3>
                    <div class="apipost-doc-paras" for-id="请求参数">
                        {{if _.has(data, 'request.header.parameter') && _.isArray(data.request.header.parameter) && data.request.header.parameter.length > 0}}
                        <h4 data-text="Header 请求参数" id="Header 请求参数" >Header 请求参数</h4>
                        <table>
                            <thead>
                            <tr>
                                <th>参数名</th>
                                <th>参数值</th>
                                <th>是否必填</th>
                                <th>参数类型</th>
                                <th>描述说明</th>
                            </tr>
                            </thead>
                            <tbody>
                            {{each data.request.header.parameter item}}
                            <tr>
                                <td><i class="copy-clipboard"  data-clipboard-text="{{item.key}}">{{item.key}}</i></td>
                                <td style="position: relative;">
                                    {{if item.value_var}}
                                    {{item.value_var}}
                                    <img src="https://img.cdn.apipost.cn/v6/docs/img/vars.svg" alt="">
                                    <div class="vars_title">
                                        变量{{item.value}} 生成
                                    </div>
                                    {{else}}
                                    <pre>{{item.value}}</pre>
                                    {{/if}}
                                </td>
                                <td>{{(item.not_null >0) ? '是' : '否'}}</td>
                                <td>{{item.field_type}}</td>
                                <td>
                                    <pre>{{item.description}}</pre>
                                </td>
                            </tr>
                            {{/each}}
                            </tbody>
                        </table>
                        {{/if}}
                        {{if _.has(data, 'request.query.parameter') && _.isArray(data.request.query.parameter) && data.request.query.parameter.length > 0}}
                        <h4 data-text="Query 请求参数" id="Query 请求参数" >Query 请求参数</h4>
                        <table>
                            <thead>
                            <tr>
                                <th>参数名</th>
                                <th>参数值</th>
                                <th>是否必填</th>
                                <th>参数类型</th>
                                <th>描述说明</th>
                            </tr>
                            </thead>
                            <tbody>
                            {{each data.request.query.parameter item}}
                            <tr>
                                <td><i class="copy-clipboard"  data-clipboard-text="{{item.key}}">{{item.key}}</i></td>
                                <td style="position: relative;">
                                    {{if item.value_var}}
                                    {{item.value_var}}
                                    <img src="https://img.cdn.apipost.cn/v6/docs/img/vars.svg" alt="">
                                    <div class="vars_title">
                                        变量{{item.value}} 生成
                                    </div>
                                    {{else}}
                                    <pre>{{item.type == 'File' ? '[文件路径]':item.value}}</pre>
                                    {{/if}}
                                </td>
                                <td>{{(item.not_null >0) ? '是' : '否'}}</td>
                                <td>{{item.field_type}}</td>
                                <td>
                                    <pre>{{item.description}}</pre>
                                </td>
                            </tr>
                            {{/each}}
                            </tbody>
                        </table>
                        {{/if}}
                        {{if _.has(data, 'request.resful.parameter') && _.isArray(data.request.resful.parameter) && data.request.resful.parameter.length > 0}}
                        <h4 data-text="路径参数及说明" id="路径参数及说明" >路径参数及说明</h4>
                        <table>
                            <thead>
                            <tr>
                                <th>参数名</th>
                                <th>参数值</th>
                                <th>描述说明</th>
                            </tr>
                            </thead>
                            <tbody>
                            {{each data.request.resful.parameter item}}
                            <tr>
                                <td><i class="copy-clipboard"  data-clipboard-text="{{item.key}}">{{item.key}}</i></td>
                                <td style="position: relative;">
                                    {{if item.value_var}}
                                    {{item.value_var}}
                                    <img src="https://img.cdn.apipost.cn/v6/docs/img/vars.svg" alt="">
                                    <div class="vars_title">
                                        变量{{item.value}} 生成
                                    </div>
                                    {{else}}
                                    <pre>{{item.type == 'File' ? '[文件路径]':item.value}}</pre>
                                    {{/if}}
                                </td>
                                <td>
                                    <pre>{{item.description}}</pre>
                                </td>
                            </tr>
                            {{/each}}
                            </tbody>
                        </table>
                        {{/if}}
                        {{if _.has(data, 'request.body.mode') && data.request.body.mode != 'none'}}
                        <h4 data-text="Body 请求参数" id="Body 请求参数" >Body 请求参数</h4>
                        {{if data.request.body.mode == 'form-data' || data.request.body.mode == 'urlencoded'}}
                        {{if _.has(data, 'request.body.parameter') && _.isArray(data.request.body.parameter) && data.request.body.parameter.length > 0}}
                        <table>
                            <thead>
                            <tr>
                                <th>参数名</th>
                                <th>参数值</th>
                                <th>是否必填</th>
                                <th>参数类型</th>
                                <th>描述说明</th>
                            </tr>
                            </thead>
                            <tbody>
                            {{each data.request.body.parameter item}}
                            <tr>
                                <td><i class="copy-clipboard"  data-clipboard-text="{{item.key}}">{{item.key}}</i></td>
                                <td style="position: relative;">
                                    {{if item.value_var}}
                                    {{item.value_var}}
                                    <img src="https://img.cdn.apipost.cn/v6/docs/img/vars.svg" alt="">
                                    <div class="vars_title">
                                        变量{{item.value}} 生成
                                    </div>
                                    {{else}}
                                    <pre>{{item.type == 'File' ? '[文件路径]':item.value}}</pre>
                                    {{/if}}
                                </td>
                                <td>{{(item.not_null >0) ? '是' : '否'}}</td>
                                <td>{{item.type == 'File' ? '[文件类型]':item.field_type}}</td>
                                <td>
                                    <pre>{{item.description}}</pre>
                                </td>
                            </tr>
                            {{/each}}
                            </tbody>
                        </table>
                        {{/if}}
                        {{else}}
                        {{if data.request.body.raw != ''}}
                        <div class="apipost-raw-json request-body-json-view">
                            <button type="button" class="copy-clipboard" data-clipboard-text="{{data.request.body.raw}}"><i class="fa fa-clipboard"></i>复制</button>
                            <input type="hidden" id="request-body-raw-editor-input" value='{{data.request.body.raw}}'>
                            <pre id="request-body-raw-editor-json"><code class="json">{{data.request.body.raw}}</code></pre>
                        </div>
                        {{if _.has(data, 'request.body.raw_para') && _.isArray(data.request.body.raw_para) && data.request.body.raw_para.length > 0}}
                        <table>
                            <thead>
                            <tr>
                                <th>参数名</th>
                                <th>参数值</th>
                                <th>是否必填</th>
                                <th>参数类型</th>
                                <th>描述说明</th>
                            </tr>
                            </thead>
                            <tbody>
                            {{each data.request.body.raw_para item}}
                            {{if item.key != ''}}
                            <tr>
                                <td><i class="copy-clipboard"  data-clipboard-text="{{item.key}}">{{item.key}}</i></td>
                                <td style="position: relative;">
                                    {{if item.value_var}}
                                    {{item.value_var}}
                                    <img src="https://img.cdn.apipost.cn/v6/docs/img/vars.svg" alt="">
                                    <div class="vars_title">
                                        变量{{item.value}} 生成
                                    </div>
                                    {{else}}
                                    <pre>{{item.value}}</pre>
                                    {{/if}}
                                </td>
                                <td>{{(item.not_null >0) ? '是' : '否'}}</td>
                                <td>{{item.field_type}}</td>
                                <td>
                                    <pre>{{item.description}}</pre>
                                </td>
                            </tr>
                            {{/if}}
                            {{/each}}
                            </tbody>
                        </table>
                        {{/if}}
                        {{/if}}
                        {{/if}}
                        {{/if}}
                    </div>
                    {{if _.has(data, 'response') && _.isObject(data.response) && Object.values(data.response).length > 0}}
                    <h3 data-text="响应示例" id="响应示例" class="apipost-doc-wrap-para-title">
                        <i class="fa fa-wpforms"></i>
                        <span>响应示例</span>
                    </h3>
                    <div class="apipost-doc-paras" for-id="响应示例">
                        {{each data.response item key}}
                        {{if _.has(item, 'expect.name') && item.raw != ''}}
                        <h4 data-text="{{item.expect.name}}（{{item.expect.code}}）" id="{{item.expect.name}}（{{item.expect.code}}）">{{item.expect.name}}（{{item.expect.code}}）</h4>
                        <div data-type="{{key}}" class="apipost-raw-json response-raw-json-view">
                            <button type="button" class="copy-clipboard" data-clipboard-text="{{item.raw}}"><i class="fa fa-clipboard"></i>复制</button>
                            <input type="hidden" id="response-raw-{{key}}-json-input" value='{{item.raw}}'>
                            <pre id="response-raw-{{key}}-json"><code class="json">{{item.raw}}</code></pre>
                        </div>
                        {{if _.has(item, 'parameter') && _.isArray(item.parameter) && item.parameter.length > 0}}
                        <table>
                            <thead>
                            <tr>
                                <th>参数名</th>
                                <th>参数值</th>
                                <!-- <th>是否必填</th> -->
                                <th>参数类型</th>
                                <th>描述说明</th>
                            </tr>
                            </thead>
                            <tbody>
                            {{each item.parameter item}}
                            {{if item.key != ''}}
                            <tr>
                                <td><i class="copy-clipboard"  data-clipboard-text="{{item.key}}">{{item.key}}</i></td>
                                <td>
                                    <pre>{{item.value}}</pre>
                                </td>
                                <!-- <td>{{(item.not_null >0) ? '是' : '否'}}</td> -->
                                <td>{{item.field_type}}</td>
                                <td>
                                    <pre>{{@replaceOperatorSymbol(item.description)}}</pre>
                                </td>
                            </tr>
                            {{/if}}
                            {{/each}}
                            </tbody>
                        </table>
                        {{/if}}
                        {{/if}}
                        {{/each}}
                    </div>
                    {{/if}}
                    {{/if}}
                    <footer> 本文档由 <a href="https://www.apipost.cn/" target="_blank">Apipost 接口管理工具 （https://www.apipost.cn）</a>
                        生成 </footer>
                </script>
            </div>
            <div class="apipost-float-navs">
                <h2>内容导航</h2>
                <ul class="apipost-float-nav-template">
                    <script type="text/html" id="apipost-float-nav-template-tpl">
                        {{if list.length > 0}}
                        {{each list item i}}
                        <li style="text-indent: {{item.indent*16}}px;" {{if i == 0}}class="active"{{/if}} data-id="{{item.id}}"><a href="#{{item.id}}">{{item.text}}</a></li>
                        {{/each}}
                        {{else}}
                        <li style="text-align: center;justify-content: center;">暂无导航数据</li>
                        {{/if}}
                    </script>
                </ul>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    // 给 arttemplate 注入新函数
    template.defaults.imports.JSON = JSON;
    template.defaults.imports.urlJoin = urlJoin;
    template.defaults.imports._ = _;
    template.defaults.imports.marked = function (md) {
        marked.setOptions({
            renderer: new marked.Renderer(),
            highlight(code) {
                return hljs.highlight('javascript', code).value;
            },
            breaks: true,
            sanitize: true,
        });
        return marked.parse(md).replace(/<pre>/g, "<pre class='hljs javascript'>");
    };
    template.defaults.imports.Object = Object;
    template.defaults.imports.keyFormat = function (params) {
        const arr = params.split('.');
        let str = '';
        for (let i = 0; i < arr.length - 1; i++) {
            str += `<span style="color:#999">${arr[i]}.</span>`;
        }
        str += arr[arr.length - 1];
        return str;
    };
    template.defaults.imports.replaceOperatorSymbol = function (originString) {
        if (!originString || originString=='') {
            return '';
        }
        const result = originString.replace(/&([^&;]+);/g, function (matchStr, b) {
            const entity = {
                quot: '"',
                lt: '<',
                gt: '>',
                apos: "'",
                amp: '&',
                ldquo: '“',
                rdquo: '”',
            };
            const r = entity[b];
            return typeof r === 'string' ? r : matchStr;
        });
        return result;
    };
    template.defaults.imports.markForName = function (key) {
        try {
            if (apipostData.project.vars.mark.filter((it) => it.key === key)[0].name) {
                return apipostData.project.vars.mark.filter((it) => it.key === key)[0].name;
            }
            return '开发中';
        } catch (error) {
            return '开发中';
        }
    };

    // 接口排序
    function sortData(data, type) {
        data.sort((a, b) => {
            // 升序
            if (type == 'ascending') {
                if (a.sort != b.sort) {
                    return a.sort - b.sort;
                }
                return a.name.localeCompare(b.name);
            }
            // 降序
            if (type == 'descending') {
                if (a.sort != b.sort) {
                    return b.sort - a.sort;
                }
                return b.name.localeCompare(a.name);
            }
        });

        // 如果有嵌套children且children里面有数据 就递归调用排序函数
        if (_.isArray(data)) {
            data.forEach((item) => {
                if (item.children && item.children.length != 0) {
                    sortData(item.children, type);
                }
            });

            return data;
        }
        return [];
    }

    // 解析 url 并返回数组
    function GetUrlQuery(uri) {
        let url = '';
        if (typeof uri === 'undefined') {
            url = window.location.search;
        } else {
            url = `?${uri.split('?')[1]}`;
        }
        const theRequest = {};
        if (url.indexOf('?') !== -1) {
            const str = url.substr(1);
            const strs = str.split('&');
            for (let i = 0; i < strs.length; i++) {
                theRequest[strs[i].split('=')[0]] = unescape(strs[i].split('=')[1]);
            }
        }
        return theRequest;
    }

    // 获取某接口的 所有父target
    function getParentTargetIDs(collection, target_id, parent_ids = []) {
        if (_.isArray(collection)) {
            const item = _.find(collection, _.matchesProperty('target_id', target_id));

            if (item) {
                parent_ids.push(item.parent_id);
                getParentTargetIDs(collection, item.parent_id, parent_ids);
            }
        }

        return parent_ids;
    }

    // 获取某接口的 所有子target
    function getChildrenTargetIDs(collection, target_id, children_ids = []) {
        if (_.isArray(collection)) {
            collection.forEach(item => {
                if (item.parent_id == target_id) {
                    children_ids.push(item.target_id);
                    getChildrenTargetIDs(collection, item.target_id, children_ids);
                }
            })
        }

        return children_ids;
    }

    // // 获取某接口的 所有子target
    // function getChildrenTargetIDs(collection, target_id, children_ids = []) {
    //     if (_.isArray(collection)) {
    //         collection.forEach(item => {
    //             if (item.target_id == target_id) {
    //                 if (item.children && _.isArray(item.children)) {
    //                     item.children.forEach((it) => {
    //                         children_ids.push(it.target_id);
    //                         if (it.children && _.isArray(it.children)) {
    //                             getChildrenTargetIDs(it.children, it.target_id, children_ids);
    //                         }
    //                     })
    //                 }
    //             }
    //         })
    //     }
    //     return children_ids;
    // }

    // 接口数据
    let apipostData = {};

    try {
        apipostData = {"data":{"id":1404786,"local_issue_id":"857e14b88e26e86a60f63b3ba55268fa","local_target_id":"a3fb6fe8-9132-41ce-af02-74be505d7d45","project_id":3209647,"name":"数字交换平台DX","url":"d1fb626aa043332a","salt":"70ee7727d6b49b5a","password":"","env_id":"-1","create_time":1696209549,"target_id":-1,"target_type":"project","expire_days":-1,"expire_time":-1,"attribute_list":[],"env_ids":["-1"],"portrait":"https:\/\/img.cdn.apipost.cn\/user\/default_profile_photo\/Vector-8.png","nick_name":"ZW","app_web_url":"https:\/\/v7-api.apipost.cn","project_name":"数字交换平台DX","publisher":"ZW","pub_name":"数字交换平台DX","project_logo":"","project":{"vars":{"request":{"cookie":[],"header":[{"is_checked":"1","type":"Text","key":"sign","value":"b8706735adc1f6a70bec6283f6089da0","description":"通过对分配的key，加上时间戳，进行MD5后的十六进制32位（小写）。MD5(key+timestamp)。接收端，按照相同算法，校验计算后数值是否正确"},{"description":"当前时间(YYYYmmDDHHMMSS格式，24小时制)","field_type":"String","is_checked":1,"key":"timestamp","value":"20230801120112","not_null":1,"type":"Text"},{"description":"UUID生成","field_type":"String","is_checked":1,"key":"nonce","value":"","not_null":1,"type":"Text"},{"description":"平台分配","field_type":"String","is_checked":1,"key":"mic-code","value":"MIC1.0001","not_null":1,"type":"Text"},{"description":"平台分配的ID。如果是平台发起的，传的就是MISC1.000001","field_type":"String","is_checked":1,"key":"misc-id","value":"MISC1.000001","not_null":1,"type":"Text"}],"query":[],"body":[]},"script":{"pre_script":"","test":"","auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""},"digest":{"username":"","password":"","realm":"","nonce":"","algorithm":"","qop":"","nc":"","cnonce":"","opaque":""},"hawk":{"authId":"","authKey":"","algorithm":"","user":"","nonce":"","extraData":"","app":"","delegation":"","timestamp":"","includePayloadHash":-1},"awsv4":{"accessKey":"","secretKey":"","region":"","service":"","sessionToken":"","addAuthDataToQuery":-1},"ntlm":{"username":"","password":"","domain":"","workstation":"","disableRetryRequest":1},"edgegrid":{"accessToken":"","clientToken":"","clientSecret":"","nonce":"","timestamp":"","baseURi":"","headersToSign":""},"oauth1":{"consumerKey":"","consumerSecret":"","signatureMethod":"","addEmptyParamsToSign":-1,"includeBodyHash":-1,"addParamsToHeader":-1,"realm":"","version":"1.0","nonce":"","timestamp":"","verifier":"","callback":"","tokenSecret":"","token":""}}},"variable":[],"methods":["POST","GET","PUT","PATCH","DELETE","COPY","HEAD","OPTIONS","LINK","UNLINK","PURGE","LOCK","UNLOCK","PROPFIND","VIEW"],"mark":[{"key":"developing","name":"开发中","color":"#3A86FF","is_default":true},{"key":"complated","name":"已完成","color":"#2BA58F","is_default":true},{"key":"modifying","name":"需修改","color":"#EC4646","is_default":true}]},"code_list":[],"attribute_list":[]},"attribute_lists":[],"children":[{"local_target_id":"473c2955-3504-45fd-a53d-4a3731078c95","local_parent_id":"0","name":"体检系统对接主检接口","method":"POST","mark":"developing","is_doc":0,"target_type":"folder","example_type":"0","project_id":3209647,"status":1,"sort":2,"create_dtime":1696206224,"update_dtime":1696295070,"local_server_id":"","target_id":"473c2955-3504-45fd-a53d-4a3731078c95","modifier_id":"U3NEF5486420","created_uuid":"U3NEF5486420","extend_server_id":"","create_time":"2023-10-02 08:23:44","create_users":{"create_user":"ZW","update_user":"ZW"},"color":"#3A86FF","attribute_lists":[],"script":{"pre_script":"","test":"","pre_script_switch":1,"test_switch":1},"request":{"description":"","header":[],"query":[],"body":[],"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""}}},"parent_id":"0","children":[{"local_target_id":"ec49338f-2f42-4317-8d79-5feb19bafa86","local_parent_id":"473c2955-3504-45fd-a53d-4a3731078c95","name":"体检系统发起","method":"POST","mark":"developing","is_doc":0,"target_type":"folder","example_type":"0","project_id":3209647,"status":1,"sort":2,"create_dtime":1696209009,"update_dtime":1696557914,"local_server_id":"","target_id":"ec49338f-2f42-4317-8d79-5feb19bafa86","modifier_id":"U3NEF5486420","created_uuid":"U3NEF5486420","extend_server_id":"","create_time":"2023-10-02 09:10:09","create_users":{"create_user":"ZW","update_user":"ZW"},"color":"#3A86FF","attribute_lists":[],"script":{"pre_script":"","test":"","pre_script_switch":1,"test_switch":1},"request":{"description":"本目录下的接口，均为体检系统主动发起。\n该请求发起后，本平台，立即存入队列中，保证立即返回体检系统。以此确保体检系统的主业务流不受本系统影响。\n体检系统如果有研发能力，可以自行设计自己的队列来异步发送给本系统。但是必须确保数据延迟不超过5分钟。","header":[],"query":[],"body":[],"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""}}},"parent_id":"473c2955-3504-45fd-a53d-4a3731078c95","children":[{"local_target_id":"2fb8f937-c8f9-4c72-8ca5-2519cf9cb8a1","local_parent_id":"ec49338f-2f42-4317-8d79-5feb19bafa86","name":"01.单次体检基本信息传输","method":"POST","mark":"developing","is_doc":0,"target_type":"api","example_type":"0","project_id":"a3fb6fe8-9132-41ce-af02-74be505d7d45","status":1,"sort":0,"create_dtime":1696207113,"update_dtime":1749642311,"local_server_id":"","target_id":"2fb8f937-c8f9-4c72-8ca5-2519cf9cb8a1","modifier_id":"6515BXWEVF9DSF07DDFF0BE164AFFE33CB0E7A81D1D0","created_uuid":"U3NEF5486420","extend_server_id":"","create_time":"2023-10-02 08:38:33","create_users":{"create_user":"ZW","update_user":"林为国"},"color":"#3A86FF","attribute_lists":[],"enable_server_mock":-1,"mock_server_url":"","mock_url":"","request":{"url":"\/dx\/inter\/sendPeInfo","description":"本接口传输体检的基本信息。在以下信息变化时，均要触发此接口调用DX：\n1. 体检单状态发生变化后（包括登记、分科完成、主检完成等等一切可能的状态）\n2. 体检单对应的档案信息发生变化。","body":{"mode":"json","parameter":[],"raw":"{\n\t\"peUserInfo\": {\n\t\t\"archiveNo\": \"档案号1\",\n\t\t\"name\": \"张三\",\n\t\t\"icCode\": \"12334456456544\",\n\t\t\"sex\": {\n\t\t\t\"code\": \"1\",\n\t\t\t\"name\": \"男\"\n\t\t},\n\t\t\"birthday\": \"20000101\",\n\t\t\"peno\": \"123456\",\n\t\t\"peDate\": \"2020-01-01 12:00:00\",\n\t\t\"phone\": \"15612345678\",\n\t\t\"ms\": {\n\t\t\t\"code\": \"七特压住义基\",\n\t\t\t\"name\": \"价热小定样带证合低位路\"\n\t\t},\n\t\t\"pregnantState\": {\n\t\t\t\"code\": \"导第\",\n\t\t\t\"name\": \"调\"\n\t\t},\n\t\t\"vipLevel\": {\n\t\t\t\"code\": \"VIP编码\",\n\t\t\t\"name\": \"VIP名称\"\n\t\t},\n\t\t\"medicalType\": {\n\t\t\t\"code\": \"招工体检\",\n\t\t\t\"name\": \"招工体检名称\"\n\t\t},\n\t\t\"isGroup\": false,\n\t\t\"company\": \"\",\n\t\t\"workDept\": \"\",\n\t\t\"teamNo\": \"\",\n\t\t\"professional\": \"\",\n\t\t\"workAge\": \"\",\n\t\t\"peStates\": {\n\t\t\t\"code\": \"7\",\n\t\t\t\"name\": \"主检终审完成\"\n\t\t},\n\t\t\"deptCount\": 12,\n\t\t\"age\": 12,\n\t\t\"deptFinishTime\": \"2020-01-01 12:00:00\",\n\t\t\"firstCheckFinishTime\": \"2020-01-01 12:00:00\",\n\t\t\"firstCheckFinishDoctor\": {\n\t\t\t\"code\": \"医生编码1\",\n\t\t\t\"name\": \"医生名称2\"\n\t\t},\n\t\t\"mainCheckFinishTime\": \"2020-01-01 12:00:00\",\n\t\t\"mainCheckFinishDoctor\": {\n\t\t\t\"code\": \"医生编码1\",\n\t\t\t\"name\": \"医生名称1\"\n\t\t},\n\t\t\"forbidGoCheck\": true,\n\t\t\"reportPrint\": false,\n\t\t\"reportGot\": false,\n\t\t\"replacementInspectionMark\": true,\n\t\t\"applyItemList\": [\n\t\t\t\"申请项目id1\",\n\t\t\t\"申请项目id2\"\n\t\t],\n\t\t\"currentNodeType\": 1,\n\t\t\"pePackage\": {\n\t\t\t\"code\": \"1\",\n\t\t\t\"name\": \"套餐1\"\n\t\t},\n\t\t\"urgentStatus\": \"报告是否加急处理0 未加急 1 加急\"\n\t},\n\t\"archiveInfo\": {\n\t\t\"name\": \"张三\",\n\t\t\"icCode\": \"12345646546455\",\n\t\t\"sex\": {\n\t\t\t\"code\": \"1\",\n\t\t\t\"name\": \"男\"\n\t\t},\n\t\t\"birthday\": \"20000101\",\n\t\t\"peNoList\": [\n\t\t\t\"体检号1\",\n\t\t\t\"体检号2\"\n\t\t]\n\t},\n\t\"hospital\": {\n\t\t\"code\": \"1\",\n\t\t\"name\": \"测试院区\"\n\t}\n}","raw_para":[{"key":"peUserInfo","value":"","description":"","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"peUserInfo.archiveNo","value":"档案号1","description":"体检者档案号  档案号代表同一个人的唯一标识，假设一个人到您的院区体检过3次，那么他应该有3个体检号，只会有一个档案号","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.name","value":"张三","description":"姓名","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.icCode","value":"12334456456544","description":"身份证编码","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.sex","value":"","description":"","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"peUserInfo.sex.code","value":"1","description":"1-男；2-女；3-未知","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.sex.name","value":"男","description":"性别名称","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.birthday","value":"20000101","description":"生日yyyyMMdd","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.peno","value":"123456","description":"体检号","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.peDate","value":"2020-01-01 12:00:00","description":"yyyy-MM-dd HH:mm:ss","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.phone","value":"15612345678","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.ms","value":"","description":"婚姻状态","not_null":-1,"field_type":"Object","type":"Object","is_checked":1},{"key":"peUserInfo.ms.code","value":"七特压住义基","description":"married: 已婚; unmarried: 未婚;divorce: 离婚;widowhood: 丧偶; unknown 未知","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.ms.name","value":"价热小定样带证合低位路","description":"婚姻状态名称","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.pregnantState","value":"","description":"怀孕状态","not_null":-1,"field_type":"Object","type":"Object","is_checked":1},{"key":"peUserInfo.pregnantState.code","value":"导第","description":"unconception: 未怀孕；iregnancy: 怀孕；inregnancy: 备孕中；unknown 未知","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.pregnantState.name","value":"调","description":"怀孕状态名称","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.vipLevel","value":"","description":"用户等级(和字典OPEVIP相同)","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"peUserInfo.vipLevel.code","value":"VIP编码","description":"用户等级体检系统自有编号","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.vipLevel.name","value":"VIP名称","description":"用户等级名称","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.medicalType","value":"","description":"体检类型(和字典OPBET相同)","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"peUserInfo.medicalType.code","value":"招工体检","description":"体检类型体检系统自有编号","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.medicalType.name","value":"招工体检名称","description":"体检类型名称","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.isGroup","value":"false","description":"是否团体 false true","not_null":1,"field_type":"Boolean","type":"Text","is_checked":1},{"key":"peUserInfo.company","value":"","description":"团体单位名称（可选）","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.workDept","value":"","description":"团体部门名称（可选）","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.teamNo","value":"","description":"团体任务编号(如果是团体报告，该字段必填)  团体任务编号代表一个批次团报，有相同团体任务编号的体检单会归类到同一个团报。比如有一家公司3年组织员工进行了3次体检，那么就应该有3个团体任务编号，团体任务编号应该具有唯一性，不能出现不同公司单位使用相同团体任务编号的情况，会造成团报人员混乱","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.professional","value":"","description":"职位（可选）","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.workAge","value":"","description":"工龄（可选）","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.peStates","value":"","description":"体检状态","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"peUserInfo.peStates.code","value":"7","description":"体检状态编号 1 2 3 4 5 6 7","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.peStates.name","value":"总审已完成","description":"体检状态名称:   登记完成   分科未完成   分科完成   主检初审中   主检初审完成   主检终审中   主检终审完成","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.deptCount","value":"12","description":"检查科室统计,和03接口传过来的科室数量对应。如果整个科室所有的项目都拒检，即相当于这个科室没有检查项目，那么科室数量也需要扣减掉这个科室","not_null":1,"field_type":"Integer","type":"Text","is_checked":1},{"key":"peUserInfo.age","value":"12","description":"年龄","not_null":1,"field_type":"Integer","type":"Text","is_checked":1},{"key":"peUserInfo.deptFinishTime","value":"2020-01-01 12:00:00","description":"分科完成时间yyyy-MM-dd HH:mm:ss   流程环节处于分科完成及之后的流程，该字段必填。若业务系统无相关字段，一般取最后一个项目的完成时间或者取准备主检的时间","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.firstCheckFinishTime","value":"2020-01-01 12:00:00","description":"初检完成时间 yyyy-MM-dd HH:mm:ss","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.firstCheckFinishDoctor","value":"","description":"","not_null":-1,"field_type":"Object","type":"Object","is_checked":1},{"key":"peUserInfo.firstCheckFinishDoctor.code","value":"医生编码1","description":"初检医生系统编号","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.firstCheckFinishDoctor.name","value":"医生名称2","description":"初检医生姓名","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.mainCheckFinishTime","value":"2020-01-01 12:00:00","description":"yyyy-MM-dd HH:mm:ss","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.mainCheckFinishDoctor","value":"","description":"","not_null":-1,"field_type":"Object","type":"Object","is_checked":1},{"key":"peUserInfo.mainCheckFinishDoctor.code","value":"医生编码1","description":"总审医生系统编号","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.mainCheckFinishDoctor.name","value":"医生名称1","description":"总审医生姓名","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.forbidGoCheck","value":"true","description":"禁止检查标识 false true","not_null":-1,"field_type":"Boolean","type":"Text","is_checked":1},{"key":"peUserInfo.reportPrint","value":"false","description":"报告是否打印 false true","not_null":-1,"field_type":"Boolean","type":"Text","is_checked":1},{"key":"peUserInfo.reportGot","value":"false","description":"报告是否领取 false true","not_null":-1,"field_type":"Boolean","type":"Text","is_checked":1},{"key":"peUserInfo.replacementInspectionMark","value":"true","description":"替检标志 false true","not_null":-1,"field_type":"Boolean","type":"Text","is_checked":1},{"key":"peUserInfo.applyItemList","value":"申请项目id1","description":"该体检者单次申请项目id","not_null":1,"field_type":"Array","type":"Text","is_checked":1},{"key":"peUserInfo.currentNodeType","value":"1","description":"当前节点状态1-登记2-检查3-主检4-总审","not_null":-1,"field_type":"Integer","type":"Text","is_checked":1},{"key":"peUserInfo.pePackage","value":"","description":"当前体检套餐","not_null":-1,"field_type":"Object","type":"Object","is_checked":1},{"key":"peUserInfo.pePackage.code","value":"1","description":"套餐编码","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.pePackage.name","value":"套餐1","description":"套餐名称","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"peUserInfo.urgentStatus","value":"报告是否加急处理0 未加急 1 加急","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"archiveInfo","value":"","description":"","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"archiveInfo.name","value":"张三","description":"姓名","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"archiveInfo.icCode","value":"12345646546455","description":"身份证号","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"archiveInfo.sex","value":"","description":"","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"archiveInfo.sex.code","value":"1","description":"1-男；2-女；3-未知","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"archiveInfo.sex.name","value":"男","description":"性别名称","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"archiveInfo.birthday","value":"20000101","description":"生日","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"archiveInfo.peNoList","value":"体检号1","description":"该档案下的所有体检号(包括当前体检号)","not_null":1,"field_type":"Array","type":"Text","is_checked":1},{"key":"hospital","value":"","description":"非必填，如果是单院区的情况可以不传","not_null":-1,"field_type":"Object","type":"Object","is_checked":1},{"key":"hospital.code","value":"1","description":"医院编码","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"hospital.name","value":"测试院区","description":"院区名称","not_null":1,"field_type":"String","type":"Text","is_checked":1}],"raw_schema":{"type":"object","properties":[],"APIPOST_ORDERS":["e6271cc3c7dc4f85b810d0b38948d221"],"APIPOST_REFS":{"e6271cc3c7dc4f85b810d0b38948d221":{"ref":"c2f03d19-a5ae-4ed2-a4f7-f614ee36ef46"}},"required":[]}},"event":{"pre_script":"","test":""},"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""},"digest":{"username":"","password":"","realm":"","nonce":"","algorithm":"","qop":"","nc":"","cnonce":"","opaque":""},"hawk":{"authId":"","authKey":"","algorithm":"","user":"","nonce":"","extraData":"","app":"","delegation":"","timestamp":"","includePayloadHash":-1},"awsv4":{"accessKey":"","secretKey":"","region":"","service":"","sessionToken":"","addAuthDataToQuery":-1},"ntlm":{"username":"","password":"","domain":"","workstation":"","disableRetryRequest":1},"edgegrid":{"accessToken":"","clientToken":"","clientSecret":"","nonce":"","timestamp":"","baseURi":"","headersToSign":""},"oauth1":{"consumerKey":"","consumerSecret":"","signatureMethod":"","addEmptyParamsToSign":-1,"includeBodyHash":-1,"addParamsToHeader":-1,"realm":"","version":"1.0","nonce":"","timestamp":"","verifier":"","callback":"","tokenSecret":"","token":""}},"header":{"parameter":[]},"query":{"parameter":[]},"cookie":{"parameter":[]},"resful":{"parameter":[]},"origin_url":"\/dx\/inter\/sendPeInfo"},"response":{"success":{"raw":"{\"code\":0,\"msg\":\"\",\"data\":null,\"reponseTime\":*************}","parameter":[],"expect":{"name":"成功","isDefault":1,"code":200,"contentType":"json","verifyType":"schema","schema":[],"mock":""}},"error":{"raw":"{\"code\":400,\"msg\":\"操作失败\",\"data\":null,\"reponseTime\":*************}","parameter":[],"expect":{"name":"失败","isDefault":-1,"code":404,"contentType":"json","verifyType":"schema","schema":[],"mock":""}}},"parent_id":"ec49338f-2f42-4317-8d79-5feb19bafa86"},{"local_target_id":"4f9d54cf-5e73-4c60-a8d5-b4a8320060bd","local_parent_id":"ec49338f-2f42-4317-8d79-5feb19bafa86","name":"02.申请项目字典数据传输","method":"POST","mark":"developing","is_doc":0,"target_type":"api","example_type":"0","project_id":"a3fb6fe8-9132-41ce-af02-74be505d7d45","status":1,"sort":1,"create_dtime":1696211780,"update_dtime":1748307457,"local_server_id":"","target_id":"4f9d54cf-5e73-4c60-a8d5-b4a8320060bd","modifier_id":"6515BXWEVF9DSF07DDFF0BE164AFFE33CB0E7A81D1D0","created_uuid":"U3NEF5486420","extend_server_id":"","create_time":"2023-10-02 09:56:20","create_users":{"create_user":"ZW","update_user":"林为国"},"color":"#3A86FF","attribute_lists":[],"enable_server_mock":-1,"mock_server_url":"","mock_url":"","request":{"url":"\/dx\/inter\/syncApplyItem","description":"在项目发生变更时，发送这个接口，通知添健云系统","body":{"mode":"json","parameter":[],"raw":"[\n\t{\n\t\t\"applyItemId\": \"\",\n\t\t\"applyItemName\": \"\",\n\t\t\"displaySequence\": \"\",\n\t\t\"deptId\": \"\",\n\t\t\"checkItemList\": [\n\t\t\t{\n\t\t\t\t\"checkItemId\": \"\",\n\t\t\t\t\"checkItemName\": \"\",\n\t\t\t\t\"displaySequence\": \"\"\t\t\t}\n\t\t],\n\t\t\"hospital\": {\n\t\t\t\"code\": \"\",\n\t\t\t\"name\": \"\"\n\t\t}\n\t}\n]","raw_para":[{"key":"applyItemId","value":"","description":"体检系统申请项目编号","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"applyItemName","value":"","description":"申请项目名称","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"displaySequence","value":"","description":"排序","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"deptId","value":"","description":"科室id","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"checkItemList","value":"","description":"","not_null":1,"field_type":"Array","type":"Text","is_checked":1},{"key":"checkItemList.checkItemId","value":"","description":"检查项目id","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"checkItemList.checkItemName","value":"","description":"检查项目名称","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"checkItemList.displaySequence","value":"","description":"排序","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"hospital","value":"","description":"非必填，如果是单院区的情况可以不传","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"hospital.code","value":"","description":"医院编码","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"hospital.name","value":"","description":"院区名称","not_null":1,"field_type":"String","type":"Text","is_checked":1}],"raw_schema":[]},"event":{"pre_script":"","test":""},"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""},"digest":{"username":"","password":"","realm":"","nonce":"","algorithm":"","qop":"","nc":"","cnonce":"","opaque":""},"hawk":{"authId":"","authKey":"","algorithm":"","user":"","nonce":"","extraData":"","app":"","delegation":"","timestamp":"","includePayloadHash":-1},"awsv4":{"accessKey":"","secretKey":"","region":"","service":"","sessionToken":"","addAuthDataToQuery":-1},"ntlm":{"username":"","password":"","domain":"","workstation":"","disableRetryRequest":1},"edgegrid":{"accessToken":"","clientToken":"","clientSecret":"","nonce":"","timestamp":"","baseURi":"","headersToSign":""},"oauth1":{"consumerKey":"","consumerSecret":"","signatureMethod":"","addEmptyParamsToSign":-1,"includeBodyHash":-1,"addParamsToHeader":-1,"realm":"","version":"1.0","nonce":"","timestamp":"","verifier":"","callback":"","tokenSecret":"","token":""}},"header":{"parameter":[]},"query":{"parameter":[]},"cookie":{"parameter":[]},"resful":{"parameter":[]},"origin_url":"\/dx\/inter\/syncApplyItem"},"response":{"success":{"raw":"{\"code\":0,\"msg\":\"\",\"data\":null,\"reponseTime\":*************}","parameter":[{"key":"applyItemId","value":"4321","description":"申请项目id","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"applyItemName","value":"血常规","description":"申请项目名称","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"updateTime","value":"2023-09-11 11:14:48","description":"更新时间","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"checkItemList","value":"","description":"","not_null":1,"field_type":"Array","type":"Text","is_checked":1},{"key":"checkItemList.checkItemId","value":"4321","description":"检查项目id","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"checkItemList.checktemName","value":"白细胞测定","description":"检查项目名称","not_null":1,"field_type":"String","type":"Text","is_checked":1}],"expect":{"name":"成功","isDefault":1,"code":200,"contentType":"json","verifyType":"schema","schema":[],"mock":""}},"error":{"raw":"{\"code\":400,\"msg\":\"\",\"data\":null,\"reponseTime\":*************}","parameter":[],"expect":{"name":"失败","isDefault":-1,"code":404,"contentType":"json","verifyType":"schema","schema":[],"mock":""}}},"parent_id":"ec49338f-2f42-4317-8d79-5feb19bafa86"},{"local_target_id":"dd5473b0-4963-4b00-835e-b8f9e300bb30","local_parent_id":"ec49338f-2f42-4317-8d79-5feb19bafa86","name":"03.体检科室结果传输","method":"POST","mark":"developing","is_doc":0,"target_type":"api","example_type":"0","project_id":"a3fb6fe8-9132-41ce-af02-74be505d7d45","status":1,"sort":2,"create_dtime":1696227786,"update_dtime":1749632044,"local_server_id":"","target_id":"dd5473b0-4963-4b00-835e-b8f9e300bb30","modifier_id":"6515BXWEVF9DSF07DDFF0BE164AFFE33CB0E7A81D1D0","created_uuid":"U3NEF5486420","extend_server_id":"","create_time":"2023-10-02 14:23:06","create_users":{"create_user":"ZW","update_user":"林为国"},"color":"#3A86FF","attribute_lists":[],"enable_server_mock":-1,"mock_server_url":"","mock_url":"","request":{"url":"\/dx\/inter\/deptInfo","description":"该体检者在各个科室检查结果发生任何变更后，通过本接口传输（包括科室审核与未审核，都调用通知）。说明：itemDesc里面的数据是以检查项目结果的。也就是一个申请项目下有多少条检查项目，就传多少条数据。","body":{"mode":"json","parameter":[],"raw":"[\n\t{\n\t\t\"peNo\": \"1212\",\n\t\t\"dept\": {\n\t\t\t\"code\": \"121\",\n\t\t\t\"name\": \"检验科\"\n\t\t},\n\t\t\"hospital\": {\n\t\t\t\"code\": \"编码\",\n\t\t\t\"name\": \"院区\"\n\t\t},\n\t\t\"checkTime\": \"2020-05-10 12:00:00\",\n\t\t\"checkDoctor\": {\n\t\t\t\"code\": \"编码\",\n\t\t\t\"name\": \"张三\"\n\t\t},\n\t\t\"summary\": \"未见明显异常\",\n\t\t\"auditDoctor\": {\n\t\t\t\"code\": \"编码\",\n\t\t\t\"name\": \"张三\"\n\t\t},\n\t\t\"auditStatus\": {\n\t\t\t\"code\": \"isAudited\",\n\t\t\t\"name\": \"已审核\"\n\t\t},\n\t\t\"auditTime\": \"2020-05-10 12:00:00\",\n\t\t\"itemDesc\": [\n\t\t\t{\n\t\t\t\t\"bizApplyId\": \"\",\n\t\t\t\t\"applyItem\": {\n\t\t\t\t\t\"code\": \"编码\",\n\t\t\t\t\t\"name\": \"血脂4项\"\n\t\t\t\t},\n\t\t\t\t\"checkDoctor\": {\n\t\t\t\t\t\"code\": \"检查医生编码\",\n\t\t\t\t\t\"name\": \"检查医生名称\"\n\t\t\t\t},\n\t\t\t\t\"checkItem\": {\n\t\t\t\t\t\"code\": \"编码\",\n\t\t\t\t\t\"name\": \"总胆固醇（TC）\"\n\t\t\t\t},\n\t\t\t\t\"inspectResult\": \"安基我研风张备建月效\",\n\t\t\t\t\"unit\": \"mmol\/L\",\n\t\t\t\t\"referenceMax\": 5.2,\n\t\t\t\t\"referenceMin\": 0,\n\t\t\t\t\"reference\": \"0-5.20\",\n\t\t\t\t\"displaySequence\": \"1\",\n\t\t\t\t\"digitValue\": \"12\",\n\t\t\t\t\"mds\": {\n\t\t\t\t\t\"code\": \"\",\n\t\t\t\t\t\"name\": \"↑\"\n\t\t\t\t}\n\t\t\t}\n\t\t]\n\t}\n]","raw_para":[{"key":"peNo","value":"1212","description":"体检号","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"dept","value":"","description":"科室","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"dept.code","value":"121","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"dept.name","value":"检验科","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"hospital","value":"","description":"医院编号","not_null":-1,"field_type":"Object","type":"Object","is_checked":1},{"key":"hospital.code","value":"编码","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"hospital.name","value":"院区","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"checkTime","value":"2020-05-10 12:00:00","description":"检查时间","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"checkDoctor","value":"","description":"检查医生","not_null":-1,"field_type":"Object","type":"Object","is_checked":1},{"key":"checkDoctor.code","value":"编码","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"checkDoctor.name","value":"张三","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"summary","value":"未见明显异常","description":"科室小结","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"auditDoctor","value":"","description":"审核医生","not_null":-1,"field_type":"Object","type":"Object","is_checked":1},{"key":"auditDoctor.code","value":"编码","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"auditDoctor.name","value":"张三","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"auditStatus","value":"","description":"审核状态","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"auditStatus.code","value":"isAudited","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"auditStatus.name","value":"已审核","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"auditTime","value":"2020-05-10 12:00:00","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"itemDesc","value":"","description":"","not_null":1,"field_type":"Array","type":"Text","is_checked":1},{"key":"itemDesc.bizApplyId","value":"","description":"申请项目id","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"itemDesc.applyItem","value":"","description":"组套项目\/申请项目","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"itemDesc.applyItem.code","value":"编码","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"itemDesc.applyItem.name","value":"血脂4项","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"itemDesc.checkDoctor","value":"","description":"组套项目\/申请项目检查医生","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"itemDesc.checkDoctor.code","value":"检查医生编码","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"itemDesc.checkDoctor.name","value":"检查医生名称","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"itemDesc.checkItem","value":"","description":"细项编码","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"itemDesc.checkItem.code","value":"编码","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"itemDesc.checkItem.name","value":"总胆固醇（TC）","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"itemDesc.inspectResult","value":"安基我研风张备建月效","description":"检查结果","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"itemDesc.unit","value":"mmol\/L","description":"单位","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"itemDesc.referenceMax","value":"5.2","description":"最大值","not_null":1,"field_type":"Number","type":"Text","is_checked":1},{"key":"itemDesc.referenceMin","value":"0","description":"最小值","not_null":1,"field_type":"Number","type":"Text","is_checked":1},{"key":"itemDesc.reference","value":"0-5.20","description":"范围","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"itemDesc.displaySequence","value":"1","description":"检查序号\/展示顺序\/项目排序","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"itemDesc.digitValue","value":"12","description":"检验数值","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"itemDesc.mds","value":"","description":"异常标识","not_null":-1,"field_type":"Object","type":"Object","is_checked":1},{"key":"itemDesc.mds.code","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"itemDesc.mds.name","value":"↑","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1}],"raw_schema":{"type":"array","properties":[],"APIPOST_ORDERS":["f8bf3aadc00a40ceb1c3073e781bb684"],"APIPOST_REFS":{"f8bf3aadc00a40ceb1c3073e781bb684":{"ref":"105c373a-bc9f-47dd-916d-7ff60429bf98"}},"required":[],"items":{"type":"object","title":"Items","properties":[],"APIPOST_ORDERS":["de6f5eb53ed542d2abda4df4d8811404"],"APIPOST_REFS":{"de6f5eb53ed542d2abda4df4d8811404":{"ref":"105c373a-bc9f-47dd-916d-7ff60429bf98"}}},"description":""}},"event":{"pre_script":"","test":""},"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""},"digest":{"username":"","password":"","realm":"","nonce":"","algorithm":"","qop":"","nc":"","cnonce":"","opaque":""},"hawk":{"authId":"","authKey":"","algorithm":"","user":"","nonce":"","extraData":"","app":"","delegation":"","timestamp":"","includePayloadHash":-1},"awsv4":{"accessKey":"","secretKey":"","region":"","service":"","sessionToken":"","addAuthDataToQuery":-1},"ntlm":{"username":"","password":"","domain":"","workstation":"","disableRetryRequest":1},"edgegrid":{"accessToken":"","clientToken":"","clientSecret":"","nonce":"","timestamp":"","baseURi":"","headersToSign":""},"oauth1":{"consumerKey":"","consumerSecret":"","signatureMethod":"","addEmptyParamsToSign":-1,"includeBodyHash":-1,"addParamsToHeader":-1,"realm":"","version":"1.0","nonce":"","timestamp":"","verifier":"","callback":"","tokenSecret":"","token":""}},"header":{"parameter":[{"description":"","field_type":"String","is_checked":1,"key":"","value":"","not_null":1,"type":"Text","static":true,"value_var":"","key_var":""}]},"query":{"parameter":[]},"cookie":{"parameter":[]},"resful":{"parameter":[]},"origin_url":"\/dx\/inter\/deptInfo"},"response":{"success":{"raw":"{\"code\":0,\"msg\":\"\",\"data\":null,\"reponseTime\":*************}","parameter":[],"expect":{"name":"成功","isDefault":1,"code":200,"contentType":"json","verifyType":"schema","schema":[],"mock":""}},"error":{"raw":"{\"code\":400,\"msg\":\"\",\"data\":null,\"reponseTime\":*************}","parameter":[],"expect":{"name":"失败","isDefault":-1,"code":404,"contentType":"json","verifyType":"schema","schema":[],"mock":""}}},"parent_id":"ec49338f-2f42-4317-8d79-5feb19bafa86"},{"local_target_id":"d83c1c03-12c8-47d2-a5e9-792866301e90","local_parent_id":"ec49338f-2f42-4317-8d79-5feb19bafa86","name":"04.医生信息传输","method":"POST","mark":"developing","is_doc":0,"target_type":"api","example_type":"0","project_id":"a3fb6fe8-9132-41ce-af02-74be505d7d45","status":1,"sort":3,"create_dtime":1696817210,"update_dtime":1748307476,"local_server_id":"","target_id":"d83c1c03-12c8-47d2-a5e9-792866301e90","modifier_id":"6515BXWEVF9DSF07DDFF0BE164AFFE33CB0E7A81D1D0","created_uuid":"UUSWO20C9AAB","extend_server_id":"","create_time":"2023-10-09 10:06:50","create_users":{"create_user":"张浩雄","update_user":"林为国"},"color":"#3A86FF","attribute_lists":[],"enable_server_mock":-1,"mock_server_url":"","mock_url":"","request":{"url":"\/dx\/inter\/syncUser","description":"","body":{"mode":"json","parameter":[{"description":"如果为空，则查询所有医生信息","field_type":"String","is_checked":1,"key":"id","value":"医生系统id","not_null":-1,"type":"Text","contentType":"","value_var":"","key_var":""}],"raw":"[\n\t{\n\t\t\"name\": \"\",\n\t\t\"icCode\": \"\",\n\t\t\"phoneNo\": \"\",\n\t\t\"sex\": {\n\t\t\t\"code\": \"\",\n\t\t\t\"name\": \"\"\n\t\t},\n\t\t\"accountCode\": \"\",\n\t\t\"accountId\": \"\",\n\t\t\"hospital\": {\n\t\t\t\"code\": \"\",\n\t\t\t\"name\": \"\"\n\t\t}\n\t}\n]","raw_para":[{"key":"name","value":"","description":"姓名","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"icCode","value":"","description":"身份证号","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"phoneNo","value":"","description":"手机号 可为空","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"sex","value":"","description":"","not_null":-1,"field_type":"Object","type":"Object","is_checked":1},{"key":"sex.code","value":"","description":"1 男 2-女 3-未知","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"sex.name","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"accountCode","value":"","description":"登录账号","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"accountId","value":"","description":"账号id","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"hospital","value":"","description":"非必填，如果是单院区的情况可以不传","not_null":-1,"field_type":"Object","type":"Object","is_checked":1},{"key":"hospital.code","value":"","description":"医院编码","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"hospital.name","value":"","description":"院区名称","not_null":1,"field_type":"String","type":"Text","is_checked":1}],"raw_schema":{"type":"object"}},"event":{"pre_script":"","test":""},"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""},"digest":{"username":"","password":"","realm":"","nonce":"","algorithm":"","qop":"","nc":"","cnonce":"","opaque":""},"hawk":{"authId":"","authKey":"","algorithm":"","user":"","nonce":"","extraData":"","app":"","delegation":"","timestamp":"","includePayloadHash":-1},"awsv4":{"accessKey":"","secretKey":"","region":"","service":"","sessionToken":"","addAuthDataToQuery":-1},"ntlm":{"username":"","password":"","domain":"","workstation":"","disableRetryRequest":1},"edgegrid":{"accessToken":"","clientToken":"","clientSecret":"","nonce":"","timestamp":"","baseURi":"","headersToSign":""},"oauth1":{"consumerKey":"","consumerSecret":"","signatureMethod":"","addEmptyParamsToSign":-1,"includeBodyHash":-1,"addParamsToHeader":-1,"realm":"","version":"1.0","nonce":"","timestamp":"","verifier":"","callback":"","tokenSecret":"","token":""}},"header":{"parameter":[]},"query":{"parameter":[]},"cookie":{"parameter":[]},"resful":{"parameter":[]},"origin_url":"\/dx\/inter\/syncUser"},"response":{"success":{"raw":"{\"code\":0,\"msg\":\"\",\"data\":null,\"reponseTime\":*************}","parameter":[{"key":"name","value":"姓名","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"icCode","value":"身份证id","description":"","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"phoneNo","value":"手机号码","description":"","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"sex","value":"性别","description":"","not_null":-1,"field_type":"Object","type":"Text","is_checked":1},{"key":"sex.code","value":"1,2,3","description":"","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"sex.name","value":"姓名","description":"","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"accountCode","value":"登录账号","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"accountId","value":"账号主检id","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1}],"expect":{"name":"成功","isDefault":1,"code":200,"contentType":"json","verifyType":"schema","schema":[],"mock":""}},"error":{"raw":"{\"code\":400,\"msg\":\"\",\"data\":null,\"reponseTime\":*************}","parameter":[],"expect":{"name":"失败","isDefault":-1,"code":404,"contentType":"json","verifyType":"schema","schema":[],"mock":""}}},"parent_id":"ec49338f-2f42-4317-8d79-5feb19bafa86"},{"local_target_id":"78b4314e-fb34-4cb9-91ba-ff7013b9a6c3","local_parent_id":"ec49338f-2f42-4317-8d79-5feb19bafa86","name":"05.科室信息传输","method":"POST","mark":"developing","is_doc":0,"target_type":"api","example_type":"0","project_id":"a3fb6fe8-9132-41ce-af02-74be505d7d45","status":1,"sort":4,"create_dtime":**********,"update_dtime":**********,"local_server_id":"","target_id":"78b4314e-fb34-4cb9-91ba-ff7013b9a6c3","modifier_id":"6515BXWEVF9DSF07DDFF0BE164AFFE33CB0E7A81D1D0","created_uuid":"UUSWO20C9AAB","extend_server_id":"","create_time":"2023-10-10 08:35:54","create_users":{"create_user":"张浩雄","update_user":"林为国"},"color":"#3A86FF","attribute_lists":[],"enable_server_mock":-1,"mock_server_url":"","mock_url":"","request":{"url":"\/dx\/inter\/syncDept","description":"","body":{"mode":"json","parameter":[],"raw":"[\n\t{\n\t\t\"name\": \"\",\n\t\t\"id\": \"\",\n\t\t\"displaySequence\": \"\",\n\t\t\"hospital\": {\n\t\t\t\"code\": \"\",\n\t\t\t\"name\": \"\"\n\t\t}\n\t}\n]","raw_para":[{"key":"name","value":"","description":"科室名称","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"id","value":"","description":"科室id","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"displaySequence","value":"","description":"科室排序","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"hospital","value":"","description":"非必填，如果是单院区的情况可以不传","not_null":-1,"field_type":"Object","type":"Object","is_checked":1},{"key":"hospital.code","value":"","description":"医院编码","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"hospital.name","value":"","description":"院区名称","not_null":1,"field_type":"String","type":"Text","is_checked":1}],"raw_schema":{"type":"object"}},"event":{"pre_script":"","test":""},"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""},"digest":{"username":"","password":"","realm":"","nonce":"","algorithm":"","qop":"","nc":"","cnonce":"","opaque":""},"hawk":{"authId":"","authKey":"","algorithm":"","user":"","nonce":"","extraData":"","app":"","delegation":"","timestamp":"","includePayloadHash":-1},"awsv4":{"accessKey":"","secretKey":"","region":"","service":"","sessionToken":"","addAuthDataToQuery":-1},"ntlm":{"username":"","password":"","domain":"","workstation":"","disableRetryRequest":1},"edgegrid":{"accessToken":"","clientToken":"","clientSecret":"","nonce":"","timestamp":"","baseURi":"","headersToSign":""},"oauth1":{"consumerKey":"","consumerSecret":"","signatureMethod":"","addEmptyParamsToSign":-1,"includeBodyHash":-1,"addParamsToHeader":-1,"realm":"","version":"1.0","nonce":"","timestamp":"","verifier":"","callback":"","tokenSecret":"","token":""}},"header":{"parameter":[]},"query":{"parameter":[]},"cookie":{"parameter":[]},"resful":{"parameter":[]},"origin_url":"\/dx\/inter\/syncDept"},"response":{"success":{"raw":"{\"code\":0,\"msg\":\"\",\"data\":null,\"reponseTime\":*************}","parameter":[],"expect":{"name":"成功","isDefault":1,"code":200,"contentType":"json","verifyType":"schema","schema":[],"mock":""}},"error":{"raw":"{\"code\":400,\"msg\":\"\",\"data\":null,\"reponseTime\":*************}","parameter":[],"expect":{"name":"失败","isDefault":-1,"code":404,"contentType":"json","verifyType":"schema","schema":[],"mock":""}}},"parent_id":"ec49338f-2f42-4317-8d79-5feb19bafa86"},{"local_target_id":"e13009a0-ae44-4673-bf3b-fb9ee2b6ad76","local_parent_id":"ec49338f-2f42-4317-8d79-5feb19bafa86","name":"06.字典信息传输","method":"POST","mark":"developing","is_doc":0,"target_type":"api","example_type":"0","project_id":"a3fb6fe8-9132-41ce-af02-74be505d7d45","status":1,"sort":5,"create_dtime":1708592226,"update_dtime":1748307509,"local_server_id":"","target_id":"e13009a0-ae44-4673-bf3b-fb9ee2b6ad76","modifier_id":"6515BXWEVF9DSF07DDFF0BE164AFFE33CB0E7A81D1D0","created_uuid":"UUSWO20C9AAB","extend_server_id":"","create_time":"2024-02-22 16:57:06","create_users":{"create_user":"张浩雄","update_user":"林为国"},"color":"#3A86FF","attribute_lists":[],"enable_server_mock":-1,"mock_server_url":"","mock_url":"","request":{"url":"\/dx\/inter\/syncDict","description":"","body":{"mode":"json","parameter":[],"raw":"[\n\t{\n\t\t\"name\": \"\",\n\t\t\"id\": \"\",\n\t\t\"type\": \"\",\n\t\t\"hospital\": {\n\t\t\t\"code\": \"\",\n\t\t\t\"name\": \"\"\n\t\t}\n\t}\n]","raw_para":[{"key":"name","value":"","description":"字典名称","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"id","value":"","description":"主键","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"type","value":"","description":"字典类型 OPEVIP--体检服务类型(例普通、vip、贵宾) OPBET--体检类型(例个人体检、儿童体检、入职体检)","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"hospital","value":"","description":"非必填，如果是单院区的情况可以不传","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"hospital.code","value":"","description":"医院编码","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"hospital.name","value":"","description":"院区名称","not_null":1,"field_type":"String","type":"Text","is_checked":1}],"raw_schema":{"type":"object"}},"event":{"pre_script":"","test":""},"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""},"digest":{"username":"","password":"","realm":"","nonce":"","algorithm":"","qop":"","nc":"","cnonce":"","opaque":""},"hawk":{"authId":"","authKey":"","algorithm":"","user":"","nonce":"","extraData":"","app":"","delegation":"","timestamp":"","includePayloadHash":-1},"awsv4":{"accessKey":"","secretKey":"","region":"","service":"","sessionToken":"","addAuthDataToQuery":-1},"ntlm":{"username":"","password":"","domain":"","workstation":"","disableRetryRequest":1},"edgegrid":{"accessToken":"","clientToken":"","clientSecret":"","nonce":"","timestamp":"","baseURi":"","headersToSign":""},"oauth1":{"consumerKey":"","consumerSecret":"","signatureMethod":"","addEmptyParamsToSign":-1,"includeBodyHash":-1,"addParamsToHeader":-1,"realm":"","version":"1.0","nonce":"","timestamp":"","verifier":"","callback":"","tokenSecret":"","token":""}},"header":{"parameter":[]},"query":{"parameter":[]},"cookie":{"parameter":[]},"resful":{"parameter":[]},"origin_url":"\/dx\/inter\/syncDict"},"response":{"success":{"raw":"{\"code\":0,\"msg\":\"\",\"data\":null,\"reponseTime\":*************}","parameter":[],"expect":{"name":"成功","isDefault":1,"code":200,"contentType":"json","verifyType":"schema","schema":[],"mock":""}},"error":{"raw":"{\"code\":400,\"msg\":\"\",\"data\":null,\"reponseTime\":*************}","parameter":[],"expect":{"name":"失败","isDefault":-1,"code":404,"contentType":"json","verifyType":"schema","schema":[],"mock":""}}},"parent_id":"ec49338f-2f42-4317-8d79-5feb19bafa86"},{"local_target_id":"eeed50fb-f555-4627-938e-1d84f35c86ea","local_parent_id":"ec49338f-2f42-4317-8d79-5feb19bafa86","name":"22.重要异常通知数据(可选)","method":"POST","mark":"developing","is_doc":0,"target_type":"api","example_type":"0","project_id":"a3fb6fe8-9132-41ce-af02-74be505d7d45","status":1,"sort":6,"create_dtime":1749469497,"update_dtime":1749631842,"local_server_id":"","target_id":"eeed50fb-f555-4627-938e-1d84f35c86ea","modifier_id":"6515BXWEVF9DSF07DDFF0BE164AFFE33CB0E7A81D1D0","created_uuid":"6515BXWEVF9DSF07DDFF0BE164AFFE33CB0E7A81D1D0","extend_server_id":"","create_time":"2025-06-09 19:44:57","create_users":{"create_user":"林为国","update_user":"林为国"},"color":"#3A86FF","attribute_lists":[],"enable_server_mock":-1,"mock_server_url":"","mock_url":"","request":{"url":"inter\/syncAbnormal","description":"接收院区健管系统的重要异常是否通知","body":{"mode":"json","parameter":[],"raw":"[\r\n\t{\r\n\t\t\"peNo\": \"245426667\",\r\n\t\t\"abnormalType\": 1,\r\n\t\t\"abnormalName\": \"甲状腺异常\",\r\n\t\t\"discoveryTime\": \"2025-03-12 10:16:33\",\r\n\t\t\"discoveryUserId\": \"2431\",\r\n\t\t\"deptId\": \"235164364\",\r\n\t\t\"deptName\": \"彩超科\",\r\n\t\t\"hasNotice\": \"0\",\r\n\t\t\"hospital\": {\r\n\t\t\t\t\"code\": \"\",\r\n\t\t\t\t\"name\": \"\"\r\n    \t\t}\t\r\n\t}\r\n]","raw_para":[{"key":"peNo","value":"","description":"体检号","not_null":"-1","field_type":"String","type":"Text","is_checked":1},{"key":"abnormalType","value":"2","description":"重要异常级别  1:A类重要异常   2:B类重要异常    3:C类重要异常     4:D类重要异常     9:其他","not_null":"-1","field_type":"Integer","type":"Text","is_checked":1},{"key":"abnormalName","value":"","description":"重要异常名称","not_null":"-1","field_type":"String","type":"Text","is_checked":1},{"key":"discoveryTime","value":"","description":"发现时间 格式： yyyy-MM-dd HH:mm:ss","not_null":"-1","field_type":"String","type":"Text","is_checked":1},{"key":"discoveryUserId","value":"","description":"上报人ID","not_null":"-1","field_type":"String","type":"Text","is_checked":1},{"key":"deptId","value":"","description":"科室ID","not_null":"-1","field_type":"String","type":"Text","is_checked":1},{"key":"deptName","value":"","description":"科室名称","not_null":"-1","field_type":"String","type":"Text","is_checked":1},{"key":"hasNotice","value":"","description":"是否已通知  1：已通知 0：未通知","not_null":"-1","field_type":"String","type":"Text","is_checked":1},{"description":"","field_type":"Object","is_checked":1,"key":"hospital","value":"","not_null":-1,"type":"Text"},{"description":"医院编码","field_type":"String","is_checked":1,"key":"hospital.code","value":"","not_null":1,"type":"Text"},{"description":"院区名称","field_type":"String","is_checked":1,"key":"hospital.name","value":"","not_null":1,"type":"Text"}],"raw_schema":{"type":"array","items":{"description":"cn.tohealth.interplugin.entity.inter.AbnormalScan","type":"object","properties":{"peNo":{"type":"string","description":"体检号"},"abnormalType":{"type":"integer","description":"重要异常级别"},"abnormalName":{"type":"string","description":"重要异常名称"},"discoveryTime":{"type":"string","description":"发现时间"},"discoveryUserId":{"type":"string","description":"上报人ID"},"deptId":{"type":"string","description":"科室ID"},"deptName":{"type":"string","description":"科室名称"},"hasNotice":{"type":"string","description":"是否已通知  1：已通知 0：未通知"}},"required":["peNo"],"APIPOST_ORDERS":["peNo","abnormalType","abnormalName","discoveryTime","discoveryUserId","deptId","deptName","hasNotice"]},"description":"重要异常数据"}},"event":{"pre_script":"","test":""},"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""},"digest":{"username":"","password":"","realm":"","nonce":"","algorithm":"","qop":"","nc":"","cnonce":"","opaque":""},"hawk":{"authId":"","authKey":"","algorithm":"","user":"","nonce":"","extraData":"","app":"","delegation":"","timestamp":"","includePayloadHash":-1},"awsv4":{"accessKey":"","secretKey":"","region":"","service":"","sessionToken":"","addAuthDataToQuery":-1},"ntlm":{"username":"","password":"","domain":"","workstation":"","disableRetryRequest":1},"edgegrid":{"accessToken":"","clientToken":"","clientSecret":"","nonce":"","timestamp":"","baseURi":"","headersToSign":""},"oauth1":{"consumerKey":"","consumerSecret":"","signatureMethod":"","addEmptyParamsToSign":-1,"includeBodyHash":-1,"addParamsToHeader":-1,"realm":"","version":"1.0","nonce":"","timestamp":"","verifier":"","callback":"","tokenSecret":"","token":""}},"header":{"parameter":[{"is_checked":"1","type":"Text","key":"sign","value":"","not_null":"1","description":"通过对分配的key，加上时间戳，进行MD5后的十六进制数值（小写）。MD5(key+timestamp)。接收端，按照相同算法，校验计算后数值是否正确","field_type":"Text","value_var":"","key_var":""},{"is_checked":"1","type":"Text","key":"timestamp","value":"","not_null":"1","description":"当前时间(YYYYmmDDHHMMSS格式，24小时制)","field_type":"Text","value_var":"","key_var":""},{"is_checked":"1","type":"Text","key":"misc-id","value":"MISC1.00000S","not_null":"1","description":"平台分配的用户ID，如果是平台发起的接口。这个值为000000","field_type":"Text","value_var":"","key_var":""},{"is_checked":"1","type":"Text","key":"nonce","value":"{{$string.uuid}}","not_null":"1","description":"UUID生成","field_type":"Text","value_var":"","key_var":""},{"is_checked":"1","type":"Text","key":"mic-code","value":"MIC1.000U","not_null":"1","description":"平台分配","field_type":"Text","value_var":"","key_var":""}]},"query":{"parameter":[]},"cookie":{"parameter":[]},"resful":{"parameter":[]},"origin_url":"inter\/syncAbnormal"},"response":{"success":{"raw":"{\"code\":0,\"msg\":\"\",\"data\":{},\"reponseTime\":\"\"}","parameter":[],"expect":{"name":"成功","isDefault":1,"code":200,"contentType":"json","verifyType":"schema","schema":{"type":"object","APIPOST_REFS":{"63c0648a-9e58-4f17-92b4-bbdaaa6452fb":{"ref":"28c03ffe5d28771f6002707431fc88ae"}},"properties":[],"APIPOST_ORDERS":["63c0648a-9e58-4f17-92b4-bbdaaa6452fb"]},"mock":""}},"error":{"raw":"","parameter":[],"expect":{"name":"失败","isDefault":-1,"code":404,"contentType":"json","verifyType":"schema","schema":{"type":"object"},"mock":""}}},"parent_id":"ec49338f-2f42-4317-8d79-5feb19bafa86"}]},{"local_target_id":"eb34599d-60d1-41a8-a023-3f7c69a06177","local_parent_id":"473c2955-3504-45fd-a53d-4a3731078c95","name":"添健云系统发起","method":"POST","mark":"developing","is_doc":0,"target_type":"folder","example_type":"0","project_id":3209647,"status":1,"sort":3,"create_dtime":1696209039,"update_dtime":1724903070,"local_server_id":"","target_id":"eb34599d-60d1-41a8-a023-3f7c69a06177","modifier_id":"UUSWO20C9AAB","created_uuid":"U3NEF5486420","extend_server_id":"","create_time":"2023-10-02 09:10:39","create_users":{"create_user":"ZW","update_user":"张浩雄"},"color":"#3A86FF","attribute_lists":[],"script":{"pre_script":"","test":"","pre_script_switch":1,"test_switch":1},"request":{"description":"","header":[],"query":[],"body":[],"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""}}},"parent_id":"473c2955-3504-45fd-a53d-4a3731078c95","children":[{"local_target_id":"757a11bf-f2ee-4a6a-b577-6e36ef230d35","local_parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177","name":"07.主检结束结论回传","method":"POST","mark":"developing","is_doc":0,"target_type":"api","example_type":"0","project_id":"a3fb6fe8-9132-41ce-af02-74be505d7d45","status":1,"sort":0,"create_dtime":1696227853,"update_dtime":1747359070,"local_server_id":"","target_id":"757a11bf-f2ee-4a6a-b577-6e36ef230d35","modifier_id":"6515BXWEVF9DSF07DDFF0BE164AFFE33CB0E7A81D1D0","created_uuid":"U3NEF5486420","extend_server_id":"","create_time":"2023-10-02 14:24:13","create_users":{"create_user":"ZW","update_user":"林为国"},"color":"#3A86FF","attribute_lists":[],"enable_server_mock":-1,"mock_server_url":"","mock_url":"","request":{"url":"","description":"主检报告写完后，通过本接口回传。会多次回写，该接口都是全量传入结论词，所以体检系统方需要判断新增、修改、删除。（或者全量替换）。该接口涉及主检初审和主检终审。以currentNodeType判断为主,如果=3,则为初审,如果=4,则为总审\n注意:该报文通过parentCode来区分子父结论词，如果parentCode为空，则为父结论词,如果存在parentCode,则为子结论词","body":{"mode":"json","parameter":[],"raw":"{\n\t\"peNo\": \"\",\n\t\"firstCheckFinishTime\": \"\",\n\t\"firstCheckFinishDoctor\": {\n\t\t\"code\": \"\",\n\t\t\"name\": \"\",\n\t\t\"synonyms\": null,\n        \"zero\": null\n\t},\n\t\"mainCheckFinishTime\": \"\",\n\t\"mainCheckFinishDoctor\": {\n\t\t\"code\": \"\",\n\t\t\"name\": \"\",\n\t\t\"synonyms\": null,\n        \"zero\": null\n\t},\n\t\"currentNodeType\": 0,\n\t\"conclusionList\": [\n\t\t{\n\t\t\t\"mappingId\": \"\",\n\t\t\t\"conclusionName\": \"\",\n\t\t\t\"conclusionCode\": \"\",\n\t\t\t\"parentCode\": \"\",\n\t\t\t\"suggest\": \"\",\n\t\t\t\"explain\": \"\",\n\t\t\t\"checkResult\": \"\",\n\t\t\t\"level\": 2,\n\t\t\t\"displaySequnce\": 1,\n\t\t\t\"childrenCode\": null,\n\t\t\t\"deptId\": \"\",\n\t\t\t\"abnormalLevel\": 1\n\t\t}\n\t]\n}","raw_para":[{"key":"peNo","value":"","description":"体检号","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"firstCheckFinishTime","value":"","description":"初检完成时间yyyy-MM-dd HH-mm-ss","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"firstCheckFinishDoctor","value":"","description":"","not_null":-1,"field_type":"Object","type":"Object","is_checked":1},{"key":"firstCheckFinishDoctor.code","value":"","description":"初审医生系统编号","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"firstCheckFinishDoctor.name","value":"","description":"初审医生姓名","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"mainCheckFinishTime","value":"","description":"总审完成时间yyyy-MM-dd HH-mm-ss","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"mainCheckFinishDoctor","value":"","description":"","not_null":-1,"field_type":"Object","type":"Object","is_checked":1},{"key":"mainCheckFinishDoctor.code","value":"","description":"总审医生系统编号","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"mainCheckFinishDoctor.name","value":"","description":"总审医生姓名","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"currentNodeType","value":"0","description":"当前操作节点用于流程控制，1登记,2分科3主检4总检","not_null":1,"field_type":"Integer","type":"Text","is_checked":1},{"key":"conclusionList","value":"","description":"","not_null":1,"field_type":"Array","type":"Text","is_checked":1},{"key":"conclusionList.mappingId","value":"","description":"健管系统结论词字典id","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"conclusionList.conclusionName","value":"","description":"结论词","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"conclusionList.conclusionCode","value":"","description":"结论词编号","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"conclusionList.parentCode","value":"","description":"父类编码,合并结论词存在","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"conclusionList.suggest","value":"","description":"解释建议","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"conclusionList.explain","value":"","description":"医学解释","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"conclusionList.checkResult","value":"","description":"检查结果汇总描述","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"conclusionList.level","value":"","description":"1-重要；2-次要；3-其他","not_null":1,"field_type":"Integer","type":"Text","is_checked":1},{"key":"conclusionList.displaySequnce","value":"","description":"显示序号","not_null":1,"field_type":"Integer","type":"Text","is_checked":1},{"key":"conclusionList.deptId","value":"","description":"科室id","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"conclusionList.abnormalLevel","value":"1","description":"重要异常等级1:A 2:B 3:C 9:OHTER","not_null":-1,"field_type":"Integer","type":"Text","is_checked":1},{"description":"子结论词编码集合","field_type":"Array","is_checked":1,"key":"conclusionList.childrenCode","value":"","not_null":1,"type":"Text"},{"description":"内部使用，可忽略","field_type":"Array","is_checked":1,"key":"firstCheckFinishDoctor.synonyms","value":"","not_null":-1,"type":"Text"},{"description":"内部使用，可忽略","field_type":"Object","is_checked":1,"key":"firstCheckFinishDoctor.zero","value":"","not_null":-1,"type":"Text"},{"description":"内部使用，可忽略","field_type":"Array","is_checked":1,"key":"mainCheckFinishDoctor.synonyms","value":"","not_null":-1,"type":"Text"},{"description":"内部使用，可忽略","field_type":"Object","is_checked":1,"key":"mainCheckFinishDoctor.zero","value":"","not_null":-1,"type":"Text"}],"raw_schema":{"type":"object"}},"event":{"pre_script":"","test":""},"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""},"digest":{"username":"","password":"","realm":"","nonce":"","algorithm":"","qop":"","nc":"","cnonce":"","opaque":""},"hawk":{"authId":"","authKey":"","algorithm":"","user":"","nonce":"","extraData":"","app":"","delegation":"","timestamp":"","includePayloadHash":-1},"awsv4":{"accessKey":"","secretKey":"","region":"","service":"","sessionToken":"","addAuthDataToQuery":-1},"ntlm":{"username":"","password":"","domain":"","workstation":"","disableRetryRequest":1},"edgegrid":{"accessToken":"","clientToken":"","clientSecret":"","nonce":"","timestamp":"","baseURi":"","headersToSign":""},"oauth1":{"consumerKey":"","consumerSecret":"","signatureMethod":"","addEmptyParamsToSign":-1,"includeBodyHash":-1,"addParamsToHeader":-1,"realm":"","version":"1.0","nonce":"","timestamp":"","verifier":"","callback":"","tokenSecret":"","token":""}},"header":{"parameter":[]},"query":{"parameter":[]},"cookie":{"parameter":[]},"resful":{"parameter":[]},"origin_url":""},"response":{"success":{"raw":"{\n\t\"code\": 0,\n\t\"data\": null,\n\t\"msg\": \"\"\n}","parameter":[],"expect":{"name":"成功","isDefault":1,"code":200,"contentType":"json","verifyType":"schema","schema":[],"mock":""}},"error":{"raw":"","parameter":[],"expect":{"name":"失败","isDefault":-1,"code":404,"contentType":"json","verifyType":"schema","schema":[],"mock":""}}},"parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177"},{"local_target_id":"b5a7de95-a67f-406c-aba8-e075ffa51d74","local_parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177","name":"08.查询字典信息接口","method":"POST","mark":"developing","is_doc":0,"target_type":"api","example_type":"0","project_id":"a3fb6fe8-9132-41ce-af02-74be505d7d45","status":1,"sort":1,"create_dtime":1696228137,"update_dtime":1730259349,"local_server_id":"","target_id":"b5a7de95-a67f-406c-aba8-e075ffa51d74","modifier_id":"UUSWO20C9AAB","created_uuid":"U3NEF5486420","extend_server_id":"","create_time":"2023-10-02 14:28:57","create_users":{"create_user":"ZW","update_user":"张浩雄"},"color":"#3A86FF","attribute_lists":[],"enable_server_mock":-1,"mock_server_url":"","mock_url":"","request":{"url":"","description":"重传字典数据。","body":{"mode":"json","parameter":[],"raw":"{\n    \"id\": \"\",\n    \"type\": \"\",\n    \"hospitalCode\":\"\"\n}","raw_para":[{"key":"id","value":"","description":"主键 如果id为空,则获取所有的对应字典的信息","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"type","value":"","description":"字典类型 OPEVIP--体检服务类型(例普通、vip、贵宾) OPBET--体检类型(例个人体检、儿童体检、入职体检)","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"hospitalCode","value":"","description":"医院编码，适用于多院区的情况","not_null":-1,"field_type":"String","type":"Text","is_checked":1}],"raw_schema":{"type":"object","properties":[],"APIPOST_ORDERS":[],"required":[]}},"event":{"pre_script":"","test":""},"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""},"digest":{"username":"","password":"","realm":"","nonce":"","algorithm":"","qop":"","nc":"","cnonce":"","opaque":""},"hawk":{"authId":"","authKey":"","algorithm":"","user":"","nonce":"","extraData":"","app":"","delegation":"","timestamp":"","includePayloadHash":-1},"awsv4":{"accessKey":"","secretKey":"","region":"","service":"","sessionToken":"","addAuthDataToQuery":-1},"ntlm":{"username":"","password":"","domain":"","workstation":"","disableRetryRequest":1},"edgegrid":{"accessToken":"","clientToken":"","clientSecret":"","nonce":"","timestamp":"","baseURi":"","headersToSign":""},"oauth1":{"consumerKey":"","consumerSecret":"","signatureMethod":"","addEmptyParamsToSign":-1,"includeBodyHash":-1,"addParamsToHeader":-1,"realm":"","version":"1.0","nonce":"","timestamp":"","verifier":"","callback":"","tokenSecret":"","token":""}},"header":{"parameter":[]},"query":{"parameter":[]},"cookie":{"parameter":[]},"resful":{"parameter":[]},"origin_url":""},"response":{"success":{"raw":"{\n    \"code\":0,\n    \"data\":[\n  {\n    \"name\": \"名称\",\n    \"id\": \"主键\",\n    \"type\": \"字典类型 OPEVIP--体检服务类型 OPBET--体检类型\"\n  }\n],\n  \"msg\": \"\"\n}","parameter":[],"expect":{"name":"成功","isDefault":1,"code":200,"contentType":"json","verifyType":"schema","schema":[],"mock":""}},"error":{"raw":"","parameter":[],"expect":{"name":"失败","isDefault":-1,"code":404,"contentType":"json","verifyType":"schema","schema":[],"mock":""}}},"parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177"},{"local_target_id":"8861966a-b2d4-4371-8aff-3087fe1e39c7","local_parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177","name":"09.体检科室结果重传接口","method":"POST","mark":"developing","is_doc":0,"target_type":"api","example_type":"0","project_id":"a3fb6fe8-9132-41ce-af02-74be505d7d45","status":1,"sort":2,"create_dtime":1696228311,"update_dtime":1749614385,"local_server_id":"","target_id":"8861966a-b2d4-4371-8aff-3087fe1e39c7","modifier_id":"6515BXWEVF9DSF07DDFF0BE164AFFE33CB0E7A81D1D0","created_uuid":"U3NEF5486420","extend_server_id":"","create_time":"2023-10-02 14:31:51","create_users":{"create_user":"ZW","update_user":"林为国"},"color":"#3A86FF","attribute_lists":[],"enable_server_mock":-1,"mock_server_url":"","mock_url":"","request":{"url":"","description":"根据体检号列表，获取体检结果信息。data数据以03.体检科室结果传输接口一致。","body":{"mode":"json","parameter":[],"raw":"{\n\t\"peNoList\": [\n\t\t\"\",\n\t],\n\t\"deptId\": \"\"\n}","raw_para":[{"key":"peNoList","value":"","description":"体检号1","not_null":1,"field_type":"Array","type":"Text","is_checked":1},{"key":"deptId","value":"","description":"如果存在Id,则只获取当前的科室体检信息,如果为空,则获取全部体检信息","not_null":-1,"field_type":"String","type":"Text","is_checked":1}],"raw_schema":{"type":"array","properties":[],"APIPOST_ORDERS":[],"required":[]}},"event":{"pre_script":"","test":""},"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""},"digest":{"username":"","password":"","realm":"","nonce":"","algorithm":"","qop":"","nc":"","cnonce":"","opaque":""},"hawk":{"authId":"","authKey":"","algorithm":"","user":"","nonce":"","extraData":"","app":"","delegation":"","timestamp":"","includePayloadHash":-1},"awsv4":{"accessKey":"","secretKey":"","region":"","service":"","sessionToken":"","addAuthDataToQuery":-1},"ntlm":{"username":"","password":"","domain":"","workstation":"","disableRetryRequest":1},"edgegrid":{"accessToken":"","clientToken":"","clientSecret":"","nonce":"","timestamp":"","baseURi":"","headersToSign":""},"oauth1":{"consumerKey":"","consumerSecret":"","signatureMethod":"","addEmptyParamsToSign":-1,"includeBodyHash":-1,"addParamsToHeader":-1,"realm":"","version":"1.0","nonce":"","timestamp":"","verifier":"","callback":"","tokenSecret":"","token":""}},"header":{"parameter":[]},"query":{"parameter":[]},"cookie":{"parameter":[]},"resful":{"parameter":[]},"origin_url":""},"response":{"success":{"raw":"[\n\t{\n\t\t\"peNo\": \"1212\",\n\t\t\"dept\": {\n\t\t\t\"code\": \"121\",\n\t\t\t\"name\": \"检验科\"\n\t\t},\n\t\t\"hospital\": {\n\t\t\t\"code\": \"编码\",\n\t\t\t\"name\": \"院区\"\n\t\t},\n\t\t\"checkTime\": \"2020-05-10 12:00:00\",\n\t\t\"checkDoctor\": {\n\t\t\t\"code\": \"编码\",\n\t\t\t\"name\": \"张三\"\n\t\t},\n\t\t\"summary\": \"未见明显异常\",\n\t\t\"auditDoctor\": {\n\t\t\t\"code\": \"编码\",\n\t\t\t\"name\": \"张三\"\n\t\t},\n\t\t\"auditStatus\": {\n\t\t\t\"code\": \"isAudited\",\n\t\t\t\"name\": \"已审核\"\n\t\t},\n\t\t\"auditTime\": \"2020-05-10 12:00:00\",\n\t\t\"itemDesc\": [\n\t\t\t{\n\t\t\t\t\"bizApplyId\": \"\",\n\t\t\t\t\"applyItem\": {\n\t\t\t\t\t\"code\": \"编码\",\n\t\t\t\t\t\"name\": \"血脂4项\"\n\t\t\t\t},\n\t\t\t\t\"checkDoctor\": {\n\t\t\t\t\t\"code\": \"检查医生编码\",\n\t\t\t\t\t\"name\": \"检查医生名称\"\n\t\t\t\t},\n\t\t\t\t\"checkItem\": {\n\t\t\t\t\t\"code\": \"编码\",\n\t\t\t\t\t\"name\": \"总胆固醇（TC）\"\n\t\t\t\t},\n\t\t\t\t\"inspectResult\": \"安基我研风张备建月效\",\n\t\t\t\t\"unit\": \"mmol\/L\",\n\t\t\t\t\"referenceMax\": \"5.2\",\n\t\t\t\t\"referenceMin\": \"0\",\n\t\t\t\t\"reference\": \"0-5.20\",\n\t\t\t\t\"displaySequence\": \"1\",\n\t\t\t\t\"digitValue\": \"12\",\n\t\t\t\t\"mds\": {\n\t\t\t\t\t\"code\": \"\",\n\t\t\t\t\t\"name\": \"↑\"\n\t\t\t\t}\n\t\t\t}\n\t\t]\n\t}\n]","parameter":[{"description":"体检号","field_type":"string","is_checked":1,"key":"peNo","type":"string","value":""},{"description":"科室信息","field_type":"object","is_checked":1,"key":"dept","type":"object","value":""},{"description":"编码","field_type":"string","is_checked":1,"key":"dept.code","type":"string","value":""},{"description":"名称","field_type":"string","is_checked":1,"key":"dept.name","type":"string","value":""},{"description":"医院信息","field_type":"object","is_checked":1,"key":"hospital","type":"object","value":""},{"description":"编码","field_type":"string","is_checked":1,"key":"hospital.code","type":"string","value":""},{"description":"名称","field_type":"string","is_checked":1,"key":"hospital.name","type":"string","value":""},{"description":"检查时间 yyyy-MM-dd HH:mm:ss","field_type":"string","is_checked":1,"key":"checkTime","type":"string","value":""},{"description":"检查医生id","field_type":"object","is_checked":1,"key":"checkDoctor","type":"object","value":""},{"description":"编码","field_type":"string","is_checked":1,"key":"checkDoctor.code","type":"string","value":""},{"description":"名称","field_type":"string","is_checked":1,"key":"checkDoctor.name","type":"string","value":""},{"description":"科室小结","field_type":"string","is_checked":1,"key":"summary","type":"string","value":""},{"description":"审核医生信息","field_type":"object","is_checked":1,"key":"auditDoctor","type":"object","value":""},{"description":"编码","field_type":"string","is_checked":1,"key":"auditDoctor.code","type":"string","value":""},{"description":"名称","field_type":"string","is_checked":1,"key":"auditDoctor.name","type":"string","value":""},{"description":"审核状态","field_type":"object","is_checked":1,"key":"auditStatus","type":"object","value":""},{"description":"编码","field_type":"string","is_checked":1,"key":"auditStatus.code","type":"string","value":""},{"description":"名称","field_type":"string","is_checked":1,"key":"auditStatus.name","type":"string","value":""},{"description":"审核状态编码 isAudited:已审核 notAudited:未审核","field_type":"string","is_checked":1,"key":"auditTime","type":"string","value":""},{"description":"检查结果详情","field_type":"array","is_checked":1,"key":"itemDesc","type":"array","value":""},{"description":"医嘱id\/业务id\/申请业务id\/申请套餐id","field_type":"string","is_checked":1,"key":"itemDesc.bizApplyId","type":"string","value":""},{"description":"申请项目信息","field_type":"object","is_checked":1,"key":"itemDesc.applyItem","type":"object","value":""},{"description":"编码","field_type":"string","is_checked":1,"key":"itemDesc.applyItem.code","type":"string","value":""},{"description":"名称","field_type":"string","is_checked":1,"key":"itemDesc.applyItem.name","type":"string","value":""},{"description":"检查项目信息","field_type":"object","is_checked":1,"key":"itemDesc.checkItem","type":"object","value":""},{"description":"编码","field_type":"string","is_checked":1,"key":"itemDesc.checkItem.code","type":"string","value":""},{"description":"名称","field_type":"string","is_checked":1,"key":"itemDesc.checkItem.name","type":"string","value":""},{"description":"检查结果","field_type":"string","is_checked":1,"key":"itemDesc.inspectResult","type":"string","value":""},{"description":"单位","field_type":"string","is_checked":1,"key":"itemDesc.unit","type":"string","value":""},{"description":"最大值","field_type":"string","is_checked":1,"key":"itemDesc.referenceMax","type":"string","value":""},{"description":"最小值","field_type":"string","is_checked":1,"key":"itemDesc.referenceMin","type":"string","value":""},{"description":"范围","field_type":"string","is_checked":1,"key":"itemDesc.reference","type":"string","value":""},{"description":"检查项目排序","field_type":"string","is_checked":1,"key":"itemDesc.displaySequence","type":"string","value":""},{"description":"数值(检验类数值,如果是检验类的，并且是数值型这边也需要设置)","field_type":"string","is_checked":1,"key":"itemDesc.digitValue","type":"string","value":""},{"description":"异常标识","field_type":"object","is_checked":1,"key":"itemDesc.mds","type":"object","value":""},{"description":"冗余字段(传空就行)","field_type":"string","is_checked":1,"key":"itemDesc.mds.code","type":"string","value":""},{"description":"异常标识(列:1+, ↑)","field_type":"string","is_checked":1,"key":"itemDesc.mds.name","type":"string","value":""},{"description":"检查医生","field_type":"object","is_checked":1,"key":"itemDesc.checkDoctor","type":"object","value":""},{"description":"检查医生主键id","field_type":"string","is_checked":1,"key":"itemDesc.checkDoctor.code","type":"string","value":""},{"description":"检查医生名称","field_type":"string","is_checked":1,"key":"itemDesc.checkDoctor.name","type":"string","value":""},{"description":"1-删除 0未删除。如果整个科室弃检、或这个科室下的所有项目弃检，需要设置1，默认不传则为0","field_type":"string","is_checked":1,"key":"deleted","type":"string","value":""}],"expect":{"name":"成功","isDefault":1,"code":200,"contentType":"json","verifyType":"schema","schema":{"type":"array","properties":[],"APIPOST_ORDERS":["cec1d2b3447d46bc8f0333c81a107324"],"APIPOST_REFS":{"cec1d2b3447d46bc8f0333c81a107324":{"ref":"105c373a-bc9f-47dd-916d-7ff60429bf98"}},"items":{"type":"object","title":"Items","properties":[],"APIPOST_ORDERS":["f4d5b0ff24c44dbdb15d4f6bc2c505db"],"APIPOST_REFS":{"f4d5b0ff24c44dbdb15d4f6bc2c505db":{"ref":"105c373a-bc9f-47dd-916d-7ff60429bf98","APIPOST_OVERRIDES":[]}}}},"mock":""}},"error":{"raw":"","parameter":[],"expect":{"name":"失败","isDefault":-1,"code":404,"contentType":"json","verifyType":"schema","schema":[],"mock":""}}},"parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177"},{"local_target_id":"c6b8db09-868f-4cb1-9076-6fe5e580a172","local_parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177","name":"10.批量获取体检单信息","method":"POST","mark":"developing","is_doc":0,"target_type":"api","example_type":"0","project_id":"a3fb6fe8-9132-41ce-af02-74be505d7d45","status":1,"sort":3,"create_dtime":1696250437,"update_dtime":1737428300,"local_server_id":"","target_id":"c6b8db09-868f-4cb1-9076-6fe5e580a172","modifier_id":"UUSWO20C9AAB","created_uuid":"U3NEF5486420","extend_server_id":"","create_time":"2023-10-02 20:40:37","create_users":{"create_user":"ZW","update_user":"张浩雄"},"color":"#3A86FF","attribute_lists":[],"enable_server_mock":-1,"mock_server_url":"","mock_url":"","request":{"url":"","description":"按照时间范围返回所有的体检信息。\n如果有体检号，那时间可以为空，如果没有体检号，就另外2个时间都要有。","body":{"mode":"json","parameter":[],"raw":"{\n\t\"start\": \"2023-04-01 09:40:22\",\n\t\"end\": \"2023-11-07 03:59:58\",\n\t\"peNo\": \"\",\n\t\"hospitalCode\":\"\"\n}","raw_para":[{"key":"start","value":"2023-04-01 09:40:22","description":"查询时间起始点（包含2023-11-07 03:59:58）","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"end","value":"2023-11-07 03:59:58","description":"查询时间终止点（不包含2023-04-01 09:40:22）","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"peNo","value":"","description":"体检号","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"hospitalCode","value":"","description":"医院编码，适用于多院区的情况","not_null":-1,"field_type":"String","type":"Text","is_checked":1}],"raw_schema":{"type":"object","properties":[],"APIPOST_ORDERS":[],"required":[]}},"event":{"pre_script":"","test":""},"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""},"digest":{"username":"","password":"","realm":"","nonce":"","algorithm":"","qop":"","nc":"","cnonce":"","opaque":""},"hawk":{"authId":"","authKey":"","algorithm":"","user":"","nonce":"","extraData":"","app":"","delegation":"","timestamp":"","includePayloadHash":-1},"awsv4":{"accessKey":"","secretKey":"","region":"","service":"","sessionToken":"","addAuthDataToQuery":-1},"ntlm":{"username":"","password":"","domain":"","workstation":"","disableRetryRequest":1},"edgegrid":{"accessToken":"","clientToken":"","clientSecret":"","nonce":"","timestamp":"","baseURi":"","headersToSign":""},"oauth1":{"consumerKey":"","consumerSecret":"","signatureMethod":"","addEmptyParamsToSign":-1,"includeBodyHash":-1,"addParamsToHeader":-1,"realm":"","version":"1.0","nonce":"","timestamp":"","verifier":"","callback":"","tokenSecret":"","token":""}},"header":{"parameter":[]},"query":{"parameter":[]},"cookie":{"parameter":[]},"resful":{"parameter":[]},"origin_url":""},"response":{"success":{"raw":"{\n\t\"code\": 0,\n\t\"msg\": \"\",\n\t\"data\": [\n\t\t{\n\t\t\t\"peUserInfo\": {\n\t\t\t\t\"archiveNo\": \"\",\n\t\t\t\t\"name\": \"\",\n\t\t\t\t\"icCode\": \"\",\n\t\t\t\t\"sex\": {\n\t\t\t\t\t\"code\": \"\",\n\t\t\t\t\t\"name\": \"\"\n\t\t\t\t},\n\t\t\t\t\"birthday\": \"\",\n\t\t\t\t\"peno\": \"\",\n\t\t\t\t\"peDate\": \"\",\n\t\t\t\t\"phone\": \"\",\n\t\t\t\t\"ms\": {\n\t\t\t\t\t\"code\": \"\",\n\t\t\t\t\t\"name\": \"\"\n\t\t\t\t},\n\t\t\t\t\"pregnantState\": {\n\t\t\t\t\t\"code\": \"\",\n\t\t\t\t\t\"name\": \"\"\n\t\t\t\t},\n\t\t\t\t\"vipLevel\": {\n\t\t\t\t\t\"code\": \"\",\n\t\t\t\t\t\"name\": \"\"\n\t\t\t\t},\n\t\t\t\t\"medicalType\": {\n\t\t\t\t\t\"code\": \"\",\n\t\t\t\t\t\"name\": \"\"\n\t\t\t\t},\n\t\t\t\t\"isGroup\": \"\",\n\t\t\t\t\"company\": \"\",\n\t\t\t\t\"workDept\": \"\",\n\t\t\t\t\"teamNo\": \"\",\n\t\t\t\t\"professional\": \"\",\n\t\t\t\t\"workAge\": \"\",\n\t\t\t\t\"peStates\": {\n\t\t\t\t\t\"code\": \"\",\n\t\t\t\t\t\"name\": \"\"\n\t\t\t\t},\n\t\t\t\t\"deptCount\": 0,\n\t\t\t\t\"age\": 0,\n\t\t\t\t\"deptFinishTime\": \"\",\n\t\t\t\t\"firstCheckFinishTime\": \"\",\n\t\t\t\t\"firstCheckFinishDoctor\": {\n\t\t\t\t\t\"code\": \"\",\n\t\t\t\t\t\"name\": \"\"\n\t\t\t\t},\n\t\t\t\t\"mainCheckFinishTime\": \"\",\n\t\t\t\t\"mainCheckFinishDoctor\": {\n\t\t\t\t\t\"code\": \"\",\n\t\t\t\t\t\"name\": \"\"\n\t\t\t\t},\n\t\t\t\t\"forbidGoCheck\": \"\",\n\t\t\t\t\"reportPrint\": \"\",\n\t\t\t\t\"reportGot\": \"\",\n\t\t\t\t\"replacementInspectionMark\": \"\",\n\t\t\t\t\"applyItemList\": [\n\t\t\t\t\t\"申请项目id1\"\n\t\t\t\t],\n\t\t\t\t\"currentNodeType\": \"\",\n\t\t\t\t\"pePackage\": {\n\t\t\t\t\t\"code\": \"\",\n\t\t\t\t\t\"name\": \"\"\n\t\t\t\t}\n\t\t\t},\n\t\t\t\"archiveInfo\": {\n\t\t\t\t\"name\": \"\",\n\t\t\t\t\"icCode\": \"\",\n\t\t\t\t\"sex\": {\n\t\t\t\t\t\"code\": \"\",\n\t\t\t\t\t\"name\": \"\"\n\t\t\t\t},\n\t\t\t\t\"birthday\": \"\",\n\t\t\t\t\"peNoList\": [\n\t\t\t\t\t\"体检号1\"\n\t\t\t\t]\n\t\t\t},\n\t\t\t\"hospital\": {\n\t\t\t\t\"code\": \"\",\n\t\t\t\t\"name\": \"\"\n\t\t\t}\n\t\t}\n\t],\n\t\"reponseTime\": *************\n}","parameter":[{"key":"code","value":"0","description":"","not_null":1,"field_type":"Integer","type":"Text","is_checked":1},{"key":"msg","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data","value":"","description":"","not_null":1,"field_type":"Array","type":"Text","is_checked":1},{"key":"data.peUserInfo","value":"","description":"","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"data.peUserInfo.archiveNo","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.name","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.icCode","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.sex","value":"","description":"","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"data.peUserInfo.sex.code","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.sex.name","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.birthday","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.peno","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.peDate","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.phone","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.ms","value":"","description":"","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"data.peUserInfo.ms.code","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.ms.name","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.pregnantState","value":"","description":"","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"data.peUserInfo.pregnantState.code","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.pregnantState.name","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.vipLevel","value":"","description":"","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"data.peUserInfo.vipLevel.code","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.vipLevel.name","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.medicalType","value":"","description":"","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"data.peUserInfo.medicalType.code","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.medicalType.name","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.isGroup","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.company","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.workDept","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.teamNo","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.professional","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.workAge","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.peStates","value":"","description":"","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"data.peUserInfo.peStates.code","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.peStates.name","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.deptCount","value":"0","description":"","not_null":1,"field_type":"Integer","type":"Text","is_checked":1},{"key":"data.peUserInfo.age","value":"0","description":"","not_null":1,"field_type":"Integer","type":"Text","is_checked":1},{"key":"data.peUserInfo.deptFinishTime","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.firstCheckFinishTime","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.firstCheckFinishDoctor","value":"","description":"","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"data.peUserInfo.firstCheckFinishDoctor.code","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.firstCheckFinishDoctor.name","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.mainCheckFinishTime","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.mainCheckFinishDoctor","value":"","description":"","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"data.peUserInfo.mainCheckFinishDoctor.code","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.mainCheckFinishDoctor.name","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.forbidGoCheck","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.reportPrint","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.reportGot","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.replacementInspectionMark","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.applyItemList","value":"申请项目id1","description":"","not_null":1,"field_type":"Array","type":"Text","is_checked":1},{"key":"data.peUserInfo.currentNodeType","value":"","description":"当前操作节点用于流程控制，1登记,2分科3主检4总检","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.pePackage","value":"","description":"","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"data.peUserInfo.pePackage.code","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.peUserInfo.pePackage.name","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.archiveInfo","value":"","description":"","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"data.archiveInfo.name","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.archiveInfo.icCode","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.archiveInfo.sex","value":"","description":"","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"data.archiveInfo.sex.code","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.archiveInfo.sex.name","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.archiveInfo.birthday","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.archiveInfo.peNoList","value":"体检号1","description":"","not_null":1,"field_type":"Array","type":"Text","is_checked":1},{"key":"data.hospital","value":"","description":"","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"data.hospital.code","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.hospital.name","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"reponseTime","value":"*************","description":"","not_null":1,"field_type":"Integer","type":"Text","is_checked":1}],"expect":{"name":"成功","isDefault":1,"code":200,"contentType":"json","verifyType":"schema","schema":{"type":"object","properties":{"code":{"type":"string"},"msg":{"type":"string"},"data":{"type":"array","items":{"type":"object","title":"Items","properties":[],"APIPOST_ORDERS":["e261e6dc78c246bcb1ba0de80c6b54db"],"APIPOST_REFS":{"e261e6dc78c246bcb1ba0de80c6b54db":{"ref":"c2f03d19-a5ae-4ed2-a4f7-f614ee36ef46"}}}}},"APIPOST_ORDERS":["code","msg","data"],"APIPOST_REFS":[],"required":["code","msg"]},"mock":""}},"error":{"raw":"","parameter":[],"expect":{"name":"失败","isDefault":-1,"code":404,"contentType":"json","verifyType":"schema","schema":[],"mock":""}}},"parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177"},{"local_target_id":"3dcad8c9-be5d-41a5-a8c8-1a2f302a077d","local_parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177","name":"11.查询项目字典信息接口","method":"POST","mark":"developing","is_doc":0,"target_type":"api","example_type":"0","project_id":"a3fb6fe8-9132-41ce-af02-74be505d7d45","status":1,"sort":4,"create_dtime":1696377394,"update_dtime":1741573719,"local_server_id":"","target_id":"3dcad8c9-be5d-41a5-a8c8-1a2f302a077d","modifier_id":"6515BXWEVF9DSF07DDFF0BE164AFFE33CB0E7A81D1D0","created_uuid":"U3NEF5486420","extend_server_id":"","create_time":"2023-10-04 07:56:34","create_users":{"create_user":"ZW","update_user":"林为国"},"color":"#3A86FF","attribute_lists":[],"enable_server_mock":-1,"mock_server_url":"","mock_url":"","request":{"url":"","description":"全量查询所有的项目字典","body":{"mode":"json","parameter":[],"raw":"{\r\n    \"id\": \"\",\r\n    \"hospitalCode\":\"\"\r\n}","raw_para":[{"key":"id","value":"","description":"申请ID  如果为空，代表查询全部","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"hospitalCode","value":"","description":"医院编码，适用于多院区的情况","not_null":-1,"field_type":"String","type":"Text","is_checked":1}],"raw_schema":{"type":"object","properties":[],"APIPOST_ORDERS":[],"required":[]}},"event":{"pre_script":"","test":""},"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""},"digest":{"username":"","password":"","realm":"","nonce":"","algorithm":"","qop":"","nc":"","cnonce":"","opaque":""},"hawk":{"authId":"","authKey":"","algorithm":"","user":"","nonce":"","extraData":"","app":"","delegation":"","timestamp":"","includePayloadHash":-1},"awsv4":{"accessKey":"","secretKey":"","region":"","service":"","sessionToken":"","addAuthDataToQuery":-1},"ntlm":{"username":"","password":"","domain":"","workstation":"","disableRetryRequest":1},"edgegrid":{"accessToken":"","clientToken":"","clientSecret":"","nonce":"","timestamp":"","baseURi":"","headersToSign":""},"oauth1":{"consumerKey":"","consumerSecret":"","signatureMethod":"","addEmptyParamsToSign":-1,"includeBodyHash":-1,"addParamsToHeader":-1,"realm":"","version":"1.0","nonce":"","timestamp":"","verifier":"","callback":"","tokenSecret":"","token":""}},"header":{"parameter":[]},"query":{"parameter":[]},"cookie":{"parameter":[]},"resful":{"parameter":[]},"origin_url":""},"response":{"success":{"raw":"{\n    \"code\":0,\n    \"data\":[\n  {\n    \"applyItemId\": \"申请ID\",\n    \"applyItemName\": \"申请名称\",\n    \"displaySequence\": \"排序\",\n    \"deptId\": \"科室id\",\n    \"checkItemList\": [\n      {\n        \"checkItemId\": \"检查项目id\",\n        \"checkItemName\": \"检查项目名称\",\n        \"displaySequence\": \"排序\"\n      }\n    ]\n  }\n],\n  \"msg\": \"\"\n}","parameter":[{"key":"applyItemId","value":"4321","description":"体检系统申请项目编号","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"applyItemName","value":"血常规","description":"申请项目名称","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"checkItemList","value":"","description":"","not_null":1,"field_type":"Array","type":"Text","is_checked":1},{"key":"checkItemList.checkItemId","value":"4321","description":"体检系统检查项目编号","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"checkItemList.checktemName","value":"白细胞测定","description":"检查项目名称","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"description":"排序","field_type":"String","is_checked":1,"key":"displaySequence","value":"","not_null":1,"type":"Text"},{"description":"排序","field_type":"String","is_checked":1,"key":"checkItemList.displaySequence","value":"","not_null":1,"type":"Text"}],"expect":{"name":"成功","isDefault":1,"code":200,"contentType":"json","verifyType":"schema","schema":[],"mock":""}},"error":{"raw":"","parameter":[],"expect":{"name":"失败","isDefault":-1,"code":404,"contentType":"json","verifyType":"schema","schema":[],"mock":""}}},"parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177"},{"local_target_id":"5de4e20d-3ac7-46a3-974a-ec8730ff4835","local_parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177","name":"12.主检锁定与解锁(可选)","method":"POST","mark":"developing","is_doc":0,"target_type":"api","example_type":"0","project_id":"a3fb6fe8-9132-41ce-af02-74be505d7d45","status":1,"sort":5,"create_dtime":1696227892,"update_dtime":1748310520,"local_server_id":"","target_id":"5de4e20d-3ac7-46a3-974a-ec8730ff4835","modifier_id":"6515BXWEVF9DSF07DDFF0BE164AFFE33CB0E7A81D1D0","created_uuid":"U3NEF5486420","extend_server_id":"","create_time":"2023-10-02 14:24:52","create_users":{"create_user":"ZW","update_user":"林为国"},"color":"#3A86FF","attribute_lists":[],"enable_server_mock":-1,"mock_server_url":"","mock_url":"","request":{"url":"","description":"主检系统，通过这个接口，锁定和解锁报告的主检任务状态","body":{"mode":"json","parameter":[],"raw":"{\n\t\"operator\": \"\",\n\t\"peInfoList\": [\n\t\t{\n\t\t\t\"accountId\": \"\",\n\t\t\t\"currentNodeType\": 0,\n\t\t\t\"force\": false,\n\t\t\t\"operationType\": 0,\n\t\t\t\"peNo\": \"\"\n\t\t}\n\t]\n}","raw_para":[{"key":"operator","value":"","description":"操作人主键id","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"peInfoList","value":"","description":"","not_null":1,"field_type":"Array","type":"Text","is_checked":1},{"key":"peInfoList.accountId","value":"","description":"分配or解锁给谁\/分配人主键id","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"peInfoList.currentNodeType","value":"0","description":"当前流程节点1登记2分科3主检4总审","not_null":1,"field_type":"Integer","type":"Text","is_checked":1},{"key":"peInfoList.force","value":"false","description":"是否强制操作","not_null":1,"field_type":"Boolean","type":"Text","is_checked":1},{"key":"peInfoList.operationType","value":"0","description":"操作类型 1锁定\/分配,2 解锁","not_null":1,"field_type":"Integer","type":"Text","is_checked":1},{"key":"peInfoList.peNo","value":"","description":"体检号","not_null":1,"field_type":"String","type":"Text","is_checked":1}],"raw_schema":{"type":"object","properties":[],"APIPOST_ORDERS":[]}},"event":{"pre_script":"","test":""},"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""},"digest":{"username":"","password":"","realm":"","nonce":"","algorithm":"","qop":"","nc":"","cnonce":"","opaque":""},"hawk":{"authId":"","authKey":"","algorithm":"","user":"","nonce":"","extraData":"","app":"","delegation":"","timestamp":"","includePayloadHash":-1},"awsv4":{"accessKey":"","secretKey":"","region":"","service":"","sessionToken":"","addAuthDataToQuery":-1},"ntlm":{"username":"","password":"","domain":"","workstation":"","disableRetryRequest":1},"edgegrid":{"accessToken":"","clientToken":"","clientSecret":"","nonce":"","timestamp":"","baseURi":"","headersToSign":""},"oauth1":{"consumerKey":"","consumerSecret":"","signatureMethod":"","addEmptyParamsToSign":-1,"includeBodyHash":-1,"addParamsToHeader":-1,"realm":"","version":"1.0","nonce":"","timestamp":"","verifier":"","callback":"","tokenSecret":"","token":""}},"header":{"parameter":[]},"query":{"parameter":[]},"cookie":{"parameter":[]},"resful":{"parameter":[]},"origin_url":""},"response":{"success":{"raw":"","parameter":[],"expect":{"name":"成功","isDefault":1,"code":200,"contentType":"json","verifyType":"schema","schema":[],"mock":""}},"error":{"raw":"","parameter":[],"expect":{"name":"失败","isDefault":-1,"code":404,"contentType":"json","verifyType":"schema","schema":[],"mock":""}}},"parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177"},{"local_target_id":"bf0e0fc8-7e7d-456a-9662-f19db0cf31c2","local_parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177","name":"13.体检报告状态更新(可选)","method":"POST","mark":"developing","is_doc":0,"target_type":"api","example_type":"0","project_id":"a3fb6fe8-9132-41ce-af02-74be505d7d45","status":1,"sort":6,"create_dtime":1696227980,"update_dtime":1734424536,"local_server_id":"","target_id":"bf0e0fc8-7e7d-456a-9662-f19db0cf31c2","modifier_id":"6515BXWEVF9DSF07DDFF0BE164AFFE33CB0E7A81D1D0","created_uuid":"U3NEF5486420","extend_server_id":"","create_time":"2023-10-02 14:26:20","create_users":{"create_user":"ZW","update_user":"林为国"},"color":"#3A86FF","attribute_lists":[],"enable_server_mock":-1,"mock_server_url":"","mock_url":"","request":{"url":"\/updatePeStatus","description":"本接口非必须","body":{"mode":"json","parameter":[],"raw":"{\r\n\t\"nodeType\": \"\",\r\n\t\"timestamp\": \"\",\r\n\t\"doUser\": {\r\n\t\t\"code\": \"\",\r\n\t\t\"name\": \"\"\r\n\t},\r\n    \"peNo\":\"\"\r\n}","raw_para":[{"key":"nodeType","value":"","description":"1 登记 2检查（分科检查）3总检 4总审","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"timestamp","value":"","description":"变更时间","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"doUser","value":"","description":"","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"doUser.code","value":"","description":"操作人编码","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"doUser.name","value":"","description":"操作人名称","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"peNo","value":"","description":"体检号","not_null":1,"field_type":"String","type":"Text","is_checked":1}],"raw_schema":{"type":"object","properties":[],"APIPOST_ORDERS":[],"required":[]}},"event":{"pre_script":"","test":""},"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""},"digest":{"username":"","password":"","realm":"","nonce":"","algorithm":"","qop":"","nc":"","cnonce":"","opaque":""},"hawk":{"authId":"","authKey":"","algorithm":"","user":"","nonce":"","extraData":"","app":"","delegation":"","timestamp":"","includePayloadHash":-1},"awsv4":{"accessKey":"","secretKey":"","region":"","service":"","sessionToken":"","addAuthDataToQuery":-1},"ntlm":{"username":"","password":"","domain":"","workstation":"","disableRetryRequest":1},"edgegrid":{"accessToken":"","clientToken":"","clientSecret":"","nonce":"","timestamp":"","baseURi":"","headersToSign":""},"oauth1":{"consumerKey":"","consumerSecret":"","signatureMethod":"","addEmptyParamsToSign":-1,"includeBodyHash":-1,"addParamsToHeader":-1,"realm":"","version":"1.0","nonce":"","timestamp":"","verifier":"","callback":"","tokenSecret":"","token":""}},"header":{"parameter":[]},"query":{"parameter":[]},"cookie":{"parameter":[]},"resful":{"parameter":[]},"origin_url":"\/updatePeStatus"},"response":{"success":{"raw":"","parameter":[],"expect":{"name":"成功","isDefault":1,"code":200,"contentType":"json","verifyType":"schema","schema":[],"mock":""}},"error":{"raw":"","parameter":[],"expect":{"name":"失败","isDefault":-1,"code":404,"contentType":"json","verifyType":"schema","schema":[],"mock":""}}},"parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177"},{"local_target_id":"2168b51d-a4e8-4cba-9d7d-b8208c95e923","local_parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177","name":"14.重要异常标注接口(可选)","method":"POST","mark":"developing","is_doc":0,"target_type":"api","example_type":"0","project_id":"a3fb6fe8-9132-41ce-af02-74be505d7d45","status":1,"sort":7,"create_dtime":1696248512,"update_dtime":1747119623,"local_server_id":"","target_id":"2168b51d-a4e8-4cba-9d7d-b8208c95e923","modifier_id":"UUSWO20C9AAB","created_uuid":"U3NEF5486420","extend_server_id":"","create_time":"2023-10-02 20:08:32","create_users":{"create_user":"ZW","update_user":"张浩雄"},"color":"#3A86FF","attribute_lists":[],"enable_server_mock":-1,"mock_server_url":"","mock_url":"","request":{"url":"","description":"主检系统通过此接口，将发现的重要异常通知给体检系统。\n本接口非必须","body":{"mode":"json","parameter":[],"raw":"{\n\t\"pe_no\": \"\",\n\t\"level\": \"\",\n\t\"importanceCode\": \"\",\n\t\"mark_doctor\": {\n\t\t\"code\": \"\",\n\t\t\"name\": \"\"\n\t},\n\t\"mark_dept\": {\n\t\t\"code\": \"\",\n\t\t\"name\": \"\"\n\t},\n\t\"relateItem\": [\n\t\t{\n\t\t\t\"code\": \"\",\n\t\t\t\"name\": \"\",\n\t\t\t\"checkItem\": [\n\t\t\t\t{\n\t\t\t\t\t\"code\": \"\",\n\t\t\t\t\t\"name\": \"\"\n\t\t\t\t}\n\t\t\t]\n\t\t}\n\t],\n\t\"relateConclusion\": [\n\t\t{\n\t\t\t\"conclusionName\": \"\",\n\t\t\t\"conclusionCode\": \"\"\n\t\t}\n\t],\n\t\"remark\": \"\",\n\t\"isNoticeUser\": false,\n\t\"noticeRemark\": \"\"\n}","raw_para":[{"key":"pe_no","value":"","description":"体检号","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"level","value":"","description":"级别：1-A;2-B;3-C;4-D;9-其他","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"importanceCode","value":"","description":"重要异常编码","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"mark_doctor","value":"","description":"","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"mark_doctor.code","value":"","description":"报告医生编号","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"mark_doctor.name","value":"","description":"报告医生姓名","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"mark_dept","value":"","description":"","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"mark_dept.code","value":"","description":"报告科室编号","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"mark_dept.name","value":"","description":"报告科室名称","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"relateItem","value":"","description":"关联申请项目","not_null":1,"field_type":"Array","type":"Text","is_checked":1},{"key":"relateItem.code","value":"","description":"申请项目主键(以02接口提供的id为主)","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"relateItem.name","value":"","description":"申请项目名称","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"relateItem.checkItem","value":"","description":"检查项目细项","not_null":1,"field_type":"Array","type":"Text","is_checked":1},{"key":"relateItem.checkItem.code","value":"","description":"检查项目细项主键(以02接口提供的id为主)","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"relateItem.checkItem.name","value":"","description":"检查项目细项名称","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"relateConclusion","value":"","description":"","not_null":1,"field_type":"Array","type":"Text","is_checked":1},{"key":"relateConclusion.conclusionName","value":"","description":"标注异常的结论词名称（如果有）","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"relateConclusion.conclusionCode","value":"","description":"标注异常的结论词编码（如果有）","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"remark","value":"","description":"异常备注","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"isNoticeUser","value":"false","description":"是否已经通知体检者","not_null":1,"field_type":"Boolean","type":"Text","is_checked":1},{"key":"noticeRemark","value":"","description":"通知结果备注","not_null":1,"field_type":"String","type":"Text","is_checked":1}],"raw_schema":{"type":"object"}},"event":{"pre_script":"","test":""},"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""},"digest":{"username":"","password":"","realm":"","nonce":"","algorithm":"","qop":"","nc":"","cnonce":"","opaque":""},"hawk":{"authId":"","authKey":"","algorithm":"","user":"","nonce":"","extraData":"","app":"","delegation":"","timestamp":"","includePayloadHash":-1},"awsv4":{"accessKey":"","secretKey":"","region":"","service":"","sessionToken":"","addAuthDataToQuery":-1},"ntlm":{"username":"","password":"","domain":"","workstation":"","disableRetryRequest":1},"edgegrid":{"accessToken":"","clientToken":"","clientSecret":"","nonce":"","timestamp":"","baseURi":"","headersToSign":""},"oauth1":{"consumerKey":"","consumerSecret":"","signatureMethod":"","addEmptyParamsToSign":-1,"includeBodyHash":-1,"addParamsToHeader":-1,"realm":"","version":"1.0","nonce":"","timestamp":"","verifier":"","callback":"","tokenSecret":"","token":""}},"header":{"parameter":[]},"query":{"parameter":[]},"cookie":{"parameter":[]},"resful":{"parameter":[]},"origin_url":""},"response":{"success":{"raw":"","parameter":[],"expect":{"name":"成功","isDefault":1,"code":200,"contentType":"json","verifyType":"schema","schema":[],"mock":""}},"error":{"raw":"","parameter":[],"expect":{"name":"失败","isDefault":-1,"code":404,"contentType":"json","verifyType":"schema","schema":[],"mock":""}}},"parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177"},{"local_target_id":"1425e5af-**************-2edc70a0263b","local_parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177","name":"15.分科退回接口(可选)","method":"POST","mark":"developing","is_doc":0,"target_type":"api","example_type":"0","project_id":"a3fb6fe8-9132-41ce-af02-74be505d7d45","status":1,"sort":8,"create_dtime":1696248559,"update_dtime":1733280493,"local_server_id":"","target_id":"1425e5af-**************-2edc70a0263b","modifier_id":"6515BXWEVF9DSF07DDFF0BE164AFFE33CB0E7A81D1D0","created_uuid":"U3NEF5486420","extend_server_id":"","create_time":"2023-10-02 20:09:19","create_users":{"create_user":"ZW","update_user":"林为国"},"color":"#3A86FF","attribute_lists":[],"enable_server_mock":-1,"mock_server_url":"","mock_url":"","request":{"url":"","description":"主检系统通过此接口，将自动发现或者人工发现的分科异常问题，返回给体检系统，由体检系统，将状态设置为退回分科，并且通知到相关医生；\n该接口会同时调用多次的情况。\n本接口非必须","body":{"mode":"json","parameter":[],"raw":"{\n    \"peNo\": \"\",\n    \"markDoctor\": \"\",\n    \"errorItem\": \"\",\n    \"returnDept\": {\n        \"code\": \"\",\n        \"name\": \"\"\n    },\n    \"receiveDoctor\": {\n        \"code\": \"\",\n        \"name\": \"\"\n    },\n    \"remark\": \"\",\n    \"currentNodeType\": 0,\n}","raw_para":[{"key":"peNo","value":"","description":"体检号","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"markDoctor","value":"","description":"发起退回医生账号","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"errorItem","value":"","description":"退回项目编号（可选）","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"returnDept","value":"","description":"退回科室信息","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"returnDept.code","value":"","description":"退回科室编号","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"returnDept.name","value":"","description":"退回科室名称","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"receiveDoctor","value":"","description":"接受医生信息","not_null":-1,"field_type":"Object","type":"Object","is_checked":1},{"key":"receiveDoctor.code","value":"","description":"接收医生编号(可选)","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"receiveDoctor.name","value":"","description":"接收医生名称(可选)","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"remark","value":"","description":"退回原因备注","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"currentNodeType","value":"0","description":"当前操作节点用于流程控制，1登记,2分科3主检4总检","not_null":1,"field_type":"Integer","type":"Text","is_checked":1}],"raw_schema":{"type":"object"}},"event":{"pre_script":"","test":""},"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""},"digest":{"username":"","password":"","realm":"","nonce":"","algorithm":"","qop":"","nc":"","cnonce":"","opaque":""},"hawk":{"authId":"","authKey":"","algorithm":"","user":"","nonce":"","extraData":"","app":"","delegation":"","timestamp":"","includePayloadHash":-1},"awsv4":{"accessKey":"","secretKey":"","region":"","service":"","sessionToken":"","addAuthDataToQuery":-1},"ntlm":{"username":"","password":"","domain":"","workstation":"","disableRetryRequest":1},"edgegrid":{"accessToken":"","clientToken":"","clientSecret":"","nonce":"","timestamp":"","baseURi":"","headersToSign":""},"oauth1":{"consumerKey":"","consumerSecret":"","signatureMethod":"","addEmptyParamsToSign":-1,"includeBodyHash":-1,"addParamsToHeader":-1,"realm":"","version":"1.0","nonce":"","timestamp":"","verifier":"","callback":"","tokenSecret":"","token":""}},"header":{"parameter":[]},"query":{"parameter":[]},"cookie":{"parameter":[]},"resful":{"parameter":[]},"origin_url":""},"response":{"success":{"raw":"","parameter":[],"expect":{"name":"成功","isDefault":1,"code":200,"contentType":"json","verifyType":"schema","schema":[],"mock":""}},"error":{"raw":"","parameter":[],"expect":{"name":"失败","isDefault":-1,"code":404,"contentType":"json","verifyType":"schema","schema":[],"mock":""}}},"parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177"},{"local_target_id":"4e32498e-942f-4b03-901d-d831d5e60ffb","local_parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177","name":"16.查询图片接口(可选)","method":"POST","mark":"developing","is_doc":0,"target_type":"api","example_type":"0","project_id":"a3fb6fe8-9132-41ce-af02-74be505d7d45","status":1,"sort":9,"create_dtime":1696249398,"update_dtime":1749604387,"local_server_id":"","target_id":"4e32498e-942f-4b03-901d-d831d5e60ffb","modifier_id":"6515BXWEVF9DSF07DDFF0BE164AFFE33CB0E7A81D1D0","created_uuid":"U3NEF5486420","extend_server_id":"","create_time":"2023-10-02 20:23:18","create_users":{"create_user":"ZW","update_user":"林为国"},"color":"#3A86FF","attribute_lists":[],"enable_server_mock":-1,"mock_server_url":"","mock_url":"","request":{"url":"","description":"本接口非必须","body":{"mode":"json","parameter":[],"raw":"{\n    \"peNo\": \"\",\n    \"deptId\": \"\",\n    \"applyItemId\": []\n}","raw_para":[{"key":"peNo","value":"","description":"体检号","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"deptId","value":"","description":"科室编号获取图片","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"applyItemId","value":"","description":"申请项目获取图片","not_null":-1,"field_type":"Array","type":"Text","is_checked":1}],"raw_schema":{"type":"object"}},"event":{"pre_script":"","test":""},"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""},"digest":{"username":"","password":"","realm":"","nonce":"","algorithm":"","qop":"","nc":"","cnonce":"","opaque":""},"hawk":{"authId":"","authKey":"","algorithm":"","user":"","nonce":"","extraData":"","app":"","delegation":"","timestamp":"","includePayloadHash":-1},"awsv4":{"accessKey":"","secretKey":"","region":"","service":"","sessionToken":"","addAuthDataToQuery":-1},"ntlm":{"username":"","password":"","domain":"","workstation":"","disableRetryRequest":1},"edgegrid":{"accessToken":"","clientToken":"","clientSecret":"","nonce":"","timestamp":"","baseURi":"","headersToSign":""},"oauth1":{"consumerKey":"","consumerSecret":"","signatureMethod":"","addEmptyParamsToSign":-1,"includeBodyHash":-1,"addParamsToHeader":-1,"realm":"","version":"1.0","nonce":"","timestamp":"","verifier":"","callback":"","tokenSecret":"","token":""}},"header":{"parameter":[]},"query":{"parameter":[]},"cookie":{"parameter":[]},"resful":{"parameter":[]},"origin_url":""},"response":{"success":{"raw":"{\n\t\"code\": 0,\n\t\"msg\": \"\",\n\t\"data\": [\n\t\t{\n\t\t\t\"fileName\": \"\",\n\t\t\t\"fileUri\": \"文件绝对路径\",\n\t\t\t\"applyItemId\": \"文件归属项目id\",\n\t\t\t\"deptId\": \"文件归属科室\",\n\t\t\t\"base64Url\": \"base64文本\"\n\t\t}\n\t]\n}","parameter":[{"key":"fileName","value":"","description":"","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"fileUri","value":"文件绝对路径","description":"","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"applyItemId","value":"文件归属项目id","description":"","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"deptId","value":"文件归属科室","description":"","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"base64Url","value":"base64文本","description":"图片base64格式采用统一标准规则：data:[<mediatype>][;base64],<data>\n样例：data:image\/png;base64,iVBORw0KGgoAAAA...\n如果格式规范标准有不懂的可自行百度或问AI","not_null":-1,"field_type":"String","type":"Text","is_checked":1}],"expect":{"name":"成功","isDefault":1,"code":200,"contentType":"json","verifyType":"schema","schema":[],"mock":""}},"error":{"raw":"","parameter":[],"expect":{"name":"失败","isDefault":-1,"code":404,"contentType":"json","verifyType":"schema","schema":[],"mock":""}}},"parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177"},{"local_target_id":"20c0a17b-0e27-4115-bdd6-41ea10058b49","local_parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177","name":"17.重要异常删除接口(可选)","method":"POST","mark":"developing","is_doc":0,"target_type":"api","example_type":"0","project_id":"a3fb6fe8-9132-41ce-af02-74be505d7d45","status":1,"sort":10,"create_dtime":1697179030,"update_dtime":1725526274,"local_server_id":"","target_id":"20c0a17b-0e27-4115-bdd6-41ea10058b49","modifier_id":"UUSWO20C9AAB","created_uuid":"UUSWO20C9AAB","extend_server_id":"","create_time":"2023-10-13 14:37:10","create_users":{"create_user":"张浩雄","update_user":"张浩雄"},"color":"#3A86FF","attribute_lists":[],"enable_server_mock":-1,"mock_server_url":"","mock_url":"","request":{"url":"","description":"","body":{"mode":"json","parameter":[],"raw":"{\n\t\"pe_no\": \"\",\n\t\"importanceCode\": \"\"\n}","raw_para":[{"key":"pe_no","value":"体检号","description":"体检号","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"importanceCode","value":"","description":"重要异常编码","not_null":1,"field_type":"String","type":"Text","is_checked":1}],"raw_schema":{"type":"object"}},"event":{"pre_script":"","test":""},"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""},"digest":{"username":"","password":"","realm":"","nonce":"","algorithm":"","qop":"","nc":"","cnonce":"","opaque":""},"hawk":{"authId":"","authKey":"","algorithm":"","user":"","nonce":"","extraData":"","app":"","delegation":"","timestamp":"","includePayloadHash":-1},"awsv4":{"accessKey":"","secretKey":"","region":"","service":"","sessionToken":"","addAuthDataToQuery":-1},"ntlm":{"username":"","password":"","domain":"","workstation":"","disableRetryRequest":1},"edgegrid":{"accessToken":"","clientToken":"","clientSecret":"","nonce":"","timestamp":"","baseURi":"","headersToSign":""},"oauth1":{"consumerKey":"","consumerSecret":"","signatureMethod":"","addEmptyParamsToSign":-1,"includeBodyHash":-1,"addParamsToHeader":-1,"realm":"","version":"1.0","nonce":"","timestamp":"","verifier":"","callback":"","tokenSecret":"","token":""}},"header":{"parameter":[]},"query":{"parameter":[]},"cookie":{"parameter":[]},"resful":{"parameter":[]},"origin_url":""},"response":{"success":{"raw":"","parameter":[],"expect":{"name":"成功","isDefault":1,"code":200,"contentType":"json","verifyType":"schema","schema":[],"mock":""}},"error":{"raw":"","parameter":[],"expect":{"name":"失败","isDefault":-1,"code":404,"contentType":"json","verifyType":"schema","schema":[],"mock":""}}},"parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177"},{"local_target_id":"28ecce9f-5caf-44c9-8621-eb58d1e4dddb","local_parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177","name":"18.查询医生信息接口","method":"POST","mark":"developing","is_doc":0,"target_type":"api","example_type":"0","project_id":"a3fb6fe8-9132-41ce-af02-74be505d7d45","status":1,"sort":11,"create_dtime":1716176541,"update_dtime":1725526269,"local_server_id":"","target_id":"28ecce9f-5caf-44c9-8621-eb58d1e4dddb","modifier_id":"UUSWO20C9AAB","created_uuid":"UUSWO20C9AAB","extend_server_id":"","create_time":"2024-05-20 11:42:21","create_users":{"create_user":"张浩雄","update_user":"张浩雄"},"color":"#3A86FF","attribute_lists":[],"enable_server_mock":-1,"mock_server_url":"","mock_url":"","request":{"url":"","description":"","body":{"mode":"json","parameter":[],"raw":"{\r\n    \"id\": \"\",\r\n    \"hospitalCode\":\"\"\r\n\r\n}","raw_para":[{"key":"id","value":"","description":"医生主键如果不传,则查询所有的数据","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"hospitalCode","value":"","description":"医院编码，适用于多院区的情况","not_null":-1,"field_type":"String","type":"Text","is_checked":1}],"raw_schema":{"type":"object"}},"event":{"pre_script":"","test":""},"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""},"digest":{"username":"","password":"","realm":"","nonce":"","algorithm":"","qop":"","nc":"","cnonce":"","opaque":""},"hawk":{"authId":"","authKey":"","algorithm":"","user":"","nonce":"","extraData":"","app":"","delegation":"","timestamp":"","includePayloadHash":-1},"awsv4":{"accessKey":"","secretKey":"","region":"","service":"","sessionToken":"","addAuthDataToQuery":-1},"ntlm":{"username":"","password":"","domain":"","workstation":"","disableRetryRequest":1},"edgegrid":{"accessToken":"","clientToken":"","clientSecret":"","nonce":"","timestamp":"","baseURi":"","headersToSign":""},"oauth1":{"consumerKey":"","consumerSecret":"","signatureMethod":"","addEmptyParamsToSign":-1,"includeBodyHash":-1,"addParamsToHeader":-1,"realm":"","version":"1.0","nonce":"","timestamp":"","verifier":"","callback":"","tokenSecret":"","token":""}},"header":{"parameter":[]},"query":{"parameter":[]},"cookie":{"parameter":[]},"resful":{"parameter":[]},"origin_url":""},"response":{"success":{"raw":"{\r\n    \"code\":0,\r\n    \"data\":[\r\n  {\r\n    \"name\": \"姓名\",\r\n    \"icCode\": \"身份证号\",\r\n    \"phoneNo\": \"手机号 可为空\",\r\n    \"sex\": {\r\n      \"code\": \"1 男 2-女 3-未知\",\r\n      \"name\": \"男性 女性 未知\"\r\n    },\r\n    \"accountCode\": \"登录账号\",\r\n    \"accountId\": \"账号id\"\r\n  }\r\n],\r\n  \"msg\": \"\"\r\n}","parameter":[],"expect":{"name":"成功","isDefault":1,"code":200,"contentType":"json","verifyType":"schema","schema":[],"mock":""}},"error":{"raw":"","parameter":[],"expect":{"name":"失败","isDefault":-1,"code":404,"contentType":"json","verifyType":"schema","schema":[],"mock":""}}},"parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177"},{"local_target_id":"04e93f2b-9a6f-4f48-9856-6c8fd3c0c6d7","local_parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177","name":"19.查询科室信息","method":"POST","mark":"developing","is_doc":0,"target_type":"api","example_type":"0","project_id":"a3fb6fe8-9132-41ce-af02-74be505d7d45","status":1,"sort":12,"create_dtime":**********,"update_dtime":**********,"local_server_id":"","target_id":"04e93f2b-9a6f-4f48-9856-6c8fd3c0c6d7","modifier_id":"UUSWO20C9AAB","created_uuid":"UUSWO20C9AAB","extend_server_id":"","create_time":"2024-05-22 15:20:51","create_users":{"create_user":"张浩雄","update_user":"张浩雄"},"color":"#3A86FF","attribute_lists":[],"enable_server_mock":-1,"mock_server_url":"","mock_url":"","request":{"url":"","description":"","body":{"mode":"json","parameter":[],"raw":"{\r\n    \"id\": \"\",\r\n    \"hospitalCode\": \"0350001\"\r\n}","raw_para":[{"key":"id","value":"","description":"主键","not_null":-1,"field_type":"String","type":"Text","is_checked":1},{"key":"hospitalCode","value":"","description":"医院编码，适用于多院区的情况","not_null":-1,"field_type":"String","type":"Text","is_checked":1}],"raw_schema":{"type":"object"}},"event":{"pre_script":"","test":""},"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""},"digest":{"username":"","password":"","realm":"","nonce":"","algorithm":"","qop":"","nc":"","cnonce":"","opaque":""},"hawk":{"authId":"","authKey":"","algorithm":"","user":"","nonce":"","extraData":"","app":"","delegation":"","timestamp":"","includePayloadHash":-1},"awsv4":{"accessKey":"","secretKey":"","region":"","service":"","sessionToken":"","addAuthDataToQuery":-1},"ntlm":{"username":"","password":"","domain":"","workstation":"","disableRetryRequest":1},"edgegrid":{"accessToken":"","clientToken":"","clientSecret":"","nonce":"","timestamp":"","baseURi":"","headersToSign":""},"oauth1":{"consumerKey":"","consumerSecret":"","signatureMethod":"","addEmptyParamsToSign":-1,"includeBodyHash":-1,"addParamsToHeader":-1,"realm":"","version":"1.0","nonce":"","timestamp":"","verifier":"","callback":"","tokenSecret":"","token":""}},"header":{"parameter":[]},"query":{"parameter":[]},"cookie":{"parameter":[]},"resful":{"parameter":[]},"origin_url":""},"response":{"success":{"raw":"{\r\n    \"code\": 0,\r\n    \"msg\": \"\",\r\n    \"data\": [\r\n  {\r\n    \"name\": \"名称\",\r\n    \"id\": \"主键\",\r\n    \"displaySequence\": \"科室排序\"\r\n  }\r\n],\r\n    \"reponseTime\": *************\r\n}","parameter":[],"expect":{"name":"成功","isDefault":1,"code":200,"contentType":"json","verifyType":"schema","schema":[],"mock":""}},"error":{"raw":"","parameter":[],"expect":{"name":"失败","isDefault":-1,"code":404,"contentType":"json","verifyType":"schema","schema":[],"mock":""}}},"parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177"},{"local_target_id":"224cbfa6-e6e5-4089-824d-c4a8c9205bdf","local_parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177","name":"20.查询个人开单情况(可选)","method":"POST","mark":"developing","is_doc":0,"target_type":"api","example_type":"0","project_id":"a3fb6fe8-9132-41ce-af02-74be505d7d45","status":1,"sort":13,"create_dtime":1739773782,"update_dtime":1746778480,"local_server_id":"","target_id":"224cbfa6-e6e5-4089-824d-c4a8c9205bdf","modifier_id":"UUSWO20C9AAB","created_uuid":"UUSWO20C9AAB","extend_server_id":"","create_time":"2025-02-17 14:29:42","create_users":{"create_user":"张浩雄","update_user":"张浩雄"},"color":"#3A86FF","attribute_lists":[],"enable_server_mock":-1,"mock_server_url":"","mock_url":"","request":{"url":"","description":"查询对应体检号下面的开单信息，只包含检查项目信息，不包含耗材类的数据","body":{"mode":"json","parameter":[],"raw":"{\n\t\"peNoList\": [],\n\t\"hospitalCode\":\"\"\n}","raw_para":[],"raw_schema":{"type":"object"}},"event":{"pre_script":"","test":""},"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""},"digest":{"username":"","password":"","realm":"","nonce":"","algorithm":"","qop":"","nc":"","cnonce":"","opaque":""},"hawk":{"authId":"","authKey":"","algorithm":"","user":"","nonce":"","extraData":"","app":"","delegation":"","timestamp":"","includePayloadHash":-1},"awsv4":{"accessKey":"","secretKey":"","region":"","service":"","sessionToken":"","addAuthDataToQuery":-1},"ntlm":{"username":"","password":"","domain":"","workstation":"","disableRetryRequest":1},"edgegrid":{"accessToken":"","clientToken":"","clientSecret":"","nonce":"","timestamp":"","baseURi":"","headersToSign":""},"oauth1":{"consumerKey":"","consumerSecret":"","signatureMethod":"","addEmptyParamsToSign":-1,"includeBodyHash":-1,"addParamsToHeader":-1,"realm":"","version":"1.0","nonce":"","timestamp":"","verifier":"","callback":"","tokenSecret":"","token":""}},"header":{"parameter":[]},"query":{"parameter":[]},"cookie":{"parameter":[]},"resful":{"parameter":[]},"origin_url":""},"response":{"success":{"raw":"{\n\t\"code\": 0,\n\t\"msg\": \"\",\n\t\"data\": [\n\t\t{\n\t\t\t\"peNo\": \"\",\n\t\t\t\"applyItemId\": \"\",\n\t\t\t\"applyItemName\": \"\",\n\t\t\t\"registrationFlag\": true,\n\t\t\t\"refusedInspectionFlag\": false,\n\t\t\t\"checkedFlag\": true,\n\t\t\t\"orderTime\": \"2025-02-17 12:00:00\",\n\t\t\t\"medicalOrderDoctor\": {\n\t\t\t\t\"code\": \"\",\n\t\t\t\t\"name\": \"\"\n\t\t\t},\n\t\t\t\"patientResponsibility\": \"123.35\"\n\t\t}\n\t],\n\t\"reponseTime\": *************\n}","parameter":[{"key":"code","value":"0","description":"","not_null":1,"field_type":"Integer","type":"Text","is_checked":1},{"key":"msg","value":"","description":"","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data","value":"","description":"","not_null":1,"field_type":"Array","type":"Text","is_checked":1},{"key":"data.peNo","value":"","description":"体检号","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.applyItemId","value":"","description":"申请项目id","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.applyItemName","value":"","description":"申请项目名称","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.registrationFlag","value":"true","description":"登记标识","not_null":1,"field_type":"Boolean","type":"Text","is_checked":1},{"key":"data.refusedInspectionFlag","value":"false","description":"弃检标识","not_null":1,"field_type":"Boolean","type":"Text","is_checked":1},{"key":"data.checkedFlag","value":"true","description":"检查标识","not_null":1,"field_type":"Boolean","type":"Text","is_checked":1},{"key":"data.orderTime","value":"2025-02-17 12:00:00","description":"开单时间","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.medicalOrderDoctor","value":"","description":"","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"data.medicalOrderDoctor.code","value":"","description":"开单医生编码","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.medicalOrderDoctor.name","value":"","description":"开单医生姓名","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.patientResponsibility","value":"123.35","description":"自费金额","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"reponseTime","value":"*************","description":"","not_null":1,"field_type":"Integer","type":"Text","is_checked":1}],"expect":{"name":"成功","isDefault":1,"code":200,"contentType":"json","verifyType":"schema","schema":[],"mock":""}},"error":{"raw":"","parameter":[],"expect":{"name":"失败","isDefault":-1,"code":404,"contentType":"json","verifyType":"schema","schema":[],"mock":""}}},"parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177"},{"local_target_id":"d2d25bf5-906c-44dc-b89e-d53e3e27a2bf","local_parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177","name":"21.查询重要异常通知数据(可选)","method":"POST","mark":"developing","is_doc":0,"target_type":"api","example_type":"0","project_id":"a3fb6fe8-9132-41ce-af02-74be505d7d45","status":1,"sort":14,"create_dtime":**********,"update_dtime":**********,"local_server_id":"","target_id":"d2d25bf5-906c-44dc-b89e-d53e3e27a2bf","modifier_id":"6515BXWEVF9DSF07DDFF0BE164AFFE33CB0E7A81D1D0","created_uuid":"6515BXWEVF9DSF07DDFF0BE164AFFE33CB0E7A81D1D0","extend_server_id":"","create_time":"2025-06-10 08:40:58","create_users":{"create_user":"林为国","update_user":"林为国"},"color":"#3A86FF","attribute_lists":[],"enable_server_mock":-1,"mock_server_url":"","mock_url":"","request":{"url":"data-external-gw\/getAbnormal","description":"查询院区健管系统的重要异常是否通知","body":{"mode":"json","parameter":[],"raw":"{\"peNo\":\"\",\"hospital\":{\"code\":\"\",\"name\":\"\"}}","raw_para":[{"key":"peNo","value":"","description":"体检号","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"hospital","value":"","description":"","not_null":1,"field_type":"Object","type":"Object","is_checked":1},{"key":"hospital.code","value":"","description":"院区编码","not_null":"-1","field_type":"String","type":"Text","is_checked":1},{"key":"hospital.name","value":"","description":"院区名称","not_null":"-1","field_type":"String","type":"Text","is_checked":1}],"raw_schema":{"APIPOST_REFS":{"b96f1614-508a-46b7-bf9c-8956b0fc872a":{"ref":"bb22f3cb5748313dbaa8515eb08ff00d"}},"properties":[],"APIPOST_ORDERS":["b96f1614-508a-46b7-bf9c-8956b0fc872a"],"type":"object"}},"event":{"pre_script":"","test":""},"auth":{"type":"noauth","kv":{"key":"","value":""},"bearer":{"key":""},"basic":{"username":"","password":""},"digest":{"username":"","password":"","realm":"","nonce":"","algorithm":"","qop":"","nc":"","cnonce":"","opaque":""},"hawk":{"authId":"","authKey":"","algorithm":"","user":"","nonce":"","extraData":"","app":"","delegation":"","timestamp":"","includePayloadHash":-1},"awsv4":{"accessKey":"","secretKey":"","region":"","service":"","sessionToken":"","addAuthDataToQuery":-1},"ntlm":{"username":"","password":"","domain":"","workstation":"","disableRetryRequest":1},"edgegrid":{"accessToken":"","clientToken":"","clientSecret":"","nonce":"","timestamp":"","baseURi":"","headersToSign":""},"oauth1":{"consumerKey":"","consumerSecret":"","signatureMethod":"","addEmptyParamsToSign":-1,"includeBodyHash":-1,"addParamsToHeader":-1,"realm":"","version":"1.0","nonce":"","timestamp":"","verifier":"","callback":"","tokenSecret":"","token":""}},"header":{"parameter":[]},"query":{"parameter":[]},"cookie":{"parameter":[]},"resful":{"parameter":[]},"origin_url":"data-external-gw\/getAbnormal"},"response":{"success":{"raw":"{\"code\":0,\"msg\":\"\",\"data\":{\"peNo\":\"\",\"abnormalType\":0,\"abnormalName\":\"\",\"discoveryTime\":\"\",\"discoveryUserId\":\"\",\"deptId\":\"\",\"deptName\":\"\",\"hasNotice\":\"\"},\"reponseTime\":\"\"}","parameter":[{"key":"code","value":0,"description":"0：调用接口成功","not_null":"-1","field_type":"Integer","type":"Text","is_checked":1},{"key":"msg","value":"","description":"","not_null":"-1","field_type":"String","type":"Text","is_checked":1},{"key":"data.peNo","value":"","description":"体检号","not_null":1,"field_type":"String","type":"Text","is_checked":1},{"key":"data.abnormalType","value":0,"description":"重要异常级别 1:A类重要异常 2:B类重要异常 3:C类重要异常 4:D类重要异常 9：其他","not_null":"-1","field_type":"Integer","type":"Text","is_checked":1},{"key":"data.abnormalName","value":"","description":"重要异常名称","not_null":"-1","field_type":"String","type":"Text","is_checked":1},{"key":"data.discoveryTime","value":"","description":"发现时间 格式： yyyy-MM-dd HH:mm:ss","not_null":"-1","field_type":"String","type":"Text","is_checked":1},{"key":"data.discoveryUserId","value":"","description":"上报人ID","not_null":"-1","field_type":"String","type":"Text","is_checked":1},{"key":"data.deptId","value":"","description":"科室ID","not_null":"-1","field_type":"String","type":"Text","is_checked":1},{"key":"data.deptName","value":"","description":"科室名称","not_null":"-1","field_type":"String","type":"Text","is_checked":1},{"key":"data.hasNotice","value":"","description":"是否已通知  1：已通知 0：未通知","not_null":"-1","field_type":"String","type":"Text","is_checked":1},{"key":"reponseTime","value":"","description":"响应时间","not_null":"-1","field_type":"String","type":"Text","is_checked":1}],"expect":{"name":"成功","isDefault":1,"code":200,"contentType":"json","verifyType":"schema","schema":{"type":"object"},"mock":""}},"error":{"raw":"","parameter":[],"expect":{"name":"失败","isDefault":-1,"code":404,"contentType":"json","verifyType":"schema","schema":{"type":"object"},"mock":""}}},"parent_id":"eb34599d-60d1-41a8-a023-3f7c69a06177"}]}]}]}};
        apipostData = apipostData.data;
        consoleLog('apipostData ---- ',apipostData);
    } catch (e) { }

    if (!_.isEmpty(apipostData)) {
        if (!_.isArray(apipostData.children)) {
            apipostData.children = [];
        }

        // 导航菜单
        apipostData.children = sortData(apipostData.children, 'ascending');

        // 数据词典（对象，键为target_id）
        const dataDB = {};
        const ruleType = (data) => {
            const d = Object.prototype.toString.call(data);
            return d.substr(8, d.length - 9);
        };

        const renderListtoObject = (List) => {
            if (ruleType(List) === 'Array') {
                List &&
                List.forEach((item) => {
                    if (item.children) {
                        renderListtoObject(item.children);
                        dataDB[item.target_id] = item;
                    } else {
                        dataDB[item.target_id] = item;
                    }
                });
            }
        };

        // 搜索
        $(document).on('input', 'input[name="search-input"]', function () {
            let keyword = _.toLower($(this).val());
            let searchResult = [];

            if (keyword == '') {
                $('.apipost-left-nav-template').find('li').removeClass('is-search-hidden search-toggle');
            } else {

                if (_.isObject(dataDB) && !_.isEmpty(dataDB)) {
                    _.forEach(dataDB, function (item) {
                        consoleLog(_.toLower(item.name), keyword, _.toLower(item.name).indexOf(keyword))
                        if (_.toLower(item.url).indexOf(keyword) > -1 || _.toLower(item.name).indexOf(keyword) > -1 || _.toLower(item.method).indexOf(keyword) > -1) {
                            searchResult.push(item.target_id);
                        }
                    });

                    if (searchResult.length == 0) {
                        $('.apipost-left-nav-template').find('li').addClass('is-search-hidden');
                    } else {
                        searchResult.forEach(target_id => {
                            searchResult = _.concat(searchResult, getParentTargetIDs(Object.values(dataDB), target_id));
                        })
                    }

                    // 隐藏其他
                    _.difference(Object.keys(dataDB), searchResult).forEach(hidden_target_id => {
                        $(`.apipost-left-nav-template li[data-target_id="${hidden_target_id}"]`).addClass('is-search-hidden');
                        $(`.apipost-left-nav-template li[data-target_id="${hidden_target_id}"][data-type="folder"]`).addClass('search-toggle');
                    })

                    // 展示结果
                    searchResult.forEach(show_target_id => {
                        $(`.apipost-left-nav-template li[data-target_id="${show_target_id}"]`).removeClass('is-search-hidden is-hidden search-toggle');
                        $(`.apipost-left-nav-template li[data-target_id="${show_target_id}"][data-type="folder"]`).removeClass('search-toggle');
                    })
                }
            }
        })

        renderListtoObject(apipostData.children);

        // 获取当前的target_id
        let _target_id = '001';
        const _urlParas = GetUrlQuery();

        if (_.isObject(_urlParas) && _.isString(_urlParas.target_id)) {
            _target_id = _urlParas.target_id;
        }

        // 修改地址栏
        function changeURLStatic(name, value) {
            const url = changeURLParam(location.href, name, value); // 修改 URL 参数
            history.replaceState(null, null, url); // 替换地址栏
        }

        function changeURLParam(url, name, value) {
            const reg = eval(`/([?|&]${name}=)[^&]*/gi`);
            value = value.toString().replace(/(^\s*)|(\s*$)/g, ''); // 移除首尾空格
            let url2;
            if (!value) {
                // remove
                url2 = url.replace(reg, '');
            } else if (url.match(reg)) {
                // edit
                url2 = url.replace(reg, `$1${value}`);
            } else {
                // add
                url2 = `${url + (url.indexOf('?') > -1 ? '&' : '?') + name}=${value}`;
            }
            return url2;
        }

        // 格式化内容
        function formatJson(json) {
            // consoleLog(json, apipostData);

            // name
            if (json.target_type == 'global') {
                _.set(json, 'name', '全局公共参数');
            }

            // mark
            if (_.has(apipostData, 'project.vars.mark') && _.isArray(apipostData.project.vars.mark)) {
                const _markedObj = {};
                apipostData.project.vars.mark.forEach((item) => {
                    _markedObj[item.key] = item;
                });

                if (_.isObject(_markedObj[json.mark])) {
                    _.set(json, 'formatMark', _markedObj[json.mark]);
                }
            }

            // time
            if (_.isNumber(json.create_dtime)) {
                if (String(json.create_dtime).length == 10) {
                    _.set(
                        json,
                        'format_create_dtime',
                        dayjs.unix(json.create_dtime).format('YYYY-MM-DD HH:mm:ss')
                    );
                }

                if (String(json.create_dtime).length == 13) {
                    _.set(json, 'format_create_dtime', dayjs(json.create_dtime).format('YYYY-MM-DD HH:mm:ss'));
                }
            } else {
                if (_.isString(json.create_dtime)) {
                    _.set(json, 'format_create_dtime', json.create_dtime);
                } else {
                    _.set(json, 'format_create_dtime', dayjs().format('YYYY-MM-DD HH:mm:ss'));
                }
            }

            if (_.isNumber(json.update_dtime)) {
                if (String(json.update_dtime).length == 10) {
                    _.set(
                        json,
                        'format_update_dtime',
                        dayjs.unix(json.update_dtime).format('YYYY-MM-DD HH:mm:ss')
                    );
                }

                if (String(json.update_dtime).length == 13) {
                    _.set(json, 'format_update_dtime', dayjs(json.update_dtime).format('YYYY-MM-DD HH:mm:ss'));
                }
            } else {
                if (_.isString(json.update_dtime)) {
                    _.set(json, 'format_update_dtime', json.update_dtime);
                } else {
                    _.set(json, 'format_update_dtime', dayjs().format('YYYY-MM-DD HH:mm:ss'));
                }
            }

            // formatMode
            if (_.has(json, 'request.body.mode')) {
                switch (json.request.body.mode) {
                    case 'none':
                        break;
                    case 'form-data':
                        _.set(json, 'request.body.formatMode', 'multipart/form-data');
                        break;
                    case 'urlencoded':
                        _.set(json, 'request.body.formatMode', 'application/x-www-form-urlencoded');
                        break;
                    case 'json':
                        _.set(json, 'request.body.formatMode', 'application/json');
                        break;
                    case 'xml':
                        _.set(json, 'request.body.formatMode', 'application/xml');
                        break;
                    case 'javascript':
                        _.set(json, 'request.body.formatMode', 'application/javascript');
                        break;
                    case 'plain':
                        _.set(json, 'request.body.formatMode', 'text/plain');
                        break;
                    case 'html':
                        _.set(json, 'request.body.formatMode', 'text/html');
                        break;
                }
            }

            if(_.has(json, 'request.auth.type')) {
                switch (json.request.auth.type) {
                    case 'noauth':
                        _.set(json, 'request.authType', '无需认证');
                        break;
                    case 'awsv4':
                        _.set(json, 'request.authType', 'AWS Signature');
                        break;
                    case 'basic':
                        _.set(json, 'request.authType', 'Basic auth');
                        break;
                    case 'bearer':
                        _.set(json, 'request.authType', 'Bearer auth');
                        break;
                    case 'digest':
                        _.set(json, 'request.authType', 'Digest auth');
                        break;
                    case 'edgegrid':
                        _.set(json, 'request.authType', 'Akamai EdgeGrid');
                        break;
                    case 'hawk':
                        _.set(json, 'request.authType', 'Hawk authentication');
                        break;
                    case 'kv':
                        _.set(json, 'request.authType', '私密键值对');
                        break;
                    case 'ntlm':
                        _.set(json, 'request.authType', 'NTLM Authentication');
                        break;
                    case 'oauth1':
                        _.set(json, 'request.authType', 'OAuth 1.0');
                        break;
                }
            }

            json.url = json.request.url;
            return json;
        }

        // 加载当前数据
        function loadData(_target_id,dataDB) {

            // 获取当前的数据信息
            let _targetData = {};

            // 右侧浮动导航
            let floatNavs = [];

            if(_target_id == '001'){
                if (_.isObject(dataDB[_target_id])) {
                    _targetData = _.cloneDeep(dataDB[_target_id]);
                } else if (
                    _.has(apipostData, 'project.vars.request') &&
                    _.isObject(apipostData.project.vars.request)
                ) {
                    _targetData = {
                        target_type: 'global',
                        request: apipostData.project.vars.request,
                        script: apipostData.project.vars.script,
                    };
                } else {
                    _targetData = {
                        target_type: 'none',
                        request: {},
                    };
                }

                _targetData = formatJson(_targetData);

                $('.apipost-doc-body-nav-list').find('li').removeClass('active');
                $('.apipost-doc-body-nav-list')
                    .find(`li[data-target_id="${_target_id}"]`)
                    .eq(0)
                    .addClass('active')
                    .toggleClass('toggle search-toggle');

                // const children_ids = getChildrenTargetIDs(Object.values(dataDB), _target_id);
                //
                // if (_.isArray(children_ids)) {
                //     if (
                //         $('.apipost-doc-body-nav-list')
                //             .find(`li[data-target_id="${_target_id}"]`)
                //             .eq(0)
                //             .hasClass('toggle')
                //     ) {
                //         children_ids.forEach((target_id) => {
                //             if ($(`li[data-target_id="${target_id}"]`).attr('data-type') == 'folder') {
                //                 $(`li[data-target_id="${target_id}"]`).addClass('is-hidden toggle');
                //             } else {
                //                 $(`li[data-target_id="${target_id}"]`).addClass('is-hidden');
                //             }
                //         });
                //     } else {
                //         children_ids.forEach((target_id) => {
                //             if ($(`li[data-target_id="${target_id}"]`).attr('data-type') == 'folder') {
                //                 $(`li[data-target_id="${target_id}"]`).removeClass('is-hidden toggle');
                //             } else {
                //                 $(`li[data-target_id="${target_id}"]`).removeClass('is-hidden');
                //             }
                //         });
                //     }
                // }

                _targetData.copyData = JSON.parse(JSON.stringify(_targetData));
                if(_targetData.copyData.target_type == 'example'){
                    _targetData.copyData.target_type = _targetData.copyData.example_type;
                }

                // 加载左侧内容
                const _docContentHtml = template('apipost-doc-wrap-tpl', {
                    data: _targetData,
                    target_id: _target_id,
                });

                $('.apipost-doc-wrap').html(_docContentHtml);

                // 右侧浮动导航
                floatNavs = [];
                $('.apipost-doc-wrap').find('h3 ,h4, h5, h6, h7').each((i, item) => {
                    let text = $(item).attr('data-text');
                    let id = $(item).attr('id');
                    if (text) {
                        floatNavs.push({
                            text, id,
                            name: $(item).prop("tagName"),
                            indent: $(item).prop("tagName").replace(/[^0-9]/ig, "") - 2,
                        })
                    }
                })

                const _floatNavs = template('apipost-float-nav-template-tpl', {
                    list: floatNavs
                });

                $('.apipost-float-nav-template').html(_floatNavs);

                // 请求body raw 参数
                try {
                    if (_targetData.target_type == 'grpc' || _targetData.example_type == 'grpc') {
                        $('.request-body-raw-editor-json').each((i, item) => {
                            let val = $(item).prev('input').val();

                            if(val){
                                new JsonEditor(
                                    `#${$(item).attr('id')}`,
                                    JSON5.parse(val),
                                    {},
                                    []
                                ).load(JSON5.parse(val), []);
                            }
                        })
                    }

                    if (_targetData.target_type == 'api' || _targetData.example_type == 'api') {
                        let obj = [];
                        if (_.has(_targetData, 'request.body.raw_para')) {
                            obj = _targetData.request.body.raw_para.map((itemRaw) => {
                                const arr = itemRaw.key.split('.');
                                itemRaw.newKey = arr[arr.length - 1];
                                return itemRaw;
                            });
                        }

                        if($('#request-body-raw-editor-input').length > 0){
                            new JsonEditor('#request-body-raw-editor-json',
                                JSON5.parse($('#request-body-raw-editor-input').val()),
                                {},
                                obj
                            ).load(JSON5.parse($('#request-body-raw-editor-input').val()), obj);
                        }
                    }
                } catch (e) { }

                // 响应示例 raw 参数
                try {
                    _.forEach(_targetData.response, function (item, key) {
                        const obj = item.parameter.map((itemRaw) => {
                            const arr = itemRaw.key.split('.');
                            itemRaw.newKey = arr[arr.length - 1];
                            return itemRaw;
                        });

                        if($(`#response-raw-${key}-json-input`).length > 0){
                            new JsonEditor(`#response-raw-${key}-json`,
                                JSON5.parse($(`#response-raw-${key}-json-input`).val()),
                                {},
                                obj
                            ).load(JSON5.parse($(`#response-raw-${key}-json-input`).val()), obj);
                        }
                    });
                } catch (e) { }

                // 更新 url
                changeURLStatic('target_id', _target_id);

                // 优化 apipost-doc-paras 展示
                $('.apipost-doc-paras').each((i, item) => {
                    if ($(item).children().length == 0) {
                        let _id = $(item).attr('for-id');
                        let _title = $(`#${_id}`).attr('data-text');
                        let _tipDiv = `<div class="apipost-no-data">暂无${_title}数据</div>`;
                        $(item).append(_tipDiv)
                    }
                })
            }else {

                let newdata = Object.values(dataDB)
                // let LevelArr = []
                //
                // function levelTree(arr1, newarr) {
                //     arr1.forEach(item => {
                //         if (item.children && item.children.length > 0) {
                //             newarr.push(item)
                //             levelTree(item.children, newarr)
                //         } else {
                //             newarr.push(item)
                //         }
                //     })
                // }
                //
                // levelTree(newdata, LevelArr)

                newdata.forEach(item => {
                    if (item.local_target_id == _target_id) {

                        _targetData = formatJson(item);

                        $('.apipost-doc-body-nav-list').find('li').removeClass('active');
                        $('.apipost-doc-body-nav-list')
                            .find(`li[data-target_id="${_target_id}"]`)
                            .eq(0)
                            .addClass('active')
                            .toggleClass('toggle search-toggle');

                        const children_ids = getChildrenTargetIDs(Object.values(dataDB), _target_id);

                        if (_.isArray(children_ids)) {
                            if (
                                $('.apipost-doc-body-nav-list')
                                    .find(`li[data-target_id="${_target_id}"]`)
                                    .eq(0)
                                    .hasClass('toggle')
                            ) {
                                children_ids.forEach((target_id) => {
                                    if ($(`li[data-target_id="${target_id}"]`).attr('data-type') == 'folder') {
                                        $(`li[data-target_id="${target_id}"]`).addClass('is-hidden toggle');
                                    } else {
                                        $(`li[data-target_id="${target_id}"]`).addClass('is-hidden');
                                    }
                                });
                            } else {
                                children_ids.forEach((target_id) => {
                                    if ($(`li[data-target_id="${target_id}"]`).attr('data-type') == 'folder') {
                                        $(`li[data-target_id="${target_id}"]`).removeClass('is-hidden toggle');
                                    } else {
                                        $(`li[data-target_id="${target_id}"]`).removeClass('is-hidden');
                                    }
                                });
                            }
                        }

                        // 加载左侧内容
                        const _docContentHtml = template('apipost-doc-wrap-tpl', {
                            data: _targetData,
                            target_id: _target_id,
                        });

                        $('.apipost-doc-wrap').html(_docContentHtml);

                        // 右侧浮动导航
                        floatNavs = [];
                        $('.apipost-doc-wrap').find('h3 ,h4, h5, h6, h7').each((i, item) => {
                            let text = $(item).attr('data-text');
                            let id = $(item).attr('id');
                            if (text) {
                                floatNavs.push({
                                    text, id,
                                    name: $(item).prop("tagName"),
                                    indent: $(item).prop("tagName").replace(/[^0-9]/ig, "") - 2,
                                })
                            }
                        })

                        const _floatNavs = template('apipost-float-nav-template-tpl', {
                            list: floatNavs
                        });

                        $('.apipost-float-nav-template').html(_floatNavs);

                        // 请求body raw 参数
                        try {
                            if (_targetData.target_type == 'grpc' || _targetData.example_type == 'grpc') {
                                $('.request-body-raw-editor-json').each((i, item) => {
                                    let val = $(item).prev('input').val();

                                    if(val){
                                        new JsonEditor(
                                            `#${$(item).attr('id')}`,
                                            JSON5.parse(val),
                                            {},
                                            []
                                        ).load(JSON5.parse(val), []);
                                    }
                                })
                            }

                            if (_targetData.target_type == 'api' || _targetData.example_type == 'api') {
                                let obj = [];
                                if (_.has(_targetData, 'request.body.raw_para')) {
                                    obj = _targetData.request.body.raw_para.map((itemRaw) => {
                                        const arr = itemRaw.key.split('.');
                                        itemRaw.newKey = arr[arr.length - 1];
                                        return itemRaw;
                                    });
                                }

                                if($('#request-body-raw-editor-input').length > 0){
                                    new JsonEditor('#request-body-raw-editor-json',
                                        JSON5.parse($('#request-body-raw-editor-input').val()),
                                        {},
                                        obj
                                    ).load(JSON5.parse($('#request-body-raw-editor-input').val()), obj);
                                }
                            }
                        } catch (e) { }

                        // 响应示例 raw 参数
                        try {
                            _.forEach(_targetData.response, function (item, key) {
                                const obj = item.parameter.map((itemRaw) => {
                                    const arr = itemRaw.key.split('.');
                                    itemRaw.newKey = arr[arr.length - 1];
                                    return itemRaw;
                                });

                                if($(`#response-raw-${key}-json-input`).length > 0){
                                    new JsonEditor(`#response-raw-${key}-json`,
                                        JSON5.parse($(`#response-raw-${key}-json-input`).val()),
                                        {},
                                        obj
                                    ).load(JSON5.parse($(`#response-raw-${key}-json-input`).val()), obj);
                                }
                            });
                        } catch (e) { }
                        // 更新 url
                        changeURLStatic('target_id', _target_id);
                    }
                    // 优化 apipost-doc-paras 展示
                    $('.apipost-doc-paras').each((i, item) => {
                        if ($(item).children().length == 0) {
                            let _id = $(item).attr('for-id');
                            let _title = $(`#${_id}`).attr('data-text');
                            let _tipDiv = `<div class="apipost-no-data">暂无${_title}数据</div>`;
                            $(item).append(_tipDiv)
                        }
                    })
                })
            }

            // 折叠导航(移动端)
            $('.apipost-doc-body-nav,.apipost-doc-body-content').removeClass('open');

            $(document).on('scroll', function (e) {
                let currentNav= '';
                let scrollTop = $(document).scrollTop();

                floatNavs.forEach(item => {
                    if ($(`#${item.id}`).offset()) {
                        if ($(`#${item.id}`).offset().top > scrollTop && currentNav == '') {
                            currentNav = item.id
                            // console.log(currentNav)
                            $('.apipost-float-nav-template').find(`li[data-id="${currentNav}"]`).addClass('active').siblings().removeClass('active');
                        }
                    }
                })
                // console.log('请求参数:', $('#请求参数').offset().top, scrollTop)
            }).on('click', '.apipost-float-nav-template li', function(){
                $(this).addClass('active').siblings().removeClass('active');
            });

        }

        // 初始化左侧导航列表
        const _leftNavHtml = template('apipost-left-nav-template-tpl', {
            list: apipostData.children,
            target_id: _target_id,
        });

        $('.apipost-left-nav-template').html(_leftNavHtml);

        // 初始化项目公共信息
        $('title').text(`${apipostData.project_name} - Powered by Apipost V7`);
        $('.apipost-doc-body-nav-title').text(`${apipostData.project_name}`);

        // project-logo
        if (_.has(apipostData, 'project_logo') && apipostData.project_logo != '') {
            $('.project-logo').attr('src', apipostData.project_logo);
        }

        $('.project-logo').on('error', function () {
            $('.project-logo').attr('src', 'https://img.cdn.apipost.cn/docs/images7/logo.svg');
        });
        // consoleLog(apipostData)

        // 复制按钮
        const clipboard = new ClipboardJS('.copy-clipboard');
        clipboard.on('success', function (e) {
            $('.copy-clipboard-success').remove();
            $(e.trigger).append('<i class="copy-clipboard-success">复制成功</i>');
            e.clearSelection();
            $('.copy-clipboard-success').fadeOut(1000);
            setTimeout(function () {
                $('.copy-clipboard-success').remove();
            }, 1500);
        });

        clipboard.on('error', function (e) { });

        // 优化左侧菜单展示，默认收起
        // 获取当前的父ID
        let _target_parent_ids = getParentTargetIDs(Object.values(dataDB), _target_id);

        $('.apipost-left-nav-template').children('li').each((i, item) => {
            if ($(item).attr('data-type') == 'folder') {
                if (_target_parent_ids.indexOf($(item).attr('data-target_id')) == -1) {
                    $(item).addClass('toggle');
                } else {
                    $(item).removeClass('toggle');
                }
            }

            //if($(item).attr('data-type') != 'example') {
                if ($(item).attr('data-parent_id') != '0' && _target_parent_ids.indexOf($(item).attr('data-target_id')) == -1 && $(item).attr('data-target_id') != _target_id) {
                    $(item).addClass('is-hidden');
                } else {
                    $(item).removeClass('is-hidden');
                }
            //}
        })

        // 同级展示
        if (dataDB[_target_id]) {
            // $('.apipost-left-nav-template').children(`li[data-parent_id="${dataDB[_target_id].parent_id}"]`).removeClass('is-hidden');
            _target_parent_ids.forEach(_parent_id => {
                $('.apipost-left-nav-template').children(`li[data-parent_id="${_parent_id}"]`).removeClass('is-hidden');
            })
        }

        // alert(dataDB)
        // 加载单接口信息
        loadData(_target_id, dataDB);

        // 绑定事件
        $(document).on('click', '.mobile-nav-toggle-link-btn', function () { // 移动端导航
            $('.apipost-doc-body-nav,.apipost-doc-body-content').addClass('open');
        }).on('click', '.load-data-btn', function () { // 加载数据
            let target_id = $(this).attr('data-target_id');
            // let parent_id = $(this).attr('data-parent_id');
            // let target_type = $(this).attr('data-type');
            // let indent = $(this).attr('data-indent');

            // 加载单条数据
            loadData(target_id, dataDB);
        }).on('click', '.export-download-btn', function () { // 导出文档
            $(this).next('ul').toggleClass('is-hidden');
        })
    } else {
        // 空数据
        // consoleLog('null')
    }
</script>
<script>
    var _hmt = _hmt || [];
    (function () {
        var hm = document.createElement("script");
        hm.src = "https://hm.baidu.com/hm.js?a046ce178828e393614822a297b8d296";
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm, s);
    })();
</script>
</body>

</html>
