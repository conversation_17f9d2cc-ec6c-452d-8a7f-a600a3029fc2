-- ============================================================================
-- 查询指定单位体检人数（修正版）
-- 文件：test/unit_query_example.sql
-- 说明：查询单位编号02000003来了多少人
-- 修正：T_Contract表中使用cUnitsCode字段，不是cClientCode
-- ============================================================================

-- 方法1：简单统计查询（推荐）
-- 查询单位编号02000003来了多少人
SELECT 
    COUNT(*) as 体检人数
FROM T_Register_Main rm
INNER JOIN T_Contract tc ON rm.cContractCode = tc.cCode
WHERE tc.cUnitsCode = '02000003';  -- 单位编号

-- 方法2：带详细统计的查询
SELECT 
    tc.cUnitsCode as 单位编码,
    tc.cName as 单位名称,
    tc.cCode as 合同编码,
    COUNT(*) as 体检人数,
    COUNT(CASE WHEN rm.cStatus >= '1' THEN 1 END) as 已确认人数,
    COUNT(CASE WHEN rm.cStatus >= '2' THEN 1 END) as 已总检人数,
    COUNT(CASE WHEN rm.cStatus >= '3' THEN 1 END) as 已打印人数
FROM T_Register_Main rm
INNER JOIN T_Contract tc ON rm.cContractCode = tc.cCode
WHERE tc.cUnitsCode = '02000003'  -- 单位编号
GROUP BY tc.cUnitsCode, tc.cName, tc.cCode;

-- 方法3：查看详细的人员列表
SELECT 
    rm.cClientCode as 客户编号,
    rm.cName as 姓名,
    rm.cSex as 性别,
    rm.cIdCard as 身份证号,
    rm.dOperdate as 登记时间,
    rm.dAffirmdate as 确认时间,
    CASE rm.cStatus 
        WHEN '0' THEN '已登记'
        WHEN '1' THEN '已确认'
        WHEN '2' THEN '已总检'
        WHEN '3' THEN '已打印'
        ELSE '未知状态'
    END as 体检状态,
    tc.cName as 单位名称,
    tc.cUnitsCode as 单位编码,
    us.cName as 套餐名称
FROM T_Register_Main rm
INNER JOIN T_Contract tc ON rm.cContractCode = tc.cCode
LEFT JOIN T_UnitsSuit_Master us ON rm.cSuitCode = us.cSuitCode
WHERE tc.cUnitsCode = '02000003'  -- 单位编号
ORDER BY rm.dOperdate DESC;

-- ============================================================================
-- 说明：正确的表关联关系
-- ============================================================================
-- T_Register_Main.cContractCode → T_Contract.cCode (合同关联)
-- T_Contract.cUnitsCode 存储单位编号
-- T_Contract.cCheckType 用于区分团检和散客
-- T_Register_Main.cSuitCode → T_UnitsSuit_Master.cSuitCode (套餐信息)
-- ============================================================================ 