#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试02-06号接口的报文打印功能
验证HTTP请求和响应报文是否正确输出到控制台
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入接口
from interface_02_syncApplyItem import TianjianInterface02
from interface_03_deptInfo import TianjianInterface03
from interface_04_syncUser import TianjianInterface04
from interface_05_syncDept import TianjianInterface05
from interface_06_syncDict import TianjianInterface06

# API配置
API_CONFIG = {
    'base_url': 'http://203.83.237.114:9300',
    'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
    'mic_code': 'MIC1.001E',
    'misc_id': 'MISC1.00001A',
    'timeout': 30
}

def test_interface_02():
    """测试02号接口报文输出"""
    print("\n" + "="*100)
    print("测试02号接口 - 申请项目字典数据传输")
    print("="*100)
    
    try:
        interface = TianjianInterface02(API_CONFIG)
        result = interface.sync_apply_items(
            limit=3,  # 只测试3条数据
            test_mode=True,  # 测试模式，不实际发送
            batch_size=3,
            verbose_message=True
        )
        print(f"02号接口测试结果: {result.get('message', '未知')}")
        return True
    except Exception as e:
        print(f"02号接口测试失败: {str(e)}")
        return False

def test_interface_03():
    """测试03号接口报文输出"""
    print("\n" + "="*100)
    print("测试03号接口 - 科室结果信息传输")
    print("="*100)
    
    try:
        interface = TianjianInterface03(API_CONFIG)
        result = interface.sync_dept_results(
            days=1,  # 只查询1天的数据
            limit=3,  # 只测试3条数据
            test_mode=True,  # 测试模式，不实际发送
            batch_size=3,
            verbose_message=True
        )
        print(f"03号接口测试结果: {result.get('message', '未知')}")
        return True
    except Exception as e:
        print(f"03号接口测试失败: {str(e)}")
        return False

def test_interface_04():
    """测试04号接口报文输出"""
    print("\n" + "="*100)
    print("测试04号接口 - 医生信息传输")
    print("="*100)
    
    try:
        interface = TianjianInterface04(API_CONFIG)
        result = interface.sync_operators(
            test_mode=True,  # 测试模式，不实际发送
            batch_size=3,
            verbose_message=True,
            limit=3  # 只测试3条数据
        )
        print(f"04号接口测试结果: {result.get('message', '未知')}")
        return True
    except Exception as e:
        print(f"04号接口测试失败: {str(e)}")
        return False

def test_interface_05():
    """测试05号接口报文输出"""
    print("\n" + "="*100)
    print("测试05号接口 - 科室信息传输")
    print("="*100)
    
    try:
        interface = TianjianInterface05(API_CONFIG)
        result = interface.sync_departments(
            test_mode=True,  # 测试模式，不实际发送
            batch_size=3,
            verbose_message=True,
            limit=3  # 只测试3条数据
        )
        print(f"05号接口测试结果: {result.get('message', '未知')}")
        return True
    except Exception as e:
        print(f"05号接口测试失败: {str(e)}")
        return False

def test_interface_06():
    """测试06号接口报文输出"""
    print("\n" + "="*100)
    print("测试06号接口 - 字典信息传输")
    print("="*100)
    
    try:
        interface = TianjianInterface06(API_CONFIG)
        result = interface.sync_dict_info(
            test_mode=True,  # 测试模式，不实际发送
            batch_size=3,
            verbose_message=True,
            limit=3  # 只测试3条数据
        )
        print(f"06号接口测试结果: {result.get('message', '未知')}")
        return True
    except Exception as e:
        print(f"06号接口测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("="*100)
    print("天健云02-06号接口报文输出测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*100)
    
    # 测试结果统计
    results = []
    
    # 测试各个接口
    test_functions = [
        ("02号接口", test_interface_02),
        ("03号接口", test_interface_03),
        ("04号接口", test_interface_04),
        ("05号接口", test_interface_05),
        ("06号接口", test_interface_06),
    ]
    
    for interface_name, test_func in test_functions:
        try:
            success = test_func()
            results.append((interface_name, success))
        except Exception as e:
            print(f"{interface_name}测试异常: {str(e)}")
            results.append((interface_name, False))
    
    # 输出测试总结
    print("\n" + "="*100)
    print("测试总结")
    print("="*100)
    
    success_count = 0
    for interface_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{interface_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(results)} 个接口测试成功")
    
    if success_count == len(results):
        print("🎉 所有接口报文输出功能正常！")
    else:
        print("⚠️  部分接口存在问题，请检查错误信息")
    
    print("="*100)

if __name__ == "__main__":
    main()
