#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试06号接口的HTTP日志记录功能
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from interface_06_syncDict import TianjianInterface06

# API配置
API_CONFIG = {
    'base_url': 'http://203.83.237.114:9300',
    'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
    'mic_code': 'MIC1.001E',
    'misc_id': 'MISC1.00001A',
    'timeout': 30
}

def test_06_interface_http_logging():
    """测试06号接口的HTTP日志记录功能"""
    print("=" * 80)
    print("测试06号接口HTTP日志记录功能")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    try:
        # 创建06号接口实例
        interface = TianjianInterface06(API_CONFIG)
        
        # 获取少量字典数据进行测试
        print("正在获取字典数据...")
        dict_data = interface.get_dict_data()
        
        if not dict_data:
            print("❌ 没有找到字典数据，创建测试数据...")
            # 创建测试数据
            dict_data = [
                {
                    "name": "性别",
                    "id": "SEX",
                    "type": "SEX",
                    "hospitalCode": "MIC1.001E",
                    "dictList": [
                        {"id": "1", "name": "男", "displaySequence": "1"},
                        {"id": "2", "name": "女", "displaySequence": "2"}
                    ]
                }
            ]
        else:
            # 只取第一条数据进行测试
            dict_data = dict_data[:1]
        
        print(f"✅ 准备发送 {len(dict_data)} 条字典数据")
        print("\n现在将实际发送HTTP请求到天健云，观察HTTP报文输出...")
        print("注意：以下将显示完整的HTTP请求和响应报文，并记录到日志文件")
        print("-" * 80)
        
        # 实际发送请求
        result = interface._send_request(dict_data)
        
        print("-" * 80)
        print("HTTP报文输出测试完成")
        
        if result.get('success'):
            print("✅ 请求成功，HTTP报文已正确输出到控制台和日志文件")
        else:
            print(f"⚠️  请求失败，但HTTP报文已输出: {result.get('error', '未知错误')}")
        
        # 检查日志文件是否生成
        today = datetime.now().strftime('%Y-%m-%d')
        log_file = f"logs/interface_06_http_messages_{today}.log"
        
        if os.path.exists(log_file):
            print(f"✅ 日志文件已生成: {log_file}")
            
            # 显示日志文件大小
            file_size = os.path.getsize(log_file)
            print(f"📄 日志文件大小: {file_size} bytes")
            
            # 显示日志文件最后几行
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if len(lines) > 10:
                    print(f"\n日志文件最后10行:")
                    print("-" * 50)
                    for line in lines[-10:]:
                        print(line.rstrip())
                    print("-" * 50)
                else:
                    print(f"\n日志文件内容:")
                    print("-" * 50)
                    for line in lines:
                        print(line.rstrip())
                    print("-" * 50)
        else:
            print(f"❌ 日志文件未生成: {log_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("天健云06号接口HTTP报文日志记录测试")
    print("本测试将验证06号接口的HTTP报文是否正确记录到logs目录中")
    print()
    
    success = test_06_interface_http_logging()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 06号接口HTTP报文日志记录功能测试成功！")
        print("✅ HTTP报文已同时输出到控制台和日志文件")
        print("✅ 日志文件位置: logs/interface_06_http_messages_YYYY-MM-DD.log")
        print("✅ 现在通过GUI启动的程序点击发送也会输出日志")
    else:
        print("❌ 06号接口HTTP报文日志记录功能测试失败")
    print("=" * 80)

if __name__ == "__main__":
    main()
