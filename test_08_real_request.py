#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用真实报文格式测试08号接口
"""

from interface_08_getDict import TianjianInterface08

# API配置
api_config = {
    'base_url': 'http://203.83.237.114:9300',
    'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
    'mic_code': 'MIC1.001E',
    'misc_id': 'MISC1.00001A',
    'timeout': 30
}

# 创建接口实例
interface = TianjianInterface08(api_config)

# 使用你提供的报文格式测试09门店
print('=== 使用你的报文格式测试09门店 ===')
request_data_09 = {
    'id': '',
    'type': '',
    'hospitalCode': '09'
}

result_09 = interface.get_dict(request_data_09)

if 'totalCount' in result_09:
    print('成功连接09门店！')
    print('   检查项目:', result_09['totalCount']['items'], '条')
    print('   科室信息:', result_09['totalCount']['departments'], '条') 
    print('   操作员:', result_09['totalCount']['operators'], '条')
else:
    print('09门店查询失败:', result_09.get('error', '未知错误'))

print()

# 对比08门店的数据
print('=== 对比08门店的数据 ===')
request_data_08 = {
    'id': '',
    'type': '',
    'hospitalCode': '08'
}

result_08 = interface.get_dict(request_data_08)

if 'totalCount' in result_08:
    print('成功连接08门店！')
    print('   检查项目:', result_08['totalCount']['items'], '条')
    print('   科室信息:', result_08['totalCount']['departments'], '条')
    print('   操作员:', result_08['totalCount']['operators'], '条')
else:
    print('08门店查询失败:', result_08.get('error', '未知错误'))

print()
print('=== 验证结果 ===')
if 'totalCount' in result_09 and 'totalCount' in result_08:
    items_09 = result_09['totalCount']['items']
    items_08 = result_08['totalCount']['items']
    
    if items_09 != items_08:
        print('✓ 成功！不同门店返回了不同的数据，动态连接工作正常')
        print('   09门店:', items_09, '项 vs 08门店:', items_08, '项')
    else:
        print('✗ 问题：两个门店返回相同的数据量')