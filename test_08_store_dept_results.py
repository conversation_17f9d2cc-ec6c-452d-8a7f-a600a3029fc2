#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试08门店的09号接口 - 体检科室结果重传
"""

import requests
import json

# 测试数据 - 08门店
test_data_08 = {
    "peNoList": [
        "085041193",
        "9000001"
    ],
    "deptId": "",
    "hospitalCode": "08"
}

def test_08_store_dept_results():
    """测试08门店的科室结果查询"""
    url = "http://localhost:5007/dx/inter/retransmitDeptInfo"
    
    print("测试08门店的09号接口 - 体检科室结果重传")
    print("=" * 60)
    print(f"请求URL: {url}")
    print(f"请求数据: {json.dumps(test_data_08, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data_08, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('code') == 0:
                data = result.get('data', [])
                print(f"成功返回 {len(data)} 条科室结果数据")
                
                # 显示第一条记录的结构
                if data:
                    first_record = data[0]
                    print(f"\\n第一条记录结构:")
                    print(f"  体检号: {first_record.get('peNo')}")
                    print(f"  科室: {first_record.get('dept', {}).get('name')} ({first_record.get('dept', {}).get('code')})")
                    print(f"  医院: {first_record.get('hospital', {}).get('name')} ({first_record.get('hospital', {}).get('code')})")
                    print(f"  检查时间: {first_record.get('checkTime')}")
                    print(f"  检查医生: {first_record.get('checkDoctor', {}).get('name')}")
                    print(f"  审核状态: {first_record.get('auditStatus', {}).get('name')}")
                    print(f"  项目明细数量: {len(first_record.get('itemDesc', []))}")
            else:
                print(f"请求失败: {result.get('msg')}")
                print("完整响应:")
                print(json.dumps(result, ensure_ascii=False, indent=2))
        else:
            print(f"HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {str(e)}")
    
    print("\\n测试完成")

if __name__ == "__main__":
    test_08_store_dept_results()