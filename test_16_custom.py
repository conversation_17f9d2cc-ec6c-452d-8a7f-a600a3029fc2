#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试16号接口 - 使用用户指定的报文数据
"""

import json
import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from interface_16_getImages import TianjianInterface16


def test_custom_request():
    """测试用户指定的报文数据"""
    # 打开日志文件
    with open('a.txt', 'w', encoding='utf-8') as log_file:
        def log_print(msg):
            """同时输出到控制台和文件"""
            print(msg)
            log_file.write(msg + '\n')
            log_file.flush()

        log_print("=" * 80)
        log_print("16号接口图片查询测试 - 用户自定义报文")
        log_print("=" * 80)
    
        # API配置
        api_config = {
            'base_url': 'http://203.83.237.114:9300',
            'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
            'mic_code': 'MIC1.001E',
            'misc_id': 'MISC1.00001A',
            'timeout': 30
        }

        # 用户指定的请求数据
        request_data = {
            'peNo': '085041193',  # 卡号
            'deptId': '',         # 空表示所有科室
            'applyItemId': [],    # 空表示所有项目
            'cshopcode': '08'     # 机构编码
        }

        log_print("[REQUEST] 请求报文:")
        log_print(json.dumps(request_data, ensure_ascii=False, indent=2))
        log_print("-" * 80)

        try:
            # 创建接口实例
            interface = TianjianInterface16(api_config)

            # 调用接口
            log_print("[QUERY] 正在查询图片...")
            result = interface.query_images_service(request_data)

            log_print("[RESPONSE] 响应结果:")
            log_print(f"状态码: {result.get('code', 'N/A')}")
            log_print(f"消息: {result.get('msg', 'N/A')}")

            # 处理返回数据
            data = result.get('data', [])
            if isinstance(data, list):
                log_print(f"图片数量: {len(data)}")

                if len(data) > 0:
                    log_print("\n[IMAGES] 图片详情:")
                    for i, image in enumerate(data):  # 显示所有图片的信息
                        log_print(f"  图片 {i+1}:")
                        log_print(f"    客户编码: {image.get('clientCode', 'N/A')}")
                        log_print(f"    主编码: {image.get('mainCode', 'N/A')}")
                        log_print(f"    图片名称: {image.get('picName', 'N/A')}")
                        log_print(f"    位置: {image.get('position', 'N/A')}")
                        log_print(f"    卡号: {image.get('cardNo', 'N/A')}")

                        # 检查图片数据
                        image_data = image.get('imageData', '')
                        if image_data:
                            log_print(f"    图片数据: 有 (长度: {len(image_data)} 字符)")
                            # 检查是否为base64格式
                            if image_data.startswith('data:image/') or len(image_data) > 100:
                                log_print(f"    数据格式: Base64编码")
                                # 显示base64数据的前100个字符
                                log_print(f"    数据预览: {image_data[:100]}...")
                            else:
                                log_print(f"    数据格式: 未知")
                        else:
                            log_print(f"    图片数据: 无")
                        log_print("")
                else:
                    log_print("  未找到图片数据")
            else:
                log_print(f"数据格式异常: {type(data)}")

            log_print("-" * 80)
            log_print("[FULL_RESPONSE] 完整响应报文:")
            # 完整保存到文件，但控制台显示截断版本
            full_result = json.dumps(result, ensure_ascii=False, indent=2)
            log_file.write(full_result + '\n')

            # 控制台显示截断版本
            display_result = result.copy()
            if 'data' in display_result and isinstance(display_result['data'], list):
                for item in display_result['data']:
                    if 'imageData' in item and len(str(item['imageData'])) > 200:
                        item['imageData'] = str(item['imageData'])[:200] + "...(截断，完整数据已保存到a.txt)"

            print(json.dumps(display_result, ensure_ascii=False, indent=2))

        except Exception as e:
            log_print(f"[ERROR] 测试异常: {str(e)}")
            import traceback
            error_info = traceback.format_exc()
            log_print("详细错误信息:")
            log_print(error_info)
    
        log_print("=" * 80)
        log_print("测试完成")
        log_print(f"详细日志已保存到: a.txt")

    print("=" * 80)
    print("测试完成，详细日志已保存到 a.txt 文件")


if __name__ == "__main__":
    test_custom_request()
