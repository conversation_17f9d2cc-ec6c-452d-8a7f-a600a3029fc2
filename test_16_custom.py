#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试16号接口 - 使用用户指定的报文数据
"""

import json
import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from interface_16_getImages import TianjianInterface16


def test_custom_request():
    """测试用户指定的报文数据"""
    print("=" * 80)
    print("16号接口图片查询测试 - 用户自定义报文")
    print("=" * 80)
    
    # API配置
    api_config = {
        'base_url': 'http://203.83.237.114:9300',
        'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
        'mic_code': 'MIC1.001E',
        'misc_id': 'MISC1.00001A',
        'timeout': 30
    }
    
    # 用户指定的请求数据
    request_data = {
        'peNo': '085041193',  # 卡号
        'deptId': '',         # 空表示所有科室
        'applyItemId': [],    # 空表示所有项目
        'cshopcode': '08'     # 机构编码
    }
    
    print("[REQUEST] 请求报文:")
    print(json.dumps(request_data, ensure_ascii=False, indent=2))
    print("-" * 80)

    try:
        # 创建接口实例
        interface = TianjianInterface16(api_config)

        # 调用接口
        print("[QUERY] 正在查询图片...")
        result = interface.query_images_service(request_data)

        print("[RESPONSE] 响应结果:")
        print(f"状态码: {result.get('code', 'N/A')}")
        print(f"消息: {result.get('msg', 'N/A')}")

        # 处理返回数据
        data = result.get('data', [])
        if isinstance(data, list):
            print(f"图片数量: {len(data)}")

            if len(data) > 0:
                print("\n[IMAGES] 图片详情:")
                for i, image in enumerate(data[:3]):  # 只显示前3张图片的信息
                    print(f"  图片 {i+1}:")
                    print(f"    客户编码: {image.get('clientCode', 'N/A')}")
                    print(f"    主编码: {image.get('mainCode', 'N/A')}")
                    print(f"    图片名称: {image.get('picName', 'N/A')}")
                    print(f"    位置: {image.get('position', 'N/A')}")
                    print(f"    卡号: {image.get('cardNo', 'N/A')}")

                    # 检查图片数据
                    image_data = image.get('imageData', '')
                    if image_data:
                        print(f"    图片数据: 有 (长度: {len(image_data)} 字符)")
                        # 检查是否为base64格式
                        if image_data.startswith('data:image/') or len(image_data) > 100:
                            print(f"    数据格式: Base64编码")
                        else:
                            print(f"    数据格式: 未知")
                    else:
                        print(f"    图片数据: 无")
                    print()

                if len(data) > 3:
                    print(f"  ... 还有 {len(data) - 3} 张图片")
            else:
                print("  未找到图片数据")
        else:
            print(f"数据格式异常: {type(data)}")

        print("-" * 80)
        print("[FULL_RESPONSE] 完整响应报文:")
        # 为了避免输出过长，如果有图片数据则截断显示
        display_result = result.copy()
        if 'data' in display_result and isinstance(display_result['data'], list):
            for item in display_result['data']:
                if 'imageData' in item and len(str(item['imageData'])) > 200:
                    item['imageData'] = str(item['imageData'])[:200] + "...(截断)"

        print(json.dumps(display_result, ensure_ascii=False, indent=2))

    except Exception as e:
        print(f"[ERROR] 测试异常: {str(e)}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()
    
    print("=" * 80)
    print("测试完成")


if __name__ == "__main__":
    test_custom_request()
