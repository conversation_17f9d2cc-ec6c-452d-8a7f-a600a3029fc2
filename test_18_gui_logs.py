#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试18号接口GUI日志显示
"""

import json
import requests
import time
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_gui_18_interface_logs():
    """测试GUI中18号接口的日志显示"""
    print("[TEST] 测试18号接口GUI日志显示")
    print("=" * 60)
    
    base_url = "http://localhost:5007"
    
    # 测试场景
    test_cases = [
        {
            "name": "查询所有医生（空ID和机构）",
            "data": {"id": "", "shopcode": ""}
        },
        {
            "name": "查询特定机构的所有医生",
            "data": {"id": "", "shopcode": "08"}
        },
        {
            "name": "查询特定医生",
            "data": {"id": "DOCTOR001", "shopcode": "08"}
        },
        {
            "name": "查询特定医生（无机构）",
            "data": {"id": "DOCTOR001", "shopcode": ""}
        }
    ]
    
    def check_gui_server():
        """检查GUI服务器是否运行"""
        try:
            response = requests.get(f"{base_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    if not check_gui_server():
        print("[ERROR] GUI服务器未运行")
        print("[INFO] 请运行: python gui_main.py")
        return False
    
    print("[INFO] GUI服务器正在运行，开始测试...")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'-' * 40}")
        print(f"[TEST {i}] {test_case['name']}")
        print(f"请求数据: {json.dumps(test_case['data'], ensure_ascii=False)}")
        
        try:
            # 发送请求
            response = requests.post(
                f"{base_url}/dx/inter/getDoctorInfo",
                json=test_case['data'],
                timeout=10
            )
            
            print(f"响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                result_code = result.get('code', -1)
                result_msg = result.get('msg', '')
                data_count = len(result.get('data', []))
                
                print(f"返回码: {result_code}")
                print(f"消息: {result_msg}")
                print(f"数据条数: {data_count}")
                
                # 分析期望的日志格式
                doctor_id = test_case['data'].get('id', '')
                hospital_code = test_case['data'].get('shopcode', '')
                
                request_info = f"医生ID: {doctor_id}" if doctor_id else "查询全部医生"
                if hospital_code:
                    request_info += f", 机构: {hospital_code}"
                
                print(f"\n期望的日志格式:")
                print(f"  请求日志: 18号接口: 收到查询医生信息请求 - {request_info}")
                
                if result_code == 0:
                    if data_count > 0:
                        print(f"  成功日志: 18号接口: 查询成功 - {request_info}, 返回医生数: {data_count}")
                    else:
                        print(f"  完成日志: 18号接口: 查询完成但未找到医生信息 - {request_info}")
                else:
                    print(f"  失败日志: 18号接口: 查询失败 - {request_info}, 错误: {result_msg}")
                    
            else:
                print(f"HTTP错误: {response.text}")
                
        except Exception as e:
            print(f"请求异常: {str(e)}")
        
        # 等待一下，方便观察日志
        time.sleep(1)
    
    print(f"\n{'-' * 40}")
    print("[INFO] 测试完成")
    print("[INFO] 请检查GUI应用程序中的日志显示，应该看到详细的请求信息包含:")
    print("  - 医生ID（或'查询全部医生'）")
    print("  - 机构编码（如果有）")
    print("  - 查询状态（成功/失败/未找到）")
    print("  - 返回的医生数量")
    
    return True

def test_error_handling():
    """测试错误处理的日志显示"""
    print(f"\n{'=' * 60}")
    print("[TEST] 测试错误处理日志")
    
    base_url = "http://localhost:5007"
    
    # 测试无效请求
    try:
        response = requests.post(
            f"{base_url}/dx/inter/getDoctorInfo",
            json=None,  # 发送空数据
            timeout=10
        )
        
        print(f"空数据请求响应: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"返回结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
    except Exception as e:
        print(f"错误处理测试异常: {str(e)}")

if __name__ == "__main__":
    print("开始测试18号接口GUI日志显示...")
    
    # 测试正常场景
    test_gui_18_interface_logs()
    
    # 测试错误处理
    test_error_handling()
    
    print(f"\n{'=' * 60}")
    print("[SUMMARY] 测试总结")
    print("已完成18号接口日志显示测试")
    print("新的日志格式应该包含:")
    print("  ✓ 医生ID或'查询全部医生'")
    print("  ✓ 机构编码（如果提供）")
    print("  ✓ 详细的查询状态")
    print("  ✓ 返回的医生数量")
    print("  ✓ 错误信息（如果失败）")