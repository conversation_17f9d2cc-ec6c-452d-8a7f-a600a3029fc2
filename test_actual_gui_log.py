#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新设置测试数据并运行实际轮询测试
"""

from interface_01_sendPeInfo import TianjianInterface01
from multi_org_config import switch_organization
from database_service import DatabaseService
from config import Config

def setup_and_test():
    """设置测试数据并运行实际测试"""
    
    print("=" * 60)
    print("重新设置测试数据并运行实际轮询测试")
    print("=" * 60)
    
    # 切换到09机构
    success, message = switch_organization('09')
    print(f"机构切换: {message}")
    
    if success:
        # 获取当前机构配置
        from multi_org_config import get_current_org_config
        org_config = get_current_org_config()
        
        # 创建数据库连接
        if org_config.get('db_host'):
            connection_string = (
                f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                f"SERVER={org_config['db_host']},{org_config.get('db_port', 1433)};"
                f"DATABASE={org_config['db_name']};"
                f"UID={org_config['db_user']};"
                f"PWD={org_config['db_password']};TrustServerCertificate=yes;"
            )
        else:
            connection_string = Config.get_interface_db_connection_string()
        
        print("\n重新设置测试数据...")
        
        local_db_service = DatabaseService(connection_string)
        if local_db_service.connect():
            try:
                # 重新设置两条测试记录
                print("设置分科未完成记录 (0220000012)")
                update_sql_1 = """
                UPDATE T_Register_Main 
                SET cCanDiagDate = NULL, AIDiagnosisStatus = 2
                WHERE cClientCode = '0220000012'
                """
                affected_rows_1 = local_db_service.execute_update(update_sql_1)
                print(f"   更新了 {affected_rows_1} 条记录")
                
                print("设置分科完成记录 (0220000017)")
                update_sql_2 = """
                UPDATE T_Register_Main 
                SET cCanDiagDate = GETDATE(), AIDiagnosisStatus = 1
                WHERE cClientCode = '0220000017'
                """
                affected_rows_2 = local_db_service.execute_update(update_sql_2)
                print(f"   更新了 {affected_rows_2} 条记录")
                
                if affected_rows_1 > 0 and affected_rows_2 > 0:
                    print("\n执行实际AI诊断轮询...")
                    print("=" * 60)
                    
                    # 创建接口实例
                    interface = TianjianInterface01()
                    
                    # 执行轮询（实际模式）
                    result = interface.poll_and_send_ai_diagnosis(limit=5, test_mode=False)
                    
                    print("\n模拟GUI日志输出:")
                    print("=" * 60)
                    
                    # 模拟GUI日志显示逻辑
                    if result.get('total', 0) > 0:
                        total = result.get('total', 0)
                        sent_01 = result.get('sent_01', 0)
                        sent_03 = result.get('sent_03', 0)
                        updated = result.get('updated', 0)
                        failed = result.get('failed', 0)
                        processed_records = result.get('processed_records', [])
                        
                        # 汇总日志
                        summary_msg = f"AI诊断轮询 | 共{total}条 | 01接口:{sent_01}成功 | 03接口:{sent_03}成功 | 状态更新:{updated}条 | 失败:{failed}条"
                        print(f"[17:35:00] [AI诊断] {summary_msg}")
                        
                        # 详细记录每条处理结果
                        for record in processed_records:
                            client_code = record.get('client_code', '')
                            name = record.get('name', '未知')
                            peno = record.get('peno', '')
                            shop_code = record.get('shop_code', '')
                            dept_status_type = record.get('dept_status_type', '')
                            
                            # 根据记录类型显示不同信息
                            if dept_status_type == 'incomplete_dept':
                                status_desc = "分科未完成"
                                interface_desc = "01接口"
                            else:
                                status_desc = "分科完成"  
                                interface_desc = "01+03接口"
                            
                            detail_msg = f"门店{shop_code} | 卡号:{peno} | {name}({client_code}) | {status_desc} | {interface_desc}传输成功"
                            print(f"[17:35:00] [AI诊断] {detail_msg}")
                    else:
                        print("[17:35:00] [AI诊断] 暂无待处理记录")
                    
                    print("=" * 60)
                    
            finally:
                local_db_service.disconnect()

if __name__ == '__main__':
    setup_and_test()