#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI诊断轮询功能测试脚本
测试01号接口的AI诊断轮询和同步日志记录功能
"""

from interface_01_sendPeInfo import TianjianInterface01
from multi_org_config import get_current_org_config
import time

def test_ai_diagnosis_polling():
    """测试AI诊断轮询功能"""
    
    print("=" * 80)
    print("AI诊断轮询功能测试")
    print("=" * 80)
    
    # 获取当前机构配置
    org_config = get_current_org_config()
    print(f"当前机构: {org_config.get('org_name', '未知')}")
    print(f"门店编码: {org_config.get('shop_code', '未知')}")
    print(f"机构编码: {org_config.get('org_code', '未知')}")
    
    # 创建01号接口实例
    interface = TianjianInterface01()
    
    print("\n[STEP 1] 测试AI诊断轮询查询...")
    
    # 测试获取待传输记录
    try:
        pending_records = interface.get_pending_ai_diagnosis_records_from_local(limit=3)
        
        if pending_records:
            print(f"[SUCCESS] 找到 {len(pending_records)} 条待传输记录")
            for i, record in enumerate(pending_records, 1):
                name = record.get('name', '未知')
                client_code = record.get('archiveNo', '')
                dept_status = record.get('dept_status_type', '')
                ai_status = record.get('AIDiagnosisStatus', '')
                print(f"  {i}. {name} ({client_code}) - {dept_status} - AI状态:{ai_status}")
        else:
            print("[INFO] 当前没有待传输的AI诊断记录")
            print("      这是正常情况，表示没有符合条件的数据")
    
    except Exception as e:
        print(f"[ERROR] 查询失败: {e}")
        return False
    
    print("\n[STEP 2] 测试AI诊断轮询处理...")
    
    # 测试完整的轮询处理流程（测试模式）
    try:
        result = interface.poll_and_send_ai_diagnosis(limit=2, test_mode=True)
        
        if result.get('success'):
            print("[SUCCESS] AI诊断轮询测试成功")
            print(f"  - 处理总数: {result.get('total', 0)}")
            print(f"  - 01接口成功: {result.get('sent_01', 0)}")
            print(f"  - 03接口成功: {result.get('sent_03', 0)}")
            print(f"  - 状态更新: {result.get('updated', 0)}")
            print(f"  - 失败数量: {result.get('failed', 0)}")
            
            # 显示处理记录详情
            processed_records = result.get('processed_records', [])
            if processed_records:
                print(f"  - 处理记录详情:")
                for i, record in enumerate(processed_records, 1):
                    print(f"    {i}. {record.get('name')} (卡号:{record.get('peno')}) - {record.get('dept_status_type')}")
        else:
            print(f"[ERROR] AI诊断轮询测试失败: {result.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"[ERROR] 轮询处理失败: {e}")
        return False
    
    print("\n[STEP 3] 测试同步日志记录...")
    
    # 测试同步日志记录器
    try:
        from sync_logger import create_sync_logger
        
        shop_code = org_config.get('shop_code', 'DEFAULT')
        org_code = org_config.get('org_code', 'DEFAULT')
        
        logger = create_sync_logger(shop_code, org_code)
        print(f"[SUCCESS] 同步日志记录器创建成功")
        print(f"  - 门店编码: {logger.shop_code}")
        print(f"  - 机构编码: {logger.org_code}")
        print(f"  - 批次ID: {logger.batch_id}")
        
        # 检查表是否存在
        table_exists = logger._ensure_table_exists()
        if table_exists:
            print("[SUCCESS] 同步日志表检查通过 - 可以记录同步日志")
            
            # 查询最近的日志记录
            recent_logs = logger.query_sync_logs(days=1, limit=3)
            print(f"[INFO] 最近1天内的同步日志: {len(recent_logs)} 条")
            
            if recent_logs:
                print("  最近的同步记录:")
                for log in recent_logs[:3]:
                    log_time = log.get('LogTime', '')
                    interface_type = log.get('InterfaceType', '')
                    client_name = log.get('ClientName', '未知')
                    sync_status = log.get('SyncStatus', '')
                    print(f"    - {log_time} | {interface_type}号接口 | {client_name} | {sync_status}")
            
        else:
            print("[WARNING] 同步日志表不存在，无法记录同步日志")
            print("          请先执行 create_center_sync_log_table.sql 创建日志表")
            
    except Exception as e:
        print(f"[ERROR] 同步日志测试失败: {e}")
    
    print("\n" + "=" * 80)
    print("[SUMMARY] AI诊断轮询功能测试完成")
    print("功能状态:")
    print("  [OK] AI诊断记录查询: 正常")
    print("  [OK] 轮询处理流程: 正常")
    print("  [OK] 01→03接口调用: 正常")
    print("  [?] 同步日志记录: 取决于表是否创建")
    print("=" * 80)
    
    return True

def show_usage_guide():
    """显示使用指南"""
    
    print("\n" + "=" * 80)
    print("AI诊断轮询功能使用指南")
    print("=" * 80)
    
    print("1. 轮询条件:")
    print("   - 分科完成记录: cCanDiagDate为当天 且 AIDiagnosisStatus=1")
    print("   - 分科未完成记录: cCanDiagDate为空 且 AIDiagnosisStatus=2")
    
    print("\n2. 处理流程:")
    print("   - 分科完成记录: 01接口 → 03接口 → 状态更新(1→2)")
    print("   - 分科未完成记录: 01接口 → 状态更新(2→1)")
    
    print("\n3. GUI启动方式:")
    print("   python gui_main.py")
    print("   - 自动轮询间隔: 5秒")
    print("   - 每次处理: 最多10条记录")
    
    print("\n4. 命令行测试:")
    print("   python interface_01_sendPeInfo.py --test-mode --limit 5")
    
    print("\n5. 同步日志:")
    print("   - 如需记录同步日志，请先创建同步日志表")
    print("   - 执行: create_center_sync_log_table.sql")
    
    print("=" * 80)

if __name__ == "__main__":
    print("开始测试AI诊断轮询功能...")
    
    if test_ai_diagnosis_polling():
        show_usage_guide()
    else:
        print("\n[ERROR] 测试过程中出现错误，请检查配置和数据库连接")