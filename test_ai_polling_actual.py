#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI诊断轮询的报文输出功能（非测试模式）
使用本部库数据直接传输，显示完整报文
"""

from interface_01_sendPeInfo import TianjianInterface01
from multi_org_config import switch_organization

def test_ai_polling_with_actual_sending():
    """测试AI诊断轮询的实际发送和报文输出"""
    
    print("=" * 60)
    print("测试AI诊断轮询的实际发送和报文输出")
    print("=" * 60)
    
    # 切换到09机构
    print("\n1. 切换到09机构")
    success, message = switch_organization('09')
    print(f"   切换结果: {message}")
    
    if success:
        # 创建接口实例
        interface = TianjianInterface01()
        
        print("\n2. 执行AI诊断轮询（实际发送模式 - 会显示完整报文）")
        print("=" * 60)
        
        # 执行轮询，使用非测试模式，现在会显示详细的报文信息并实际发送
        result = interface.poll_and_send_ai_diagnosis(limit=2, test_mode=False)
        
        print("=" * 60)
        print("\n3. 轮询结果汇总:")
        print(f"   总记录数: {result.get('total', 0)}")
        print(f"   发送成功: {result.get('sent', 0)}")
        print(f"   发送失败: {result.get('failed', 0)}")
        print(f"   状态更新: {result.get('updated', 0)}")
        print(f"   处理消息: {result.get('message', '无')}")
        
        if result.get('errors'):
            print(f"   错误详情:")
            for error in result['errors'][:3]:  # 只显示前3个错误
                print(f"     - {error}")
    else:
        print("   机构切换失败，无法测试")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    print("\n💡 说明：")
    print("   - test_mode=False 表示实际发送模式")
    print("   - 会显示完整的HTTP请求和响应报文")
    print("   - 会实际发送数据到天健云API")
    print("   - 发送成功后会更新本部库状态")

if __name__ == '__main__':
    test_ai_polling_with_actual_sending()