#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的AI诊断轮询报文显示功能
现在测试模式也会显示报文
"""

from interface_01_sendPeInfo import TianjianInterface01
from multi_org_config import switch_organization

def test_ai_polling_messages():
    """测试AI诊断轮询的报文显示（测试模式）"""
    
    print("=" * 60)
    print("测试AI诊断轮询的报文显示（测试模式）")
    print("=" * 60)
    
    # 切换到09机构
    print("\n1. 切换到09机构")
    success, message = switch_organization('09')
    print(f"   切换结果: {message}")
    
    if success:
        # 创建接口实例
        interface = TianjianInterface01()
        
        print("\n2. 执行AI诊断轮询（测试模式 - 现在会显示模拟报文）")
        print("=" * 60)
        
        # 执行轮询，现在测试模式也会显示报文了
        result = interface.poll_and_send_ai_diagnosis(limit=2, test_mode=True)
        
        print("=" * 60)
        print("\n3. 轮询结果汇总:")
        print(f"   总记录数: {result.get('total', 0)}")
        print(f"   发送成功: {result.get('sent', 0)}")
        print(f"   发送失败: {result.get('failed', 0)}")
        print(f"   状态更新: {result.get('updated', 0)}")
        print(f"   处理消息: {result.get('message', '无')}")
        
        if result.get('errors'):
            print(f"   错误详情:")
            for error in result['errors'][:3]:
                print(f"     - {error}")
                
        if result.get('total', 0) == 0:
            print("\n   没有找到符合条件的记录。")
            print("   可能原因:")
            print("   - 今天没有cCanDiagDate的记录")
            print("   - 没有AIDiagnosisStatus=1的记录")
            print("   - 记录已经被处理过")
    else:
        print("   机构切换失败，无法测试")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == '__main__':
    test_ai_polling_messages()