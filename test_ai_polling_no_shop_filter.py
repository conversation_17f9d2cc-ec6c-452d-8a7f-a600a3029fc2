#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试移除cShopCode过滤后的AI诊断轮询功能
"""

from interface_01_sendPeInfo import TianjianInterface01
from multi_org_config import switch_organization, get_current_org_config

def test_ai_polling_without_shop_filter():
    """测试移除门店编码过滤后的AI轮询功能"""
    
    print("=" * 60)
    print("测试AI诊断轮询功能（移除cShopCode过滤）")
    print("=" * 60)
    
    # 测试09机构
    print("\n1. 切换到09机构")
    success, message = switch_organization('09')
    print(f"   切换结果: {message}")
    
    if success:
        # 获取当前机构信息
        org_config = get_current_org_config()
        print(f"   当前机构: {org_config.get('org_code')} - {org_config.get('org_name')}")
        
        # 创建接口实例
        interface = TianjianInterface01()
        
        print("\n2. 直接查询本部库中的AI诊断记录")
        # 直接调用获取待处理记录的方法
        pending_records = interface.get_pending_ai_diagnosis_records(limit=10)
        
        print(f"   本部库查询结果: 找到 {len(pending_records)} 条记录")
        
        if pending_records:
            print("\n   记录详情:")
            for i, record in enumerate(pending_records[:5], 1):  # 只显示前5条
                client_code = record.get('archiveNo', '未知')
                name = record.get('name', '未知')
                exam_date = record.get('exam_date', '未知')
                print(f"     {i}. 客户编码: {client_code}, 姓名: {name}, 体检时间: {exam_date}")
        else:
            print("   当前没有待传输的AI诊断记录")
            
        print("\n3. 测试完整的轮询和发送流程")
        result = interface.poll_and_send_ai_diagnosis(limit=5, test_mode=True)
        
        print(f"   轮询测试结果:")
        print(f"     总记录数: {result.get('total', 0)}")
        print(f"     发送成功: {result.get('sent', 0)}")
        print(f"     发送失败: {result.get('errors', 0)}")
        print(f"     状态消息: {result.get('message', '无')}")
        
    else:
        print("   机构切换失败，无法继续测试")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == '__main__':
    test_ai_polling_without_shop_filter()