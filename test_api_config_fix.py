#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API配置修复是否成功
验证02号接口是否使用正确的天健云API地址
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_config_loading():
    """测试配置加载"""
    print("=" * 80)
    print("测试配置加载")
    print("=" * 80)
    
    # 测试环境变量
    print("1. 环境变量配置:")
    tianjian_base_url = os.environ.get('TIANJIAN_BASE_URL')
    print(f"   TIANJIAN_BASE_URL = {tianjian_base_url}")
    
    # 测试Config类
    print("\n2. Config类配置:")
    from config import Config
    api_config = Config.get_tianjian_api_config()
    print(f"   base_url = {api_config['base_url']}")
    print(f"   api_key = {api_config['api_key'][:10]}...")
    print(f"   mic_code = {api_config['mic_code']}")
    print(f"   misc_id = {api_config['misc_id']}")
    
    # 测试API配置管理器
    print("\n3. API配置管理器:")
    from api_config_manager import get_api_config_manager
    manager = get_api_config_manager()
    print(f"   生产环境URL = {manager.get_base_url('production')}")
    print(f"   测试环境URL = {manager.get_base_url('test')}")
    print(f"   是否测试环境 = {manager.is_test_environment()}")
    
    # 测试机构配置
    print("\n4. 机构配置:")
    from multi_org_config import get_current_org_config
    org_config = get_current_org_config()
    print(f"   tianjian_base_url = {org_config.get('tianjian_base_url', 'N/A')}")
    print(f"   tianjian_mic_code = {org_config.get('tianjian_mic_code', 'N/A')}")
    print(f"   tianjian_misc_id = {org_config.get('tianjian_misc_id', 'N/A')}")


def test_interface_02_config():
    """测试02号接口的配置"""
    print("\n" + "=" * 80)
    print("测试02号接口配置")
    print("=" * 80)
    
    from interface_02_syncApplyItem import TianjianInterface02
    from config import Config
    
    # 测试1：不传入API配置（使用默认配置）
    print("1. 不传入API配置（使用默认配置）:")
    interface1 = TianjianInterface02()
    print(f"   base_url = {interface1.api_config['base_url']}")
    print(f"   mic_code = {interface1.api_config['mic_code']}")
    
    # 测试2：传入API配置
    print("\n2. 传入API配置:")
    api_config = Config.get_tianjian_api_config()
    interface2 = TianjianInterface02(api_config)
    print(f"   base_url = {interface2.api_config['base_url']}")
    print(f"   mic_code = {interface2.api_config['mic_code']}")
    
    # 检查是否使用了正确的地址
    expected_url = "http://**************:9300"
    if interface2.api_config['base_url'] == expected_url:
        print(f"✅ 配置正确：使用天健云地址 {expected_url}")
    else:
        print(f"❌ 配置错误：使用了 {interface2.api_config['base_url']}，应该是 {expected_url}")


def test_gui_interface_call():
    """测试GUI接口调用的配置"""
    print("\n" + "=" * 80)
    print("测试GUI接口调用配置")
    print("=" * 80)
    
    # 模拟GUI程序的接口调用
    from interface_02_syncApplyItem import TianjianInterface02
    from config import Config
    
    # 获取API配置（模拟GUI程序的做法）
    api_config = Config.get_tianjian_api_config()
    print(f"GUI获取的API配置: {api_config['base_url']}")
    
    # 创建接口实例（模拟修复后的GUI调用）
    interface = TianjianInterface02(api_config)
    print(f"接口实例使用的配置: {interface.api_config['base_url']}")
    
    # 检查URL构建
    url = f"{interface.api_config['base_url']}/dx/inter/syncApplyItem"
    print(f"构建的完整URL: {url}")
    
    expected_url = "http://**************:9300/dx/inter/syncApplyItem"
    if url == expected_url:
        print(f"✅ URL构建正确：{url}")
    else:
        print(f"❌ URL构建错误：{url}，应该是 {expected_url}")


def test_actual_request():
    """测试实际HTTP请求（不发送，只检查URL）"""
    print("\n" + "=" * 80)
    print("测试实际HTTP请求URL")
    print("=" * 80)
    
    from interface_02_syncApplyItem import TianjianInterface02
    from config import Config
    
    # 使用修复后的方式创建接口
    api_config = Config.get_tianjian_api_config()
    interface = TianjianInterface02(api_config)
    
    # 模拟_send_request方法中的URL构建
    url = f"{interface.api_config['base_url']}/dx/inter/syncApplyItem"
    
    print(f"实际请求将发送到: {url}")
    
    if "127.0.0.1" in url or "localhost" in url:
        print("❌ 错误：仍然使用本地地址")
    elif "**************" in url:
        print("✅ 正确：使用天健云地址")
    else:
        print(f"⚠️  未知地址: {url}")


def main():
    """主测试函数"""
    print("API配置修复验证测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        test_config_loading()
        test_interface_02_config()
        test_gui_interface_call()
        test_actual_request()
        
        print("\n" + "=" * 80)
        print("🎉 API配置修复验证完成！")
        print("✅ 现在GUI程序应该使用正确的天健云API地址")
        print("✅ HTTP日志中应该显示 http://**************:9300")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
