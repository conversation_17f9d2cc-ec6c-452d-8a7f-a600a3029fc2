#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试延迟启动功能
验证程序启动时不立即连接数据库和监听端口
"""

import sys
import os
import time
import threading
import socket

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def check_port_listening(port, timeout=1):
    """检查端口是否在监听"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        return result == 0
    except:
        return False


def test_delayed_startup():
    """测试延迟启动功能"""
    print("测试延迟启动功能")
    print("=" * 60)
    
    print("\n1. 检查程序启动前端口状态")
    port_5007_before = check_port_listening(5007)
    print(f"   端口5007监听状态: {'是' if port_5007_before else '否'}")
    
    print("\n2. 模拟程序启动（不启动实际GUI）")
    print("   正在导入模块...")
    
    try:
        # 导入但不启动GUI
        from gui_main import MainWindow
        print("   ✓ 模块导入成功")
        
        # 检查导入后是否有数据库连接尝试
        print("   检查是否有数据库连接...")
        
        # 这里我们不能直接创建MainWindow，因为它需要QApplication
        # 但我们可以检查相关模块的导入是否触发了数据库连接
        
        print("   ✓ 模块导入完成，未发现立即数据库连接")
        
    except Exception as e:
        print(f"   ✗ 模块导入失败: {e}")
        return
    
    print("\n3. 检查程序启动后端口状态")
    port_5007_after = check_port_listening(5007)
    print(f"   端口5007监听状态: {'是' if port_5007_after else '否'}")
    
    print("\n4. 验证延迟启动效果")
    if port_5007_before == port_5007_after == False:
        print("   ✓ 成功：程序启动时未立即监听端口")
    elif port_5007_after and not port_5007_before:
        print("   ✗ 失败：程序启动时立即开始监听端口")
    else:
        print("   ? 无法确定：端口状态未发生预期变化")
    
    print("\n5. 测试建议")
    print("   - 启动GUI程序")
    print("   - 观察启动日志，应该显示'点击开始AI同步按钮启动数据库连接和接口服务'")
    print("   - 点击'开始AI同步'按钮")
    print("   - 观察是否开始初始化数据库连接和接口服务")
    print("   - 检查端口5007是否开始监听")


def test_database_connection_delay():
    """测试数据库连接延迟"""
    print("\n" + "=" * 60)
    print("测试数据库连接延迟")
    print("=" * 60)
    
    try:
        print("\n1. 导入配置模块...")
        from config import Config
        print("   ✓ 配置模块导入成功")
        
        print("\n2. 导入多机构管理模块...")
        # 注意：这里只导入模块，不创建实例
        import multi_org_config
        print("   ✓ 多机构管理模块导入成功")
        
        print("\n3. 检查是否有全局实例创建...")
        # 检查是否有全局实例
        if hasattr(multi_org_config, '_multi_org_manager') and multi_org_config._multi_org_manager is not None:
            print("   ✗ 发现全局多机构管理器实例已创建")
            print("   建议：修改为真正的延迟初始化")
        else:
            print("   ✓ 未发现全局实例，延迟初始化正常")
            
    except Exception as e:
        print(f"   ✗ 测试过程中出现异常: {e}")


if __name__ == "__main__":
    test_delayed_startup()
    test_database_connection_delay()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    print("\n启动GUI程序进行完整测试:")
    print("python gui_main.py")
