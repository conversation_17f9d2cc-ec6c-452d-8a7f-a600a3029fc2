#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试双状态AI诊断轮询功能
1. cCanDiagDate为当天且AIDiagnosisStatus=1 (分科完成状态)
2. cCanDiagDate为空且AIDiagnosisStatus=2 (分科未完成状态)
"""

from interface_01_sendPeInfo import TianjianInterface01
from multi_org_config import switch_organization
from database_service import DatabaseService
from config import Config

def create_test_data_and_test():
    """创建测试数据并测试双状态AI诊断轮询"""
    
    print("=" * 60)
    print("测试双状态AI诊断轮询功能")
    print("=" * 60)
    
    # 切换到09机构
    print("\n1. 切换到09机构")
    success, message = switch_organization('09')
    print(f"   切换结果: {message}")
    
    if success:
        # 获取当前机构配置
        from multi_org_config import get_current_org_config
        org_config = get_current_org_config()
        
        # 创建数据库连接
        if org_config.get('db_host'):
            connection_string = (
                f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                f"SERVER={org_config['db_host']},{org_config.get('db_port', 1433)};"
                f"DATABASE={org_config['db_name']};"
                f"UID={org_config['db_user']};"
                f"PWD={org_config['db_password']};TrustServerCertificate=yes;"
            )
        else:
            connection_string = Config.get_interface_db_connection_string()
        
        print("\n2. 创建测试数据")
        
        local_db_service = DatabaseService(connection_string)
        if local_db_service.connect():
            try:
                # 查询现有记录
                check_sql = """
                SELECT TOP 3
                    cClientCode, cName, cCanDiagDate, AIDiagnosisStatus
                FROM T_Register_Main 
                WHERE cClientCode IN ('0220000012', '0220000017')
                ORDER BY cClientCode
                """
                
                existing_records = local_db_service.execute_query(check_sql)
                print(f"   找到 {len(existing_records)} 条测试记录:")
                
                for record in existing_records:
                    client_code = record.get('cClientCode', '')
                    name = record.get('cName', '')
                    can_diag_date = record.get('cCanDiagDate', '')
                    ai_status = record.get('AIDiagnosisStatus', '')
                    print(f"     {client_code} | {name} | {can_diag_date} | AI状态={ai_status}")
                
                if len(existing_records) >= 2:
                    # 设置第一条记录为分科未完成状态 (cCanDiagDate=空, AIDiagnosisStatus=2)
                    first_record = existing_records[0]
                    client_code_1 = first_record.get('cClientCode', '')
                    
                    print(f"\n3. 设置 {client_code_1} 为分科未完成状态")
                    update_sql_1 = """
                    UPDATE T_Register_Main 
                    SET cCanDiagDate = NULL, AIDiagnosisStatus = 2
                    WHERE cClientCode = ?
                    """
                    affected_rows_1 = local_db_service.execute_update(update_sql_1, (client_code_1,))
                    print(f"   更新了 {affected_rows_1} 条记录: cCanDiagDate=空, AIDiagnosisStatus=2")
                    
                    # 设置第二条记录为分科完成状态 (cCanDiagDate=今天, AIDiagnosisStatus=1)
                    second_record = existing_records[1]
                    client_code_2 = second_record.get('cClientCode', '')
                    
                    print(f"\n4. 设置 {client_code_2} 为分科完成状态")
                    update_sql_2 = """
                    UPDATE T_Register_Main 
                    SET cCanDiagDate = GETDATE(), AIDiagnosisStatus = 1
                    WHERE cClientCode = ?
                    """
                    affected_rows_2 = local_db_service.execute_update(update_sql_2, (client_code_2,))
                    print(f"   更新了 {affected_rows_2} 条记录: cCanDiagDate=今天, AIDiagnosisStatus=1")
                    
                    if affected_rows_1 > 0 and affected_rows_2 > 0:
                        print("\n5. 测试双状态AI诊断轮询（测试模式）")
                        print("=" * 60)
                        
                        # 创建接口实例并测试
                        interface = TianjianInterface01()
                        result = interface.poll_and_send_ai_diagnosis(limit=5, test_mode=True)
                        
                        print("=" * 60)
                        print("\n6. 轮询结果:")
                        print(f"   总记录数: {result.get('total', 0)}")
                        print(f"   01接口成功: {result.get('sent_01', 0)}")
                        print(f"   03接口成功: {result.get('sent_03', 0)}")
                        print(f"   状态更新: {result.get('updated', 0)}")
                        print(f"   处理失败: {result.get('failed', 0)}")
                        print(f"   处理消息: {result.get('message', '无')}")
                        
                        if result.get('errors'):
                            print(f"   错误详情:")
                            for error in result['errors']:
                                print(f"     - {error}")
                    else:
                        print("   测试数据创建失败")
                else:
                    print("   测试记录不足，需要至少2条记录")
                    
            finally:
                local_db_service.disconnect()
        else:
            print("   数据库连接失败")
    else:
        print("   机构切换失败")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == '__main__':
    create_test_data_and_test()