#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI中01号接口的报文显示功能
模拟GUI调用01号接口的方式
"""

import requests
import json

def test_gui_01_interface():
    """测试GUI中01号接口的报文显示"""
    
    print("=" * 60)
    print("测试GUI中01号接口的报文显示")
    print("=" * 60)
    
    # GUI中01号接口的本地服务URL
    url = "http://localhost:5007/dx/inter/sendPeInfo"
    
    # 构建请求数据（GUI方式）- 提供一些测试参数
    request_data = {
        "days": 180,
        "limit": 3,
        "test_mode": False
    }
    
    try:
        print("\n1. 发送请求到本地服务...")
        print(f"   URL: {url}")
        print(f"   方法: POST")
        print(f"   数据: {json.dumps(request_data, ensure_ascii=False)}")
        
        # 发送请求
        response = requests.post(
            url,
            json=request_data,
            headers={'Content-Type': 'application/json'},
            timeout=120
        )
        
        print(f"\n2. 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"\n3. 响应结果:")
                print(json.dumps(result, ensure_ascii=False, indent=2))
                
                if result.get('code') == 0:
                    print(f"\n✅ 01号接口调用成功！")
                    print(f"   传输消息: {result.get('msg', '')}")
                else:
                    print(f"\n❌ 01号接口返回错误:")
                    print(f"   错误码: {result.get('code')}")
                    print(f"   错误消息: {result.get('msg', '')}")
                    
            except json.JSONDecodeError:
                print(f"\n❌ 响应解析失败:")
                print(f"   响应内容: {response.text}")
        else:
            print(f"\n❌ HTTP请求失败:")
            print(f"   状态码: {response.status_code}")
            print(f"   响应内容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print(f"\n❌ 连接失败:")
        print(f"   GUI服务可能未启动，请先运行 'python gui_main.py'")
        
    except Exception as e:
        print(f"\n❌ 请求异常:")
        print(f"   错误: {str(e)}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    print("\n💡 注意：")
    print("   - 如果修改成功，控制台应该显示完整的HTTP请求和响应报文")
    print("   - 包括请求URL、请求头、请求体、响应状态码、响应头、响应体等详细信息")
    print("   - 这些报文信息会显示在运行GUI的控制台窗口中")

if __name__ == '__main__':
    test_gui_01_interface()