#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI 16号接口的日志显示功能
"""

import json
import requests
import time

def test_gui_16_interface_logging():
    """测试GUI 16号接口的日志显示"""
    print("测试GUI 16号接口日志显示功能")
    print("=" * 60)
    
    # 测试数据
    test_data = {
        "peNo": "085041193",
        "deptId": "",
        "applyItemId": [],
        "shopcode": "08"
    }
    
    url = "http://localhost:5007/dx/inter/getImages"
    headers = {'Content-Type': 'application/json'}
    
    print(f"请求URL: {url}")
    print(f"请求数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    print("\n发送请求...")
    
    try:
        response = requests.post(url, json=test_data, headers=headers, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            # 保存响应到文件
            with open('gui_16_response.txt', 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print("\n响应已保存到 gui_16_response.txt")
            
        else:
            print(f"请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("连接失败: GUI服务可能未启动 (localhost:5007)")
    except Exception as e:
        print(f"请求异常: {str(e)}")

def test_health_check():
    """测试健康检查接口"""
    print("\n" + "=" * 60)
    print("测试健康检查接口")
    print("=" * 60)
    
    try:
        response = requests.get("http://localhost:5007/health", timeout=10)
        if response.status_code == 200:
            print("[OK] GUI服务正在运行")
            return True
        else:
            print(f"[FAIL] 健康检查失败: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("[FAIL] GUI服务未启动或无法连接")
        return False
    except Exception as e:
        print(f"[FAIL] 健康检查异常: {str(e)}")
        return False

def main():
    """主函数"""
    print("GUI 16号接口日志测试")
    print("=" * 80)
    
    # 首先检查GUI服务是否运行
    if test_health_check():
        # 等待一下确保服务稳定
        time.sleep(1)
        # 测试16号接口
        test_gui_16_interface_logging()
    else:
        print("\n请先启动GUI服务:")
        print("python gui_main.py")

if __name__ == "__main__":
    main()