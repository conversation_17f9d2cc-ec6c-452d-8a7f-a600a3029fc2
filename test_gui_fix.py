#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI修复效果
验证interface_completed不会再出现KeyError
"""

import sys
import os

# 模拟打包环境
sys.frozen = True
sys._MEIPASS = os.getcwd()

# 设置环境变量
os.environ['GUI_SUBPROCESS'] = '1'
os.environ['TIANJIAN_NO_GUI'] = '1'
os.environ['NO_GUI_MODE'] = '1'

# 导入GUI模块进行测试
from gui_main import InterfaceWorker

def test_interface_worker():
    """测试InterfaceWorker的直接调用功能"""
    print("=== 测试InterfaceWorker直接调用 ===")
    
    try:
        # 创建worker实例
        worker = InterfaceWorker("02", {
            'test_mode': True,
            'limit': 1,
            'batch_size': 1
        })
        
        print(f"[TEST] 创建InterfaceWorker实例成功: {worker.interface_num}号接口")
        
        # 测试直接调用方法
        result = worker._call_interface_directly()
        
        print(f"[TEST] 直接调用结果:")
        print(f"  - success: {result.get('success')}")
        print(f"  - interface: {result.get('interface')}")
        print(f"  - stdout: {result.get('stdout', '')[:100]}...")
        print(f"  - returncode: {result.get('returncode')}")
        
        # 检查必需的键是否都存在
        required_keys = ['success', 'interface', 'stdout', 'stderr', 'returncode', 'raw_stdout', 'output']
        missing_keys = [key for key in required_keys if key not in result]
        
        if missing_keys:
            print(f"[ERROR] 缺少必需的键: {missing_keys}")
            return False
        else:
            print(f"[SUCCESS] 所有必需的键都已包含")
            return True
            
    except Exception as e:
        print(f"[ERROR] 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=== GUI修复测试 ===\n")
    
    # 测试InterfaceWorker
    success = test_interface_worker()
    
    print(f"\n=== 测试结果 ===")
    if success:
        print("[SUCCESS] GUI修复测试通过")
        print("[SUCCESS] interface_completed不会再出现KeyError")
    else:
        print("[FAILED] GUI修复测试失败")
    
    return success

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"测试异常: {e}")
        sys.exit(1)