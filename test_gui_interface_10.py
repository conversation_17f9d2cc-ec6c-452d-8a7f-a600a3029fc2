#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI中集成的10号接口
验证10号接口在gui_main.py中的集成是否正常工作
"""

import json
import requests
import time
from datetime import datetime, timed<PERSON><PERSON>

def test_gui_interface_10():
    """测试GUI中集成的10号接口"""
    
    print("GUI集成10号接口测试")
    print("=" * 60)
    
    # 接口基础URL
    base_url = "http://localhost:5007"
    
    # 测试1: 健康检查
    print("\n1. 测试健康检查接口")
    print("-" * 40)
    
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ 健康检查成功")
            print(f"  服务状态: {health_data.get('status')}")
            print(f"  服务名称: {health_data.get('service')}")
            
            interfaces = health_data.get('interfaces', {})
            if '10' in interfaces:
                print(f"  10号接口: {interfaces['10']}")
                print(f"✅ 10号接口已在健康检查中注册")
            else:
                print(f"❌ 10号接口未在健康检查中注册")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到服务: {e}")
        print("请确保GUI程序正在运行并且天健云接口服务已启动")
        return False
    
    # 测试2: 10号接口 - 按时间范围查询
    print(f"\n2. 测试10号接口 - 按时间范围查询")
    print("-" * 40)
    
    end_time = datetime.now()
    start_time = end_time - timedelta(days=30)
    
    request_data = {
        "start": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "peNo": "",
        "hospitalCode": ""
    }
    
    print(f"请求URL: {base_url}/dx/inter/batchGetPeInfo")
    print(f"请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(
            f"{base_url}/dx/inter/batchGetPeInfo",
            json=request_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 10号接口调用成功")
            print(f"  返回码: {result.get('code')}")
            print(f"  消息: {result.get('msg')}")
            print(f"  数据数量: {len(result.get('data', []))}")
            print(f"  响应时间: {result.get('reponseTime')}")
            
            # 检查数据格式
            if result.get('data'):
                first_record = result['data'][0]
                print(f"\n数据格式检查:")
                print(f"  peUserInfo字段数: {len(first_record.get('peUserInfo', {}))}")
                print(f"  archiveInfo字段数: {len(first_record.get('archiveInfo', {}))}")
                print(f"  hospital字段数: {len(first_record.get('hospital', {}))}")
                
                # 显示关键字段
                pe_user_info = first_record.get('peUserInfo', {})
                print(f"\n关键字段值:")
                print(f"  姓名: {pe_user_info.get('name')}")
                print(f"  体检号: {pe_user_info.get('peno')}")
                print(f"  性别: {pe_user_info.get('sex', {}).get('name')}")
                print(f"  年龄: {pe_user_info.get('age')}")
                print(f"  体检状态: {pe_user_info.get('peStates', {}).get('name')}")
                print(f"  当前节点: {pe_user_info.get('currentNodeType')}")
                
                return first_record  # 返回第一条记录用于后续测试
            else:
                print(f"⚠️ 返回数据为空")
                return None
        else:
            print(f"❌ 10号接口调用失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_specific_pe_no(pe_no):
    """测试指定体检号查询"""
    print(f"\n3. 测试10号接口 - 按体检号查询")
    print("-" * 40)
    
    base_url = "http://localhost:5007"
    
    request_data = {
        "start": "",
        "end": "",
        "peNo": pe_no,
        "hospitalCode": ""
    }
    
    print(f"查询体检号: {pe_no}")
    print(f"请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(
            f"{base_url}/dx/inter/batchGetPeInfo",
            json=request_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 按体检号查询成功")
            print(f"  返回码: {result.get('code')}")
            print(f"  数据数量: {len(result.get('data', []))}")
            
            if result.get('data'):
                pe_info = result['data'][0]['peUserInfo']
                print(f"  查询到的体检号: {pe_info.get('peno')}")
                print(f"  姓名: {pe_info.get('name')}")
                print(f"  体检状态: {pe_info.get('peStates', {}).get('name')}")
            else:
                print(f"⚠️ 未找到指定体检号的数据")
        else:
            print(f"❌ 按体检号查询失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")

def test_error_handling():
    """测试错误处理"""
    print(f"\n4. 测试错误处理")
    print("-" * 40)
    
    base_url = "http://localhost:5007"
    
    # 测试空请求
    print("测试空请求:")
    try:
        response = requests.post(
            f"{base_url}/dx/inter/batchGetPeInfo",
            json=None,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        result = response.json()
        print(f"  返回码: {result.get('code')}")
        print(f"  错误消息: {result.get('msg')}")
        print(f"  是否正确处理: {'✅' if result.get('code') != 0 else '❌'}")
        
    except Exception as e:
        print(f"  异常: {e}")

def test_response_format():
    """测试响应格式"""
    print(f"\n5. 测试响应格式完整性")
    print("-" * 40)
    
    base_url = "http://localhost:5007"
    
    request_data = {
        "start": "2025-07-20 00:00:00",
        "end": "2025-07-23 23:59:59",
        "peNo": "",
        "hospitalCode": ""
    }
    
    try:
        response = requests.post(
            f"{base_url}/dx/inter/batchGetPeInfo",
            json=request_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            # 检查顶层字段
            required_fields = ['code', 'msg', 'data', 'reponseTime']
            missing_fields = [field for field in required_fields if field not in result]
            
            if missing_fields:
                print(f"❌ 缺少顶层字段: {missing_fields}")
            else:
                print(f"✅ 顶层字段完整")
            
            # 检查数据类型
            if isinstance(result.get('code'), int):
                print(f"✅ code字段类型正确")
            else:
                print(f"❌ code字段类型错误")
            
            if isinstance(result.get('data'), list):
                print(f"✅ data字段类型正确")
            else:
                print(f"❌ data字段类型错误")
            
            if isinstance(result.get('reponseTime'), int):
                print(f"✅ reponseTime字段类型正确")
            else:
                print(f"❌ reponseTime字段类型错误")
                
        else:
            print(f"❌ 响应格式测试失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 响应格式测试异常: {e}")

if __name__ == "__main__":
    print("GUI集成10号接口测试")
    print("=" * 80)
    print("目标：验证10号接口在gui_main.py中的集成是否正常")
    print("前提：请确保GUI程序正在运行并且天健云接口服务已启动")
    print("=" * 80)
    
    try:
        # 执行基础测试
        first_record = test_gui_interface_10()
        
        # 如果有数据，测试按体检号查询
        if first_record:
            pe_no = first_record['peUserInfo']['peno']
            test_specific_pe_no(pe_no)
        
        # 测试错误处理
        test_error_handling()
        
        # 测试响应格式
        test_response_format()
        
        print(f"\n" + "=" * 80)
        print("测试完成！")
        print("✅ 10号接口已成功集成到GUI中")
        print("✅ 接口路由: /dx/inter/batchGetPeInfo")
        print("✅ 支持按时间范围和体检号查询")
        print("✅ 返回格式符合天健云标准")
        print("✅ 错误处理机制完善")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {str(e)}")
        import traceback
        traceback.print_exc()
