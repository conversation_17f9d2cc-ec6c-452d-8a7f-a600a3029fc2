#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试天健云11号接口在GUI服务中的集成
"""

import requests
import json

def test_interface_11():
    """测试11号接口"""
    print("测试天健云11号接口 - GUI服务集成")
    print("=" * 50)
    
    # 测试服务器地址
    base_url = "http://127.0.0.1:5007"
    
    # 1. 测试健康检查接口
    print("\n1. 测试健康检查接口")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            health_data = response.json()
            print("  健康检查成功:")
            print(f"  服务状态: {health_data.get('status')}")
            print(f"  服务名称: {health_data.get('service')}")
            print("  支持的接口:")
            for k, v in health_data.get('interfaces', {}).items():
                print(f"    {k}: {v}")
        else:
            print(f"  健康检查失败: {response.status_code}")
    except Exception as e:
        print(f"  健康检查异常: {str(e)}")
    
    # 2. 测试11号接口 - 查询全部
    print("\n2. 测试11号接口 - 查询全部项目字典")
    test_data_1 = {
        "id": "",
        "hospitalCode": ""
    }
    
    try:
        response = requests.post(
            f"{base_url}/dx/inter/getApplyItemDict",
            headers={'Content-Type': 'application/json'},
            data=json.dumps(test_data_1)
        )
        
        print(f"  响应状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"  返回码: {result.get('code')}")
            print(f"  消息: {result.get('msg', '无')}")
            print(f"  数据数量: {len(result.get('data', []))}")
            
            if result.get('data'):
                print("  前2条数据示例:")
                for i, item in enumerate(result['data'][:2], 1):
                    print(f"    {i}. {item.get('applyItemName')} (ID: {item.get('applyItemId')})")
                    check_items = item.get('checkItemList', [])
                    print(f"       检查项目数量: {len(check_items)}")
        else:
            print(f"  请求失败: {response.text}")
            
    except Exception as e:
        print(f"  测试异常: {str(e)}")
    
    # 3. 测试11号接口 - 查询特定项目
    print("\n3. 测试11号接口 - 查询特定项目")
    test_data_2 = {
        "id": "JB0002",
        "hospitalCode": ""
    }
    
    try:
        response = requests.post(
            f"{base_url}/dx/inter/getApplyItemDict",
            headers={'Content-Type': 'application/json'},
            data=json.dumps(test_data_2)
        )
        
        print(f"  响应状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"  返回码: {result.get('code')}")
            print(f"  消息: {result.get('msg', '无')}")
            print(f"  数据数量: {len(result.get('data', []))}")
            
            if result.get('data'):
                item = result['data'][0]
                print(f"  项目名称: {item.get('applyItemName')}")
                print(f"  科室ID: {item.get('deptId')}")
                check_items = item.get('checkItemList', [])
                print(f"  检查项目数量: {len(check_items)}")
                for check_item in check_items:
                    print(f"    - {check_item.get('checkItemName')}")
        else:
            print(f"  请求失败: {response.text}")
            
    except Exception as e:
        print(f"  测试异常: {str(e)}")
    
    print(f"\n{'='*50}")
    print("测试完成！")
    print("请检查GUI日志窗口中是否显示了11号接口的调用记录")


if __name__ == '__main__':
    test_interface_11()