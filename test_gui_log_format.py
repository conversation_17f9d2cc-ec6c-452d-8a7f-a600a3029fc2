#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的GUI日志显示
"""

from interface_01_sendPeInfo import TianjianInterface01
from multi_org_config import switch_organization

def test_gui_log_format():
    """测试GUI日志格式"""
    
    print("=" * 60)
    print("测试修改后的GUI日志显示格式")
    print("=" * 60)
    
    # 切换到09机构
    success, message = switch_organization('09')
    print(f"机构切换: {message}")
    
    if success:
        # 创建接口实例
        interface = TianjianInterface01()
        
        # 执行轮询（实际模式）
        print("\n执行AI诊断轮询（实际模式）...")
        result = interface.poll_and_send_ai_diagnosis(limit=5, test_mode=False)
        
        print("\n模拟GUI日志输出:")
        print("=" * 50)
        
        # 模拟GUI日志显示逻辑
        if result.get('total', 0) > 0:
            total = result.get('total', 0)
            sent_01 = result.get('sent_01', 0)
            sent_03 = result.get('sent_03', 0)
            updated = result.get('updated', 0)
            failed = result.get('failed', 0)
            processed_records = result.get('processed_records', [])
            
            # 汇总日志
            summary_msg = f"AI诊断轮询 | 共{total}条 | 01接口:{sent_01}成功 | 03接口:{sent_03}成功 | 状态更新:{updated}条 | 失败:{failed}条"
            print(f"[17:30:15] [AI诊断] {summary_msg}")
            
            # 详细记录每条处理结果
            for record in processed_records:
                client_code = record.get('client_code', '')
                name = record.get('name', '未知')
                peno = record.get('peno', '')
                shop_code = record.get('shop_code', '')
                dept_status_type = record.get('dept_status_type', '')
                
                # 根据记录类型显示不同信息
                if dept_status_type == 'incomplete_dept':
                    status_desc = "分科未完成"
                    interface_desc = "01接口"
                else:
                    status_desc = "分科完成"  
                    interface_desc = "01+03接口"
                
                detail_msg = f"门店{shop_code} | 卡号:{peno} | {name}({client_code}) | {status_desc} | {interface_desc}传输成功"
                print(f"[17:30:15] [AI诊断] {detail_msg}")
        else:
            print("[17:30:15] [AI诊断] 暂无待处理记录")
        
        print("=" * 50)
        print("\n日志格式说明:")
        print("- 汇总信息：显示总数、各接口成功数、失败数")
        print("- 详细信息：门店编码 | 卡号 | 姓名(客户编码) | 状态 | 接口类型")
        print("- 格式清晰，便于查看传输结果")

if __name__ == '__main__':
    test_gui_log_format()