#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试GUI中01号接口的报文显示功能
"""

import requests
import json

def test_gui_01_interface():
    """测试GUI中01号接口的报文显示"""
    
    print("测试GUI中01号接口的报文显示")
    print("=" * 50)
    
    # GUI中01号接口的本地服务URL
    url = "http://localhost:5007/dx/inter/sendPeInfo"
    
    # 构建请求数据
    request_data = {
        "days": 180,
        "limit": 2,
        "test_mode": False
    }
    
    try:
        print(f"发送请求到: {url}")
        print(f"请求数据: {json.dumps(request_data)}")
        
        # 发送请求
        response = requests.post(
            url,
            json=request_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应结果: {json.dumps(result, ensure_ascii=False)}")
            
            if result.get('code') == 0:
                print("01号接口调用成功！")
                print("请查看GUI服务的控制台窗口，应该显示详细的HTTP报文信息")
            else:
                print(f"接口返回错误: {result.get('msg')}")
        else:
            print(f"HTTP错误: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {str(e)}")
    
    print("=" * 50)
    print("测试完成")

if __name__ == '__main__':
    test_gui_01_interface()