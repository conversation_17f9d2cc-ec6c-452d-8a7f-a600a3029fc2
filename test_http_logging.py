#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试HTTP报文日志功能
验证02-06号接口的HTTP报文是否正确记录到logs目录中
"""

import os
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from http_message_logger import HTTPMessageLogger, get_http_logger


def test_http_logger():
    """测试HTTP报文日志记录器"""
    print("=" * 80)
    print("测试HTTP报文日志记录器")
    print("=" * 80)
    
    # 测试02号接口的日志记录器
    logger_02 = get_http_logger("02")
    
    # 模拟HTTP请求
    test_headers = {
        "Content-Type": "application/json",
        "mic-code": "MIC1.001E",
        "misc-id": "MISC1.00001A",
        "timestamp": "20250805213023",
        "sign": "test_signature_123456",
        "x-request-id": "TEST_REQ_001"
    }
    
    test_request_data = [
        {
            "applyItemId": "JB0002",
            "applyItemName": "内科",
            "displaySequence": "1",
            "deptId": "08_000006",
            "price": 10.0
        }
    ]
    
    # 记录请求
    logger_02.log_request(
        url="http://**************:9300/dx/inter/syncApplyItem",
        method="POST",
        headers=test_headers,
        request_data=test_request_data,
        request_id="TEST_REQ_001"
    )
    
    # 模拟HTTP响应
    test_response_headers = {
        "Content-Type": "application/json",
        "Server": "nginx/1.28.0",
        "Connection": "close",
        "Date": "Tue, 05 Aug 2025 13:30:25 GMT"
    }
    
    test_response_data = {
        "code": 0,
        "msg": "成功",
        "data": None,
        "reponseTime": 1754400625306
    }
    
    # 记录响应
    logger_02.log_response(
        status_code=200,
        headers=test_response_headers,
        response_data=test_response_data,
        request_id="TEST_REQ_001"
    )
    
    # 记录错误
    logger_02.log_error(
        error_type="测试错误",
        error_message="这是一个测试错误消息",
        request_id="TEST_REQ_001"
    )
    
    print("✅ HTTP报文日志记录测试完成")
    
    # 检查日志文件是否生成
    today = datetime.now().strftime('%Y-%m-%d')
    log_file = f"logs/interface_02_http_messages_{today}.log"
    
    if os.path.exists(log_file):
        print(f"✅ 日志文件已生成: {log_file}")
        
        # 显示日志文件内容
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"\n日志文件内容预览 (前500字符):")
            print("-" * 50)
            print(content[:500])
            if len(content) > 500:
                print("...")
            print("-" * 50)
    else:
        print(f"❌ 日志文件未生成: {log_file}")


def test_multiple_interfaces():
    """测试多个接口的日志记录器"""
    print("\n" + "=" * 80)
    print("测试多个接口的HTTP报文日志记录器")
    print("=" * 80)
    
    interfaces = ["02", "03", "04", "05", "06"]
    
    for interface_num in interfaces:
        print(f"\n测试 {interface_num}号接口日志记录器...")
        
        logger = get_http_logger(interface_num)
        
        # 记录测试请求
        logger.log_request(
            url=f"http://**************:9300/dx/inter/test{interface_num}",
            method="POST",
            headers={"Content-Type": "application/json"},
            request_data={"test": f"interface_{interface_num}"},
            request_id=f"TEST_{interface_num}_001"
        )
        
        # 记录测试响应
        logger.log_response(
            status_code=200,
            headers={"Content-Type": "application/json"},
            response_data={"code": 0, "msg": "测试成功"},
            request_id=f"TEST_{interface_num}_001"
        )
        
        # 检查日志文件
        today = datetime.now().strftime('%Y-%m-%d')
        log_file = f"logs/interface_{interface_num}_http_messages_{today}.log"
        
        if os.path.exists(log_file):
            print(f"  ✅ {interface_num}号接口日志文件已生成: {log_file}")
        else:
            print(f"  ❌ {interface_num}号接口日志文件未生成: {log_file}")


def check_logs_directory():
    """检查logs目录状态"""
    print("\n" + "=" * 80)
    print("检查logs目录状态")
    print("=" * 80)
    
    logs_dir = Path("logs")
    
    if not logs_dir.exists():
        print("❌ logs目录不存在")
        return
    
    print(f"✅ logs目录存在: {logs_dir.absolute()}")
    
    # 列出所有HTTP报文日志文件
    http_log_files = list(logs_dir.glob("interface_*_http_messages_*.log"))
    
    if http_log_files:
        print(f"\n📁 找到 {len(http_log_files)} 个HTTP报文日志文件:")
        for log_file in sorted(http_log_files):
            file_size = log_file.stat().st_size
            print(f"  📄 {log_file.name} ({file_size} bytes)")
    else:
        print("\n📁 未找到HTTP报文日志文件")
    
    # 列出所有日志文件
    all_log_files = list(logs_dir.glob("*.log"))
    print(f"\n📁 logs目录中共有 {len(all_log_files)} 个日志文件")


def main():
    """主函数"""
    print("HTTP报文日志功能测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查logs目录
    check_logs_directory()
    
    # 测试单个接口
    test_http_logger()
    
    # 测试多个接口
    test_multiple_interfaces()
    
    # 再次检查logs目录
    check_logs_directory()
    
    print("\n" + "=" * 80)
    print("🎉 HTTP报文日志功能测试完成！")
    print("✅ 每个接口都有独立的日志文件")
    print("✅ 日志文件按日期命名，便于管理")
    print("✅ 包含完整的HTTP请求和响应信息")
    print("=" * 80)


if __name__ == "__main__":
    main()
