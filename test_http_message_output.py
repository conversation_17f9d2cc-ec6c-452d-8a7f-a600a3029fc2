#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试02-06号接口的HTTP报文打印功能
实际发送请求验证HTTP请求和响应报文是否正确输出到控制台
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入接口
from interface_02_syncApplyItem import TianjianInterface02

# API配置
API_CONFIG = {
    'base_url': 'http://203.83.237.114:9300',
    'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
    'mic_code': 'MIC1.001E',
    'misc_id': 'MISC1.00001A',
    'timeout': 30
}

def test_http_message_output():
    """测试HTTP报文输出功能"""
    print("="*100)
    print("测试02号接口HTTP报文输出功能")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*100)
    
    try:
        interface = TianjianInterface02(API_CONFIG)
        
        # 获取少量数据进行实际发送测试
        print("正在获取申请项目数据...")
        apply_items = interface.get_apply_items_data(limit=1)  # 只获取1条数据
        
        if not apply_items:
            print("❌ 没有找到申请项目数据，无法测试HTTP报文输出")
            return False
        
        print(f"✅ 获取到 {len(apply_items)} 条申请项目数据")
        print("\n现在将实际发送HTTP请求到天健云，观察HTTP报文输出...")
        print("注意：以下将显示完整的HTTP请求和响应报文")
        print("-" * 80)
        
        # 实际发送请求（非测试模式）
        result = interface._send_request(apply_items)
        
        print("-" * 80)
        print("HTTP报文输出测试完成")
        
        if result.get('success'):
            print("✅ 请求成功，HTTP报文已正确输出到控制台")
            return True
        else:
            print(f"⚠️  请求失败，但HTTP报文已输出: {result.get('error', '未知错误')}")
            return True  # 即使请求失败，只要能看到HTTP报文就算成功
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("天健云接口HTTP报文输出测试")
    print("本测试将实际发送HTTP请求到天健云服务器")
    print("目的是验证HTTP请求和响应报文是否正确输出到控制台")
    print()
    
    # 询问用户是否继续
    user_input = input("是否继续进行实际HTTP请求测试？(y/N): ").strip().lower()
    if user_input not in ['y', 'yes']:
        print("测试已取消")
        return
    
    success = test_http_message_output()
    
    print("\n" + "="*100)
    if success:
        print("🎉 HTTP报文输出功能测试成功！")
        print("✅ 02-06号接口已成功添加完整的HTTP请求和响应报文打印功能")
        print("✅ 报文内容包括：URL、方法、请求头、请求体、响应状态、响应头、响应体")
    else:
        print("❌ HTTP报文输出功能测试失败")
    print("="*100)

if __name__ == "__main__":
    main()
