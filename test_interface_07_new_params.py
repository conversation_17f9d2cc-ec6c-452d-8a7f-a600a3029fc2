#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试07号接口新增参数处理功能
验证mappingId、deptId、abnormalLevel、childrenCode等新字段的处理
"""

import json
import requests
import time
from datetime import datetime

def test_07_interface_with_new_params():
    """测试07号接口新增参数处理"""
    
    # 接收端URL
    url = "http://localhost:5007/dx/inter/receiveConclusion"
    
    # 测试数据 - 包含所有新增字段
    test_data = {
        "hospital": {
            "code": "09",
            "name": "测试医院"
        },
        "peNo": "5000006",
        "firstCheckFinishTime": "2025-07-23 10:30:00",
        "firstCheckFinishDoctor": {
            "code": "DOC001",
            "name": "张医生",
            "synonyms": None,
            "zero": None
        },
        "mainCheckFinishTime": "2025-07-23 11:00:00",
        "mainCheckFinishDoctor": {
            "code": "DOC002", 
            "name": "李主任",
            "synonyms": None,
            "zero": None
        },
        "currentNodeType": 4,
        "conclusionList": [
            {
                "mappingId": "MAPPING_001",
                "conclusionName": "血压偏高",
                "conclusionCode": "BP001",
                "parentCode": "CARDIO",
                "suggest": "建议低盐饮食，适量运动",
                "explain": "收缩压超过正常范围，需要注意心血管健康",
                "checkResult": "收缩压150mmHg，舒张压95mmHg",
                "level": 1,
                "displaySequnce": 1,
                "childrenCode": ["BP001_1", "BP001_2"],
                "deptId": "DEPT01",
                "abnormalLevel": 1
            },
            {
                "mappingId": "MAPPING_002", 
                "conclusionName": "血脂轻度异常",
                "conclusionCode": "LIPID001",
                "parentCode": "BIOCHE",
                "suggest": "建议控制饮食，定期复查",
                "explain": "总胆固醇略高于正常值",
                "checkResult": "总胆固醇6.2mmol/L",
                "level": 2,
                "displaySequnce": 2,
                "childrenCode": None,
                "deptId": "DEPT02",
                "abnormalLevel": 2
            },
            {
                "mappingId": "MAPPING_003",
                "conclusionName": "轻微脂肪肝",
                "conclusionCode": "LIVER001", 
                "parentCode": "ULTRA",
                "suggest": "建议减重，避免饮酒",
                "explain": "肝脏回声增强，符合轻度脂肪肝表现",
                "checkResult": "肝脏大小正常，回声增强",
                "level": 3,
                "displaySequnce": 3,
                "childrenCode": ["LIVER001_A"],
                "deptId": "DEPT03",
                "abnormalLevel": 3
            },
            {
                "mappingId": "MAPPING_004",
                "conclusionName": "其他异常",
                "conclusionCode": "OTHER001",
                "parentCode": "MISC",
                "suggest": "建议进一步检查",
                "explain": "需要专科医生进一步评估",
                "checkResult": "检查结果待进一步分析",
                "level": 3,
                "displaySequnce": 4,
                "childrenCode": [],
                "deptId": "DEPT99",
                "abnormalLevel": 9
            }
        ]
    }
    
    print("=" * 60)
    print("07号接口新增参数测试")
    print("=" * 60)
    
    # 发送测试请求
    try:
        print(f"发送测试数据到: {url}")
        print(f"测试体检号: {test_data['peNo']}")
        print(f"结论数量: {len(test_data['conclusionList'])}")
        print()
        
        # 显示新增字段信息
        print("新增字段测试内容:")
        for i, conclusion in enumerate(test_data['conclusionList'], 1):
            print(f"  结论{i}: {conclusion['conclusionName']}")
            print(f"    mappingId: {conclusion['mappingId']}")
            print(f"    deptId: {conclusion['deptId']}")
            print(f"    abnormalLevel: {conclusion['abnormalLevel']}")
            print(f"    childrenCode: {conclusion['childrenCode']}")
            print(f"    displaySequnce: {conclusion['displaySequnce']}")
            print()
        
        # 发送请求
        headers = {
            'Content-Type': 'application/json',
            'mic-code': 'TEST001',
            'misc-id': 'TEST_SYSTEM'
        }
        
        response = requests.post(
            url, 
            json=test_data, 
            headers=headers,
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 测试成功!")
                print(f"更新记录数: {result.get('data', {}).get('updated_records', 0)}")
                print(f"结论数量: {result.get('data', {}).get('conclusion_count', 0)}")
            else:
                print("❌ 测试失败!")
                print(f"错误信息: {result.get('error')}")
        else:
            print("❌ 请求失败!")
            print(f"HTTP状态码: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败! 请确保07号接收端服务已启动")
        print("启动命令: python interface_07_receiveConclusion.py")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def test_backward_compatibility():
    """测试向后兼容性 - 使用旧版本数据格式"""
    
    url = "http://localhost:5007/dx/inter/receiveConclusion"
    
    # 旧版本数据格式（不包含新增字段）
    old_format_data = {
        "hospital": {
            "code": "09",
            "name": "测试医院"
        },
        "peNo": "5000003",
        "firstCheckFinishTime": "2025-07-23 10:30:00",
        "firstCheckFinishDoctor": {
            "code": "DOC001",
            "name": "张医生"
        },
        "mainCheckFinishTime": "2025-07-23 11:00:00",
        "mainCheckFinishDoctor": {
            "code": "DOC002",
            "name": "李主任"
        },
        "currentNodeType": 4,
        "conclusionList": [
            {
                "conclusionName": "血压正常",
                "conclusionCode": "BP002",
                "parentCode": "CARDIO",
                "suggest": "继续保持健康生活方式",
                "explain": "血压在正常范围内",
                "checkResult": "收缩压120mmHg，舒张压80mmHg",
                "level": 3,
                "displaySequnce": 1
            }
        ]
    }
    
    print("=" * 60)
    print("向后兼容性测试（旧版本数据格式）")
    print("=" * 60)
    
    try:
        headers = {
            'Content-Type': 'application/json',
            'mic-code': 'TEST001',
            'misc-id': 'TEST_SYSTEM'
        }
        
        response = requests.post(
            url,
            json=old_format_data,
            headers=headers,
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 向后兼容性测试成功!")
            else:
                print("❌ 向后兼容性测试失败!")
                print(f"错误信息: {result.get('error')}")
        else:
            print("❌ 请求失败!")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    print("开始07号接口新增参数测试...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试新增参数
    test_07_interface_with_new_params()
    
    print()
    time.sleep(2)
    
    # 测试向后兼容性
    test_backward_compatibility()
    
    print()
    print("测试完成!")
