#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修正后的08号接口 - 使用正确的数据库字段映射
"""

import requests
import json

# 测试数据 - 08门店
test_data_08 = {
    "id": "",
    "type": "",
    "hospitalCode": "08"
}

# 测试数据 - 09门店
test_data_09 = {
    "id": "",
    "type": "",  
    "hospitalCode": "09"
}

def test_interface_08():
    """测试08号接口"""
    url = "http://localhost:5007/dx/inter/queryDict"
    
    print("测试修正后的08号接口 - 查询字典信息")
    print("=" * 60)
    
    # 测试08门店
    print(f"\n1. 测试08门店数据查询")
    print(f"请求URL: {url}")
    print(f"请求数据: {json.dumps(test_data_08, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data_08, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("08门店响应结果:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
            if result.get('code') == 0:
                data = result.get('data', [])
                print(f"✅ 08门店成功返回 {len(data)} 条字典数据")
            else:
                print(f"❌ 08门店请求失败: {result.get('msg')}")
        else:
            print(f"❌ 08门店HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 08门店请求异常: {str(e)}")
    
    # 测试09门店对比
    print(f"\n2. 测试09门店数据查询（对比）")
    print(f"请求数据: {json.dumps(test_data_09, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data_09, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('code') == 0:
                data = result.get('data', [])
                print(f"✅ 09门店成功返回 {len(data)} 条字典数据")
                
                # 对比数据量
                print(f"\n📊 数据量对比:")
                print(f"  08门店: 预期较多数据")
                print(f"  09门店: {len(data)} 条")
            else:
                print(f"❌ 09门店请求失败: {result.get('msg')}")
        else:
            print(f"❌ 09门店HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 09门店请求异常: {str(e)}")
    
    print("\n测试完成 - 请检查08门店是否使用了正确的字段映射")

if __name__ == "__main__":
    test_interface_08()