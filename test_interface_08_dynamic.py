#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试08号接口的动态数据库连接功能
"""

from interface_08_getDict import TianjianInterface08

# API配置
api_config = {
    'base_url': 'http://203.83.237.114:9300',
    'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
    'mic_code': 'MIC1.001E',
    'misc_id': 'MISC1.00001A',
    'timeout': 30
}

# 创建接口实例
interface = TianjianInterface08(api_config)

# 测试09门店的字典数据
print('=== 测试09门店的字典数据 ===')
result_09 = interface.get_dict({
    'id': '',
    'type': '',
    'hospitalCode': '09'
})

if 'totalCount' in result_09:
    print('✅ 成功连接09门店！')
    print(f'   检查项目: {result_09["totalCount"]["items"]} 条')
    print(f'   科室信息: {result_09["totalCount"]["departments"]} 条') 
    print(f'   操作员: {result_09["totalCount"]["operators"]} 条')
else:
    print(f'❌ 09门店查询失败: {result_09.get("error", "未知错误")}')

print()

# 测试08门店的字典数据对比
print('=== 测试08门店的字典数据 ===')
result_08 = interface.get_dict({
    'id': '',
    'type': '',  
    'hospitalCode': '08'
})

if 'totalCount' in result_08:
    print('✅ 成功连接08门店！')
    print(f'   检查项目: {result_08["totalCount"]["items"]} 条')
    print(f'   科室信息: {result_08["totalCount"]["departments"]} 条')
    print(f'   操作员: {result_08["totalCount"]["operators"]} 条')
else:
    print(f'❌ 08门店查询失败: {result_08.get("error", "未知错误")}')

print()
print('=== 对比结果 ===')
if 'totalCount' in result_09 and 'totalCount' in result_08:
    items_09 = result_09['totalCount']['items']
    items_08 = result_08['totalCount']['items']
    depts_09 = result_09['totalCount']['departments']
    depts_08 = result_08['totalCount']['departments']
    
    if items_09 != items_08 or depts_09 != depts_08:
        print('✅ 成功！不同门店返回了不同的数据量，动态数据库连接工作正常')
        print(f'   数据差异: 09门店({items_09}项/{depts_09}科室) vs 08门店({items_08}项/{depts_08}科室)')
    else:
        print('⚠️  警告：两个门店返回相同的数据量，可能仍然存在问题')