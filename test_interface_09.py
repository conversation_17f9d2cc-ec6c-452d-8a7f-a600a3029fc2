#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试09号接口的动态数据库连接功能
"""

import requests
import json

# 测试数据
test_data = {
    "peNoList": ["085041193", "9000001"],
    "deptId": "",
    "hospitalCode": "09"
}

# 发送请求到GUI服务
url = "http://localhost:5007/dx/inter/retransmitDeptInfo"

print("测试09号接口 - 体检科室结果重传")
print("=" * 50)
print(f"请求URL: {url}")
print(f"请求数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")

try:
    response = requests.post(url, json=test_data, timeout=30)
    
    print(f"\n响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("响应结果:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        if result.get('code') == 0:
            data = result.get('data', [])
            print(f"\n✅ 成功返回 {len(data)} 条科室结果数据")
            
            # 显示第一条记录的结构
            if data:
                first_record = data[0]
                print(f"\n第一条记录结构:")
                print(f"  体检号: {first_record.get('peNo')}")
                print(f"  科室: {first_record.get('dept', {}).get('name')} ({first_record.get('dept', {}).get('code')})")
                print(f"  医院: {first_record.get('hospital', {}).get('name')} ({first_record.get('hospital', {}).get('code')})")
                print(f"  检查时间: {first_record.get('checkTime')}")
                print(f"  项目明细数量: {len(first_record.get('itemDesc', []))}")
        else:
            print(f"❌ 请求失败: {result.get('msg')}")
    else:
        print(f"❌ HTTP错误: {response.status_code}")
        print(f"响应内容: {response.text}")
        
except requests.exceptions.ConnectionError:
    print("❌ 连接失败: GUI服务可能未启动")
    print("请确保GUI服务在端口5007上运行")
except Exception as e:
    print(f"❌ 请求异常: {str(e)}")

print("\n测试完成")