#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试10号接口的独立实现
只测试核心功能，不涉及复杂的交互
"""

import sys
from datetime import datetime, timedelta
from interface_10_batchGetPeInfo import TianjianInterface10

def test_simple_10_interface():
    """简单测试10号接口"""
    print("10号接口简单测试")
    print("=" * 50)
    
    try:
        # 创建接口实例
        interface = TianjianInterface10()
        
        # 测试1: 标准格式API测试
        print("1. 测试标准格式API")
        print("-" * 30)
        
        end_time = datetime.now()
        start_time = end_time - timedelta(days=7)
        
        request_data = {
            "start": start_time.strftime('%Y-%m-%d %H:%M:%S'),
            "end": end_time.strftime('%Y-%m-%d %H:%M:%S'),
            "peNo": "",
            "hospitalCode": ""
        }
        
        print(f"请求参数: {request_data}")
        
        result = interface.batch_get_pe_info_standard(request_data)
        print(f"返回码: {result.get('code')}")
        print(f"消息: {result.get('msg')}")
        print(f"数据数量: {len(result.get('data', []))}")
        
        if result.get('code') == 0 and result.get('data'):
            print("标准格式API测试成功")
            first_record = result['data'][0]
            pe_user_info = first_record.get('peUserInfo', {})
            print(f"  示例数据 - 姓名: {pe_user_info.get('name')}")
            print(f"  示例数据 - 体检号: {pe_user_info.get('peno')}")
            print(f"  示例数据 - 性别: {pe_user_info.get('sex', {}).get('name')}")
            return True
        else:
            print(f"标准格式API测试失败: {result.get('msg')}")
            return False
            
    except Exception as e:
        print(f"测试过程异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple_10_interface()
    if success:
        print("\n10号接口基本功能正常")
        sys.exit(0)
    else:
        print("\n10号接口存在问题")
        sys.exit(1)