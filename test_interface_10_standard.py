#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试10号接口标准报文格式
验证按照天健云提供的标准格式实现
"""

import json
from datetime import datetime, timedelta
from interface_10_batchGetPeInfo import TianjianInterface10

def test_10_interface_standard_format():
    """测试10号接口标准报文格式"""
    
    print("天健云10号接口标准报文格式测试")
    print("=" * 60)
    
    # 创建接口实例
    interface = TianjianInterface10()
    
    # 测试1: 按时间范围查询
    print("\n1. 测试按时间范围查询")
    print("-" * 40)
    
    end_time = datetime.now()
    start_time = end_time - timedelta(days=30)
    
    request_data = {
        "start": start_time.strftime('%Y-%m-%d %H:%M:%S'),
        "end": end_time.strftime('%Y-%m-%d %H:%M:%S'),
        "peNo": "",
        "hospitalCode": ""
    }
    
    print(f"请求参数: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
    
    result = interface.batch_get_pe_info_standard(request_data)
    
    print(f"返回结果:")
    print(f"  code: {result.get('code')}")
    print(f"  msg: {result.get('msg')}")
    print(f"  data数量: {len(result.get('data', []))}")
    print(f"  reponseTime: {result.get('reponseTime')}")
    
    # 显示第一条数据的结构
    if result.get('data'):
        first_record = result['data'][0]
        print(f"\n第一条数据结构:")
        print(f"  peUserInfo字段数: {len(first_record.get('peUserInfo', {}))}")
        print(f"  archiveInfo字段数: {len(first_record.get('archiveInfo', {}))}")
        print(f"  hospital字段数: {len(first_record.get('hospital', {}))}")
        
        # 显示关键字段
        pe_user_info = first_record.get('peUserInfo', {})
        print(f"\n关键字段值:")
        print(f"  姓名: {pe_user_info.get('name')}")
        print(f"  体检号: {pe_user_info.get('peno')}")
        print(f"  性别: {pe_user_info.get('sex', {}).get('name')}")
        print(f"  年龄: {pe_user_info.get('age')}")
        print(f"  体检状态: {pe_user_info.get('peStates', {}).get('name')}")
        print(f"  当前节点: {pe_user_info.get('currentNodeType')}")
        print(f"  申请项目数: {len(pe_user_info.get('applyItemList', []))}")
    
    # 测试2: 按体检号查询
    print(f"\n2. 测试按体检号查询")
    print("-" * 40)
    
    if result.get('data'):
        # 使用第一条记录的体检号进行查询
        test_pe_no = result['data'][0]['peUserInfo']['peno']
        
        request_data = {
            "start": "",
            "end": "",
            "peNo": test_pe_no,
            "hospitalCode": ""
        }
        
        print(f"请求参数: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
        
        result2 = interface.batch_get_pe_info_standard(request_data)
        
        print(f"返回结果:")
        print(f"  code: {result2.get('code')}")
        print(f"  msg: {result2.get('msg')}")
        print(f"  data数量: {len(result2.get('data', []))}")
        
        if result2.get('data'):
            pe_info = result2['data'][0]['peUserInfo']
            print(f"  查询到的体检号: {pe_info.get('peno')}")
            print(f"  姓名: {pe_info.get('name')}")
    
    # 测试3: 验证返回格式完整性
    print(f"\n3. 验证返回格式完整性")
    print("-" * 40)
    
    if result.get('data'):
        first_record = result['data'][0]
        
        # 检查必需字段
        required_fields = {
            'peUserInfo': [
                'archiveNo', 'name', 'icCode', 'sex', 'birthday', 'peno', 'peDate',
                'phone', 'ms', 'pregnantState', 'vipLevel', 'medicalType', 'isGroup',
                'company', 'workDept', 'teamNo', 'professional', 'workAge', 'peStates',
                'deptCount', 'age', 'deptFinishTime', 'firstCheckFinishTime',
                'firstCheckFinishDoctor', 'mainCheckFinishTime', 'mainCheckFinishDoctor',
                'forbidGoCheck', 'reportPrint', 'reportGot', 'replacementInspectionMark',
                'applyItemList', 'currentNodeType', 'pePackage'
            ],
            'archiveInfo': ['name', 'icCode', 'sex', 'birthday', 'peNoList'],
            'hospital': ['code', 'name']
        }
        
        missing_fields = []
        
        for section, fields in required_fields.items():
            section_data = first_record.get(section, {})
            for field in fields:
                if field not in section_data:
                    missing_fields.append(f"{section}.{field}")
        
        if missing_fields:
            print(f"❌ 缺少字段: {missing_fields}")
        else:
            print(f"✅ 所有必需字段都存在")
        
        # 检查嵌套对象字段
        nested_objects = {
            'peUserInfo.sex': ['code', 'name'],
            'peUserInfo.ms': ['code', 'name'],
            'peUserInfo.pregnantState': ['code', 'name'],
            'peUserInfo.vipLevel': ['code', 'name'],
            'peUserInfo.medicalType': ['code', 'name'],
            'peUserInfo.peStates': ['code', 'name'],
            'peUserInfo.firstCheckFinishDoctor': ['code', 'name'],
            'peUserInfo.mainCheckFinishDoctor': ['code', 'name'],
            'peUserInfo.pePackage': ['code', 'name'],
            'archiveInfo.sex': ['code', 'name'],
            'hospital': ['code', 'name']
        }
        
        nested_missing = []
        for obj_path, fields in nested_objects.items():
            path_parts = obj_path.split('.')
            obj_data = first_record
            for part in path_parts:
                obj_data = obj_data.get(part, {})
            
            for field in fields:
                if field not in obj_data:
                    nested_missing.append(f"{obj_path}.{field}")
        
        if nested_missing:
            print(f"❌ 缺少嵌套字段: {nested_missing}")
        else:
            print(f"✅ 所有嵌套对象字段都存在")

def test_error_handling():
    """测试错误处理"""
    
    print(f"\n4. 测试错误处理")
    print("-" * 40)
    
    interface = TianjianInterface10()
    
    # 测试无效时间格式
    request_data = {
        "start": "invalid-date",
        "end": "2023-11-07 03:59:58",
        "peNo": "",
        "hospitalCode": ""
    }
    
    result = interface.batch_get_pe_info_standard(request_data)
    
    print(f"无效时间格式测试:")
    print(f"  code: {result.get('code')}")
    print(f"  msg: {result.get('msg')}")
    print(f"  是否正确处理错误: {'✅' if result.get('code') != 0 else '❌'}")

def validate_response_format(response_data):
    """验证响应格式是否符合标准"""
    
    print(f"\n5. 响应格式验证")
    print("-" * 40)
    
    # 检查顶层字段
    required_top_fields = ['code', 'msg', 'data', 'reponseTime']
    missing_top = [field for field in required_top_fields if field not in response_data]
    
    if missing_top:
        print(f"❌ 缺少顶层字段: {missing_top}")
        return False
    else:
        print(f"✅ 顶层字段完整")
    
    # 检查数据类型
    if not isinstance(response_data['code'], int):
        print(f"❌ code字段应为整数")
        return False
    
    if not isinstance(response_data['msg'], str):
        print(f"❌ msg字段应为字符串")
        return False
    
    if not isinstance(response_data['data'], list):
        print(f"❌ data字段应为数组")
        return False
    
    if not isinstance(response_data['reponseTime'], int):
        print(f"❌ reponseTime字段应为整数")
        return False
    
    print(f"✅ 数据类型正确")
    return True

if __name__ == "__main__":
    print("10号接口标准报文格式测试")
    print("=" * 80)
    print("目标：验证接口返回格式完全符合天健云标准")
    print("=" * 80)
    
    try:
        # 执行标准格式测试
        test_10_interface_standard_format()
        
        # 执行错误处理测试
        test_error_handling()
        
        print(f"\n" + "=" * 80)
        print("测试完成！")
        print("✅ 10号接口已按照天健云标准报文格式实现")
        print("✅ 支持按时间范围和体检号查询")
        print("✅ 返回格式完全符合标准规范")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {str(e)}")
        import traceback
        traceback.print_exc()
