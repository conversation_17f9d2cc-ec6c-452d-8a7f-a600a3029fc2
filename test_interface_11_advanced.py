#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云11号接口高级测试工具
支持多种测试场景和参数验证
"""

import json
from interface_11_getApplyItemDict_standard import TianjianInterface11


def test_parameter_validation():
    """测试参数验证功能"""
    print("=" * 60)
    print("参数验证测试")
    print("=" * 60)
    
    interface = TianjianInterface11()
    
    # 测试用例
    test_cases = [
        {
            "name": "正常请求 - 查询全部",
            "data": {"id": "", "hospitalCode": ""},
            "expected_code": 0
        },
        {
            "name": "正常请求 - 查询特定ID",
            "data": {"id": "JB0002", "hospitalCode": ""},
            "expected_code": 0
        },
        {
            "name": "缺少id参数",
            "data": {"hospitalCode": ""},
            "expected_code": -1
        },
        {
            "name": "缺少hospitalCode参数",
            "data": {"id": ""},
            "expected_code": -1
        },
        {
            "name": "id参数类型错误",
            "data": {"id": 123, "hospitalCode": ""},
            "expected_code": -1
        },
        {
            "name": "hospitalCode参数类型错误",
            "data": {"id": "", "hospitalCode": 456},
            "expected_code": -1
        },
        {
            "name": "非字典格式请求",
            "data": "invalid",
            "expected_code": -1
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        try:
            result = interface.get_apply_item_dict_standard(test_case['data'])
            actual_code = result.get('code', -999)
            
            if actual_code == test_case['expected_code']:
                print(f"   [通过] 测试通过 (返回码: {actual_code})")
            else:
                print(f"   [失败] 测试失败 (期望: {test_case['expected_code']}, 实际: {actual_code})")
            
            if actual_code != 0:
                print(f"   错误信息: {result.get('msg', '无')}")
                
        except Exception as e:
            print(f"   [异常] 测试异常: {str(e)}")


def test_data_query():
    """测试数据查询功能"""
    print("\n" + "=" * 60)
    print("数据查询测试")
    print("=" * 60)
    
    interface = TianjianInterface11()
    
    # 测试1: 查询前5条申请项目
    print("\n1. 查询前5条申请项目")
    request_data = {"id": "", "hospitalCode": ""}
    result = interface.get_apply_item_dict_standard(request_data)
    
    if result.get('code') == 0:
        data_list = result.get('data', [])
        print(f"   查询成功，返回 {len(data_list)} 条记录")
        
        for i, item in enumerate(data_list[:3], 1):
            print(f"   {i}. {item.get('applyItemName')} (ID: {item.get('applyItemId')})")
            check_items = item.get('checkItemList', [])
            print(f"      检查项目数量: {len(check_items)}")
            
            if check_items:
                print(f"      首个检查项目: {check_items[0].get('checkItemName')}")
    else:
        print(f"   查询失败: {result.get('msg')}")
    
    # 测试2: 查询特定申请项目
    if result.get('code') == 0 and result.get('data'):
        first_item_id = result['data'][0]['applyItemId']
        print(f"\n2. 查询特定申请项目: {first_item_id}")
        
        specific_request = {"id": first_item_id, "hospitalCode": ""}
        specific_result = interface.get_apply_item_dict_standard(specific_request)
        
        if specific_result.get('code') == 0:
            specific_data = specific_result.get('data', [])
            if specific_data:
                item = specific_data[0]
                print(f"   项目名称: {item.get('applyItemName')}")
                print(f"   科室ID: {item.get('deptId')}")
                print(f"   检查项目数量: {len(item.get('checkItemList', []))}")
                
                # 显示检查项目详情
                for check_item in item.get('checkItemList', []):
                    print(f"     - {check_item.get('checkItemName')} (ID: {check_item.get('checkItemId')})")
        else:
            print(f"   查询失败: {specific_result.get('msg')}")


def test_response_format():
    """测试响应格式是否符合天健云标准"""
    print("\n" + "=" * 60)
    print("响应格式验证")
    print("=" * 60)
    
    interface = TianjianInterface11()
    request_data = {"id": "", "hospitalCode": ""}
    result = interface.get_apply_item_dict_standard(request_data)
    
    # 验证响应结构
    print("\n1. 验证响应结构")
    required_fields = ['code', 'data', 'msg']
    for field in required_fields:
        if field in result:
            print(f"   [OK] 包含字段: {field}")
        else:
            print(f"   [ERROR] 缺少字段: {field}")
    
    # 验证数据项结构
    if result.get('code') == 0 and result.get('data'):
        print("\n2. 验证数据项结构")
        data_item = result['data'][0]
        
        required_item_fields = ['applyItemId', 'applyItemName', 'displaySequence', 'deptId', 'checkItemList']
        for field in required_item_fields:
            if field in data_item:
                print(f"   [OK] 数据项包含字段: {field}")
            else:
                print(f"   [ERROR] 数据项缺少字段: {field}")
        
        # 验证检查项目结构
        check_items = data_item.get('checkItemList', [])
        if check_items:
            print("\n3. 验证检查项目结构")
            check_item = check_items[0]
            
            required_check_fields = ['checkItemId', 'checkItemName', 'displaySequence']
            for field in required_check_fields:
                if field in check_item:
                    print(f"   [OK] 检查项目包含字段: {field}")
                else:
                    print(f"   [ERROR] 检查项目缺少字段: {field}")
    
    # 输出JSON格式示例
    print("\n4. 响应JSON示例（前1条记录）:")
    if result.get('data'):
        sample_data = {
            "code": result.get('code'),
            "data": result.get('data')[:1],  # 只显示第一条
            "msg": result.get('msg')
        }
        print(json.dumps(sample_data, ensure_ascii=False, indent=2))


def main():
    """主测试函数"""
    print("天健云11号接口高级测试工具")
    print("测试项目字典信息接口的各种功能")
    
    try:
        # 参数验证测试
        test_parameter_validation()
        
        # 数据查询测试
        test_data_query()
        
        # 响应格式测试
        test_response_format()
        
        print("\n" + "=" * 60)
        print("所有测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n测试过程中出现异常: {str(e)}")


if __name__ == '__main__':
    main()