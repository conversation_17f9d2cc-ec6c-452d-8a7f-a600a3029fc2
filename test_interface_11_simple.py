#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云11号接口简单测试脚本
演示查询项目字典信息接口的基本功能
"""

import json
from interface_11_getApplyItemDict_standard import TianjianInterface11


def main():
    """简单测试天健云11号接口"""
    print("天健云11号接口简单测试")
    print("=" * 50)
    
    # 创建接口实例
    try:
        interface = TianjianInterface11()
        print("[成功] 接口初始化成功")
    except Exception as e:
        print(f"[失败] 接口初始化失败: {str(e)}")
        return
    
    # 测试1: 查询所有项目字典（前10条）
    print("\n1. 查询所有项目字典（前10条）")
    request1 = {
        "id": "",
        "hospitalCode": ""
    }
    
    result1 = interface.get_apply_item_dict_standard(request1)
    if result1.get('code') == 0:
        print(f"[成功] 查询成功，返回 {len(result1.get('data', []))} 条记录")
        
        # 显示前3条记录摘要
        for i, item in enumerate(result1.get('data', [])[:3], 1):
            print(f"  {i}. {item.get('applyItemName')} (ID: {item.get('applyItemId')})")
    else:
        print(f"[失败] 查询失败: {result1.get('msg')}")
    
    # 测试2: 查询特定项目
    if result1.get('code') == 0 and result1.get('data'):
        first_id = result1['data'][0]['applyItemId']
        print(f"\n2. 查询特定项目 (ID: {first_id})")
        
        request2 = {
            "id": first_id,
            "hospitalCode": ""
        }
        
        result2 = interface.get_apply_item_dict_standard(request2)
        if result2.get('code') == 0:
            if result2.get('data'):
                item = result2['data'][0]
                print(f"[成功] 查询成功")
                print(f"  项目名称: {item.get('applyItemName')}")
                print(f"  科室ID: {item.get('deptId')}")
                print(f"  检查项目数量: {len(item.get('checkItemList', []))}")
                
                # 显示检查项目
                for check_item in item.get('checkItemList', []):
                    print(f"    - {check_item.get('checkItemName')}")
            else:
                print("[成功] 查询成功，但无数据返回")
        else:
            print(f"[失败] 查询失败: {result2.get('msg')}")
    
    # 测试3: 参数验证测试
    print("\n3. 参数验证测试")
    invalid_request = {
        "id": 123,  # 错误的类型
        "hospitalCode": ""
    }
    
    result3 = interface.get_apply_item_dict_standard(invalid_request)
    if result3.get('code') == -1:
        print("[成功] 参数验证正常工作")
        print(f"  错误信息: {result3.get('msg')}")
    else:
        print("[失败] 参数验证未正常工作")
    
    print(f"\n{'='*50}")
    print("测试完成！")
    
    # 输出一个完整的JSON响应示例
    if result1.get('code') == 0 and result1.get('data'):
        print("\n完整响应示例（第1条记录）:")
        sample_response = {
            "code": 0,
            "data": [result1['data'][0]],
            "msg": ""
        }
        print(json.dumps(sample_response, ensure_ascii=False, indent=2))


if __name__ == '__main__':
    main()