#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试12号接口 - 主检锁定与解锁接口
"""

import json
import requests
from datetime import datetime

def test_interface_12():
    """测试12号接口完整功能"""
    print("=" * 60)
    print("测试天健云12号接口 - 主检锁定与解锁")
    print("=" * 60)
    
    base_url = "http://localhost:5007"
    endpoint = "/dx/inter/lockPeInfo"
    url = f"{base_url}{endpoint}"
    
    print(f"测试URL: {url}")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试用例1: 锁定主检任务
    print("\n🔒 测试用例1: 锁定主检任务")
    lock_data = {
        "operator": "ADMIN001",
        "peInfoList": [
            {
                "accountId": "DOCTOR001",
                "currentNodeType": 3,  # 主检
                "force": False,
                "operationType": 1,  # 1=锁定
                "peNo": "PE202501010001"
            },
            {
                "accountId": "DOCTOR002", 
                "currentNodeType": 3,  # 主检
                "force": False,
                "operationType": 1,  # 1=锁定
                "peNo": "PE202501010002"
            }
        ]
    }
    
    print("请求数据:")
    print(json.dumps(lock_data, ensure_ascii=False, indent=2))
    
    try:
        response = requests.post(url, json=lock_data, timeout=30)
        print(f"\n响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("响应数据:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
            if result.get('code') == 0:
                print("✅ 锁定操作成功")
            else:
                print(f"❌ 锁定操作失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    # 测试用例2: 解锁主检任务
    print("\n🔓 测试用例2: 解锁主检任务")
    unlock_data = {
        "operator": "ADMIN001",
        "peInfoList": [
            {
                "accountId": "DOCTOR001",
                "currentNodeType": 3,  # 主检
                "force": True,  # 强制解锁
                "operationType": 2,  # 2=解锁
                "peNo": "PE202501010001"
            }
        ]
    }
    
    print("请求数据:")
    print(json.dumps(unlock_data, ensure_ascii=False, indent=2))
    
    try:
        response = requests.post(url, json=unlock_data, timeout=30)
        print(f"\n响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("响应数据:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
            if result.get('code') == 0:
                print("✅ 解锁操作成功")
            else:
                print(f"❌ 解锁操作失败: {result.get('msg')}")
        else:
            print(f"❌ HTTP请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    # 测试用例3: 参数验证测试
    print("\n⚠️ 测试用例3: 参数验证测试")
    invalid_data = {
        "operator": "",  # 空操作人
        "peInfoList": []  # 空列表
    }
    
    print("请求数据（无效参数）:")
    print(json.dumps(invalid_data, ensure_ascii=False, indent=2))
    
    try:
        response = requests.post(url, json=invalid_data, timeout=30)
        print(f"\n响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("响应数据:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
            if result.get('code') != 0:
                print("✅ 参数验证正常工作")
            else:
                print("❌ 参数验证未生效")
        else:
            print(f"❌ HTTP请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    # 测试用例4: 缺少必要字段测试
    print("\n⚠️ 测试用例4: 缺少必要字段测试")
    missing_field_data = {
        "operator": "ADMIN001",
        "peInfoList": [
            {
                "accountId": "DOCTOR001",
                # 缺少 currentNodeType, operationType, peNo
            }
        ]
    }
    
    print("请求数据（缺少字段）:")
    print(json.dumps(missing_field_data, ensure_ascii=False, indent=2))
    
    try:
        response = requests.post(url, json=missing_field_data, timeout=30)
        print(f"\n响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("响应数据:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
            if result.get('code') != 0:
                print("✅ 字段验证正常工作")
            else:
                print("❌ 字段验证未生效")
        else:
            print(f"❌ HTTP请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    print("\n" + "=" * 60)
    print("12号接口测试完成")
    print("=" * 60)

def test_health_check():
    """测试健康检查接口，确认12号接口已注册"""
    print("\n🔍 检查12号接口注册状态")
    
    try:
        response = requests.get("http://localhost:5007/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            interfaces = health_data.get('interfaces', {})
            
            if '12' in interfaces:
                print(f"✅ 12号接口已注册: {interfaces['12']}")
            else:
                print("❌ 12号接口未在健康检查中找到")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")

if __name__ == "__main__":
    print("请确保GUI服务已启动 (python gui_main.py)")
    print("等待5秒后开始测试...")
    
    import time
    time.sleep(5)
    
    # 先检查服务状态
    test_health_check()
    
    # 然后测试12号接口
    test_interface_12()