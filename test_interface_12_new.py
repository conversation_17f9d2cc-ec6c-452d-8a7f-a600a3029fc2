#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试12号接口 - 支持门店编码在peInfoList中的新格式
"""

import json
from interface_12_lockPeInfo import TianjianInterface12

def test_interface_12_new_format():
    """测试12号接口 - 支持门店编码在peInfoList中"""
    print("[TEST] 测试天健云12号接口 - 门店编码在peInfoList中")
    print("=" * 60)
    
    # API配置
    api_config = {
        'base_url': 'http://203.83.237.114:9300',
        'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
        'mic_code': 'MIC1.001E',
        'misc_id': 'MISC1.00001A',
        'timeout': 30
    }
    
    # 创建接口实例
    interface = TianjianInterface12(api_config)
    
    # 测试数据 - shopCode在peInfoList中
    request_data = {
        'operator': 'ADMIN001',
        'peInfoList': [
            {
                'accountId': 'DO001',
                'currentNodeType': 3,
                'force': False,
                'operationType': 1,  # 锁定
                'peNo': '5000003',
                'shopCode': '09'  # 门店编码09在peInfo中
            }
        ]
    }
    
    print("\\n[LOCK] 测试锁定操作 - GUI服务调用格式")
    print(f"请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
    
    # 使用GUI服务调用格式进行测试
    result = interface.lock_pe_info(request_data)
    print(f"结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
    # 测试解锁操作
    print("\\n[UNLOCK] 测试解锁操作 - 删除T_Diag_result记录")
    unlock_request_data = {
        'operator': 'ADMIN001',
        'peInfoList': [
            {
                'accountId': 'DO001',
                'currentNodeType': 3,
                'force': False,
                'operationType': 2,  # 解锁 - 删除记录
                'peNo': '5000003',
                'shopCode': '09'
            }
        ]
    }
    
    print(f"解锁请求数据: {json.dumps(unlock_request_data, ensure_ascii=False, indent=2)}")
    
    # 执行解锁测试
    unlock_result = interface.lock_pe_info(unlock_request_data)
    print(f"解锁结果: {json.dumps(unlock_result, ensure_ascii=False, indent=2)}")
    
    # 测试多个门店的情况
    print("\\n[MULTI] 测试多门店数据")
    multi_request_data = {
        'operator': 'ADMIN001',
        'peInfoList': [
            {
                'accountId': 'DO001',
                'currentNodeType': 3,
                'force': False,
                'operationType': 1,
                'peNo': '5000003',
                'shopCode': '09'  # 门店09
            },
            {
                'accountId': 'DO002',
                'currentNodeType': 3,
                'force': False,
                'operationType': 1,
                'peNo': '5000006',
                'shopCode': '09'  # 门店09
            }
        ]
    }
    
    print(f"多门店请求数据: {json.dumps(multi_request_data, ensure_ascii=False, indent=2)}")
    
    # 执行多门店测试
    result2 = interface.lock_pe_info(multi_request_data)
    print(f"多门店结果: {json.dumps(result2, ensure_ascii=False, indent=2)}")
    
    print("\\n[OK] 天健云12号接口测试完成 - 支持门店编码在peInfoList中")

if __name__ == "__main__":
    test_interface_12_new_format()