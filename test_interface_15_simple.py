#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云15号接口简化测试
"""

import json
import hashlib
import requests
from datetime import datetime
from config import Config

def generate_signature(api_key: str, timestamp: str) -> str:
    """生成API签名"""
    sign_string = f"{api_key}{timestamp}"
    return hashlib.md5(sign_string.encode('utf-8')).hexdigest()

def test_dept_return_interface():
    """测试分科退回接口"""
    print("[TEST] 测试天健云15号接口 - 分科退回")
    print("=" * 50)
    
    # 加载配置
    config = Config()
    api_config = config.get_tianjian_api_config()
    
    # 生成时间戳和签名
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    signature = generate_signature(api_config['api_key'], timestamp)
    
    # 构建请求头
    headers = {
        'Content-Type': 'application/json',
        'sign': signature,
        'timestamp': timestamp,
        'mic-code': api_config['mic_code'],
        'misc-id': api_config['misc_id']
    }
    
    # 构建请求数据（扁平化为URL参数）
    request_params = {
        "peNo": "5000003",
        "markDoctor": "DOC001", 
        "errorItem": "ITEM001",
        "returnDept.code": "DEPT001",
        "returnDept.name": "内科",
        "receiveDoctor.code": "DOC002", 
        "receiveDoctor.name": "李医生",
        "remark": "检查结果需要重新确认",
        "currentNodeType": 2,
        "shopCode": "09"  # 门店编码
    }
    
    # 原始的嵌套结构（用于发送）
    request_data = {
        "peNo": "5000003",
        "markDoctor": "DOC001", 
        "errorItem": "ITEM001",
        "returnDept": {
            "code": "DEPT001",
            "name": "内科"
        },
        "receiveDoctor": {
            "code": "DOC002", 
            "name": "李医生"
        },
        "remark": "检查结果需要重新确认",
        "currentNodeType": 2,
        "shopCode": "09"  # 门店编码
    }
    
    # 构建请求URL - 指向本地服务而不是外部天健云API
    endpoint = "/dx/inter/returnDept"  # 正确的端点路径
    local_base_url = "http://localhost:5007"  # 本地服务地址
    url = f"{local_base_url}{endpoint}"
    
    print(f"[INFO] 请求URL: {url}")
    print(f"[INFO] 请求头:")
    for key, value in headers.items():
        if key.lower() in ['sign', 'api-key']:
            print(f"  {key}: {value[:8]}...")
        else:
            print(f"  {key}: {value}")
    
    print(f"[INFO] 请求数据:")
    print(json.dumps(request_data, ensure_ascii=False, indent=2))
    
    try:
        # 发送POST请求到本地服务（发送JSON数据）
        print(f"\n[SEND] 发送分科退回请求到本地服务...")
        response = requests.post(
            url=url,
            headers=headers,
            json=request_data,  # 发送嵌套结构的JSON数据
            timeout=30
        )
        
        print(f"[RESP] HTTP状态码: {response.status_code}")
        print(f"[RESP] 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                print(f"[RESP] 响应数据:")
                print(json.dumps(response_data, ensure_ascii=False, indent=2))
                
                if response_data.get('code') == 0:
                    print(f"[OK] 分科退回成功: {response_data.get('msg', '')}")
                else:
                    print(f"[FAIL] 分科退回失败: {response_data.get('msg', '')}")
                    
            except json.JSONDecodeError:
                print(f"[RESP] 响应文本: {response.text}")
        else:
            print(f"[FAIL] HTTP请求失败: {response.status_code}")
            print(f"[RESP] 响应文本: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"[ERROR] 请求异常: {e}")
    
    print(f"\n[DONE] 测试完成")

if __name__ == "__main__":
    test_dept_return_interface()