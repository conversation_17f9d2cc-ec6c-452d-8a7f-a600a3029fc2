#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云16号接口专用测试文件 - 查询图片接口
测试PACS数据库图片查询功能，包括base64编码和多种查询条件
"""

import json
import sys
import os
from datetime import datetime
from typing import Dict, Any, List

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from interface_16_getImages import TianjianInterface16


class Interface16TestSuite:
    """16号接口测试套件"""
    
    def __init__(self):
        """初始化测试套件"""
        self.api_config = {
            'base_url': 'http://203.83.237.114:9300',
            'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
            'mic_code': 'MIC1.001E',
            'misc_id': 'MISC1.00001A',
            'timeout': 30
        }
        self.interface = TianjianInterface16(self.api_config)
        self.test_results = []
        
    def log_test_result(self, test_name: str, success: bool, message: str, data: Dict = None):
        """记录测试结果"""
        result = {
            'test_name': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'data': data
        }
        self.test_results.append(result)
        
        status = "[PASS]" if success else "[FAIL]"
        print(f"{status} {test_name}: {message}")
        if data and not success:
            print(f"    详细信息: {json.dumps(data, ensure_ascii=False, indent=4)}")
    
    def test_query_all_images(self):
        """测试1：查询指定体检号的所有图片"""
        print("\n" + "="*60)
        print("测试1：查询指定体检号的所有图片")
        print("="*60)
        
        request_data = {
            'peNo': '085041193',  # 用户指定的卡号
            'deptId': '',         # 空表示所有科室
            'applyItemId': [],    # 空表示所有项目
            'cshopcode': '08'     # 用户指定的机构编码
        }
        
        print(f"请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
        
        try:
            result = self.interface.query_images_service(request_data)
            
            # 验证返回格式
            if 'code' in result and 'msg' in result and 'data' in result:
                if result['code'] == 0:
                    image_count = len(result['data'])
                    self.log_test_result(
                        "查询所有图片", 
                        True, 
                        f"查询成功，返回{image_count}张图片",
                        result
                    )
                    
                    # 如果有图片数据，验证数据格式
                    if image_count > 0:
                        self.validate_image_data_format(result['data'][0])
                else:
                    self.log_test_result(
                        "查询所有图片", 
                        True, 
                        f"查询完成: {result['msg']}",
                        result
                    )
            else:
                self.log_test_result(
                    "查询所有图片", 
                    False, 
                    "返回格式不正确，缺少必要字段",
                    result
                )
                
        except Exception as e:
            self.log_test_result(
                "查询所有图片", 
                False, 
                f"查询异常: {str(e)}",
                {'error': str(e)}
            )
    
    def test_query_by_dept(self):
        """测试2：按科室编号查询图片"""
        print("\n" + "="*60)
        print("测试2：按科室编号查询图片")
        print("="*60)
        
        request_data = {
            'peNo': '9000001',     # 测试卡号
            'deptId': 'XRAY001',   # 指定科室（X光科）
            'applyItemId': [],     # 空表示该科室所有项目
            'cshopcode': '09'      # 机构编码
        }
        
        print(f"请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
        
        try:
            result = self.interface.query_images_service(request_data)
            
            if result['code'] == 0:
                image_count = len(result['data'])
                self.log_test_result(
                    "按科室查询图片", 
                    True, 
                    f"科室查询成功，返回{image_count}张图片",
                    result
                )
            else:
                self.log_test_result(
                    "按科室查询图片", 
                    True, 
                    f"科室查询完成: {result['msg']}",
                    result
                )
                
        except Exception as e:
            self.log_test_result(
                "按科室查询图片", 
                False, 
                f"科室查询异常: {str(e)}",
                {'error': str(e)}
            )
    
    def test_query_by_apply_items(self):
        """测试3：按申请项目ID列表查询图片"""
        print("\n" + "="*60)
        print("测试3：按申请项目ID列表查询图片")
        print("="*60)
        
        request_data = {
            'peNo': '9000001',        # 测试卡号
            'deptId': '',             # 空表示所有科室
            'applyItemId': ['ITEM001', 'ITEM002', 'XRAY001'],  # 指定多个项目
            'cshopcode': '09'         # 机构编码
        }
        
        print(f"请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
        
        try:
            result = self.interface.query_images_service(request_data)
            
            if result['code'] == 0:
                image_count = len(result['data'])
                self.log_test_result(
                    "按项目查询图片", 
                    True, 
                    f"项目查询成功，返回{image_count}张图片",
                    result
                )
            else:
                self.log_test_result(
                    "按项目查询图片", 
                    True, 
                    f"项目查询完成: {result['msg']}",
                    result
                )
                
        except Exception as e:
            self.log_test_result(
                "按项目查询图片", 
                False, 
                f"项目查询异常: {str(e)}",
                {'error': str(e)}
            )
    
    def test_query_combined_conditions(self):
        """测试4：组合查询条件"""
        print("\n" + "="*60)
        print("测试4：组合查询条件（科室+项目）")
        print("="*60)
        
        request_data = {
            'peNo': '9000001',        # 测试卡号
            'deptId': 'XRAY001',      # 指定科室
            'applyItemId': ['ITEM001'],  # 指定项目
            'cshopcode': '09'         # 机构编码
        }
        
        print(f"请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
        
        try:
            result = self.interface.query_images_service(request_data)
            
            if result['code'] == 0:
                image_count = len(result['data'])
                self.log_test_result(
                    "组合条件查询", 
                    True, 
                    f"组合查询成功，返回{image_count}张图片",
                    result
                )
            else:
                self.log_test_result(
                    "组合条件查询", 
                    True, 
                    f"组合查询完成: {result['msg']}",
                    result
                )
                
        except Exception as e:
            self.log_test_result(
                "组合条件查询", 
                False, 
                f"组合查询异常: {str(e)}",
                {'error': str(e)}
            )
    
    def test_invalid_card_no(self):
        """测试5：无效卡号测试"""
        print("\n" + "="*60)
        print("测试5：无效卡号测试")
        print("="*60)
        
        request_data = {
            'peNo': '9999999',    # 不存在的卡号
            'deptId': '',
            'applyItemId': [],
            'cshopcode': '09'
        }
        
        print(f"请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
        
        try:
            result = self.interface.query_images_service(request_data)
            
            # 应该返回错误或空结果
            if result['code'] == -1 or (result['code'] == 0 and len(result['data']) == 0):
                self.log_test_result(
                    "无效卡号测试", 
                    True, 
                    "正确处理无效卡号",
                    result
                )
            else:
                self.log_test_result(
                    "无效卡号测试", 
                    False, 
                    "无效卡号处理异常",
                    result
                )
                
        except Exception as e:
            self.log_test_result(
                "无效卡号测试", 
                True,  # 异常也是预期的处理方式
                f"无效卡号抛出异常（正常）: {str(e)}",
                {'error': str(e)}
            )
    
    def test_empty_request(self):
        """测试6：空请求测试"""
        print("\n" + "="*60)
        print("测试6：空请求测试")
        print("="*60)
        
        request_data = {
            'peNo': '',           # 空卡号
            'deptId': '',
            'applyItemId': [],
            'cshopcode': '09'
        }
        
        print(f"请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
        
        try:
            result = self.interface.query_images_service(request_data)
            
            # 应该返回错误
            if result['code'] == -1:
                self.log_test_result(
                    "空请求测试", 
                    True, 
                    "正确处理空卡号请求",
                    result
                )
            else:
                self.log_test_result(
                    "空请求测试", 
                    False, 
                    "空卡号处理异常",
                    result
                )
                
        except Exception as e:
            self.log_test_result(
                "空请求测试", 
                True,  # 异常也是预期的处理方式
                f"空请求抛出异常（正常）: {str(e)}",
                {'error': str(e)}
            )
    
    def test_gui_service_integration(self):
        """测试7：GUI服务集成测试"""
        print("\n" + "="*60)
        print("测试7：GUI服务集成测试")
        print("="*60)
        
        try:
            import requests
            
            url = 'http://localhost:5007/dx/inter/getImages'
            headers = {'Content-Type': 'application/json'}
            data = {
                'peNo': '9000001',
                'deptId': '',
                'applyItemId': [],
                'cshopcode': '09'
            }
            
            print(f"请求URL: {url}")
            print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            response = requests.post(url, json=data, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                self.log_test_result(
                    "GUI服务集成", 
                    True, 
                    f"GUI服务调用成功，状态码: {response.status_code}",
                    result
                )
            else:
                self.log_test_result(
                    "GUI服务集成", 
                    False, 
                    f"GUI服务调用失败，状态码: {response.status_code}",
                    {'status_code': response.status_code, 'response': response.text}
                )
                
        except requests.exceptions.ConnectionError:
            self.log_test_result(
                "GUI服务集成", 
                False, 
                "GUI服务未启动，无法连接到localhost:5007",
                {'error': '连接被拒绝'}
            )
        except Exception as e:
            self.log_test_result(
                "GUI服务集成", 
                False, 
                f"GUI服务测试异常: {str(e)}",
                {'error': str(e)}
            )
    
    def validate_image_data_format(self, image_data: Dict[str, Any]):
        """验证图片数据格式"""
        print("\n" + "="*60)
        print("验证图片数据格式")
        print("="*60)
        
        required_fields = ['fileName', 'fileUri', 'applyItemId', 'deptId', 'base64Url']
        missing_fields = []
        
        for field in required_fields:
            if field not in image_data:
                missing_fields.append(field)
        
        if missing_fields:
            self.log_test_result(
                "图片数据格式验证", 
                False, 
                f"缺少必要字段: {', '.join(missing_fields)}",
                image_data
            )
        else:
            # 验证base64格式
            base64_url = image_data.get('base64Url', '')
            if base64_url.startswith('data:image/'):
                self.log_test_result(
                    "图片数据格式验证", 
                    True, 
                    "图片数据格式正确，包含所有必要字段",
                    {'sample_data': {k: str(v)[:50] + '...' if len(str(v)) > 50 else v for k, v in image_data.items()}}
                )
            else:
                self.log_test_result(
                    "图片数据格式验证", 
                    False, 
                    "base64Url格式不正确",
                    {'base64Url': base64_url[:100] + '...' if len(base64_url) > 100 else base64_url}
                )
    
    def run_all_tests(self):
        """运行所有测试"""
        print("天健云16号接口测试套件")
        print("=" * 80)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"接口名称: 查询图片接口 (/dx/inter/getImages)")
        print(f"测试目标: PACS数据库图片查询，base64编码，多种查询条件")
        print("=" * 80)
        
        # 执行所有测试
        self.test_query_all_images()
        self.test_query_by_dept()
        self.test_query_by_apply_items()
        self.test_query_combined_conditions()
        self.test_invalid_card_no()
        self.test_empty_request()
        self.test_gui_service_integration()
        
        # 生成测试报告
        self.generate_test_report()
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n" + "=" * 80)
        print("测试报告")
        print("=" * 80)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['success']])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} [PASS]")
        print(f"失败: {failed_tests} [FAIL]")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print(f"\n失败的测试:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  [FAIL] {result['test_name']}: {result['message']}")
        
        # 保存详细报告到文件
        report_filename = f"test_report_interface_16_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_filename, 'w', encoding='utf-8') as f:
                json.dump({
                    'summary': {
                        'total_tests': total_tests,
                        'passed_tests': passed_tests,
                        'failed_tests': failed_tests,
                        'success_rate': f"{(passed_tests/total_tests*100):.1f}%"
                    },
                    'test_results': self.test_results
                }, f, ensure_ascii=False, indent=2)
            
            print(f"\n详细测试报告已保存到: {report_filename}")
            
        except Exception as e:
            print(f"\n保存测试报告失败: {e}")
        
        print("=" * 80)


def main():
    """主函数"""
    # 创建测试套件并运行所有测试
    test_suite = Interface16TestSuite()
    test_suite.run_all_tests()


if __name__ == "__main__":
    main()