#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的16号接口
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from interface_16_getImages import TianjianInterface16


def test_interface_16_fix():
    """测试修复后的16号接口"""
    print("测试修复后的16号接口 - 查询图片")
    print("=" * 60)
    
    # API配置
    api_config = {
        'base_url': 'http://203.83.237.114:9300',
        'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
        'mic_code': 'MIC1.001E',
        'misc_id': 'MISC1.00001A',
        'timeout': 30
    }
    
    # 创建接口实例
    interface = TianjianInterface16(api_config)
    
    # 测试用户提供的请求报文
    print("\n[TEST] 测试用户提供的请求报文")
    request_data = {
        "peNo": "085041193",
        "deptId": "",
        "applyItemId": [],
        "cshopcode": "08"  # 使用cshopcode字段，值为08
    }
    
    print(f"请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
    
    try:
        result = interface.query_images_service(request_data)
        print(f"结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 检查结果
        if result.get('code') == 0:
            print(f"[SUCCESS] 接口调用成功，返回 {len(result.get('data', []))} 条图片记录")
        elif result.get('code') == -1:
            error_msg = result.get('msg', '')
            if 'Shop code' in error_msg and 'not found' in error_msg:
                print(f"[ERROR] 仍然存在shop_code配置问题: {error_msg}")
            else:
                print(f"[INFO] 接口返回错误（可能是正常的业务错误）: {error_msg}")
        else:
            print(f"[INFO] 接口返回代码: {result.get('code')}, 消息: {result.get('msg')}")
            
    except Exception as e:
        print(f"[ERROR] 接口调用异常: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试兼容性：使用shopcode字段
    print("\n[TEST] 测试兼容性：使用shopcode字段")
    request_data_compat = {
        "peNo": "085041193",
        "deptId": "",
        "applyItemId": [],
        "shopcode": "08"  # 使用shopcode字段（兼容模式）
    }
    
    print(f"请求数据: {json.dumps(request_data_compat, ensure_ascii=False, indent=2)}")
    
    try:
        result = interface.query_images_service(request_data_compat)
        print(f"结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 检查结果
        if result.get('code') == 0:
            print(f"[SUCCESS] 兼容模式调用成功，返回 {len(result.get('data', []))} 条图片记录")
        elif result.get('code') == -1:
            error_msg = result.get('msg', '')
            if 'Shop code' in error_msg and 'not found' in error_msg:
                print(f"[ERROR] 兼容模式仍然存在shop_code配置问题: {error_msg}")
            else:
                print(f"[INFO] 兼容模式返回错误（可能是正常的业务错误）: {error_msg}")
        else:
            print(f"[INFO] 兼容模式返回代码: {result.get('code')}, 消息: {result.get('msg')}")
            
    except Exception as e:
        print(f"[ERROR] 兼容模式调用异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_interface_16_fix()
