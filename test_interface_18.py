#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试18号接口 - 查询医生信息
"""

import json
from interface_18_getDoctorInfo import TianjianInterface18
from config import Config

def test_interface_18():
    """测试18号接口"""
    print("[TEST] 测试天健云18号接口 - 查询医生信息接口")
    print("=" * 50)
    
    # 获取API配置
    api_config = Config.get_tianjian_api_config()
    
    # 创建接口实例
    interface = TianjianInterface18(api_config)
    
    # 测试场景1：查询所有医生信息（测试模式）
    print("\n[DOCTOR] 测试场景1：查询所有医生信息（测试模式）")
    result1 = interface.query_doctor_info(test_mode=True)
    print(f"结果: {json.dumps(result1, ensure_ascii=False, indent=2)}")
    
    # 测试场景2：查询特定医生信息（测试模式）
    print("\n[DOCTOR] 测试场景2：查询特定医生信息（测试模式）")
    result2 = interface.query_doctor_info(
        doctor_id="DOCTOR001",
        hospital_code="0350001",
        test_mode=True
    )
    print(f"结果: {json.dumps(result2, ensure_ascii=False, indent=2)}")
    
    # 测试场景3：从数据库获取医生信息
    print("\n[DOCTOR] 测试场景3：从数据库获取医生信息")
    try:
        doctors = interface.get_doctor_info_from_db(limit=5)
        print(f"获取到 {len(doctors)} 条医生信息")
        if doctors:
            print("前2条医生信息:")
            for i, doctor in enumerate(doctors[:2], 1):
                print(f"  医生{i}: {doctor.get('name', '')} ({doctor.get('accountCode', '')})")
    except Exception as e:
        print(f"获取医生信息时出错: {str(e)}")
    
    # 测试场景4：按关键字查询医生
    print("\n[DOCTOR] 测试场景4：按关键字查询医生")
    try:
        doctors = interface.get_doctor_info_from_db(name_keyword="医生", limit=3)
        print(f"关键字查询结果: 获取到 {len(doctors)} 条医生信息")
        if doctors:
            for i, doctor in enumerate(doctors, 1):
                print(f"  医生{i}: {doctor.get('name', '')} ({doctor.get('accountCode', '')})")
    except Exception as e:
        print(f"关键字查询时出错: {str(e)}")
    
    print("\n[OK] 天健云18号接口测试完成")

if __name__ == "__main__":
    test_interface_18()