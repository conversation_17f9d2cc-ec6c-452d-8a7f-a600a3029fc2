#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试18号接口修复
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_interface_18_direct():
    """直接测试18号接口修复"""
    print("[TEST] 直接测试18号接口修复")
    print("=" * 50)
    
    try:
        from interface_18_getDoctorInfo import TianjianInterface18
        from config import Config
        
        # 获取API配置
        api_config = Config.get_tianjian_api_config()
        
        # 创建接口实例
        interface = TianjianInterface18(api_config)
        
        # 测试请求报文: {"id": "", "shopcode": "08"}
        test_data = {
            "id": "",
            "shopcode": "08"
        }
        
        print(f"[REQUEST] 测试请求数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
        
        # 直接调用get_doctor_info方法（模拟GUI调用）
        result = interface.get_doctor_info(test_data)
        
        print(f"\n[RESPONSE] 返回结果:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        # 检查是否有NoneType错误
        if 'NoneType' in result.get('msg', ''):
            print(f"\n[FAILED] 仍然存在NoneType错误!")
            return False
        elif result['code'] == 0:
            data = result.get('data', [])
            print(f"\n[SUCCESS] 查询成功，返回 {len(data)} 条医生信息，没有NoneType错误")
            return True
        else:
            print(f"\n[INFO] 查询返回码: {result.get('code')}, 消息: {result.get('msg')}")
            print(f"[SUCCESS] 没有NoneType错误，接口修复成功")
            return True
            
    except Exception as e:
        print(f"\n[ERROR] 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_with_test_mode():
    """测试模式下的测试"""
    print("\n" + "=" * 50)
    print("[TEST] 测试模式下的18号接口")
    
    try:
        from interface_18_getDoctorInfo import TianjianInterface18
        from config import Config
        
        # 获取API配置
        api_config = Config.get_tianjian_api_config()
        
        # 创建接口实例
        interface = TianjianInterface18(api_config)
        
        # 调用query_doctor_info方法（测试模式）
        result = interface.query_doctor_info(
            doctor_id="",
            hospital_code="08",
            test_mode=True
        )
        
        print(f"\n[TEST MODE RESPONSE] 测试模式返回结果:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        if result['code'] == 0 and result.get('data'):
            print(f"\n[SUCCESS] 测试模式成功，返回 {len(result['data'])} 条医生信息")
            return True
        else:
            print(f"\n[INFO] 测试模式返回码: {result.get('code')}")
            return False
        
    except Exception as e:
        print(f"\n[ERROR] 测试模式异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_database_query():
    """测试数据库查询"""
    print("\n" + "=" * 50)
    print("[TEST] 测试数据库查询医生信息")
    
    try:
        from interface_18_getDoctorInfo import TianjianInterface18
        from config import Config
        
        # 获取API配置
        api_config = Config.get_tianjian_api_config()
        
        # 创建接口实例
        interface = TianjianInterface18(api_config)
        
        # 调用数据库查询方法
        doctors = interface.get_doctor_info_from_db(limit=5)
        
        print(f"\n[DATABASE RESPONSE] 数据库查询结果:")
        if doctors:
            print(f"找到 {len(doctors)} 条医生信息:")
            for i, doctor in enumerate(doctors[:3], 1):
                print(f"  医生{i}: {doctor.get('name', 'N/A')} ({doctor.get('accountCode', 'N/A')})")
            if len(doctors) > 3:
                print(f"  ... 共 {len(doctors)} 条记录")
            return True
        else:
            print("没有找到医生信息")
            return False
        
    except Exception as e:
        print(f"\n[ERROR] 数据库查询异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始18号接口修复验证...")
    
    success_count = 0
    
    # 测试1: 直接接口调用
    if test_interface_18_direct():
        success_count += 1
    
    # 测试2: 测试模式
    if test_with_test_mode():
        success_count += 1
    
    # 测试3: 数据库查询
    if test_database_query():
        success_count += 1
    
    print(f"\n" + "=" * 50)
    print(f"[SUMMARY] 测试总结: {success_count}/3 项测试通过")
    
    if success_count >= 2:
        print("[SUCCESS] 18号接口修复成功!")
        print("[INFO] NoneType错误已修复")
    else:
        print("[WARNING] 部分测试失败，可能需要进一步检查")