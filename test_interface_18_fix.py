#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试18号接口修复
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from interface_18_getDoctorInfo import TianjianInterface18
from config import Config

def test_interface_18_fix():
    """测试18号接口修复"""
    print("[TEST] 测试天健云18号接口修复")
    print("=" * 50)
    
    try:
        # 获取API配置
        api_config = Config.get_tianjian_api_config()
        print(f"[CONFIG] API配置: {api_config}")
        
        # 创建接口实例
        interface = TianjianInterface18(api_config)
        
        # 测试请求报文: {"id": "", "shopcode": "08"}
        test_data = {
            "id": "",
            "shopcode": "08"
        }
        
        print(f"\n[REQUEST] 测试请求数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
        
        # 调用get_doctor_info方法（这是GUI中调用的方法）
        result = interface.get_doctor_info(test_data)
        
        print(f"\n[RESPONSE] 返回结果:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        # 检查结果
        if result['code'] == 0:
            data = result.get('data')
            if data:
                print(f"\n[SUCCESS] 查询成功，返回 {len(data)} 条医生信息")
            else:
                print(f"\n[SUCCESS] 查询成功，但没有返回医生信息数据")
        else:
            print(f"\n[ERROR] 查询失败: {result['msg']}")
            
    except Exception as e:
        print(f"\n[EXCEPTION] 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()

def test_with_test_mode():
    """测试模式下的测试"""
    print("\n" + "=" * 50)
    print("[TEST] 测试模式下的18号接口")
    
    try:
        # 获取API配置
        api_config = Config.get_tianjian_api_config()
        
        # 创建接口实例
        interface = TianjianInterface18(api_config)
        
        # 测试请求报文
        test_data = {
            "id": "",
            "shopcode": "08"
        }
        
        # 调用query_doctor_info方法（测试模式）
        result = interface.query_doctor_info(
            doctor_id="",
            hospital_code="08",
            test_mode=True
        )
        
        print(f"\n[TEST MODE RESPONSE] 测试模式返回结果:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
    except Exception as e:
        print(f"\n[EXCEPTION] 测试模式异常: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_interface_18_fix()
    test_with_test_mode()