#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修正后的21号接口 - 支持动态数据库连接
"""

import requests
import json

def test_interface_21_dynamic_db():
    """测试21号接口的动态数据库连接"""
    url = "http://localhost:5007/dx/inter/getAbnormalNotice"
    
    print("测试修正后的21号接口 - 查询异常通知")
    print("=" * 60)
    
    # 测试09门店
    test_data_09 = {
        "peNo": "0825751692",
        "hospital": {
            "code": "09",
            "name": "wu"
        }
    }
    
    print(f"1. 测试09门店异常通知查询")
    print(f"请求URL: {url}")
    print(f"请求数据: {json.dumps(test_data_09, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data_09, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("09门店响应结果:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
            if result.get('code') == 0:
                data = result.get('data')
                if data:
                    print(f"成功返回异常通知数据:")
                    print(f"  体检号: {data.get('peNo')}")
                    print(f"  异常名称: {data.get('abnormalName')}")
                    print(f"  通知状态: {'已通知' if data.get('hasNotice') == '1' else '未通知'}")
                else:
                    print("未找到异常通知数据")
            else:
                print(f"请求失败: {result.get('msg')}")
        else:
            print(f"HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {str(e)}")
    
    # 测试08门店对比
    test_data_08 = {
        "peNo": "0825751692", 
        "hospital": {
            "code": "08",
            "name": "test"
        }
    }
    
    print(f"\\n2. 测试08门店异常通知查询（对比）")
    print(f"请求数据: {json.dumps(test_data_08, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data_08, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('code') == 0:
                data = result.get('data')
                if data:
                    print(f"08门店成功返回异常通知数据:")
                    print(f"  体检号: {data.get('peNo')}")
                    print(f"  异常名称: {data.get('abnormalName')}")
                    print(f"  通知状态: {'已通知' if data.get('hasNotice') == '1' else '未通知'}")
                else:
                    print("08门店未找到异常通知数据")
                    
                print(f"\\n验证结果: 08门店和09门店数据{'相同' if data == result.get('data') else '不同'}")
            else:
                print(f"08门店请求失败: {result.get('msg')}")
        else:
            print(f"08门店HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"08门店请求异常: {str(e)}")
    
    print("\\n测试完成 - 验证21号接口是否正确使用对应门店的数据库")

if __name__ == "__main__":
    test_interface_21_dynamic_db()