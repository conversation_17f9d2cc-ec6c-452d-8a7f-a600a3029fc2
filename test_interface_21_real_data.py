#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用实际存在的体检号测试21号接口的动态数据库连接
"""

import requests
import json

def test_interface_21_with_real_data():
    """使用实际存在的体检号测试21号接口"""
    url = "http://localhost:5007/dx/inter/getAbnormalNotice"
    
    print("使用实际存在的体检号测试21号接口")
    print("=" * 60)
    
    # 测试09门店 - 使用09门店中实际存在的体检号
    test_data_09 = {
        "peNo": "0825749350",  # 09门店中存在的体检号
        "hospital": {
            "code": "09",
            "name": "wu"
        }
    }
    
    print(f"1. 测试09门店 - 使用09门店中存在的体检号")
    print(f"请求数据: {json.dumps(test_data_09, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data_09, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("09门店响应结果:")
            
            if result.get('code') == 0:
                data = result.get('data')
                if data:
                    print(f"✅ 09门店成功返回异常通知数据:")
                    print(f"  体检号: {data.get('peNo')}")
                    print(f"  患者姓名: {data.get('patient', {}).get('name')}")
                    print(f"  异常名称: {data.get('abnormalName', '无')}")
                else:
                    print("❌ 09门店未找到异常通知数据") 
            else:
                print(f"❌ 09门店查询失败: {result.get('msg')}")
        else:
            print(f"❌ 09门店HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 09门店请求异常: {str(e)}")
    
    # 测试08门店 - 使用08门店中实际存在的带异常通知的体检号
    test_data_08 = {
        "peNo": "**********",  # 08门店中存在的体检号（有异常记录）
        "hospital": {
            "code": "08", 
            "name": "test"
        }
    }
    
    print(f"\\n2. 测试08门店 - 使用08门店中存在的体检号")
    print(f"请求数据: {json.dumps(test_data_08, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data_08, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("08门店响应结果:")
            
            if result.get('code') == 0:
                data = result.get('data')
                if data:
                    print(f"✅ 08门店成功返回异常通知数据:")
                    print(f"  体检号: {data.get('peNo')}")
                    print(f"  患者姓名: {data.get('patient', {}).get('name')}")
                    print(f"  异常名称: {data.get('abnormalName', '无')}")
                else:
                    print("❌ 08门店未找到异常通知数据")
            else:
                print(f"❌ 08门店查询失败: {result.get('msg')}")
        else:
            print(f"❌ 08门店HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 08门店请求异常: {str(e)}")
        
    # 验证数据隔离 - 用08门店的体检号查询09门店
    print(f"\\n3. 验证数据隔离 - 用08门店的体检号查询09门店")
    test_data_cross = {
        "peNo": "**********",  # 08门店的体检号
        "hospital": {
            "code": "09",  # 但查询09门店
            "name": "wu"
        }
    }
    
    print(f"请求数据: {json.dumps(test_data_cross, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data_cross, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('code') == 0:
                data = result.get('data')
                if data:
                    print(f"⚠️  意外：09门店找到了08门店的体检号数据，数据隔离可能有问题")
                else:
                    print("✅ 正确：09门店没有找到08门店的体检号，数据隔离正常")
            else:
                print(f"✅ 正确：09门店查询失败，数据隔离正常: {result.get('msg')}")
    except Exception as e:
        print(f"验证请求异常: {str(e)}")
    
    print("\\n✅ 21号接口动态数据库连接测试完成")

if __name__ == "__main__":
    test_interface_21_with_real_data()