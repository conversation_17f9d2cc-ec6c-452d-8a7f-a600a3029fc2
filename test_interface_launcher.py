#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试接口启动器功能
验证在不同环境下是否能正确启动接口而不会出现GUI重复启动问题
"""

import os
import sys
import subprocess
import time


def test_interface_launcher():
    """测试接口启动器"""
    print("=== 测试接口启动器功能 ===\n")
    
    # 检测当前环境
    is_packaged = getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')
    print(f"当前环境: {'打包环境' if is_packaged else '开发环境'}")
    
    if is_packaged:
        print(f"打包目录: {sys._MEIPASS}")
    
    # 测试接口列表
    test_interfaces = ["01", "02", "03"]
    
    for interface_num in test_interfaces:
        print(f"\n--- 测试{interface_num}号接口启动器 ---")
        
        try:
            # 构建启动器命令
            launcher_script = "interface_launcher.py"
            
            if is_packaged and hasattr(sys, '_MEIPASS'):
                launcher_path = os.path.join(sys._MEIPASS, launcher_script)
            else:
                launcher_path = launcher_script
            
            if not os.path.exists(launcher_path):
                print(f"[ERROR] 启动器不存在: {launcher_path}")
                continue
                
            print(f"[OK] 启动器存在: {launcher_path}")
            
            # 测试命令（测试模式，限制数据量）
            cmd = [
                sys.executable, 
                launcher_path, 
                interface_num,
                "--test-mode",
                "--limit", "2"
            ]
            
            print(f"[CMD] 测试命令: {' '.join(cmd)}")
            
            # 设置测试环境变量
            env = os.environ.copy()
            env['GUI_SUBPROCESS'] = '1'
            env['TIANJIAN_NO_GUI'] = '1'
            env['NO_GUI_MODE'] = '1'
            
            start_time = time.time()
            
            # 执行测试（5秒超时）
            result = subprocess.run(
                cmd,
                env=env,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='replace',
                timeout=5
            )
            
            end_time = time.time()
            elapsed = end_time - start_time
            
            print(f"[TIME] 执行时间: {elapsed:.2f}秒")
            print(f"[CODE] 返回码: {result.returncode}")
            
            if result.stdout:
                print("[STDOUT] 标准输出:")
                print(result.stdout[:500] + "..." if len(result.stdout) > 500 else result.stdout)
            
            if result.stderr:
                print("[STDERR] 错误输出:")
                print(result.stderr[:500] + "..." if len(result.stderr) > 500 else result.stderr)
            
            # 检查是否有GUI相关的错误输出
            gui_keywords = ['gui_main', 'QApplication', 'PySide6', 'tkinter', 'GUI']
            has_gui_error = any(keyword.lower() in result.stderr.lower() for keyword in gui_keywords)
            
            if has_gui_error:
                print("[WARNING] 检测到GUI相关错误!")
            else:
                print("[SUCCESS] 无GUI相关错误")
                
        except subprocess.TimeoutExpired:
            print(f"[TIMEOUT] {interface_num}号接口测试超时")
        except FileNotFoundError:
            print(f"[ERROR] Python解释器或脚本文件未找到")
        except Exception as e:
            print(f"[ERROR] 测试异常: {e}")
    
    print("\n=== 测试完成 ===")


def check_environment():
    """检查环境配置"""
    print("=== 环境检查 ===\n")
    
    print(f"Python版本: {sys.version}")
    print(f"Python可执行文件: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查关键文件
    key_files = [
        "interface_launcher.py",
        "interface_01_sendPeInfo.py", 
        "interface_02_syncApplyItem.py",
        "interface_03_deptInfo.py",
        "config.py",
        "gui_main.py"
    ]
    
    print("\n文件检查:")
    for file_name in key_files:
        exists = os.path.exists(file_name)
        status = "[OK] 存在" if exists else "[MISSING] 不存在"
        print(f"  {file_name}: {status}")
    
    # 检查环境变量
    print("\n环境变量检查:")
    env_vars = ["GUI_SUBPROCESS", "TIANJIAN_NO_GUI", "NO_GUI_MODE", "PYTHONPATH"]
    for var in env_vars:
        value = os.environ.get(var, "未设置")
        print(f"  {var}: {value}")
    
    print()


if __name__ == '__main__':
    try:
        check_environment()
        test_interface_launcher()
        
        # 等待用户输入（便于查看结果）
        input("\n按Enter键继续...")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试失败: {e}")
        sys.exit(1)