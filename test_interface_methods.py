#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试接口方法调用是否正确
"""

from config import Config

def test_interface_13():
    """测试13号接口方法"""
    print("测试13号接口...")
    
    try:
        from interface_13_updatePeStatus import TianjianInterface13
        interface = TianjianInterface13(Config.get_tianjian_api_config())
        
        # 测试数据
        test_data = {
            "nodeType": "3",
            "timestamp": "2025-01-24 10:30:00",
            "doUser": {
                "code": "DOCTOR001",
                "name": "张医生"
            },
            "peNo": "PE202501010001"
        }
        
        # 检查方法是否存在
        if hasattr(interface, 'update_pe_status_service'):
            print("[OK] update_pe_status_service 方法存在")
            try:
                result = interface.update_pe_status_service(test_data)
                print(f"[OK] update_pe_status_service 调用成功: {result.get('code', 'N/A')}")
            except Exception as e:
                print(f"[FAIL] update_pe_status_service 调用失败: {e}")
        else:
            print("[FAIL] update_pe_status_service 方法不存在")
            
        if hasattr(interface, 'update_pe_status'):
            print("[WARN] update_pe_status 旧方法存在")
        
    except Exception as e:
        print(f"[FAIL] 13号接口测试失败: {e}")

def test_interface_14():
    """测试14号接口方法"""
    print("\n测试14号接口...")
    
    try:
        from interface_14_markAbnormal import TianjianInterface14
        interface = TianjianInterface14(Config.get_tianjian_api_config())
        
        # 测试数据
        test_data = {
            "abnormalList": [
                {
                    "peNo": "PE202501010001",
                    "abnormalType": "URGENT",
                    "abnormalContent": "血压异常偏高",
                    "deptCode": "DEPT001",
                    "doctorCode": "DOCTOR001",
                    "remark": "需要立即复查"
                }
            ]
        }
        
        # 检查方法是否存在
        if hasattr(interface, 'mark_abnormal_service'):
            print("[OK] mark_abnormal_service 方法存在")
            try:
                result = interface.mark_abnormal_service(test_data)
                print(f"[OK] mark_abnormal_service 调用成功: {result.get('code', 'N/A')}")
            except Exception as e:
                print(f"[FAIL] mark_abnormal_service 调用失败: {e}")
        else:
            print("[FAIL] mark_abnormal_service 方法不存在")
            
        if hasattr(interface, 'mark_abnormal'):
            print("[WARN] mark_abnormal 旧方法存在")
        
    except Exception as e:
        print(f"[FAIL] 14号接口测试失败: {e}")

def test_interface_15():
    """测试15号接口方法"""
    print("\n测试15号接口...")
    
    try:
        from interface_15_returnDept import TianjianInterface15
        interface = TianjianInterface15(Config.get_tianjian_api_config())
        
        # 测试数据
        test_data = {
            "returnList": [
                {
                    "peNo": "PE202501010001",
                    "deptCode": "DEPT001",
                    "returnReason": "INCOMPLETE",
                    "returnDoctorCode": "DOCTOR001",
                    "returnRemark": "检查项目不完整"
                }
            ]
        }
        
        # 检查方法是否存在
        if hasattr(interface, 'return_dept_service'):
            print("[OK] return_dept_service 方法存在")
            try:
                result = interface.return_dept_service(test_data)
                print(f"[OK] return_dept_service 调用成功: {result.get('code', 'N/A')}")
            except Exception as e:
                print(f"[FAIL] return_dept_service 调用失败: {e}")
        else:
            print("[FAIL] return_dept_service 方法不存在")
            
        if hasattr(interface, 'return_dept'):
            print("[WARN] return_dept 旧方法存在")
        
    except Exception as e:
        print(f"[FAIL] 15号接口测试失败: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("测试12-15号接口方法调用")
    print("=" * 60)
    
    test_interface_13()
    test_interface_14() 
    test_interface_15()
    
    print("\n=" * 60)
    print("测试完成")
    print("=" * 60)