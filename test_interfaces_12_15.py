#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试12-15号接口综合功能
包含：
- 12号接口：主检锁定与解锁
- 13号接口：体检状态更新
- 14号接口：重要异常标注
- 15号接口：分科退回
"""

import json
import requests
import time
from datetime import datetime

def test_interface_12():
    """测试12号接口 - 主检锁定与解锁"""
    print("\n" + "="*60)
    print("🔒 测试12号接口 - 主检锁定与解锁")
    print("="*60)
    
    url = "http://localhost:5007/dx/inter/lockPeInfo"
    
    # 测试锁定
    lock_data = {
        "operator": "ADMIN001",
        "peInfoList": [
            {
                "accountId": "DO001",
                "currentNodeType": 3,
                "force": False,
                "operationType": 2,  # 锁定
                "peNo": "5000003",
                "shopCode": "09"  # 门店编码09  
            }
        ]       
    }
    
    print("\n🔒 测试锁定操作")
    print("请求数据:", json.dumps(lock_data, ensure_ascii=False, indent=2))
    
    try:
        response = requests.post(url, json=lock_data, timeout=30)
        result = response.json()
        print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        return result.get('code') == 0
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_interface_13():
    """测试13号接口 - 体检状态更新"""
    print("\n" + "="*60)
    print("📋 测试13号接口 - 体检状态更新")
    print("="*60)
    
    url = "http://localhost:5007/dx/inter/updatePeStatus"
    
    update_data = {
        "nodeType": "3",  # 总检
        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "doUser": {
            "code": "DOCTOR001",
            "name": "张医生"
        },
        "peNo": "5000003",
        "cshopcode": "09"  # 门店编码09
    }
    
    print("\n📋 测试状态更新操作")
    print("请求数据:", json.dumps(update_data, ensure_ascii=False, indent=2))
    
    try:
        response = requests.post(url, json=update_data, timeout=30)
        result = response.json()
        print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        return result.get('code') == 0
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_interface_14():
    """测试14号接口 - 重要异常标注"""
    print("\n" + "="*60)
    print("⚠️ 测试14号接口 - 重要异常标注")
    print("="*60)
    
    url = "http://localhost:5007/dx/inter/markAbnormal"
    
    abnormal_data = {
        "abnormalList": [
            {
                "peNo": "5000003",  # 测试卡号
                "abnormalType": "URGENT",
                "abnormalContent": "血压异常偏高",
                "deptCode": "09DEPT001",  # 门店编号09
                "doctorCode": "09DOCTOR001",
                "remark": "需要立即复查"
            },
            {
                "peNo": "5000006",  # 测试卡号
                "abnormalType": "IMPORTANT",
                "abnormalContent": "心电图异常",
                "deptCode": "09DEPT002",
                "doctorCode": "09DOCTOR002",
                "remark": "建议专科检查"
            }
        ]
    }
    
    print("\n⚠️ 测试异常标注操作")
    print("请求数据:", json.dumps(abnormal_data, ensure_ascii=False, indent=2))
    
    try:
        response = requests.post(url, json=abnormal_data, timeout=30)
        result = response.json()
        print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        return result.get('code') == 0
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_interface_15():
    """测试15号接口 - 分科退回"""
    print("\n" + "="*60)
    print("↩️ 测试15号接口 - 分科退回")
    print("="*60)
    
    url = "http://localhost:5007/dx/inter/returnDept"
    
    return_data = {
        "returnList": [
            {
                "peNo": "5000003",  # 测试卡号
                "deptCode": "09DEPT001",  # 门店编号09
                "returnReason": "INCOMPLETE",
                "returnDoctorCode": "09DOCTOR001",
                "returnRemark": "检查项目不完整，需要重新检验"
            },
            {
                "peNo": "5000006",  # 测试卡号
                "deptCode": "09DEPT002", 
                "returnReason": "ERROR",
                "returnDoctorCode": "09DOCTOR002",
                "returnRemark": "数据录入有误，需要更正"
            }
        ]
    }
    
    print("\n↩️ 测试分科退回操作")
    print("请求数据:", json.dumps(return_data, ensure_ascii=False, indent=2))
    
    try:
        response = requests.post(url, json=return_data, timeout=30)
        result = response.json()
        print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        return result.get('code') == 0
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_parameter_validation():
    """测试参数验证功能"""
    print("\n" + "="*60)
    print("🔍 测试参数验证功能")
    print("="*60)
    
    # 测试12号接口参数验证
    print("\n🔒 测试12号接口参数验证")
    url_12 = "http://localhost:5007/dx/inter/lockPeInfo"
    invalid_data_12 = {"operator": "", "peInfoList": []}
    
    try:
        response = requests.post(url_12, json=invalid_data_12, timeout=30)
        result = response.json()
        if result.get('code') != 0:
            print("✅ 12号接口参数验证正常")
        else:
            print("❌ 12号接口参数验证失效")
    except Exception as e:
        print(f"❌ 12号接口验证测试失败: {e}")
    
    # 测试13号接口参数验证
    print("\n📋 测试13号接口参数验证")
    url_13 = "http://localhost:5007/dx/inter/updatePeStatus"
    invalid_data_13 = {"peNo": "", "nodeType": ""}
    
    try:
        response = requests.post(url_13, json=invalid_data_13, timeout=30)
        result = response.json()
        if result.get('code') != 0:
            print("✅ 13号接口参数验证正常")
        else:
            print("❌ 13号接口参数验证失效")
    except Exception as e:
        print(f"❌ 13号接口验证测试失败: {e}")

def main():
    """主测试函数"""
    print("天健云12-15号接口综合测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n请确保GUI服务已启动 (python gui_main.py)")
    print("等待3秒后开始测试...")
    time.sleep(3)
    
    # 先检查服务状态
    print("\n🔍 检查服务状态")
    try:
        response = requests.get("http://localhost:5007/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            interfaces = health_data.get('interfaces', {})
            
            required_interfaces = ['12', '13', '14', '15']
            missing = [i for i in required_interfaces if i not in interfaces]
            
            if missing:
                print(f"❌ 缺少接口: {', '.join(missing)}")
                print("请确保服务完全启动")
                return
            else:
                print("✅ 所有12-15号接口已注册")
        else:
            print(f"❌ 服务健康检查失败: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 无法连接到服务: {e}")
        return
    
    # 执行测试
    results = []
    
    results.append(("12号接口", test_interface_12()))
    results.append(("13号接口", test_interface_13())) 
    results.append(("14号接口", test_interface_14()))
    results.append(("15号接口", test_interface_15()))
    
    # 测试参数验证
    test_parameter_validation()
    
    # 总结
    print("\n" + "="*60)
    print("📊 测试结果总结")
    print("="*60)
    
    success_count = 0
    for interface, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{interface}: {status}")
        if success:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(results)} 个接口测试通过")
    
    if success_count == len(results):
        print("🎉 所有接口测试通过！")
    else:
        print("⚠️ 部分接口测试失败，请检查日志")
    
    print(f"\n测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()