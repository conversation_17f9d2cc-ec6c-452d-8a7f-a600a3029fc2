#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试本地15号接口实现
"""

import json
import requests
from datetime import datetime

def test_local_dept_return():
    """测试本地分科退回接口"""
    print("[TEST] 测试本地15号接口 - 分科退回接收端")
    print("=" * 50)
    
    # 构建测试数据
    test_data = {
        "peNo": "5000003",
        "markDoctor": "DOC001",
        "errorItem": "ITEM001",
        "returnDept": {
            "code": "DEPT001",
            "name": "内科"
        },
        "receiveDoctor": {
            "code": "DOC002",
            "name": "李医生"
        },
        "remark": "检查结果需要重新确认",
        "currentNodeType": 2
    }
    
    # 本地服务URL
    local_url = "http://localhost:5007/dx/inter/returnDept"
    
    headers = {
        'Content-Type': 'application/json'
    }
    
    print(f"[INFO] 请求URL: {local_url}")
    print(f"[INFO] 请求数据:")
    print(json.dumps(test_data, ensure_ascii=False, indent=2))
    
    try:
        print(f"\n[SEND] 发送分科退回请求到本地服务...")
        response = requests.post(
            url=local_url,
            headers=headers,
            json=test_data,
            timeout=30
        )
        
        print(f"[RESP] HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                print(f"[RESP] 响应数据:")
                print(json.dumps(response_data, ensure_ascii=False, indent=2))
                
                if response_data.get('code') == 0:
                    print(f"[OK] 分科退回处理成功: {response_data.get('msg', '')}")
                else:
                    print(f"[FAIL] 分科退回处理失败: {response_data.get('msg', '')}")
                    
            except json.JSONDecodeError:
                print(f"[RESP] 响应文本: {response.text}")
        else:
            print(f"[FAIL] HTTP请求失败: {response.status_code}")
            print(f"[RESP] 响应文本: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print(f"[ERROR] 连接失败 - 请确保 gui_main.py 已运行并且5007端口已启动")
    except requests.exceptions.RequestException as e:
        print(f"[ERROR] 请求异常: {e}")
    
    print(f"\n[DONE] 测试完成")

if __name__ == "__main__":
    test_local_dept_return()