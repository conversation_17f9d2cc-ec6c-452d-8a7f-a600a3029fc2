#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试支持多门店的15号接口
"""

import json
import requests
from datetime import datetime

def test_multi_shop_interface():
    """测试支持多门店的分科退回接口"""
    print("[TEST] 测试本地15号接口 - 多门店分科退回")
    print("=" * 60)
    
    # 测试数据（包含shopCode）
    test_data = {
        "peNo": "5000003",
        "markDoctor": "DOC001",
        "errorItem": "ITEM001",
        "returnDept": {
            "code": "DEPT001",
            "name": "内科"
        },
        "receiveDoctor": {
            "code": "DOC002",
            "name": "李医生"
        },
        "remark": "检查结果需要重新确认",
        "currentNodeType": 2,
        "shopCode": "09"  # 关键：门店编码
    }
    
    # 本地服务URL
    local_url = "http://localhost:5007/dx/inter/returnDept"
    
    headers = {
        'Content-Type': 'application/json'
    }
    
    print(f"[INFO] 请求URL: {local_url}")
    print(f"[INFO] 测试门店编码: {test_data['shopCode']}")
    print(f"[INFO] 请求数据:")
    print(json.dumps(test_data, ensure_ascii=False, indent=2))
    
    try:
        print(f"\n[SEND] 发送分科退回请求到本地服务...")
        response = requests.post(
            url=local_url,
            headers=headers,
            json=test_data,
            timeout=30
        )
        
        print(f"[RESP] HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                print(f"[RESP] 响应数据:")
                print(json.dumps(response_data, ensure_ascii=False, indent=2))
                
                if response_data.get('code') == 0:
                    print(f"\n[OK] 分科退回处理成功")
                    data = response_data.get('data', {})
                    if 'shopCode' in data:
                        print(f"   门店编码: {data['shopCode']}")
                    if 'returnId' in data:
                        print(f"   记录ID: {data['returnId']}")
                    if 'note' in data:
                        print(f"   处理说明: {data['note']}")
                else:
                    print(f"\n[FAIL] 分科退回处理失败: {response_data.get('msg', '')}")
                    
            except json.JSONDecodeError:
                print(f"[RESP] 响应文本: {response.text}")
        else:
            print(f"[FAIL] HTTP请求失败: {response.status_code}")
            print(f"[RESP] 响应文本: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print(f"[ERROR] 连接失败 - 请确保 gui_main.py 已运行并且5007端口已启动")
    except requests.exceptions.RequestException as e:
        print(f"[ERROR] 请求异常: {e}")
    
    print(f"\n[DONE] 测试完成")

def test_batch_multi_shop():
    """测试批量多门店分科退回"""
    print("\n" + "=" * 60)
    print("[TEST] 测试批量多门店分科退回")
    
    # 批量测试数据
    batch_data = {
        "returnList": [
            {
                "peNo": "5000003",
                "markDoctor": "DOC001",
                "errorItem": "ITEM001",
                "returnDept": {"code": "DEPT001", "name": "内科"},
                "receiveDoctor": {"code": "DOC002", "name": "李医生"},
                "remark": "检查结果需要重新确认",
                "currentNodeType": 2,
                "shopCode": "09"
            },
            {
                "peNo": "5000004", 
                "markDoctor": "DOC003",
                "errorItem": "ITEM002",
                "returnDept": {"code": "DEPT002", "name": "外科"},
                "receiveDoctor": {"code": "DOC004", "name": "王医生"},
                "remark": "影像资料不清晰",
                "currentNodeType": 3,
                "shopCode": "09"
            }
        ]
    }
    
    local_url = "http://localhost:5007/dx/inter/returnDept"
    headers = {'Content-Type': 'application/json'}
    
    print(f"[INFO] 批量数据条数: {len(batch_data['returnList'])}")
    
    try:
        response = requests.post(
            url=local_url,
            headers=headers,
            json=batch_data,
            timeout=30
        )
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"[RESP] 批量处理结果:")
            print(json.dumps(response_data, ensure_ascii=False, indent=2))
            
            if response_data.get('code') == 0:
                print(f"\n[OK] 批量分科退回处理成功")
                data = response_data.get('data', {})
                print(f"   总数: {data.get('total', 0)}")
                print(f"   成功: {data.get('success', 0)}")
                print(f"   失败: {data.get('error', 0)}")
        else:
            print(f"[FAIL] 批量处理失败: {response.status_code}")
            
    except Exception as e:
        print(f"[ERROR] 批量测试异常: {e}")

if __name__ == "__main__":
    # 测试单条记录
    test_multi_shop_interface()
    
    # 测试批量记录
    test_batch_multi_shop()