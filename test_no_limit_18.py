#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试18号接口移除数量限制
"""

import json
import requests
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_no_limit():
    """测试移除限制后的18号接口"""
    print("[TEST] 测试18号接口移除数量限制")
    print("=" * 50)
    
    base_url = "http://localhost:5007"
    
    # 测试数据
    test_data = {"id": "", "shopcode": "08"}
    
    try:
        # 检查GUI服务器
        try:
            health_response = requests.get(f"{base_url}/health", timeout=5)
            if health_response.status_code != 200:
                raise Exception("服务器未正常响应")
        except:
            print("[错误] GUI服务器未运行")
            print("[信息] 请运行: python gui_main.py")
            print("\n[备选] 直接测试接口文件...")
            return test_interface_directly()
        
        print(f"[信息] GUI服务器正在运行")
        print(f"[请求] {json.dumps(test_data, ensure_ascii=False)}")
        
        # 发送测试请求
        response = requests.post(
            f"{base_url}/dx/inter/getDoctorInfo",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"[响应] 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            result_code = result.get('code', 'N/A')
            result_msg = result.get('msg', 'N/A')
            data_count = len(result.get('data', []))
            
            print(f"[响应] 返回码: {result_code}")
            print(f"[响应] 消息: {result_msg}")
            print(f"[响应] 数据条数: {data_count}")
            
            if result_code == 0:
                if data_count > 0:
                    print(f"\n[成功] 查询成功，返回 {data_count} 条医生信息（无数量限制）")
                    
                    # 显示前几条记录的示例
                    data = result.get('data', [])
                    print(f"[示例] 前3条医生信息:")
                    for i, doctor in enumerate(data[:3], 1):
                        name = doctor.get('name', 'N/A')
                        account_code = doctor.get('accountCode', 'N/A')
                        print(f"  医生{i}: {name} ({account_code})")
                    
                    if data_count > 3:
                        print(f"  ... 共 {data_count} 条记录")
                    
                    # 检查是否有明显的限制（比如正好100条或其他固定数量）
                    if data_count in [50, 100, 200, 500, 1000]:
                        print(f"[注意] 返回数量为 {data_count}，请确认是否仍有限制")
                    else:
                        print(f"[确认] 数量限制已移除，返回了所有符合条件的记录")
                        
                else:
                    print(f"[信息] 查询成功但没有找到医生信息")
            else:
                print(f"[失败] 查询失败: {result_msg}")
            
            return True
        else:
            print(f"[错误] HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"[异常] {str(e)}")
        return False

def test_interface_directly():
    """直接测试接口文件"""
    print(f"\n{'-' * 30}")
    print("[DIRECT TEST] 直接测试接口文件")
    
    try:
        from interface_18_getDoctorInfo import TianjianInterface18
        from config import Config
        
        # 获取API配置
        api_config = Config.get_tianjian_api_config()
        
        # 创建接口实例
        interface = TianjianInterface18(api_config)
        
        # 测试请求
        test_data = {"id": "", "shopcode": "08"}
        result = interface.get_doctor_info(test_data)
        
        print(f"[直接测试] 返回码: {result.get('code', 'N/A')}")
        print(f"[直接测试] 消息: {result.get('msg', 'N/A')}")
        data_count = len(result.get('data', []))
        print(f"[直接测试] 数据条数: {data_count}")
        
        if result.get('code') == 0 and data_count > 0:
            print(f"[成功] 直接测试成功，返回 {data_count} 条记录（无限制）")
            return True
        elif result.get('code') == 0:
            print(f"[信息] 直接测试成功但无数据")
            return True
        else:
            print(f"[失败] 直接测试失败: {result.get('msg', '')}")
            return False
            
    except Exception as e:
        print(f"[异常] 直接测试异常: {str(e)}")
        return False

def test_with_test_mode():
    """测试模式验证"""
    print(f"\n{'-' * 30}")
    print("[TEST MODE] 测试模式验证")
    
    try:
        from interface_18_getDoctorInfo import TianjianInterface18
        from config import Config
        
        api_config = Config.get_tianjian_api_config()
        interface = TianjianInterface18(api_config)
        
        # 测试模式调用
        result = interface.query_doctor_info(
            doctor_id="",
            hospital_code="08",
            test_mode=True
        )
        
        print(f"[测试模式] 返回码: {result.get('code', 'N/A')}")
        data_count = len(result.get('data', []))
        print(f"[测试模式] 数据条数: {data_count}")
        
        if result.get('code') == 0:
            print(f"[成功] 测试模式正常工作")
            return True
        else:
            print(f"[失败] 测试模式失败")
            return False
            
    except Exception as e:
        print(f"[异常] 测试模式异常: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始测试18号接口移除数量限制...")
    
    success_count = 0
    total_tests = 3
    
    # 测试1: GUI接口
    if test_no_limit():
        success_count += 1
    
    # 测试2: 直接接口文件测试
    if test_interface_directly():
        success_count += 1
    
    # 测试3: 测试模式
    if test_with_test_mode():
        success_count += 1
    
    print(f"\n" + "=" * 50)
    print(f"[总结] 测试结果: {success_count}/{total_tests} 项测试通过")
    
    if success_count >= 2:
        print("[成功] 18号接口数量限制已成功移除")
        print("[信息] 现在将返回所有符合条件的医生信息记录")
    else:
        print("[警告] 部分测试失败，请检查修改是否正确")
    
    print(f"\n[修改总结]")
    print(f"✓ 移除了 query_doctor_info 中的 limit=100 限制")
    print(f"✓ 移除了 get_doctor_info_from_db 中的 TOP 限制")
    print(f"✓ 移除了测试函数中的 limit 参数")
    print(f"✓ 现在返回所有符合条件的医生信息记录")