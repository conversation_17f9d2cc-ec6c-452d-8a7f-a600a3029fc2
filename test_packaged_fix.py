#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试打包环境修复效果
验证GUI不会重复启动，接口可以正常调用
"""

import sys
import os
import time


def simulate_packaged_environment():
    """模拟打包环境"""
    # 模拟PyInstaller的打包环境标记
    sys.frozen = True
    sys._MEIPASS = os.getcwd()
    
    # 设置环境变量
    os.environ['GUI_SUBPROCESS'] = '1'
    os.environ['TIANJIAN_NO_GUI'] = '1'
    os.environ['NO_GUI_MODE'] = '1'
    
    print("[SIMULATION] 模拟打包环境设置完成")
    print(f"[SIMULATION] sys.frozen = {getattr(sys, 'frozen', False)}")
    print(f"[SIMULATION] sys._MEIPASS = {getattr(sys, '_MEIPASS', 'NOT_SET')}")


def test_interface_import():
    """测试接口模块导入"""
    print("\n=== 测试接口模块导入 ===")
    
    interfaces = [
        ("01", "interface_01_sendPeInfo"),
        ("02", "interface_02_syncApplyItem"),
        ("03", "interface_03_deptInfo"),
        ("04", "interface_04_syncUser"),
        ("05", "interface_05_syncDept"),
        ("06", "interface_06_syncDict")
    ]
    
    for interface_num, module_name in interfaces:
        try:
            module = __import__(module_name)
            print(f"[{interface_num}] [OK] {module_name} 导入成功")
        except ImportError as e:
            print(f"[{interface_num}] [ERROR] {module_name} 导入失败: {e}")
        except Exception as e:
            print(f"[{interface_num}] [WARN] {module_name} 导入异常: {e}")


def test_interface_02_direct_call():
    """测试02号接口直接调用"""
    print("\n=== 测试02号接口直接调用 ===")
    
    try:
        from interface_02_syncApplyItem import TianjianInterface02
        
        # 创建接口实例
        interface = TianjianInterface02()
        print("[02] [OK] 接口实例创建成功")
        
        # 测试模式调用
        result = interface.sync_apply_items(
            limit=2,
            test_mode=True,
            batch_size=1,
            verbose_message=False
        )
        
        print(f"[02] [OK] 接口调用完成")
        print(f"[02] 结果: {result.get('message', '无消息')}")
        print(f"[02] 成功: {result.get('success', False)}")
        
        return True
        
    except Exception as e:
        print(f"[02] [ERROR] 接口调用失败: {e}")
        return False


def test_gui_import_safety():
    """测试GUI导入安全性"""
    print("\n=== 测试GUI导入安全性 ===")
    
    # 检查GUI模块是否被禁用
    gui_modules = ['tkinter', 'PySide6', 'PyQt5', 'gui_main']
    
    for module_name in gui_modules:
        if module_name in sys.modules:
            module_value = sys.modules[module_name]
            if module_value is None:
                print(f"[SAFE] [OK] {module_name} 已被禁用")
            else:
                print(f"[WARN] [WARN] {module_name} 仍然可用: {module_value}")
        else:
            print(f"[INFO] - {module_name} 未在sys.modules中")


def main():
    print("=== 打包环境GUI防护测试 ===\n")
    
    # 步骤1: 模拟打包环境
    simulate_packaged_environment()
    
    # 步骤2: 测试接口导入
    test_interface_import()
    
    # 步骤3: 测试GUI安全性
    test_gui_import_safety()
    
    # 步骤4: 测试接口直接调用
    success = test_interface_02_direct_call()
    
    # 总结
    print("\n=== 测试总结 ===")
    if success:
        print("[SUCCESS] 所有测试通过 - 打包环境GUI防护修复成功！")
        print("[SUCCESS] 不会启动新的GUI窗口")
        print("[SUCCESS] 接口功能正常工作")
    else:
        print("[FAILED] 测试失败 - 需要进一步调试")
    
    return success


if __name__ == '__main__':
    try:
        success = main()
        input("\n按Enter键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试异常: {e}")
        sys.exit(1)