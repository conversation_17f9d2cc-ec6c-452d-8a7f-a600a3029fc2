{"summary": {"total_tests": 7, "passed_tests": 7, "failed_tests": 0, "success_rate": "100.0%"}, "test_results": [{"test_name": "查询所有图片", "success": true, "message": "查询成功，返回0张图片", "timestamp": "2025-07-24 23:56:50", "data": {"code": 0, "msg": "未找到相关图片", "data": []}}, {"test_name": "按科室查询图片", "success": true, "message": "科室查询成功，返回0张图片", "timestamp": "2025-07-24 23:56:50", "data": {"code": 0, "msg": "未找到相关图片", "data": []}}, {"test_name": "按项目查询图片", "success": true, "message": "项目查询成功，返回0张图片", "timestamp": "2025-07-24 23:56:50", "data": {"code": 0, "msg": "未找到相关图片", "data": []}}, {"test_name": "组合条件查询", "success": true, "message": "组合查询成功，返回0张图片", "timestamp": "2025-07-24 23:56:50", "data": {"code": 0, "msg": "未找到相关图片", "data": []}}, {"test_name": "无效卡号测试", "success": true, "message": "正确处理无效卡号", "timestamp": "2025-07-24 23:56:50", "data": {"code": -1, "msg": "卡号 9999999 对应的客户编码不存在", "data": []}}, {"test_name": "空请求测试", "success": true, "message": "正确处理空卡号请求", "timestamp": "2025-07-24 23:56:50", "data": {"code": -1, "msg": "peNo不能为空", "data": []}}, {"test_name": "GUI服务集成", "success": true, "message": "GUI服务调用成功，状态码: 200", "timestamp": "2025-07-24 23:56:53", "data": {"code": 0, "data": [], "msg": "未找到相关图片"}}]}