#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试门店编码过滤条件的修改
从精确匹配改为包含匹配
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_shop_code_filter():
    """测试门店编码过滤条件"""
    print("=" * 80)
    print("测试门店编码过滤条件修改")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    try:
        from interface_02_syncApplyItem import TianjianInterface02
        from config import Config
        
        # 获取API配置
        api_config = Config.get_tianjian_api_config()
        print(f"使用API配置: {api_config['base_url']}")
        
        # 创建接口实例
        interface = TianjianInterface02(api_config)
        
        # 获取门店编码
        shop_code = interface.org_config.get('shop_code', '08')
        print(f"当前门店编码: {shop_code}")
        
        print("\n" + "=" * 60)
        print("测试1：获取少量数据验证过滤条件")
        print("=" * 60)
        
        # 获取少量数据测试
        apply_items = interface.get_apply_items_data(limit=5)
        
        if apply_items:
            print(f"✅ 成功获取 {len(apply_items)} 个申请项目")
            
            print("\n前3个申请项目信息:")
            for i, item in enumerate(apply_items[:3], 1):
                print(f"  {i}. {item['applyItemId']} - {item['applyItemName']}")
                print(f"     科室ID: {item['deptId']}, 价格: {item['price']}")
            
            # 检查ID前缀
            all_have_prefix = all(item['applyItemId'].startswith(f'{shop_code}_') for item in apply_items)
            if all_have_prefix:
                print(f"✅ 所有申请项目ID都包含门店编码前缀 '{shop_code}_'")
            else:
                print(f"⚠️  部分申请项目ID不包含门店编码前缀 '{shop_code}_'")
                for item in apply_items:
                    if not item['applyItemId'].startswith(f'{shop_code}_'):
                        print(f"     - {item['applyItemId']} - {item['applyItemName']}")
        else:
            print("❌ 未获取到任何申请项目数据")
            return False
        
        print("\n" + "=" * 60)
        print("测试2：检查过滤条件的SQL语句")
        print("=" * 60)
        
        # 显示实际使用的SQL条件
        print("过滤条件说明:")
        print(f"  - 精确匹配（修改前）: cPrivateShopCodes = '{shop_code}'")
        print(f"  - 包含匹配（修改后）: cPrivateShopCodes LIKE '%{shop_code}%'")
        print(f"  - 空值处理: cPrivateShopCodes IS NULL OR cPrivateShopCodes = ''")
        
        print("\n包含匹配的优势:")
        print("  ✅ 可以匹配复合门店编码，如 '08,09' 包含 '08'")
        print("  ✅ 可以匹配带前后缀的编码，如 'SHOP08' 包含 '08'")
        print("  ✅ 更灵活的匹配规则，适应不同的数据格式")
        
        print("\n" + "=" * 60)
        print("测试3：对比数据量变化")
        print("=" * 60)
        
        # 获取更多数据查看总量
        all_items = interface.get_apply_items_data(limit=None)
        print(f"当前过滤条件下的总数据量: {len(all_items)} 个申请项目")
        
        if len(all_items) > 0:
            print("✅ 包含匹配过滤条件工作正常")
            
            # 分析数据分布
            dept_count = {}
            for item in all_items:
                dept_id = item['deptId']
                dept_count[dept_id] = dept_count.get(dept_id, 0) + 1
            
            print(f"\n科室分布（前5个）:")
            sorted_depts = sorted(dept_count.items(), key=lambda x: x[1], reverse=True)
            for dept_id, count in sorted_depts[:5]:
                print(f"  - {dept_id}: {count} 个申请项目")
        else:
            print("❌ 包含匹配过滤条件可能有问题")
            return False
        
        print("\n" + "=" * 80)
        print("🎉 门店编码过滤条件修改测试完成！")
        print("✅ 从精确匹配改为包含匹配成功")
        print(f"✅ 当前门店 '{shop_code}' 的数据量: {len(all_items)} 条")
        print("✅ 过滤条件工作正常，可以获取相关数据")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    success = test_shop_code_filter()
    
    if success:
        print("\n🎯 测试结论:")
        print("门店编码过滤条件已成功从精确匹配改为包含匹配")
        print("现在可以匹配更多包含机构ID的数据记录")
    else:
        print("\n⚠️  测试失败，请检查配置和数据库连接")


if __name__ == "__main__":
    main()
