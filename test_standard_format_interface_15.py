#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
严格按照天健云标准格式测试15号接口
"""

import json
import requests

def test_standard_format():
    """测试严格按照天健云标准格式的分科退回接口"""
    print("[TEST] 天健云15号接口 - 标准格式测试")
    print("=" * 50)
    
    # 严格按照标准格式的测试数据
    standard_data = {
        "peNo": "5000003",                    # 体检号
        "markDoctor": "DOC001",               # 发起退回医生账号
        "errorItem": "ITEM001",               # 退回项目编号（可选）
        "returnDept": {                       # 退回科室信息
            "code": "DEPT001",                # 退回科室编号
            "name": "内科"                     # 退回科室名称
        },
        "receiveDoctor": {                    # 接受医生信息
            "code": "DOC002",                 # 接收医生编号(可选)
            "name": "李医生"                   # 接收医生名称(可选)
        },
        "remark": "检查结果需要重新确认",        # 退回原因备注
        "currentNodeType": 2                  # 当前操作节点用于流程控制，1登记,2分科3主检4总检
    }
    
    local_url = "http://localhost:5007/dx/inter/returnDept"
    headers = {'Content-Type': 'application/json'}
    
    print("[INFO] 标准格式请求数据:")
    print(json.dumps(standard_data, ensure_ascii=False, indent=2))
    
    print(f"\n[INFO] currentNodeType={standard_data['currentNodeType']} 表示：分科")
    
    try:
        print(f"\n[SEND] 发送标准格式请求...")
        response = requests.post(
            url=local_url,
            headers=headers,
            json=standard_data,
            timeout=30
        )
        
        print(f"[RESP] HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"[RESP] 响应数据:")
            print(json.dumps(response_data, ensure_ascii=False, indent=2))
            
            if response_data.get('code') == 0:
                print(f"\n[OK] 标准格式分科退回处理成功")
            else:
                print(f"\n[FAIL] 标准格式分科退回处理失败: {response_data.get('msg', '')}")
        else:
            print(f"[FAIL] HTTP请求失败: {response.status_code}")
            print(f"[RESP] 响应内容: {response.text}")
            
    except Exception as e:
        print(f"[ERROR] 请求异常: {e}")

def test_minimal_format():
    """测试最小必需字段格式"""
    print("\n" + "=" * 50)
    print("[TEST] 最小必需字段格式测试")
    
    # 最小必需字段（可选字段为空）
    minimal_data = {
        "peNo": "5000004",                    # 体检号 - 必需
        "markDoctor": "DOC003",               # 发起退回医生账号 - 必需
        "errorItem": "",                      # 退回项目编号（可选）- 空值
        "returnDept": {                       # 退回科室信息 - 必需
            "code": "DEPT002",                # 退回科室编号 - 必需
            "name": "外科"                     # 退回科室名称 - 必需
        },
        "receiveDoctor": {                    # 接受医生信息 - 可选
            "code": "",                       # 接收医生编号(可选) - 空值
            "name": ""                        # 接收医生名称(可选) - 空值
        },
        "remark": "影像资料不清晰，需要重新拍摄",  # 退回原因备注 - 建议填写
        "currentNodeType": 3                  # 当前操作节点：3主检
    }
    
    local_url = "http://localhost:5007/dx/inter/returnDept"
    headers = {'Content-Type': 'application/json'}
    
    print("[INFO] 最小字段请求数据:")
    print(json.dumps(minimal_data, ensure_ascii=False, indent=2))
    
    try:
        response = requests.post(
            url=local_url,
            headers=headers,
            json=minimal_data,
            timeout=30
        )
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"[RESP] 最小字段测试结果:")
            print(json.dumps(response_data, ensure_ascii=False, indent=2))
            
            if response_data.get('code') == 0:
                print(f"\n[OK] 最小字段格式处理成功")
            else:
                print(f"\n[FAIL] 最小字段格式处理失败: {response_data.get('msg', '')}")
        else:
            print(f"[FAIL] 最小字段测试失败: {response.status_code}")
            
    except Exception as e:
        print(f"[ERROR] 最小字段测试异常: {e}")

if __name__ == "__main__":
    # 测试标准格式
    test_standard_format()
    
    # 测试最小必需字段
    test_minimal_format()
    
    print("\n[INFO] 格式验证说明:")
    print("- 我们的接口完全支持天健云标准格式")
    print("- 所有字段定义和数据类型都符合规范")
    print("- 支持可选字段为空值的情况")
    print("- currentNodeType数值含义: 1登记, 2分科, 3主检, 4总检")