# 测试目录说明

本目录包含天健云AI对接系统的所有测试脚本。

## 目录结构

```
tests/
├── README.md                           # 本说明文件
├── validate_all_tests.py               # 验证所有测试文件的导入和基本功能
├── run_all_tests.py                    # 综合测试运行器，自动发现并运行所有测试
│
├── 核心测试文件 (优先运行)
├── test_db_connection.py               # 数据库连接测试
├── test_api_connection.py              # API连接测试
├── test_config.py                      # 配置测试
├── test_interfaces_07_10.py            # 07-10号接口测试
├── test_interfaces_11_21.py            # 11-21号接口测试
├── tianjian_interface_test_suite.py    # 接口测试套件
│
├── 接口专项测试
├── test_01_*.py                        # 01号接口相关测试
├── test_02_*.py                        # 02号接口相关测试
├── test_apply_item_interface.py        # 申请项目接口测试
├── test_interfaces_08_11.py            # 08-11号接口测试
│
├── GUI测试
├── test_gui.py                         # GUI功能测试
├── test_gui_button.py                  # GUI按钮测试
├── test_gui_save.py                    # GUI保存功能测试
├── test_tkinter.py                     # Tkinter GUI测试
│
├── 数据库和配置测试
├── test_affirmdate_query.py            # 确认日期查询测试
├── test_date_field_comparison.py       # 日期字段比较测试
├── test_multi_org_switch.py            # 多机构切换测试
├── test_org_*.py                       # 机构配置相关测试
├── test_unified_config.py              # 统一配置测试
│
├── API和网络测试
├── test_api_status_simple.py           # API状态简单测试
├── test_basic_api.py                   # 基础API测试
├── test_nginx_detection.py             # Nginx检测测试
├── test_tianjian_api.py                # 天健云API测试
│
├── 工具和实用测试
├── test_log_function.py                # 日志功能测试
├── test_sync_data.py                   # 同步数据测试
├── test_window_size.py                 # 窗口大小测试
├── real_api_test.py                    # 真实API测试
│
└── 历史版本文件 (重命名保留)
    ├── test_*_root.py                  # 从根目录移动的重名文件
    └── ...
```

## 使用方法

### 1. 验证所有测试文件
```bash
python tests/validate_all_tests.py
```
检查所有测试文件是否能正常导入，发现语法错误和依赖问题。

### 2. 运行综合测试
```bash
python tests/run_all_tests.py
```
自动发现并运行所有测试文件，生成详细的测试报告。

### 3. 运行单个测试
```bash
python tests/test_db_connection.py
python tests/test_api_connection.py
```

### 4. 运行特定类型的测试
```bash
# 接口测试
python tests/test_interfaces_07_10.py
python tests/test_interfaces_11_21.py

# GUI测试
python tests/test_gui.py

# 配置测试
python tests/test_unified_config.py
```

## 测试文件说明

- **核心测试文件**: 系统最重要的功能测试，优先运行
- **接口专项测试**: 针对特定接口的详细测试
- **GUI测试**: 图形界面相关功能测试
- **数据库和配置测试**: 数据库连接、配置管理等测试
- **API和网络测试**: 网络连接、API调用等测试
- **工具和实用测试**: 辅助功能和工具类测试

## 注意事项

1. 运行测试前请确保数据库连接配置正确
2. API测试需要网络连接和有效的API密钥
3. GUI测试可能需要图形界面环境
4. 某些测试可能需要特定的测试数据

## 测试报告

运行 `run_all_tests.py` 会生成详细的测试报告，包括：
- 测试统计信息
- 成功/失败的测试列表
- 错误详情和建议
- 测试报告文件 (test_report_YYYYMMDD_HHMMSS.txt)
