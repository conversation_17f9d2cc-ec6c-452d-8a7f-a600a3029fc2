#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云接口综合测试运行器
统一执行所有接口测试，提供完整的测试报告
"""

import sys
import os
import importlib.util
import traceback
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        self.test_results = {}
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        
    def run_test_module(self, module_name, module_path):
        """运行单个测试模块"""
        print(f"\n{'='*80}")
        print(f"执行测试模块: {module_name}")
        print(f"{'='*80}")
        
        try:
            # 动态导入测试模块
            spec = importlib.util.spec_from_file_location(module_name, module_path)
            module = importlib.util.module_from_spec(spec)
            sys.modules[module_name] = module
            spec.loader.exec_module(module)
            
            # 执行main函数
            if hasattr(module, 'main'):
                result = module.main()
                self.test_results[module_name] = {
                    'success': result,
                    'status': '[OK] 通过' if result else '[FAIL] 失败'
                }
                if result:
                    self.passed_tests += 1
                else:
                    self.failed_tests += 1
            else:
                print(f"[WARN]  模块 {module_name} 没有main函数")
                self.test_results[module_name] = {
                    'success': False,
                    'status': '[WARN]  无main函数'
                }
                self.failed_tests += 1
                
            self.total_tests += 1
            
        except Exception as e:
            print(f"[FAIL] 执行模块 {module_name} 时出现异常: {e}")
            traceback.print_exc()
            self.test_results[module_name] = {
                'success': False,
                'status': f'[FAIL] 异常: {str(e)}'
            }
            self.failed_tests += 1
            self.total_tests += 1
    
    def run_individual_tests(self):
        """运行单独的测试文件"""
        tests_dir = Path(__file__).parent

        # 自动发现所有测试文件
        test_files = []

        # 核心测试文件（优先运行）
        priority_tests = [
            ('test_db_connection.py', 'DB连接测试'),
            ('test_api_connection.py', 'API连接测试'),
            ('test_config.py', '配置测试'),
            ('test_interfaces_07_10.py', '07-10号接口测试'),
            ('test_interfaces_11_21.py', '11-21号接口测试'),
            ('tianjian_interface_test_suite.py', '接口测试套件'),
        ]

        # 添加优先测试文件
        for filename, description in priority_tests:
            test_path = tests_dir / filename
            if test_path.exists():
                test_files.append((filename, description))

        # 自动发现其他测试文件
        all_test_files = list(tests_dir.glob('test_*.py'))
        priority_filenames = [f[0] for f in priority_tests]

        for test_file in sorted(all_test_files):
            filename = test_file.name
            if filename not in priority_filenames and filename != 'run_all_tests.py':
                # 生成描述
                description = filename.replace('test_', '').replace('.py', '').replace('_', ' ').title() + '测试'
                test_files.append((filename, description))

        # 运行所有测试文件
        for filename, description in test_files:
            test_path = tests_dir / filename
            if test_path.exists():
                self.run_test_module(description, str(test_path))
            else:
                print(f"[WARN]  测试文件不存在: {filename}")
    
    def run_system_tests(self):
        """运行系统级别测试"""
        print(f"\n{'='*80}")
        print("系统级别测试")
        print(f"{'='*80}")

        # 简单的系统状态检查（不依赖main.py）
        try:
            print("\n🔍 执行基础系统检查...")

            # 检查核心模块是否可导入
            import config
            import database_service
            import gui_main

            print("[OK] 核心模块导入成功")
            print("[OK] 配置模块正常")
            print("[OK] 数据库服务模块正常")
            print("[OK] GUI主模块正常")

            self.test_results['系统状态检查'] = {
                'success': True,
                'status': '[OK] 完成'
            }
            self.passed_tests += 1
        except Exception as e:
            print(f"[FAIL] 系统状态检查失败: {e}")
            self.test_results['系统状态检查'] = {
                'success': False,
                'status': f'[FAIL] 失败: {str(e)}'
            }
            self.failed_tests += 1

        self.total_tests += 1
    
    def generate_report(self):
        """生成测试报告"""
        print(f"\n{'='*80}")
        print("测试报告总结")
        print(f"{'='*80}")
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总测试数: {self.total_tests}")
        print(f"通过测试: {self.passed_tests}")
        print(f"失败测试: {self.failed_tests}")
        print(f"成功率: {(self.passed_tests/self.total_tests*100):.1f}%" if self.total_tests > 0 else "0%")
        
        print(f"\n详细结果:")
        print("-" * 80)
        for test_name, result in self.test_results.items():
            print(f"{result['status']:<20} {test_name}")
        
        # 生成报告文件
        report_content = self._generate_report_content()
        report_file = Path(__file__).parent / f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            print(f"\n[LIST] 详细测试报告已保存到: {report_file}")
        except Exception as e:
            print(f"[WARN]  保存测试报告失败: {e}")
        
        return self.failed_tests == 0
    
    def _generate_report_content(self):
        """生成报告内容"""
        content = f"""天健云接口系统测试报告
{'='*80}
测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
测试环境: Windows
项目版本: v1.2.0

测试统计:
- 总测试数: {self.total_tests}
- 通过测试: {self.passed_tests}
- 失败测试: {self.failed_tests}
- 成功率: {(self.passed_tests/self.total_tests*100):.1f}%

详细结果:
{'-'*80}
"""
        for test_name, result in self.test_results.items():
            content += f"{result['status']} {test_name}\n"
        
        content += f"""
{'-'*80}
测试总结:
"""
        if self.failed_tests == 0:
            content += """
🎉 所有测试通过！系统状态良好。

已验证功能:
[OK] 数据库连接正常
[OK] API连接正常
[OK] 配置文件有效
[OK] 07-10号接口功能完整
[OK] 11-21号接口功能完整
[OK] 系统集成正常

系统现在支持完整的天健云01-21号接口！
"""
        else:
            content += f"""
[WARN] 发现 {self.failed_tests} 个测试失败，请检查相关模块。

建议措施:
1. 检查数据库连接配置
2. 验证API端点可用性
3. 确认接口参数配置
4. 查看详细错误日志
"""
        
        return content

def main():
    """主函数"""
    print("天健云接口系统 - 综合测试运行器")
    print(f"{'='*80}")
    print("开始执行全面测试...")
    
    runner = TestRunner()
    
    # 运行各类测试
    runner.run_individual_tests()
    runner.run_system_tests()
    
    # 生成测试报告
    success = runner.generate_report()
    
    if success:
        print(f"\n🎉 所有测试通过！系统运行正常。")
        print(f"天健云01-21号接口系统已准备就绪！")
    else:
        print(f"\n[FAIL] 部分测试失败，请查看测试报告了解详情。")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)