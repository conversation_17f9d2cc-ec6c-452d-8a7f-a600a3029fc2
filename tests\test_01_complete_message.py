#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云01号接口完整报文测试
基于real_api_test修改，显示完整的请求和响应报文
"""

import hashlib
import json
import requests
import uuid
from datetime import datetime
from test_config import TestConfig


class TianjianInterface01CompleteTest:
    """完整报文测试类 - 01号接口"""
    
    def __init__(self):
        self.api_config = TestConfig.API_CONFIG
        self.auth_config = TestConfig.API_AUTH_CONFIG
        self.base_url = self.api_config['base_url']
        
    def generate_timestamp(self):
        """生成时间戳 - YYYYmmDDHHMMSS格式"""
        return datetime.now().strftime('%Y%m%d%H%M%S')
    
    def generate_nonce(self):
        """生成UUID"""
        return str(uuid.uuid4())
    
    def generate_signature(self, timestamp):
        """生成MD5签名 - MD5(key+timestamp)"""
        sign_string = self.auth_config['api_key'] + timestamp
        return hashlib.md5(sign_string.encode('utf-8')).hexdigest()
    
    def create_headers(self):
        """创建请求头"""
        timestamp = self.generate_timestamp()
        nonce = self.generate_nonce()
        signature = self.generate_signature(timestamp)
        
        headers = {
            'Content-Type': 'application/json',
            'sign': signature,
            'timestamp': timestamp,
            'nonce': nonce,
            'mic-code': self.auth_config['mic_code'],
            'misc-id': self.auth_config['misc_id']
        }
        
        return headers, timestamp, nonce, signature
    
    def print_separator(self, title: str):
        """打印分隔线"""
        print("\n" + "="*80)
        print(f" {title} ".center(80, "="))
        print("="*80)
    
    def test_01_interface_complete_message(self):
        """测试01号接口并显示完整报文"""
        self.print_separator("🚀 天健云01号接口完整报文测试")
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 接口基本信息
        url = f"{self.base_url}/dx/inter/sendPeInfo"
        headers, timestamp, nonce, signature = self.create_headers()
        
        # 创建测试数据
        pe_data = self.create_test_pe_data()
        
        # 打印请求信息
        self.print_separator("📤 请求基本信息")
        print(f"接口URL: {url}")
        print(f"请求方法: POST")
        print(f"时间戳: {timestamp}")
        print(f"随机数: {nonce}")
        print(f"签名: {signature}")
        print(f"机构代码: {self.auth_config['mic_code']}")
        print(f"系统ID: {self.auth_config['misc_id']}")
        
        # 打印请求头
        self.print_separator("📤 请求头 (Headers)")
        print(json.dumps(headers, indent=2, ensure_ascii=False))
        
        # 打印请求体
        self.print_separator("📤 请求报文 (Request Body)")
        print(json.dumps(pe_data, indent=2, ensure_ascii=False))
        
        try:
            # 发送请求
            response = requests.post(
                url=url,
                headers=headers,
                json=pe_data,
                timeout=30,
                verify=False
            )
            
            # 打印响应基本信息
            self.print_separator("📥 响应基本信息")
            print(f"响应状态码: {response.status_code}")
            print(f"响应时间: {response.elapsed.total_seconds():.3f}s")
            print(f"响应URL: {response.url}")
            
            # 打印响应头
            self.print_separator("📥 响应头 (Response Headers)")
            response_headers = dict(response.headers)
            print(json.dumps(response_headers, indent=2, ensure_ascii=False))
            
            # 打印响应体
            self.print_separator("📥 响应报文 (Response Body)")
            try:
                response_json = response.json()
                print(json.dumps(response_json, indent=2, ensure_ascii=False))
                
                # 分析响应结果
                self.print_separator("📊 响应结果分析")
                if response_json.get('code') == 0:
                    print("✅ 接口调用成功")
                    print(f"📈 响应码: {response_json.get('code')}")
                    print(f"📈 响应消息: {response_json.get('msg', '成功')}")
                    print(f"📈 响应数据: {response_json.get('data', 'null')}")
                    print(f"📈 响应时间戳: {response_json.get('reponseTime', 'N/A')}")
                    return True
                else:
                    print("❌ 接口调用失败")
                    print(f"❌ 错误码: {response_json.get('code')}")
                    print(f"❌ 错误消息: {response_json.get('msg', '未知错误')}")
                    return False
                    
            except json.JSONDecodeError:
                print("❌ 响应不是有效的JSON格式")
                print(f"原始响应: {response.text[:1000]}")
                return False
                
        except Exception as e:
            self.print_separator("❌ 请求异常")
            print(f"请求发生异常: {e}")
            return False
    
    def create_test_pe_data(self):
        """创建测试体检数据"""
        return {
            "peUserInfo": {
                "archiveNo": "TEST2025001",
                "name": "测试用户01",
                "icCode": "510823198705127845",
                "sex": {
                    "code": "1",
                    "name": "男"
                },
                "birthday": "19870512",
                "peno": "TEST2025001",
                "peDate": "2025-07-10 15:30:00",
                "phone": "13800138000",
                "ms": {
                    "code": "married",
                    "name": "已婚"
                },
                "pregnantState": {
                    "code": "unconception",
                    "name": "未怀孕"
                },
                "vipLevel": {
                    "code": "VIP001",
                    "name": "普通会员"
                },
                "medicalType": {
                    "code": "NORMAL",
                    "name": "常规体检"
                },
                "isGroup": False,
                "company": "",
                "workDept": "",
                "teamNo": "",
                "professional": "软件工程师",
                "workAge": "5",
                "peStates": {
                    "code": "1",
                    "name": "登记完成"
                },
                "deptCount": 5,
                "age": 37,
                "deptFinishTime": "2025-07-10 16:00:00",
                "firstCheckFinishTime": "2025-07-10 16:30:00",
                "firstCheckFinishDoctor": {
                    "code": "DOC001",
                    "name": "测试医生"
                },
                "mainCheckFinishTime": "2025-07-10 17:00:00",
                "mainCheckFinishDoctor": {
                    "code": "DOC002",
                    "name": "主检医生"
                },
                "forbidGoCheck": False,
                "reportPrint": False,
                "reportGot": False,
                "replacementInspectionMark": False,
                "applyItemList": ["ITEM001", "ITEM002", "ITEM003"],
                "currentNodeType": 1,
                "pePackage": {
                    "code": "PKG001",
                    "name": "基础体检套餐"
                }
            }
        }


def main():
    """主函数"""
    print("天健云01号接口完整报文测试工具 v1.0")
    print("开发: 福能AI对接项目组")
    
    # 检查配置
    auth_config = TestConfig.API_AUTH_CONFIG
    if not auth_config['api_key']:
        print("❌ API密钥未配置")
        return
    
    # 创建测试对象
    test = TianjianInterface01CompleteTest()
    
    # 执行测试
    success = test.test_01_interface_complete_message()
    
    # 打印总结
    test.print_separator("🎯 测试完成")
    if success:
        print("✅ 01号接口测试成功！")
        print("📋 以上显示了完整的请求和响应报文")
        print("🔍 可以根据报文信息进行接口调试和验证")
    else:
        print("❌ 01号接口测试失败！")
        print("📋 请检查上述错误信息和报文内容")
    
    test.print_separator("📚 报文说明")
    print("📤 请求报文包含:")
    print("   - 请求头: 包含签名、时间戳、机构代码等认证信息")
    print("   - 请求体: 包含体检用户信息的完整JSON数据")
    print("📥 响应报文包含:")
    print("   - 响应头: 服务器返回的HTTP头信息")
    print("   - 响应体: 天健云返回的处理结果JSON数据")
    print("🔍 这些报文可用于:")
    print("   - 接口调试和问题排查")
    print("   - 与天健云技术支持沟通")
    print("   - 验证数据格式和字段映射")


if __name__ == "__main__":
    main() 