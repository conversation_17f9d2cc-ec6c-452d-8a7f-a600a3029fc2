#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试使用dAffirmdate的查询效果
"""

def test_affirmdate_query():
    """测试使用dAffirmdate的查询效果"""
    try:
        print("="*80)
        print("测试使用dAffirmdate的查询效果")
        print("="*80)
        
        from interface_01_sendPeInfo import TianjianInterface01
        interface = TianjianInterface01()
        
        # 1. 直接测试SQL查询
        print("1. 直接测试不同天数范围的查询结果:")
        
        test_ranges = [7, 30, 90, 180, 365]
        
        for days in test_ranges:
            count_sql = f"""
            SELECT COUNT(*) as count
            FROM T_Register_Main rm
            WHERE rm.dAffirmdate >= DATEADD(day, -{days}, GETDATE())
            """
            
            count_result = interface.db_service.connection_manager.execute_query_with_cache(
                interface.db_service.connection_string,
                count_sql,
                cache_key=f"affirmdate_count_{days}",
                use_cache=False
            )
            
            count = count_result[0]['count'] if count_result else 0
            print(f"   最近{days:3d}天 (dAffirmdate): {count:4d} 条记录")
        
        # 2. 测试修改后的01号接口
        print(f"\n2. 测试修改后的01号接口:")
        
        # 测试不同的天数范围
        for days in [90, 180, 365]:
            print(f"\n   测试 {days} 天范围:")
            
            result = interface.send_exam_info(days=days, limit=5, test_mode=True)
            
            print(f"     成功: {result.get('success')}")
            print(f"     总数: {result.get('total', 0)}")
            print(f"     消息: {result.get('message', 'N/A')}")
            
            if result.get('success') and result.get('total', 0) > 0:
                print(f"     ✅ {days}天范围找到 {result.get('total', 0)} 条记录")
                return days
            else:
                print(f"     ⚠️ {days}天范围没有找到记录")
        
        # 3. 如果还是没有数据，检查最近的记录
        print(f"\n3. 检查最近的记录分布:")
        
        recent_sql = """
        SELECT TOP 10
            cClientCode,
            cName,
            dAffirmdate,
            DATEDIFF(day, dAffirmdate, GETDATE()) as days_ago
        FROM T_Register_Main 
        WHERE dAffirmdate IS NOT NULL
        ORDER BY dAffirmdate DESC
        """
        
        recent_result = interface.db_service.connection_manager.execute_query_with_cache(
            interface.db_service.connection_string,
            recent_sql,
            cache_key="recent_affirm_records",
            use_cache=False
        )
        
        if recent_result:
            print(f"   最近10条记录:")
            for i, record in enumerate(recent_result, 1):
                print(f"     {i:2d}. {record['cName']} ({record['cClientCode']}) - {record['dAffirmdate']} ({record['days_ago']}天前)")
        else:
            print(f"   没有找到任何记录")
        
        return None
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    effective_days = test_affirmdate_query()
    
    print("\n" + "="*80)
    print("测试结果:")
    print("="*80)
    
    if effective_days:
        print(f"✅ 修改成功！使用dAffirmdate查询，{effective_days}天范围内有数据")
        print(f"\n现在可以正常使用01号接口:")
        print(f"  python interface_01_sendPeInfo.py --days {effective_days} --limit 10 --test-mode")
    else:
        print("⚠️ 修改后仍然没有找到足够的数据")
        print("\n可能的原因:")
        print("1. 数据库中确实没有最近的数据")
        print("2. dAffirmdate字段为NULL")
        print("3. 需要检查数据库连接是否正确")

if __name__ == "__main__":
    main()
