#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云API连接测试脚本
用于测试API连接状态和签名机制
"""

import sys
import json
import hashlib
import uuid
import traceback
from datetime import datetime
try:
    import requests
except ImportError:
    print("错误: 请先安装requests: pip install requests")
    sys.exit(1)

from test_config import TestConfig

class APITester:
    """天健云API测试器"""
    
    def __init__(self):
        self.api_config = TestConfig.API_CONFIG
        self.auth_config = TestConfig.API_AUTH_CONFIG
        self.base_url = self.api_config['base_url']
        
    def generate_signature(self, timestamp):
        """生成MD5签名"""
        if not self.auth_config['api_key']:
            raise ValueError("API密钥未配置")
        
        # MD5(key+timestamp)
        sign_string = self.auth_config['api_key'] + timestamp
        signature = hashlib.md5(sign_string.encode('utf-8')).hexdigest().lower()
        return signature
    
    def generate_headers(self):
        """生成请求头"""
        # 生成时间戳 (YYYYmmDDHHMMSS格式)
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        
        # 生成签名
        signature = self.generate_signature(timestamp)
        
        # 生成UUID
        nonce = str(uuid.uuid4())
        
        headers = {
            'Content-Type': 'application/json',
            'sign': signature,
            'timestamp': timestamp,
            'nonce': nonce,
            'mic-code': self.auth_config['mic_code'],
            'misc-id': self.auth_config['misc_id']
        }
        
        return headers
    
    def test_health_endpoint(self):
        """测试健康检查接口（如果存在）"""
        print("\n正在测试API基础连接...")
        print(f"API地址: {self.base_url}")
        
        try:
            # 尝试简单的GET请求
            response = requests.get(
                f"{self.base_url}/health",
                timeout=self.api_config['timeout']
            )
            
            print(f"[OK] 基础连接成功!")
            print(f"   状态码: {response.status_code}")
            print(f"   响应时间: {response.elapsed.total_seconds():.3f}s")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   响应内容: {json.dumps(data, indent=2, ensure_ascii=False)}")
                except:
                    print(f"   响应内容: {response.text[:200]}...")
            
            return True, None
            
        except requests.ConnectionError as e:
            error_msg = f"连接错误: {str(e)}"
            print(f"[FAIL] 基础连接失败!")
            print(f"   {error_msg}")
            return False, error_msg
            
        except requests.Timeout as e:
            error_msg = f"请求超时: {str(e)}"
            print(f"[FAIL] 基础连接失败!")
            print(f"   {error_msg}")
            return False, error_msg
            
        except Exception as e:
            error_msg = f"未知错误: {str(e)}"
            print(f"[FAIL] 基础连接失败!")
            print(f"   {error_msg}")
            return False, error_msg
    
    def test_query_dict_api(self):
        """测试查询字典信息接口"""
        print("\n正在测试查询字典信息接口...")
        
        try:
            headers = self.generate_headers()
            
            # 测试查询OPEVIP字典
            test_data = {
                "id": "",
                "type": "OPEVIP",
                "hospitalCode": ""
            }
            
            print(f"请求头: {json.dumps(headers, indent=2, ensure_ascii=False)}")
            print(f"请求体: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
            
            response = requests.post(
                f"{self.base_url}/dx/inter-info/syncDict",
                headers=headers,
                json=test_data,
                timeout=self.api_config['timeout']
            )
            
            print(f"[OK] 字典查询接口调用成功!")
            print(f"   状态码: {response.status_code}")
            print(f"   响应时间: {response.elapsed.total_seconds():.3f}s")
            
            try:
                response_data = response.json()
                print(f"   响应内容: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                
                # 检查响应格式
                if 'code' in response_data:
                    if response_data['code'] == 0:
                        print("   [OK] API调用成功，签名验证通过")
                        return True, None
                    else:
                        error_msg = f"API返回错误: {response_data.get('msg', '未知错误')}"
                        print(f"   [FAIL] {error_msg}")
                        return False, error_msg
                else:
                    print("   [WARN] 响应格式异常")
                    return False, "响应格式异常"
                    
            except json.JSONDecodeError:
                print(f"   响应内容: {response.text[:200]}...")
                return False, "响应格式不是有效JSON"
                
        except requests.ConnectionError as e:
            error_msg = f"连接错误: {str(e)}"
            print(f"[FAIL] 字典查询接口调用失败!")
            print(f"   {error_msg}")
            return False, error_msg
            
        except Exception as e:
            error_msg = f"调用错误: {str(e)}"
            print(f"[FAIL] 字典查询接口调用失败!")
            print(f"   {error_msg}")
            return False, error_msg
    
    def test_signature_validation(self):
        """测试签名验证机制"""
        print("\n正在测试签名验证机制...")
        
        try:
            # 测试1: 正确的签名
            print("测试1: 使用正确的签名")
            headers = self.generate_headers()
            
            test_data = {"id": "", "type": "OPEVIP", "hospitalCode": ""}
            
            response = requests.post(
                f"{self.base_url}/dx/inter-info/syncDict",
                headers=headers,
                json=test_data,
                timeout=self.api_config['timeout']
            )
            
            print(f"   正确签名响应: {response.status_code}")
            
            # 测试2: 错误的签名
            print("测试2: 使用错误的签名")
            wrong_headers = headers.copy()
            wrong_headers['sign'] = 'invalid_signature_123456789'
            
            response2 = requests.post(
                f"{self.base_url}/dx/inter-info/syncDict",
                headers=wrong_headers,
                json=test_data,
                timeout=self.api_config['timeout']
            )
            
            print(f"   错误签名响应: {response2.status_code}")
            
            try:
                response2_data = response2.json()
                print(f"   错误签名返回: {response2_data}")
            except:
                pass
            
            # 如果错误签名被拒绝，说明签名机制工作正常
            if response.status_code != response2.status_code or (response2.status_code == 200 and response2.json().get('code', 0) != 0):
                print("   [OK] 签名验证机制工作正常")
                return True, None
            else:
                print("   [WARN] 签名验证机制可能存在问题")
                return False, "签名验证机制异常"
                
        except Exception as e:
            error_msg = f"签名测试失败: {str(e)}"
            print(f"   [FAIL] {error_msg}")
            return False, error_msg
    
    def test_sync_apply_item_api(self, data):
        """测试申请项目字典同步接口"""
        try:
            headers = self.generate_headers()
            
            response = requests.post(
                f"{self.base_url}/dx/inter/syncApplyItem",
                headers=headers,
                json=data,
                timeout=self.api_config['timeout']
            )
            
            if response.status_code == 200:
                response_data = response.json()
                if response_data.get('code') == 0:
                    return {'success': True, 'data': response_data}
                else:
                    return {'success': False, 'error': response_data.get('msg', '接口返回错误')}
            else:
                return {'success': False, 'error': f'HTTP错误: {response.status_code}'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_sync_dept_api(self, data):
        """测试科室信息同步接口"""
        try:
            headers = self.generate_headers()
            
            response = requests.post(
                f"{self.base_url}/dx/inter/syncDept",
                headers=headers,
                json=data,
                timeout=self.api_config['timeout']
            )
            
            if response.status_code == 200:
                response_data = response.json()
                if response_data.get('code') == 0:
                    return {'success': True, 'data': response_data}
                else:
                    return {'success': False, 'error': response_data.get('msg', '接口返回错误')}
            else:
                return {'success': False, 'error': f'HTTP错误: {response.status_code}'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_sync_user_api(self, data):
        """测试医生信息同步接口"""
        try:
            headers = self.generate_headers()
            
            response = requests.post(
                f"{self.base_url}/dx/inter/syncUser",
                headers=headers,
                json=data,
                timeout=self.api_config['timeout']
            )
            
            if response.status_code == 200:
                response_data = response.json()
                if response_data.get('code') == 0:
                    return {'success': True, 'data': response_data}
                else:
                    return {'success': False, 'error': response_data.get('msg', '接口返回错误')}
            else:
                return {'success': False, 'error': f'HTTP错误: {response.status_code}'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_sync_dict_api(self, data):
        """测试字典信息同步接口"""
        try:
            headers = self.generate_headers()
            
            response = requests.post(
                f"{self.base_url}/dx/inter/syncDict",
                headers=headers,
                json=data,
                timeout=self.api_config['timeout']
            )
            
            if response.status_code == 200:
                response_data = response.json()
                if response_data.get('code') == 0:
                    return {'success': True, 'data': response_data}
                else:
                    return {'success': False, 'error': response_data.get('msg', '接口返回错误')}
            else:
                return {'success': False, 'error': f'HTTP错误: {response.status_code}'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_send_pe_info_api(self, data):
        """测试体检信息传输接口"""
        try:
            headers = self.generate_headers()
            
            response = requests.post(
                f"{self.base_url}/dx/inter/sendPeInfo",
                headers=headers,
                json=data,
                timeout=self.api_config['timeout']
            )
            
            if response.status_code == 200:
                response_data = response.json()
                if response_data.get('code') == 0:
                    return {'success': True, 'data': response_data}
                else:
                    return {'success': False, 'error': response_data.get('msg', '接口返回错误')}
            else:
                return {'success': False, 'error': f'HTTP错误: {response.status_code}'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def run_full_test(self):
        """运行完整的API测试"""
        print("="*60)
        print("健康同步系统 - 天健云API连接测试")
        print("="*60)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        results = {}
        
        # 检查配置
        print("\n[配置检查]")
        config_errors = []
        if not self.auth_config['api_key']:
            config_errors.append("API密钥未配置")
        if not self.auth_config['mic_code']:
            config_errors.append("机构代码未配置")
        if not self.auth_config['misc_id']:
            config_errors.append("系统ID未配置")
        
        if config_errors:
            print("[FAIL] 配置检查失败:")
            for error in config_errors:
                print(f"   - {error}")
            results['config'] = {'success': False, 'errors': config_errors}
            return results
        else:
            print("[OK] 配置检查通过")
            results['config'] = {'success': True, 'errors': []}
        
        # 测试基础连接
        health_success, health_error = self.test_health_endpoint()
        results['health'] = {'success': health_success, 'error': health_error}
        
        # 如果基础连接成功，继续测试API
        if health_success:
            # 测试字典查询接口
            dict_success, dict_error = self.test_query_dict_api()
            results['dict_api'] = {'success': dict_success, 'error': dict_error}
            
            # 测试签名验证
            if dict_success:
                sign_success, sign_error = self.test_signature_validation()
                results['signature'] = {'success': sign_success, 'error': sign_error}
        
        # 输出测试总结
        self.print_test_summary(results)
        
        return results
    
    def print_test_summary(self, results):
        """打印测试总结"""
        print("\n" + "="*60)
        print("API测试总结")
        print("="*60)
        
        # 配置状态
        config_status = "[OK] 成功" if results['config']['success'] else "[FAIL] 失败"
        print(f"配置检查: {config_status}")
        
        # 基础连接状态
        if 'health' in results:
            health_status = "[OK] 成功" if results['health']['success'] else "[FAIL] 失败"
            print(f"基础连接: {health_status}")
        
        # 字典API状态
        if 'dict_api' in results:
            dict_status = "[OK] 成功" if results['dict_api']['success'] else "[FAIL] 失败"
            print(f"字典查询API: {dict_status}")
        
        # 签名验证状态
        if 'signature' in results:
            sign_status = "[OK] 成功" if results['signature']['success'] else "[FAIL] 失败"
            print(f"签名验证: {sign_status}")
        
        # 整体状态
        all_tests = [results['config']['success']]
        if 'health' in results:
            all_tests.append(results['health']['success'])
        if 'dict_api' in results:
            all_tests.append(results['dict_api']['success'])
        if 'signature' in results:
            all_tests.append(results['signature']['success'])
        
        if all(all_tests):
            print("\n🎉 天健云API配置验证通过！可以开始使用同步功能。")
        else:
            print("\n[WARN] API配置有问题，请检查以下内容:")
            if not results['config']['success']:
                print("  - API认证配置参数")
            if 'health' in results and not results['health']['success']:
                print("  - 网络连接和API地址")
            if 'dict_api' in results and not results['dict_api']['success']:
                print("  - API密钥和签名算法")
        
        print("="*60)

def main():
    """主函数"""
    print("健康同步系统 - 天健云API连接测试工具")
    print("="*60)
    
    try:
        # 创建测试器并运行测试
        tester = APITester()
        results = tester.run_full_test()
        
        # 如果有失败的测试，提供帮助信息
        all_success = all([
            results['config']['success'],
            results.get('health', {}).get('success', True),
            results.get('dict_api', {}).get('success', True),
            results.get('signature', {}).get('success', True)
        ])
        
        if not all_success:
            print("\n" + "="*60)
            print("故障排查建议")
            print("="*60)
            print("1. 检查网络连接和防火墙设置")
            print("2. 确认API地址是否正确")
            print("3. 验证API密钥是否有效")
            print("4. 检查机构代码(mic-code)和系统ID(misc-id)")
            print("5. 确认时间戳格式是否正确")
            print("6. 验证MD5签名算法实现")
            print("7. 联系天健云技术支持确认接口状态")
        
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        traceback.print_exc()

if __name__ == '__main__':
    main() 