#!/usr/bin/env python3
"""
天健云2号接口测试脚本 - 申请项目字典数据传输
测试 syncApplyItem 接口的完整功能
"""
import sys
import json
import traceback
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 暂时注释掉health_sync模块的导入，避免语法错误
# from health_sync.config.settings import settings
# from health_sync.services.apply_item_service import apply_item_service
# from health_sync.db.session import init_database
# from health_sync.utils.logger import app_logger, log_startup_info
from config import Config


def print_separator(title: str, char: str = "="):
    """打印分隔线"""
    print(f"\n{char * 60}")
    print(f"{title}")
    print(f"{char * 60}")


def test_database_connection():
    """测试数据库连接"""
    print_separator("[DATA] 数据库连接测试", "=")
    
    try:
        # 初始化数据库
        init_database()
        print("[OK] 数据库连接初始化成功")
        
        # 测试获取数据
        items = apply_item_service.get_apply_items_from_db(include_stopped=False)
        print(f"[OK] 成功获取申请项目数据: {len(items)} 个项目")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] 数据库连接失败: {e}")
        traceback.print_exc()
        return False


def test_data_format():
    """测试数据格式化"""
    print_separator("[SYNC] 数据格式化测试", "=")
    
    try:
        # 获取数据库数据
        db_items = apply_item_service.get_apply_items_from_db(include_stopped=False)
        print(f"[LIST] 数据库项目数: {len(db_items)}")
        
        # 选择前3个项目进行测试
        test_items = db_items[:3]
        print(f"[NOTE] 测试项目数: {len(test_items)}")
        
        # 格式化为API格式
        api_items = apply_item_service.format_for_tianjian_api(test_items)
        print(f"[TARGET] API格式项目数: {len(api_items)}")
        
        # 显示第一个项目的详细格式
        if api_items:
            print(f"\n[DOC] 第一个项目的API格式:")
            first_item = api_items[0].dict()
            print(json.dumps(first_item, indent=2, ensure_ascii=False))
        
        return True
        
    except Exception as e:
        print(f"[FAIL] 数据格式化失败: {e}")
        traceback.print_exc()
        return False


def test_api_data_structure():
    """测试API数据结构是否符合接口文档要求"""
    print_separator("[NOTE] API数据结构验证", "=")
    
    try:
        # 获取并格式化数据
        db_items = apply_item_service.get_apply_items_from_db(include_stopped=False)
        api_items = apply_item_service.format_for_tianjian_api(db_items[:1])
        
        if not api_items:
            print("[FAIL] 没有有效的API数据")
            return False
        
        item = api_items[0].dict()
        
        print("🔍 验证必填字段:")
        
        # 验证必填字段
        required_fields = [
            'applyItemId',
            'applyItemName', 
            'deptId',
            'checkItemList'
        ]
        
        for field in required_fields:
            if field in item and item[field]:
                print(f"  [OK] {field}: {item[field]}")
            else:
                print(f"  [FAIL] {field}: 缺失或为空")
                return False
        
        # 验证checkItemList结构
        print("\n🔍 验证检查项目列表结构:")
        check_items = item.get('checkItemList', [])
        
        if not check_items:
            print("  [FAIL] checkItemList为空")
            return False
        
        first_check_item = check_items[0]
        check_required_fields = ['checkItemId', 'checkItemName']
        
        for field in check_required_fields:
            if field in first_check_item and first_check_item[field]:
                print(f"  [OK] {field}: {first_check_item[field]}")
            else:
                print(f"  [FAIL] {field}: 缺失或为空")
                return False
        
        # 验证数据类型
        print("\n🔍 验证数据类型:")
        print(f"  [OK] applyItemId是字符串: {isinstance(item['applyItemId'], str)}")
        print(f"  [OK] applyItemName是字符串: {isinstance(item['applyItemName'], str)}")
        print(f"  [OK] checkItemList是列表: {isinstance(item['checkItemList'], list)}")
        
        print("\n[OK] API数据结构验证通过！")
        return True
        
    except Exception as e:
        print(f"[FAIL] API数据结构验证失败: {e}")
        traceback.print_exc()
        return False


def test_api_request_simulation():
    """模拟API请求测试（不实际发送）"""
    print_separator("[TEST] API请求模拟测试", "=")
    
    try:
        # 获取数据
        db_items = apply_item_service.get_apply_items_from_db(include_stopped=False)
        api_items = apply_item_service.format_for_tianjian_api(db_items[:2])  # 只取2个项目测试
        
        print(f"[DATA] 准备发送的数据:")
        print(f"  项目数量: {len(api_items)}")
        
        # 转换为JSON格式（模拟真实请求体）
        request_data = [item.dict() for item in api_items]
        json_data = json.dumps(request_data, ensure_ascii=False, indent=2)
        
        print(f"  JSON大小: {len(json_data)} 字符")
        print(f"  接口路径: /dx/inter/syncApplyItem")
        print(f"  请求方法: POST")
        print(f"  Content-Type: application/json")
        
        # 显示认证头信息（不包含敏感信息）
        print(f"\n[LOCK] 认证信息:")
        print(f"  mic-code: {settings.api.mic_code}")
        print(f"  misc-id: {settings.api.misc_id}")
        print(f"  服务器: {settings.api.base_url}")
        
        # 显示部分请求体示例
        print(f"\n[DOC] 请求体示例 (前200字符):")
        print(json_data[:200] + "..." if len(json_data) > 200 else json_data)
        
        print(f"\n[OK] API请求数据准备就绪！")
        return True
        
    except Exception as e:
        print(f"[FAIL] API请求模拟失败: {e}")
        traceback.print_exc()
        return False


def test_real_api_sync():
    """测试真实的API同步（可选）"""
    print_separator("[ROCKET] 真实API同步测试", "=")
    
    # 询问用户是否要进行真实同步
    print("[WARN]  这将向天健云服务器发送真实的API请求！")
    print("[LIST] 将同步前5个申请项目数据")
    
    confirm = input("\n是否继续进行真实API同步测试？(y/N): ").strip().lower()
    
    if confirm not in ['y', 'yes']:
        print("[FAIL] 用户取消了真实API同步测试")
        return False
    
    try:
        print("\n⏳ 开始真实API同步...")
        
        # 获取前5个项目进行测试
        db_items = apply_item_service.get_apply_items_from_db(include_stopped=False)
        test_items = db_items[:5]
        
        print(f"[DATA] 测试数据: {len(test_items)} 个申请项目")
        
        # 执行同步
        result = apply_item_service.sync_apply_items_to_tianjian(
            apply_item_service.format_for_tianjian_api(test_items),
            batch_size=5  # 小批量测试
        )
        
        # 显示结果
        print(f"\n[DATA] 同步结果:")
        print(f"  总数: {result['total_count']}")
        print(f"  成功: {result['success_count']}")
        print(f"  失败: {result['failed_count']}")
        print(f"  成功率: {result['success_rate']:.1f}%")
        
        if result['failed_items']:
            print(f"  失败项目: {', '.join(result['failed_items'])}")
        
        # 判断测试是否成功
        if result['success_count'] > 0:
            print(f"\n[OK] 真实API同步测试成功！")
            return True
        else:
            print(f"\n[FAIL] 真实API同步测试失败")
            return False
        
    except Exception as e:
        print(f"[FAIL] 真实API同步测试异常: {e}")
        traceback.print_exc()
        return False


def show_data_summary():
    """显示数据概览"""
    print_separator("[LIST] 数据概览", "=")
    
    try:
        summary = apply_item_service.get_apply_item_summary()
        
        print(f"[DATA] 申请项目统计:")
        print(f"  总项目数: {summary['total_items']}")
        print(f"  启用项目: {summary['active_items']}")
        print(f"  停用项目: {summary['stopped_items']}")
        
        if summary['type_distribution']:
            print(f"\n🏷️  项目类型分布:")
            for item_type, count in summary['type_distribution'].items():
                print(f"  {item_type}: {count}个")
        
        print(f"\n🕐 数据更新时间: {summary['last_updated']}")
        
    except Exception as e:
        print(f"[FAIL] 获取数据概览失败: {e}")


def main():
    """主函数"""
    print_separator("[HOSPITAL] 天健云2号接口测试工具", "*")
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"[TARGET] 测试接口: 申请项目字典数据传输 (syncApplyItem)")
    print(f"📍 接口路径: /dx/inter/syncApplyItem")
    
    # 设置日志
    log_startup_info()
    
    # 测试计数器
    tests = {
        'total': 0,
        'passed': 0,
        'failed': 0
    }
    
    # 执行测试
    test_functions = [
        ("数据库连接测试", test_database_connection),
        ("数据格式化测试", test_data_format),
        ("API数据结构验证", test_api_data_structure),
        ("API请求模拟测试", test_api_request_simulation)
    ]
    
    for test_name, test_func in test_functions:
        tests['total'] += 1
        print(f"\n[TEST] 执行测试: {test_name}")
        
        try:
            if test_func():
                tests['passed'] += 1
                print(f"[OK] {test_name} - 通过")
            else:
                tests['failed'] += 1
                print(f"[FAIL] {test_name} - 失败")
        except Exception as e:
            tests['failed'] += 1
            print(f"💥 {test_name} - 异常: {e}")
    
    # 可选的真实API测试
    print(f"\n[FIRE] 可选测试:")
    tests['total'] += 1
    if test_real_api_sync():
        tests['passed'] += 1
    else:
        tests['failed'] += 1
    
    # 显示数据概览
    show_data_summary()
    
    # 测试总结
    print_separator("[DATA] 测试总结", "*")
    print(f"[TARGET] 测试项目: 天健云2号接口 - 申请项目字典数据传输")
    print(f"🔢 总测试数: {tests['total']}")
    print(f"[OK] 通过数: {tests['passed']}")
    print(f"[FAIL] 失败数: {tests['failed']}")
    
    success_rate = (tests['passed'] / tests['total'] * 100) if tests['total'] > 0 else 0
    print(f"[CHART] 成功率: {success_rate:.1f}%")
    
    if tests['failed'] == 0:
        print(f"\n🎉 所有测试通过！2号接口实现正常")
    else:
        print(f"\n[WARN]  有 {tests['failed']} 个测试失败，请检查相关功能")
    
    print(f"\n📅 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 返回退出码
    return 0 if tests['failed'] == 0 else 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试程序异常: {e}")
        traceback.print_exc()
        sys.exit(1) 