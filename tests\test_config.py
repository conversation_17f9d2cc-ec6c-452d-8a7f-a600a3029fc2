#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置文件 - 用于配置数据库连接和天健云API参数
注意：此文件已废弃，请使用根目录的config.py进行统一配置管理
"""

import os
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import Config

class TestConfig:
    """测试配置类"""
    
    # ===========================================
    # 数据库连接配置
    # ===========================================
    
    # 主数据库配置（体检系统数据库）- 使用统一配置
    MAIN_DB_CONFIG = Config.INTERFACE_DB_CONFIG.copy()
    MAIN_DB_CONFIG.update({
        'charset': 'utf8',
        'timeout': 30,
        'pool_size': 5,
        'max_overflow': 10
    })

    # PACS数据库配置（影像系统数据库）
    PACS_DB_CONFIG = {
        'host': os.environ.get('PACS_DB_HOST', '************'),
        'port': int(os.environ.get('PACS_DB_PORT', '1433')),
        'database': os.environ.get('PACS_DB_NAME', 'Examdb'),
        'username': os.environ.get('PACS_DB_USER', 'znzj'),
        'password': os.environ.get('PACS_DB_PASSWORD', '2025znzj/888'),
        'driver': os.environ.get('PACS_DB_DRIVER', 'ODBC Driver 17 for SQL Server'),
        'charset': 'utf8',
        'timeout': 30,
        'pool_size': 3,
        'max_overflow': 5
    }
    
    # ===========================================
    # 天健云API配置
    # ===========================================
    
    # API基础配置
    API_CONFIG = {
        'base_url': os.environ.get('API_BASE_URL', 'http://**************:9300'),
        'timeout': int(os.environ.get('API_TIMEOUT', 30)),
        'retry_times': int(os.environ.get('API_RETRY_TIMES', 3)),
        'retry_delay': int(os.environ.get('API_RETRY_DELAY', 1)),
    }
    
    # 天健云认证配置
    API_AUTH_CONFIG = {
        'mic_code': os.environ.get('API_MIC_CODE', 'MIC1.001E'),  # 宜昌伍家岗仁康综合门诊部
        'misc_id': os.environ.get('API_MISC_ID', 'MISC1.00001A'),
        'api_key': os.environ.get('API_KEY', '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM'),
    }
    
    # ===========================================
    # 系统配置
    # ===========================================
    
    # 日志配置
    LOG_CONFIG = {
        'level': 'DEBUG',
        'file_path': 'logs/health_sync.log',
        'max_size': '10MB',
        'backup_count': 5,
        'format': '{time:YYYY-MM-DD HH:mm:ss} | {level} | {module}:{function}:{line} | {message}'
    }
    
    # 同步任务配置
    SYNC_CONFIG = {
        'batch_size': 100,  # 批量处理大小
        'sync_interval': 300,  # 同步间隔（秒）
        'max_retry_times': 3,  # 最大重试次数
        'enable_auto_sync': True,  # 是否启用自动同步
    }
    
    # ===========================================
    # 业务配置
    # ===========================================
    
    # 体检状态映射
    PE_STATUS_MAPPING = {
        '1': '登记完成',
        '2': '分科未完成', 
        '3': '分科完成',
        '4': '主检初审中',
        '5': '主检初审完成',
        '6': '主检终审中',
        '7': '主检终审完成'
    }
    
    # 性别映射
    GENDER_MAPPING = {
        '1': '男',
        '2': '女',
        '3': '未知'
    }
    
    # 婚姻状态映射
    MARRIAGE_STATUS_MAPPING = {
        'married': '已婚',
        'unmarried': '未婚',
        'divorce': '离婚',
        'widowhood': '丧偶',
        'unknown': '未知'
    }
    
    # 怀孕状态映射
    PREGNANT_STATUS_MAPPING = {
        'unconception': '未怀孕',
        'pregnancy': '怀孕',
        'preparing': '备孕中',
        'unknown': '未知'
    }

def get_connection_string(db_config):
    """生成数据库连接字符串"""
    driver = db_config['driver'].replace(' ', '+')
    return (
        f"mssql+pyodbc://{db_config['username']}:{db_config['password']}"
        f"@{db_config['host']}:{db_config['port']}/{db_config['database']}"
        f"?driver={driver}&charset={db_config['charset']}"
    )

def validate_config():
    """验证配置参数"""
    errors = []
    
    # 检查数据库配置
    main_db = TestConfig.MAIN_DB_CONFIG
    if not main_db['password']:
        errors.append("主数据库密码未配置")
    
    pacs_db = TestConfig.PACS_DB_CONFIG  
    if not pacs_db['password']:
        errors.append("PACS数据库密码未配置")
    
    # 检查API配置
    auth_config = TestConfig.API_AUTH_CONFIG
    if not auth_config['api_key']:
        errors.append("天健云API密钥未配置")
    
    return errors

def print_config_info():
    """打印配置信息（隐藏敏感信息）"""
    print("="*50)
    print("健康同步系统 - 配置信息")
    print("="*50)
    
    print("\n[主数据库配置]")
    main_db = TestConfig.MAIN_DB_CONFIG
    print(f"服务器: {main_db['host']}:{main_db['port']}")
    print(f"数据库: {main_db['database']}")
    print(f"用户名: {main_db['username']}")
    print(f"密码: {'*' * len(main_db['password']) if main_db['password'] else '未配置'}")
    
    print("\n[PACS数据库配置]")
    pacs_db = TestConfig.PACS_DB_CONFIG
    print(f"服务器: {pacs_db['host']}:{pacs_db['port']}")
    print(f"数据库: {pacs_db['database']}")
    print(f"用户名: {pacs_db['username']}")
    print(f"密码: {'*' * len(pacs_db['password']) if pacs_db['password'] else '未配置'}")
    
    print("\n[天健云API配置]")
    api_config = TestConfig.API_CONFIG
    auth_config = TestConfig.API_AUTH_CONFIG
    print(f"API地址: {api_config['base_url']}")
    print(f"机构代码: {auth_config['mic_code']}")
    print(f"系统ID: {auth_config['misc_id']}")
    print(f"API密钥: {'*' * len(auth_config['api_key']) if auth_config['api_key'] else '未配置'}")
    
    print("\n[验证结果]")
    errors = validate_config()
    if errors:
        print("[FAIL] 配置验证失败:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("[OK] 配置验证通过")
    
    print("="*50)

if __name__ == '__main__':
    print_config_info() 