#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接测试脚本
用于测试主数据库和PACS数据库的连接状态
"""

import sys
import traceback
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import Config

try:
    import sqlalchemy
    from sqlalchemy import create_engine, text
    from sqlalchemy.exc import SQLAlchemyError
except ImportError:
    print("错误: 请先安装SQLAlchemy: pip install sqlalchemy")
    sys.exit(1)

try:
    import pyodbc
except ImportError:
    print("错误: 请先安装pyodbc: pip install pyodbc")
    sys.exit(1)

from test_config import TestConfig, get_connection_string

class DatabaseTester:
    """数据库连接测试器"""
    
    def __init__(self):
        self.main_db_config = TestConfig.MAIN_DB_CONFIG
        self.pacs_db_config = TestConfig.PACS_DB_CONFIG
    
    def test_connection(self, db_config, db_name):
        """测试数据库连接"""
        print(f"\n正在测试{db_name}连接...")
        print(f"服务器: {db_config['host']}:{db_config['port']}")
        print(f"数据库: {db_config['database']}")
        print(f"用户名: {db_config['username']}")
        
        try:
            # 生成连接字符串
            connection_string = get_connection_string(db_config)
            
            # 创建引擎
            engine = create_engine(
                connection_string,
                pool_timeout=db_config['timeout'],
                pool_size=db_config['pool_size'],
                max_overflow=db_config['max_overflow']
            )
            
            # 测试连接
            with engine.connect() as conn:
                # 执行简单查询
                result = conn.execute(text("SELECT @@VERSION as version, GETDATE() as server_time"))
                row = result.fetchone()
                
                print(f"[OK] {db_name}连接成功!")
                print(f"   数据库版本: {row.version[:50]}...")
                print(f"   服务器时间: {row.server_time}")
                
                return True, None
                
        except SQLAlchemyError as e:
            error_msg = str(e)
            print(f"[FAIL] {db_name}连接失败!")
            print(f"   SQLAlchemy错误: {error_msg}")
            return False, error_msg
            
        except Exception as e:
            error_msg = str(e)
            print(f"[FAIL] {db_name}连接失败!")
            print(f"   系统错误: {error_msg}")
            return False, error_msg
    
    def test_table_access(self, db_config, db_name, table_name):
        """测试表访问权限"""
        print(f"\n正在测试{db_name}中表 {table_name} 的访问权限...")
        
        try:
            connection_string = get_connection_string(db_config)
            engine = create_engine(connection_string)
            
            with engine.connect() as conn:
                # 测试查询表结构
                result = conn.execute(text(f"SELECT TOP 1 * FROM {table_name}"))
                columns = list(result.keys())
                
                print(f"[OK] 表 {table_name} 访问成功!")
                print(f"   字段数量: {len(columns)}")
                print(f"   前5个字段: {columns[:5]}")
                
                return True, None
                
        except SQLAlchemyError as e:
            error_msg = str(e)
            print(f"[FAIL] 表 {table_name} 访问失败!")
            print(f"   错误: {error_msg}")
            return False, error_msg
            
        except Exception as e:
            error_msg = str(e)
            print(f"[FAIL] 表 {table_name} 访问失败!")
            print(f"   错误: {error_msg}")
            return False, error_msg
    
    def run_full_test(self):
        """运行完整测试"""
        print("="*60)
        print("健康同步系统 - 数据库连接测试")
        print("="*60)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        results = {}
        
        # 测试主数据库连接
        main_success, main_error = self.test_connection(self.main_db_config, "主数据库")
        results['main_db'] = {'success': main_success, 'error': main_error}
        
        # 测试PACS数据库连接
        pacs_success, pacs_error = self.test_connection(self.pacs_db_config, "PACS数据库")
        results['pacs_db'] = {'success': pacs_success, 'error': pacs_error}
        
        # 如果主数据库连接成功，测试关键表
        if main_success:
            key_tables = [
                'T_Register_Main',
                'T_Register_Detail', 
                'T_Check_result',
                'T_Check_result_Main',
                'Code_Item_Main',
                'Code_Dept_dict',
                'Code_Operator_dict'
            ]
            
            table_results = {}
            for table in key_tables:
                success, error = self.test_table_access(self.main_db_config, "主数据库", table)
                table_results[table] = {'success': success, 'error': error}
            
            results['main_tables'] = table_results
        
        # 如果PACS数据库连接成功，测试关键表
        if pacs_success:
            pacs_tables = [
                't_check_result_main_pic'
            ]
            
            pacs_table_results = {}
            for table in pacs_tables:
                success, error = self.test_table_access(self.pacs_db_config, "PACS数据库", table)
                pacs_table_results[table] = {'success': success, 'error': error}
            
            results['pacs_tables'] = pacs_table_results
        
        # 输出测试总结
        self.print_test_summary(results)
        
        return results
    
    def print_test_summary(self, results):
        """打印测试总结"""
        print("\n" + "="*60)
        print("测试总结")
        print("="*60)
        
        # 数据库连接总结
        main_status = "[OK] 成功" if results['main_db']['success'] else "[FAIL] 失败"
        pacs_status = "[OK] 成功" if results['pacs_db']['success'] else "[FAIL] 失败"
        
        print(f"主数据库连接: {main_status}")
        print(f"PACS数据库连接: {pacs_status}")
        
        # 表访问总结
        if 'main_tables' in results:
            success_count = sum(1 for r in results['main_tables'].values() if r['success'])
            total_count = len(results['main_tables'])
            print(f"主数据库表访问: {success_count}/{total_count}")
        
        if 'pacs_tables' in results:
            success_count = sum(1 for r in results['pacs_tables'].values() if r['success'])
            total_count = len(results['pacs_tables'])
            print(f"PACS数据库表访问: {success_count}/{total_count}")
        
        # 整体状态
        all_db_connected = results['main_db']['success'] and results['pacs_db']['success']
        if all_db_connected:
            print("\n🎉 数据库配置验证通过！可以开始使用健康同步系统。")
        else:
            print("\n[WARN] 数据库配置有问题，请检查以下配置:")
            if not results['main_db']['success']:
                print("  - 主数据库连接参数")
            if not results['pacs_db']['success']:
                print("  - PACS数据库连接参数")
        
        print("="*60)

def check_drivers():
    """检查ODBC驱动"""
    print("检查可用的ODBC驱动...")
    try:
        drivers = pyodbc.drivers()
        print(f"找到 {len(drivers)} 个ODBC驱动:")
        for driver in drivers:
            print(f"  - {driver}")
        
        # 检查推荐的驱动
        recommended_drivers = [
            'ODBC Driver 17 for SQL Server',
            'ODBC Driver 13 for SQL Server',
            'SQL Server Native Client 11.0',
            'SQL Server'
        ]
        
        available_recommended = [d for d in recommended_drivers if d in drivers]
        if available_recommended:
            print(f"\n推荐使用的驱动: {available_recommended[0]}")
        else:
            print("\n[WARN] 未找到推荐的SQL Server ODBC驱动")
            print("请安装 ODBC Driver 17 for SQL Server")
        
        return drivers
    except Exception as e:
        print(f"检查ODBC驱动失败: {e}")
        return []

def main():
    """主函数"""
    print("健康同步系统 - 数据库连接测试工具")
    print("="*60)
    
    try:
        # 检查ODBC驱动
        drivers = check_drivers()
        
        # 创建测试器并运行测试
        tester = DatabaseTester()
        results = tester.run_full_test()
        
        # 如果有失败的连接，提供帮助信息
        if not (results['main_db']['success'] and results['pacs_db']['success']):
            print("\n" + "="*60)
            print("故障排查建议")
            print("="*60)
            print("1. 检查数据库服务是否启动")
            print("2. 检查网络连接和防火墙设置")
            print("3. 确认用户名和密码是否正确")
            print("4. 确认数据库名称是否存在")
            print("5. 检查SQL Server配置:")
            print("   - 启用TCP/IP协议")
            print("   - 启用混合身份验证模式")
            print("   - 确认端口设置(默认1433)")
            print("6. 安装或更新ODBC驱动")
        
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        traceback.print_exc()

if __name__ == '__main__':
    main() 