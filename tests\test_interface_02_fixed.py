#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试天健云2号接口 - 申请项目字典数据传输
打印完整的请求和响应报文 (修复编码版本)
"""

import json
import hashlib
import requests
import uuid
from datetime import datetime
import urllib3
import sys
import os

# 设置控制台编码为UTF-8
if sys.platform == 'win32':
    os.system('chcp 65001 >nul')

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# API配置
API_CONFIG = {
    'base_url': 'http://203.83.237.114:9300',
    'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
    'mic_code': 'MIC1.001E',
    'misc_id': 'MISC1.00001A',
    'timeout': 30
}

def safe_print(text):
    """安全打印，避免编码错误"""
    try:
        print(text)
    except UnicodeEncodeError:
        # 如果有编码错误，将中文字符替换为拼音或英文
        safe_text = text.encode('ascii', 'ignore').decode('ascii')
        print(safe_text)

def generate_signature(api_key, timestamp):
    """生成MD5签名"""
    sign_string = api_key + timestamp
    return hashlib.md5(sign_string.encode('utf-8')).hexdigest()

def create_test_data():
    """创建测试数据"""
    return [
        {
            "applyItemId": "TEST_BLOOD_001",
            "applyItemName": "血常规检查",
            "displaySequence": "1",
            "deptId": "LAB_DEPT_001",
            "checkItemList": [
                {
                    "checkItemId": "WBC_001",
                    "checkItemName": "白细胞计数",
                    "displaySequence": "1"
                },
                {
                    "checkItemId": "RBC_002",
                    "checkItemName": "红细胞计数",
                    "displaySequence": "2"
                },
                {
                    "checkItemId": "HGB_003",
                    "checkItemName": "血红蛋白浓度",
                    "displaySequence": "3"
                }
            ],
            "hospital": {
                "code": "JIAREN_001",
                "name": "嘉仁体检中心"
            }
        },
        {
            "applyItemId": "TEST_LIVER_002",
            "applyItemName": "肝功能检查",
            "displaySequence": "2",
            "deptId": "LAB_DEPT_001",
            "checkItemList": [
                {
                    "checkItemId": "ALT_001",
                    "checkItemName": "丙氨酸氨基转移酶",
                    "displaySequence": "1"
                },
                {
                    "checkItemId": "AST_002",
                    "checkItemName": "天门冬氨酸氨基转移酶",
                    "displaySequence": "2"
                }
            ],
            "hospital": {
                "code": "JIAREN_001",
                "name": "嘉仁体检中心"
            }
        }
    ]

def test_interface_02():
    """测试2号接口"""
    print("=" * 100)
    print("Tianjian Cloud Interface 02 Test - Apply Item Dictionary Data Transmission")
    print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 100)
    
    # 生成认证信息
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    nonce = str(uuid.uuid4())
    signature = generate_signature(API_CONFIG['api_key'], timestamp)
    
    # 构建请求头
    headers = {
        'Content-Type': 'application/json',
        'sign': signature,
        'timestamp': timestamp,
        'nonce': nonce,
        'mic-code': API_CONFIG['mic_code'],
        'misc-id': API_CONFIG['misc_id']
    }
    
    # 请求数据
    request_data = create_test_data()
    url = f"{API_CONFIG['base_url']}/dx/inter/syncApplyItem"
    
    print(">>> Interface Information")
    print(f"URL: {url}")
    print(f"Method: POST")
    print(f"Content-Type: application/json")
    print()
    
    print(">>> Authentication Information")
    print(f"Institution Code: {API_CONFIG['mic_code']}")
    print(f"System ID: {API_CONFIG['misc_id']}")
    print(f"API Key: {API_CONFIG['api_key']}")
    print(f"Timestamp: {timestamp}")
    print(f"Nonce: {nonce}")
    print(f"Signature String: {API_CONFIG['api_key']}{timestamp}")
    print(f"MD5 Signature: {signature}")
    print()
    
    print(">>> Complete Request Headers")
    for key, value in headers.items():
        print(f"{key}: {value}")
    print()
    
    print(">>> Request Body Data")
    request_json = json.dumps(request_data, ensure_ascii=False, indent=2)
    print(request_json)
    print()
    
    print(">>> Request Statistics")
    print(f"Apply Item Count: {len(request_data)}")
    total_check_items = sum(len(item['checkItemList']) for item in request_data)
    print(f"Total Check Items: {total_check_items}")
    print(f"Request Body Size: {len(request_json.encode('utf-8'))} bytes")
    print()
    
    print(">>> Sending HTTP Request")
    print("-" * 80)
    
    try:
        # 记录开始时间
        start_time = datetime.now()
        
        # 发送请求
        response = requests.post(
            url,
            headers=headers,
            json=request_data,
            timeout=API_CONFIG['timeout'],
            verify=False
        )
        
        # 记录结束时间
        end_time = datetime.now()
        response_time = (end_time - start_time).total_seconds() * 1000
        
        print(">>> HTTP Response Information")
        print(f"Status Code: {response.status_code}")
        print(f"Response Time: {response_time:.2f}ms")
        print(f"Response Size: {len(response.content)} bytes")
        print()
        
        print(">>> Complete Response Headers")
        for key, value in response.headers.items():
            print(f"{key}: {value}")
        print()
        
        print(">>> Response Body Content")
        try:
            # 尝试解析JSON响应
            response_json = response.json()
            response_text = json.dumps(response_json, ensure_ascii=False, indent=2)
            print(response_text)
            
            # 分析响应结果
            print()
            print(">>> Response Analysis")
            if response_json.get('code') == 0:
                print("SUCCESS: Interface call successful!")
                print(f"Return Code: {response_json.get('code')}")
                print(f"Return Message: {response_json.get('msg', 'No message')}")
                print(f"Return Data: {response_json.get('data', 'No data')}")
                print(f"Response Timestamp: {response_json.get('reponseTime', 'Not provided')}")
                success = True
            else:
                print("FAILED: Interface call failed!")
                print(f"Error Code: {response_json.get('code')}")
                print(f"Error Message: {response_json.get('msg', 'No error message')}")
                success = False
                
        except json.JSONDecodeError:
            print("WARNING: Response is not valid JSON format")
            print("Raw Response Content:")
            print(response.text)
            success = False
        
        print()
        print("=" * 100)
        print(">>> Test Summary")
        print(f"Interface: 02 - Apply Item Dictionary Data Transmission")
        print(f"URL: {url}")
        print(f"HTTP Status: {response.status_code} ({'Success' if response.status_code == 200 else 'Failed'})")
        print(f"Response Time: {response_time:.2f}ms")
        
        if response.status_code == 200:
            try:
                resp_data = response.json()
                if resp_data.get('code') == 0:
                    print("API Result: SUCCESS (code=0)")
                    print("Description: Apply item dictionary data successfully synced to Tianjian Cloud")
                else:
                    print(f"API Result: FAILED (code={resp_data.get('code')})")
                    print(f"Failure Reason: {resp_data.get('msg', 'Unknown error')}")
            except:
                print("API Result: Unable to parse response")
        else:
            print(f"API Result: HTTP Error {response.status_code}")
        
        print("=" * 100)
        
        return success and response.status_code == 200
        
    except requests.exceptions.Timeout:
        print("ERROR: Request timeout")
        print("Possible cause: Network delay or slow server response")
        return False
        
    except requests.exceptions.ConnectionError as e:
        print(f"ERROR: Connection error: {e}")
        print("Possible cause: Network unreachable or server down")
        return False
        
    except Exception as e:
        print(f"ERROR: Request exception: {e}")
        return False

def main():
    """主函数"""
    print("Starting Tianjian Cloud Interface 02 test...")
    print()
    
    try:
        success = test_interface_02()
        
        if success:
            print("\nTest completed - Interface call successful!")
        else:
            print("\nTest completed - Interface call failed, please check message format or network connection")
            
        return success
        
    except KeyboardInterrupt:
        print("\nUser interrupted test")
        return False
    except Exception as e:
        print(f"\nError occurred during test: {e}")
        return False

if __name__ == '__main__':
    import sys
    result = main()
    sys.exit(0 if result else 1)