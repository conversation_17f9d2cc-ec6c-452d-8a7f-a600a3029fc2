#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试18号接口
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from interface_18_getDoctorInfo import TianjianInterface18

# API配置
API_CONFIG = {
    'base_url': 'http://203.83.237.114:9300',
    'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
    'mic_code': 'MIC1.001E',
    'misc_id': 'MISC1.00001A',
    'timeout': 30
}

def test_interface_18():
    """测试18号接口 - 查询医生信息"""
    print("="*60)
    print("测试18号接口 - 查询医生信息")
    print("="*60)
    
    try:
        interface = TianjianInterface18(API_CONFIG)
        result = interface.query_doctor_info(
            doctor_id="",
            hospital_code="0350001",
            test_mode=True
        )
        print("18号接口测试通过")
        print(f"返回结果: {result}")
        return True
    except Exception as e:
        print(f"18号接口测试失败: {e}")
        return False

if __name__ == "__main__":
    test_interface_18()