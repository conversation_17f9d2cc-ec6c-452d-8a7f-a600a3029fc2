#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云07-10号接口综合测试脚本
测试主检结论回传、字典查询、科室结果重传、批量获取体检单信息接口
"""

import sys
import traceback
import json
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入07-10号接口
from interface_07_sendConclusion import TianjianInterface07
from interface_08_getDict import TianjianInterface08
from interface_09_retransmitDeptResult import TianjianInterface09
from interface_10_batchGetPeInfo import TianjianInterface10

# 统一API配置
API_CONFIG = {
    'base_url': 'http://203.83.237.114:9300',
    'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
    'mic_code': 'MIC1.001E',
    'misc_id': 'MISC1.00001A',
    'timeout': 30
}

def test_interface_07():
    """测试07号接口 - 主检结束结论回传"""
    print("\n" + "="*60)
    print("测试07号接口 - 主检结束结论回传")
    print("="*60)
    
    try:
        interface = TianjianInterface07(API_CONFIG)
        
        # 测试场景1：单个体检号结论发送（总审模式）
        print("\n[场景1] 测试单个体检号结论发送（总审模式）")
        result1 = interface.send_conclusion_data(
            pe_no="0220000001", 
            current_node_type=4,  # 总审
            test_mode=True
        )
        
        if result1['success']:
            print("=> 07号接口总审模式测试通过")
            print(f"   结论数量: {result1.get('conclusion_count', 0)}")
        else:
            print("=> 07号接口总审模式测试失败")
            print(f"   错误: {result1.get('message', '未知错误')}")
        
        # 测试场景2：主检模式结论发送
        print("\n[场景2] 测试主检模式结论发送")
        result2 = interface.send_conclusion_data(
            pe_no="0220000002",
            current_node_type=3,  # 主检
            test_mode=True
        )
        
        if result2['success']:
            print("=> 07号接口主检模式测试通过")
            print(f"   结论数量: {result2.get('conclusion_count', 0)}")
        else:
            print("=> 07号接口主检模式测试失败")
            print(f"   错误: {result2.get('message', '未知错误')}")
        
        return result1['success'] and result2['success']
        
    except Exception as e:
        print(f"[FAIL] 07号接口测试异常: {e}")
        return False

def test_interface_08():
    """测试08号接口 - 查询字典信息接口"""
    print("\n" + "="*60)
    print("测试08号接口 - 查询字典信息接口")
    print("="*60)
    
    try:
        interface = TianjianInterface08(API_CONFIG)
        
        # 测试场景1：查询所有字典信息
        print("\n📚 测试场景1：查询所有字典信息")
        result1 = interface.query_dict_info(
            dict_type="all",
            include_details=False,
            test_mode=True
        )
        
        if result1['success']:
            print("[OK] 08号接口查询所有字典测试通过")
            print(f"   检查项目: {result1.get('item_count', 0)} 条")
            print(f"   科室信息: {result1.get('dept_count', 0)} 条")
            print(f"   操作员: {result1.get('operator_count', 0)} 条")
        else:
            print("[FAIL] 08号接口查询所有字典测试失败")
            print(f"   错误: {result1.get('message', '未知错误')}")
        
        # 测试场景2：查询检查项目字典（包含明细）
        print("\n📚 测试场景2：查询检查项目字典（包含明细）")
        result2 = interface.query_dict_info(
            dict_type="item",
            include_details=True,
            test_mode=True
        )
        
        if result2['success']:
            print("[OK] 08号接口查询项目字典（含明细）测试通过")
            print(f"   检查项目: {result2.get('item_count', 0)} 条")
        else:
            print("[FAIL] 08号接口查询项目字典（含明细）测试失败")
            print(f"   错误: {result2.get('message', '未知错误')}")
        
        # 测试场景3：查询指定科室字典
        print("\n📚 测试场景3：查询指定科室字典")
        result3 = interface.query_dict_info(
            dict_type="dept",
            dict_code="01",
            test_mode=True
        )
        
        if result3['success']:
            print("[OK] 08号接口查询指定科室字典测试通过")
            print(f"   科室信息: {result3.get('dept_count', 0)} 条")
        else:
            print("[FAIL] 08号接口查询指定科室字典测试失败")
            print(f"   错误: {result3.get('message', '未知错误')}")
        
        return result1['success'] and result2['success'] and result3['success']
        
    except Exception as e:
        print(f"[FAIL] 08号接口测试异常: {e}")
        return False

def test_interface_09():
    """测试09号接口 - 体检科室结果重传接口"""
    print("\n" + "="*60)
    print("测试09号接口 - 体检科室结果重传接口")
    print("="*60)
    
    try:
        interface = TianjianInterface09(API_CONFIG)
        
        # 测试场景1：重传指定体检号的所有科室结果
        print("\n[SYNC] 测试场景1：重传指定体检号的所有科室结果")
        result1 = interface.retransmit_dept_result(
            pe_no="0220000001",
            test_mode=True
        )
        
        if result1['success']:
            print("[OK] 09号接口重传所有科室结果测试通过")
            print(f"   结果数量: {result1.get('result_count', 0)}")
        else:
            print("[FAIL] 09号接口重传所有科室结果测试失败")
            print(f"   错误: {result1.get('message', '未知错误')}")
        
        # 测试场景2：重传指定体检号指定科室的结果
        print("\n[SYNC] 测试场景2：重传指定体检号指定科室的结果")
        result2 = interface.retransmit_dept_result(
            pe_no="0220000002",
            dept_code="01",
            test_mode=True
        )
        
        if result2['success']:
            print("[OK] 09号接口重传指定科室结果测试通过")
            print(f"   结果数量: {result2.get('result_count', 0)}")
        else:
            print("[FAIL] 09号接口重传指定科室结果测试失败")
            print(f"   错误: {result2.get('message', '未知错误')}")
        
        # 测试场景3：批量重传科室结果
        print("\n[SYNC] 测试场景3：批量重传科室结果")
        result3 = interface.batch_retransmit_dept_results(
            pe_no_list=["0220000001", "0220000002"],
            test_mode=True
        )
        
        if result3['total'] > 0:
            success_rate = float(result3['success_rate'].replace('%', ''))
            if success_rate >= 50:  # 至少50%成功率
                print("[OK] 09号接口批量重传测试通过")
                print(f"   成功率: {result3['success_rate']}")
            else:
                print("[FAIL] 09号接口批量重传测试失败")
                print(f"   成功率过低: {result3['success_rate']}")
        else:
            print("[FAIL] 09号接口批量重传测试失败")
            print("   没有处理任何记录")
        
        return result1['success'] and result2['success']
        
    except Exception as e:
        print(f"[FAIL] 09号接口测试异常: {e}")
        return False

def test_interface_10():
    """测试10号接口 - 批量获取体检单信息"""
    print("\n" + "="*60)
    print("测试10号接口 - 批量获取体检单信息")
    print("="*60)
    
    try:
        interface = TianjianInterface10(API_CONFIG)
        
        # 测试场景1：根据体检号列表获取信息
        print("\n[LIST] 测试场景1：根据体检号列表获取信息")
        result1 = interface.batch_get_exam_info(
            client_codes=["0220000001", "0220000002"],
            limit=5,
            test_mode=True
        )
        
        if result1['success']:
            print("[OK] 10号接口根据体检号获取信息测试通过")
            print(f"   总计: {result1.get('total', 0)} 条")
        else:
            print("[FAIL] 10号接口根据体检号获取信息测试失败")
            print(f"   错误: {result1.get('error', '未知错误')}")
        
        # 测试场景2：根据日期范围获取信息
        print("\n[LIST] 测试场景2：根据日期范围获取信息")
        result2 = interface.batch_get_exam_info(
            start_date='2024-01-01',
            end_date='2024-12-31',
            limit=3,
            test_mode=True
        )
        
        if result2['success']:
            print("[OK] 10号接口根据日期范围获取信息测试通过")
            print(f"   总计: {result2.get('total', 0)} 条")
        else:
            print("[FAIL] 10号接口根据日期范围获取信息测试失败")
            print(f"   错误: {result2.get('error', '未知错误')}")
        
        # 测试场景3：根据状态过滤获取信息
        print("\n[LIST] 测试场景3：根据状态过滤获取信息")
        result3 = interface.batch_get_exam_info(
            pe_status='1',  # 已确认状态
            limit=2,
            test_mode=True
        )
        
        if result3['success']:
            print("[OK] 10号接口根据状态过滤获取信息测试通过")
            print(f"   总计: {result3.get('total', 0)} 条")
        else:
            print("[FAIL] 10号接口根据状态过滤获取信息测试失败")
            print(f"   错误: {result3.get('error', '未知错误')}")
        
        return result1['success'] and result2['success'] and result3['success']
        
    except Exception as e:
        print(f"[FAIL] 10号接口测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("天健云07-10号接口综合测试")
    print("="*60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"API配置: {API_CONFIG['base_url']}")
    print("="*60)
    
    # 测试函数列表
    test_functions = [
        ("07号接口", test_interface_07),
        ("08号接口", test_interface_08),
        ("09号接口", test_interface_09),
        ("10号接口", test_interface_10)
    ]
    
    # 执行测试
    total_tests = len(test_functions)
    passed_tests = 0
    failed_tests = 0
    
    for interface_name, test_func in test_functions:
        try:
            print(f"\n开始测试 {interface_name}...")
            if test_func():
                passed_tests += 1
                print(f"[OK] {interface_name} 测试通过")
            else:
                failed_tests += 1
                print(f"[FAIL] {interface_name} 测试失败")
        except Exception as e:
            print(f"[FAIL] {interface_name} 测试函数执行异常: {e}")
            traceback.print_exc()
            failed_tests += 1
    
    # 输出测试总结
    print("\n" + "="*60)
    print("测试总结")
    print("="*60)
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {failed_tests}")
    print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if failed_tests == 0:
        print("\n🎉 所有天健云07-10号接口测试通过！")
        print("Phase 1开发计划第一阶段完成：")
        print("[OK] 07号接口 - 主检结束结论回传")
        print("[OK] 08号接口 - 查询字典信息接口")
        print("[OK] 09号接口 - 体检科室结果重传接口")
        print("[OK] 10号接口 - 批量获取体检单信息")
        print("\n现在系统支持完整的01-21号天健云接口！")
        return True
    else:
        print(f"\n[FAIL] 有 {failed_tests} 个接口测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)