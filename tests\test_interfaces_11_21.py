#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云11-21号接口综合测试脚本
测试所有新实现的查询类接口
"""

import sys
import traceback
from datetime import datetime

# 导入所有11-21号接口
from interface_11_getApplyItemDict import TianjianInterface11
from interface_12_lockPeInfo import TianjianInterface12
from interface_13_updatePeStatus import TianjianInterface13
from interface_14_markAbnormal import TianjianInterface14
from interface_15_returnDept import TianjianInterface15
from interface_16_getImages import TianjianInterface16
from interface_17_deleteAbnormal import TianjianInterface17
from interface_18_getDoctorInfo import TianjianInterface18
from interface_19_getDeptInfo import TianjianInterface19
from interface_20_getPersonalOrders import TianjianInterface20
from interface_21_getAbnormalNotice import TianjianInterface21

# 统一API配置
API_CONFIG = {
    'base_url': 'http://203.83.237.114:9300',
    'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
    'mic_code': 'MIC1.001E',
    'misc_id': 'MISC1.00001A',
    'timeout': 30
}

def test_interface_11():
    """测试11号接口 - 查询项目字典信息"""
    print("\n" + "="*60)
    print("测试11号接口 - 查询项目字典信息")
    print("="*60)
    
    try:
        interface = TianjianInterface11(API_CONFIG)
        result = interface.query_apply_item_dict(limit=3, test_mode=True)
        print("11号接口测试通过")
        return True
    except Exception as e:
        print(f"11号接口测试失败: {e}")
        return False

def test_interface_12():
    """测试12号接口 - 主检锁定与解锁"""
    print("\n" + "="*60)
    print("测试12号接口 - 主检锁定与解锁")
    print("="*60)
    
    try:
        interface = TianjianInterface12(API_CONFIG)
        pe_info_list = [{
            "accountId": "09DOCTOR001",
            "currentNodeType": 3,
            "force": False,
            "operationType": 1,
            "peNo": "5000003"
        }]
        result = interface.lock_or_unlock_pe_info("09ADMIN001", pe_info_list, test_mode=True)
        print("12号接口测试通过")
        return True
    except Exception as e:
        print(f"12号接口测试失败: {e}")
        return False

def test_interface_13():
    """测试13号接口 - 体检报告状态更新"""
    print("\n" + "="*60)
    print("测试13号接口 - 体检报告状态更新")
    print("="*60)
    
    try:
        interface = TianjianInterface13(API_CONFIG)
        result = interface.update_pe_status(
            pe_no="5000003",
            node_type="3",
            do_user={"code": "09DOCTOR001", "name": "张医生"},
            test_mode=True
        )
        print("13号接口测试通过")
        return True
    except Exception as e:
        print(f"13号接口测试失败: {e}")
        return False

def test_interface_14():
    """测试14号接口 - 重要异常标注"""
    print("\n" + "="*60)
    print("测试14号接口 - 重要异常标注")
    print("="*60)
    
    try:
        interface = TianjianInterface14(API_CONFIG)
        result = interface.mark_abnormal(
            pe_no="5000006",
            level="1",
            importance_code="ABN001",
            mark_doctor={"code": "09DOCTOR001", "name": "张医生"},
            mark_dept={"code": "09DEPT001", "name": "内科"},
            relate_item=["ITEM001"],
            relate_conclusion=["血pressure偏高"],
            remark="建议进一步检查",
            test_mode=True
        )
        print("14号接口测试通过")
        return True
    except Exception as e:
        print(f"14号接口测试失败: {e}")
        return False

def test_interface_15():
    """测试15号接口 - 分科退回"""
    print("\n" + "="*60)
    print("测试15号接口 - 分科退回")
    print("="*60)
    
    try:
        interface = TianjianInterface15(API_CONFIG)
        result = interface.return_dept(
            pe_no="5000006",
            mark_doctor="09DOCTOR001",
            return_dept={"code": "09DEPT001", "name": "内科"},
            error_item="ITEM001",
            remark="检查结果需要重新确认",
            test_mode=True
        )
        print("15号接口测试通过")
        return True
    except Exception as e:
        print(f"15号接口测试失败: {e}")
        return False

def test_interface_16():
    """测试16号接口 - 查询图片"""
    print("\n" + "="*60)
    print("测试16号接口 - 查询图片")
    print("="*60)
    
    try:
        interface = TianjianInterface16(API_CONFIG)
        result = interface.query_images(
            pe_no="PE202501010001",
            dept_id="XRAY001",
            apply_item_ids=["XRAY001"],
            test_mode=True
        )
        print("16号接口测试通过")
        return True
    except Exception as e:
        print(f"16号接口测试失败: {e}")
        return False

def test_interface_17():
    """测试17号接口 - 重要异常删除"""
    print("\n" + "="*60)
    print("测试17号接口 - 重要异常删除")
    print("="*60)
    
    try:
        interface = TianjianInterface17(API_CONFIG)
        result = interface.delete_abnormal(
            pe_no="PE202501010001",
            importance_code="ABN001",
            test_mode=True
        )
        print("17号接口测试通过")
        return True
    except Exception as e:
        print(f"17号接口测试失败: {e}")
        return False

def test_interface_18():
    """测试18号接口 - 查询医生信息"""
    print("\n" + "="*60)
    print("测试18号接口 - 查询医生信息")
    print("="*60)
    
    try:
        interface = TianjianInterface18(API_CONFIG)
        result = interface.query_doctor_info(
            doctor_id="",
            hospital_code="0350001",
            test_mode=True
        )
        print("18号接口测试通过")
        return True
    except Exception as e:
        print(f"18号接口测试失败: {e}")
        return False

def test_interface_19():
    """测试19号接口 - 查询科室信息"""
    print("\n" + "="*60)
    print("测试19号接口 - 查询科室信息")
    print("="*60)
    
    try:
        interface = TianjianInterface19(API_CONFIG)
        result = interface.query_dept_info(
            dept_id="",
            hospital_code="0350001",
            test_mode=True
        )
        print("19号接口测试通过")
        return True
    except Exception as e:
        print(f"19号接口测试失败: {e}")
        return False

def test_interface_20():
    """测试20号接口 - 查询个人开单情况"""
    print("\n" + "="*60)
    print("测试20号接口 - 查询个人开单情况")
    print("="*60)
    
    try:
        interface = TianjianInterface20(API_CONFIG)
        result = interface.query_personal_orders(
            pe_no_list=["PE202501010001"],
            hospital_code="0350001",
            test_mode=True
        )
        print("20号接口测试通过")
        return True
    except Exception as e:
        print(f"20号接口测试失败: {e}")
        return False

def test_interface_21():
    """测试21号接口 - 查询重要异常通知数据"""
    print("\n" + "="*60)
    print("测试21号接口 - 查询重要异常通知数据")
    print("="*60)
    
    try:
        interface = TianjianInterface21(API_CONFIG)
        result = interface.query_abnormal_notice(
            pe_no="PE202501010001",
            hospital_code="0350001",
            hospital_name="嘉仁体检中心",
            test_mode=True
        )
        print("21号接口测试通过")
        return True
    except Exception as e:
        print(f"21号接口测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("天健云11-21号接口综合测试")
    print("="*60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"API配置: {API_CONFIG['base_url']}")
    print("="*60)
    
    # 测试函数列表
    test_functions = [
        test_interface_11,
        test_interface_12,
        test_interface_13,
        test_interface_14,
        test_interface_15,
        test_interface_16,
        test_interface_17,
        test_interface_18,
        test_interface_19,
        test_interface_20,
        test_interface_21
    ]
    
    # 执行测试
    total_tests = len(test_functions)
    passed_tests = 0
    failed_tests = 0
    
    for test_func in test_functions:
        try:
            if test_func():
                passed_tests += 1
            else:
                failed_tests += 1
        except Exception as e:
            print(f"测试函数 {test_func.__name__} 执行异常: {e}")
            traceback.print_exc()
            failed_tests += 1
    
    # 输出测试总结
    print("\n" + "="*60)
    print("测试总结")
    print("="*60)
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {failed_tests}")
    print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if failed_tests == 0:
        print("\n所有天健云11-21号接口测试通过！")
        return True
    else:
        print(f"\n有 {failed_tests} 个接口测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)