天健云接口系统测试报告
================================================================================
测试时间: 2025-07-24 20:57:22
测试环境: Windows
项目版本: v1.2.0

测试统计:
- 总测试数: 12
- 通过测试: 1
- 失败测试: 11
- 成功率: 8.3%

详细结果:
--------------------------------------------------------------------------------
[FAIL] 失败 DB连接测试
[FAIL] 失败 API连接测试
[WARN]  无main函数 配置测试
[FAIL] 失败 07-10号接口测试
[FAIL] 失败 11-21号接口测试
[FAIL] 异常: 'gbk' codec can't encode character '\u26a0' in position 0: illegal multibyte sequence 接口测试套件
[FAIL] 异常: 'gbk' codec can't encode character '\U0001f680' in position 32: illegal multibyte sequence 01 Complete Message测试
[FAIL] 异常: 'gbk' codec can't encode character '\u26a0' in position 0: illegal multibyte sequence Affirmdate Query测试
[FAIL] 异常: 'gbk' codec can't encode character '\U0001f550' in position 0: illegal multibyte sequence Apply Item Interface测试
[OK] 通过 Interface 02 Fixed测试
[WARN]  无main函数 Sync Data测试
[FAIL] 失败: 'gbk' codec can't encode character '\U0001f50d' in position 2: illegal multibyte sequence 系统状态检查

--------------------------------------------------------------------------------
测试总结:

[WARN] 发现 11 个测试失败，请检查相关模块。

建议措施:
1. 检查数据库连接配置
2. 验证API端点可用性
3. 确认接口参数配置
4. 查看详细错误日志
