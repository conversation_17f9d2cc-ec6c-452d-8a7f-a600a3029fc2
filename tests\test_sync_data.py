#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试同步数据脚本
查看数据库中的体检数据分布情况
"""

from sync_service import HealthSyncService
from test_config import TestConfig, get_connection_string
from sqlalchemy import create_engine, text
from datetime import datetime, timedelta
from config import Config

def check_data_distribution():
    """检查数据分布情况"""
    print("="*60)
    print("数据分布检查")
    print("="*60)
    
    config = TestConfig.MAIN_DB_CONFIG
    engine = create_engine(get_connection_string(config))
    
    try:
        with engine.connect() as conn:
            # 检查总数据量
            result = conn.execute(text("SELECT COUNT(*) as total FROM T_Register_Main"))
            total = result.fetchone()[0]
            print(f"\n总体检记录数: {total}")
            
            # 检查有效数据（有姓名和身份证）
            result = conn.execute(text("""
                SELECT COUNT(*) as valid_count 
                FROM T_Register_Main 
                WHERE cName IS NOT NULL AND cName != '' 
                  AND cIdCard IS NOT NULL AND cIdCard != ''
            """))
            valid_count = result.fetchone()[0]
            print(f"有效记录数（有姓名和身份证）: {valid_count}")
            
            # 检查最近数据的日期分布
            print("\n最近数据分布:")
            for days in [1, 7, 30, 90, 365]:
                result = conn.execute(text(f"""
                    SELECT COUNT(*) as count 
                    FROM T_Register_Main 
                    WHERE dOperdate >= DATEADD(day, -{days}, GETDATE())
                      AND cName IS NOT NULL AND cName != ''
                """))
                count = result.fetchone()[0]
                print(f"  最近{days:3d}天: {count:4d} 条记录")
            
            # 查看最新的10条记录（不管日期）
            print("\n最新的10条记录:")
            result = conn.execute(text("""
                SELECT TOP 10 
                    cClientCode, cName, cSex, dOperdate, cIdCard, cStatus
                FROM T_Register_Main 
                WHERE cName IS NOT NULL AND cName != ''
                ORDER BY cClientCode DESC
            """))
            
            for i, row in enumerate(result, 1):
                date_str = row[3].strftime('%Y-%m-%d') if row[3] else '无日期'
                print(f"  {i:2d}. {row[0]} - {row[1]} - {date_str} - {row[5] or '无状态'}")
                
            # 查看日期范围
            print("\n数据日期范围:")
            result = conn.execute(text("""
                SELECT 
                    MIN(dOperdate) as min_date,
                    MAX(dOperdate) as max_date
                FROM T_Register_Main 
                WHERE dOperdate IS NOT NULL
            """))
            row = result.fetchone()
            if row[0] and row[1]:
                print(f"  最早日期: {row[0].strftime('%Y-%m-%d')}")
                print(f"  最晚日期: {row[1].strftime('%Y-%m-%d')}")
            
    except Exception as e:
        print(f"检查数据分布失败: {e}")

def test_sync_service_with_all_data():
    """测试同步服务获取所有数据"""
    print("\n" + "="*60)
    print("测试同步服务（所有数据）")
    print("="*60)
    
    sync_service = HealthSyncService()
    
    # 获取最近1年的数据
    examinations = sync_service.get_recent_examinations(365)
    
    if examinations:
        print(f"\n找到 {len(examinations)} 条体检数据:")
        
        # 显示前5条
        for i, exam in enumerate(examinations[:5], 1):
            print(f"  {i}. 编号:{exam['client_code']} 姓名:{exam['name']} 性别:{exam['sex']} 日期:{exam['register_date']}")
        
        if len(examinations) > 5:
            print(f"  ... 还有 {len(examinations) - 5} 条数据")
        
        # 测试第一条数据的详细信息
        if examinations:
            print(f"\n获取第一条数据的详细信息:")
            details = sync_service.get_examination_details(examinations[0]['client_code'])
            
            if details:
                basic = details['basic_info']
                print(f"  客户编号: {basic.get('cClientCode', '')}")
                print(f"  姓名: {basic.get('cName', '')}")
                print(f"  性别: {basic.get('cSex', '')}")
                print(f"  身份证: {basic.get('cIdCard', '')}")
                print(f"  电话: {basic.get('cTel', '')}")
                print(f"  体检项目数: {len(details['exam_items'])}")
                print(f"  检查结果数: {len(details['check_results'])}")
        
        # 测试API数据格式化
        print(f"\n测试API数据格式化:")
        api_data = sync_service.format_for_tianjian_api(examinations[0])
        print(f"  天健云API数据格式:")
        print(f"    机构代码: {api_data['org_code']}")
        print(f"    系统ID: {api_data['system_id']}")
        print(f"    姓名: {api_data['person_info']['name']}")
        print(f"    身份证: {api_data['person_info']['id_card']}")
        print(f"    体检编号: {api_data['exam_info']['exam_id']}")
        
        # 测试同步（前3条）
        print(f"\n测试同步前3条数据:")
        test_data = examinations[:3]
        result = sync_service.sync_to_tianjian(test_data)
        print(f"  同步结果: 总计{result['total']}, 成功{result['success']}, 失败{result['error']}")
        
    else:
        print("未找到任何体检数据")

if __name__ == '__main__':
    check_data_distribution()
    test_sync_service_with_all_data() 