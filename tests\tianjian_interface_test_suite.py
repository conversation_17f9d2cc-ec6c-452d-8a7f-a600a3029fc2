#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天健云接口完整测试套件
测试所有01-06号接口的完整功能
"""

import sys
import traceback
from interface_01_sendPeInfo import TianjianInterface01
from interface_02_syncApplyItem import TianjianInterface02
from interface_03_deptInfo import TianjianInterface03
from interface_04_syncUser import TianjianInterface04
from interface_05_syncDept import TianjianInterface05
from interface_06_syncDict import TianjianInterface06

# 统一API配置
API_CONFIG = {
    'base_url': 'http://203.83.237.114:9300',
    'api_key': '3CNVizIjUq87IrczWqQB8SxjvPmVMTKM',
    'mic_code': 'MIC1.001E',
    'misc_id': 'MISC1.00001A',
    'timeout': 30
}


class TianjianInterfaceTestSuite:
    """天健云接口完整测试套件"""
    
    def __init__(self):
        """初始化所有接口实例"""
        self.interface_01 = TianjianInterface01(API_CONFIG)
        self.interface_02 = TianjianInterface02(API_CONFIG)
        self.interface_03 = TianjianInterface03(API_CONFIG)
        self.interface_04 = TianjianInterface04(API_CONFIG)
        self.interface_05 = TianjianInterface05(API_CONFIG)
        self.interface_06 = TianjianInterface06(API_CONFIG)
    
    def run_all_tests(self, test_mode: bool = True, send_actual: bool = False):
        """
        运行所有接口测试
        
        Args:
            test_mode: 是否运行测试模式
            send_actual: 是否实际发送数据
        """
        print("=" * 80)
        print("天健云接口完整测试套件")
        print("=" * 80)
        
        test_results = {}
        
        # 测试01号接口 - 单次体检基本信息传输
        print(f"\n{'='*20} 01号接口测试 {'='*20}")
        print("接口名称: 单次体检基本信息传输")
        print("接口地址: /dx/inter/sendPeInfo")
        
        try:
            if test_mode:
                result = self.interface_01.send_exam_info(days=30, limit=2, test_mode=True)
                test_results['01_sendPeInfo_test'] = result
                print(f"测试模式结果: {result['message']}")
            
            if send_actual:
                result = self.interface_01.send_exam_info(days=30, limit=2, test_mode=False)
                test_results['01_sendPeInfo_actual'] = result
                print(f"实际发送结果: {result['message']}")
        
        except Exception as e:
            error_msg = f"01号接口测试异常: {str(e)}"
            print(f"错误: {error_msg}")
            test_results['01_sendPeInfo_error'] = error_msg
        
        # 测试02号接口 - 申请项目字典数据传输
        print(f"\n{'='*20} 02号接口测试 {'='*20}")
        print("接口名称: 申请项目字典数据传输")
        print("接口地址: /dx/inter/syncApplyItem")
        
        try:
            if test_mode:
                result = self.interface_02.sync_apply_items(limit=5, test_mode=True)
                test_results['02_syncApplyItem_test'] = result
                print(f"测试模式结果: {result['message']}")
            
            if send_actual:
                result = self.interface_02.sync_apply_items(limit=5, test_mode=False, batch_size=3)
                test_results['02_syncApplyItem_actual'] = result
                print(f"实际发送结果: {result['message']}")
        
        except Exception as e:
            error_msg = f"02号接口测试异常: {str(e)}"
            print(f"错误: {error_msg}")
            test_results['02_syncApplyItem_error'] = error_msg
        
        # 测试03号接口 - 体检科室结果传输
        print(f"\n{'='*20} 03号接口测试 {'='*20}")
        print("接口名称: 体检科室结果传输")
        print("接口地址: /dx/inter/deptInfo")
        
        try:
            if test_mode:
                result = self.interface_03.sync_dept_results(days=30, limit=3, test_mode=True)
                test_results['03_deptInfo_test'] = result
                print(f"测试模式结果: {result['message']}")
            
            if send_actual:
                result = self.interface_03.sync_dept_results(days=30, limit=3, test_mode=False, batch_size=2)
                test_results['03_deptInfo_actual'] = result
                print(f"实际发送结果: {result['message']}")
        
        except Exception as e:
            error_msg = f"03号接口测试异常: {str(e)}"
            print(f"错误: {error_msg}")
            test_results['03_deptInfo_error'] = error_msg
        
        # 测试04号接口 - 医生信息传输
        print(f"\n{'='*20} 04号接口测试 {'='*20}")
        print("接口名称: 医生信息传输")
        print("接口地址: /dx/inter/syncUser")
        
        try:
            if test_mode:
                result = self.interface_04.sync_operators(test_mode=True)
                test_results['04_syncUser_test'] = result
                print(f"测试模式结果: {result['message']}")
            
            if send_actual:
                result = self.interface_04.sync_operators(test_mode=False, batch_size=10)
                test_results['04_syncUser_actual'] = result
                print(f"实际发送结果: {result['message']}")
        
        except Exception as e:
            error_msg = f"04号接口测试异常: {str(e)}"
            print(f"错误: {error_msg}")
            test_results['04_syncUser_error'] = error_msg
        
        # 测试05号接口 - 科室信息传输
        print(f"\n{'='*20} 05号接口测试 {'='*20}")
        print("接口名称: 科室信息传输")
        print("接口地址: /dx/inter/syncDept")
        
        try:
            if test_mode:
                result = self.interface_05.sync_departments(test_mode=True)
                test_results['05_syncDept_test'] = result
                print(f"测试模式结果: {result['message']}")
            
            if send_actual:
                result = self.interface_05.sync_departments(test_mode=False, batch_size=10)
                test_results['05_syncDept_actual'] = result
                print(f"实际发送结果: {result['message']}")
        
        except Exception as e:
            error_msg = f"05号接口测试异常: {str(e)}"
            print(f"错误: {error_msg}")
            test_results['05_syncDept_error'] = error_msg
        
        # 测试06号接口 - 字典信息传输
        print(f"\n{'='*20} 06号接口测试 {'='*20}")
        print("接口名称: 字典信息传输")
        print("接口地址: /dx/inter/syncDict")
        
        try:
            if test_mode:
                result = self.interface_06.sync_dict_info(test_mode=True)
                test_results['06_syncDict_test'] = result
                print(f"测试模式结果: {result['message']}")
            
            if send_actual:
                result = self.interface_06.sync_dict_info(test_mode=False, batch_size=10)
                test_results['06_syncDict_actual'] = result
                print(f"实际发送结果: {result['message']}")
        
        except Exception as e:
            error_msg = f"06号接口测试异常: {str(e)}"
            print(f"错误: {error_msg}")
            test_results['06_syncDict_error'] = error_msg
        
        # 输出测试总结
        self._print_test_summary(test_results)
        
        return test_results
    
    def _print_test_summary(self, test_results: dict):
        """打印测试总结"""
        print(f"\n{'='*80}")
        print("测试总结")
        print(f"{'='*80}")
        
        # 统计结果
        total_tests = 0
        success_tests = 0
        failed_tests = 0
        error_tests = 0
        
        interface_status = {}
        
        for key, result in test_results.items():
            total_tests += 1
            interface_num = key.split('_')[0]
            
            if interface_num not in interface_status:
                interface_status[interface_num] = {'test': None, 'actual': None, 'error': None}
            
            if 'error' in key:
                error_tests += 1
                interface_status[interface_num]['error'] = result
            elif isinstance(result, dict) and result.get('success'):
                success_tests += 1
                if 'test' in key:
                    interface_status[interface_num]['test'] = 'success'
                else:
                    interface_status[interface_num]['actual'] = 'success'
            else:
                failed_tests += 1
                if 'test' in key:
                    interface_status[interface_num]['test'] = 'failed'
                else:
                    interface_status[interface_num]['actual'] = 'failed'
        
        # 输出接口状态
        print("\n接口测试状态:")
        print(f"{'接口':<15} {'测试模式':<15} {'实际发送':<15} {'错误信息':<30}")
        print("-" * 80)
        
        interface_names = {
            '01': '体检信息传输',
            '02': '申请项目字典',
            '03': '科室结果传输',
            '04': '医生信息传输',
            '05': '科室信息传输',
            '06': '字典信息传输'
        }
        
        for interface_num in sorted(interface_status.keys()):
            status = interface_status[interface_num]
            name = interface_names.get(interface_num, f'{interface_num}号接口')
            
            test_status = status['test'] or '未测试'
            actual_status = status['actual'] or '未发送'
            error_info = status['error'] or ''
            
            # 状态显示
            test_display = '✓ 成功' if test_status == 'success' else ('✗ 失败' if test_status == 'failed' else test_status)
            actual_display = '✓ 成功' if actual_status == 'success' else ('✗ 失败' if actual_status == 'failed' else actual_status)
            
            print(f"{name:<15} {test_display:<15} {actual_display:<15} {str(error_info)[:30]:<30}")
        
        # 输出统计信息
        print(f"\n总体统计:")
        print(f"  总测试数: {total_tests}")
        print(f"  成功数: {success_tests}")
        print(f"  失败数: {failed_tests}")
        print(f"  异常数: {error_tests}")
        print(f"  成功率: {(success_tests/total_tests*100):.1f}%" if total_tests > 0 else "成功率: 0%")


def main():
    """主函数"""
    print("天健云接口完整测试套件")
    print("支持01-06号所有接口的测试")
    
    # 创建测试套件
    test_suite = TianjianInterfaceTestSuite()
    
    # 菜单选择
    print("\n请选择测试模式:")
    print("1. 仅测试模式 (验证数据格式，不实际发送)")
    print("2. 测试模式 + 实际发送 (小批量数据)")
    print("3. 仅实际发送 (不运行测试模式)")
    print("4. 退出")
    
    try:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            print("\n运行测试模式...")
            test_suite.run_all_tests(test_mode=True, send_actual=False)
        
        elif choice == '2':
            print("\n运行测试模式 + 实际发送...")
            confirm = input("确认要实际发送数据到天健云？(y/N): ")
            if confirm.lower() == 'y':
                test_suite.run_all_tests(test_mode=True, send_actual=True)
            else:
                print("已取消实际发送，仅运行测试模式")
                test_suite.run_all_tests(test_mode=True, send_actual=False)
        
        elif choice == '3':
            print("\n仅运行实际发送...")
            confirm = input("确认要实际发送数据到天健云？(y/N): ")
            if confirm.lower() == 'y':
                test_suite.run_all_tests(test_mode=False, send_actual=True)
            else:
                print("已取消")
        
        elif choice == '4':
            print("退出程序")
            sys.exit(0)
        
        else:
            print("无效选择")
            sys.exit(1)
    
    except KeyboardInterrupt:
        print("\n\n用户中断程序")
        sys.exit(0)
    except Exception as e:
        print(f"\n程序执行异常: {e}")
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()