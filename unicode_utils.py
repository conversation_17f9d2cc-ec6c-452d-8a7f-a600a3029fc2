#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Unicode编码处理工具
解决Windows控制台GBK编码问题
"""

import sys
import os


def safe_print(*args, **kwargs):
    """
    安全打印函数，处理Unicode字符编码问题
    
    Args:
        *args: 要打印的参数
        **kwargs: print函数的关键字参数
    """
    try:
        # 尝试直接打印
        print(*args, **kwargs)
    except UnicodeEncodeError:
        # 如果编码失败，替换为ASCII字符
        safe_args = []
        for arg in args:
            if isinstance(arg, str):
                # 替换常用的emoji字符
                safe_arg = arg
                emoji_replacements = {
                    '[OK]': '[OK]',
                    '[FAIL]': '[FAIL]', 
                    '[WARN]': '[WARN]',
                    '[TARGET]': '[TARGET]',
                    '[DATA]': '[DATA]',
                    '[FIRE]': '[FIRE]',
                    '[IDEA]': '[IDEA]',
                    '[ROCKET]': '[ROCKET]',
                    '[LIST]': '[LIST]',
                    '[TOOL]': '[TOOL]',
                    '[NOTE]': '[NOTE]',
                    '[STAR]': '[STAR]',
                    '[MAGIC]': '[MAGIC]',
                    '[CHART]': '[CHART]',
                    '[PARTY]': '[PARTY]',
                    '[SOS]': '[SOS]',
                    '[GAME]': '[GAME]',
                    '[DOC]': '[DOC]',
                    '[SAVE]': '[SAVE]',
                    '[WEB]': '[WEB]',
                    '[SYNC]': '[SYNC]',
                    '[PC]': '[PC]',
                    '[HOSPITAL]': '[HOSPITAL]',
                    '[SIGNAL]': '[SIGNAL]',
                    '[LOCK]': '[LOCK]',
                    '[BOLT]': '[BOLT]',
                    '[USER]': '[USER]',
                    '[BUILDING]': '[BUILDING]',
                    '[DOCTOR]': '[DOCTOR]',
                    '[TEST]': '[TEST]',
                    '[PHONE]': '[PHONE]',
                    '[EMAIL]': '[EMAIL]',
                    '[BOOK]': '[BOOK]',
                    '💰': '[MONEY]',
                    '🛠️': '[WRENCH]',
                    '🎨': '[ART]',
                    '📅': '[CALENDAR]',
                    '🔍': '[SEARCH]',
                    '💪': '[MUSCLE]',
                    '🎵': '[MUSIC]'
                }
                
                for emoji, replacement in emoji_replacements.items():
                    safe_arg = safe_arg.replace(emoji, replacement)
                
                # 进一步处理其他Unicode字符
                safe_arg = safe_arg.encode('ascii', errors='replace').decode('ascii')
                safe_args.append(safe_arg)
            else:
                safe_args.append(arg)
        
        print(*safe_args, **kwargs)


def configure_console_encoding():
    """
    配置控制台编码，尝试设置为UTF-8
    """
    if sys.platform == 'win32':
        try:
            # 尝试设置控制台代码页为UTF-8
            os.system('chcp 65001 >nul 2>&1')
        except:
            pass
    
    # 设置标准输出编码
    if hasattr(sys.stdout, 'reconfigure'):
        try:
            sys.stdout.reconfigure(encoding='utf-8', errors='replace')
        except:
            pass
    elif hasattr(sys.stdout, 'buffer'):
        try:
            import io
            sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
        except:
            pass


def get_safe_status_symbol(status):
    """
    获取安全的状态符号
    
    Args:
        status (str): 状态类型 ('success', 'error', 'warning', 'info')
    
    Returns:
        str: 安全的状态符号
    """
    symbols = {
        'success': '[OK]',
        'error': '[FAIL]',
        'warning': '[WARN]', 
        'info': '[INFO]',
        'pending': '[PENDING]',
        'running': '[RUNNING]',
        'completed': '[DONE]'
    }
    return symbols.get(status, '[?]')


def format_safe_message(message, status=None):
    """
    格式化安全消息
    
    Args:
        message (str): 消息内容
        status (str): 状态类型
    
    Returns:
        str: 格式化后的安全消息
    """
    if status:
        symbol = get_safe_status_symbol(status)
        return f"{symbol} {message}"
    return message


# 导出常用函数
__all__ = [
    'safe_print',
    'configure_console_encoding', 
    'get_safe_status_symbol',
    'format_safe_message'
]


# 如果直接运行此文件，进行测试
if __name__ == "__main__":
    configure_console_encoding()
    
    print("Unicode编码测试:")
    safe_print("[OK] 这是一个成功消息")
    safe_print("[FAIL] 这是一个错误消息") 
    safe_print("[WARN] 这是一个警告消息")
    safe_print("[TARGET] 这是一个目标消息")
    
    print("\n状态符号测试:")
    print(format_safe_message("数据库连接", "success"))
    print(format_safe_message("API调用失败", "error"))
    print(format_safe_message("配置检查", "warning"))
    print(format_safe_message("系统信息", "info"))