#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证18号接口GUI日志修改
"""

import json
import requests

def verify_18_interface_logs():
    """验证18号接口日志修改"""
    print("验证18号接口GUI日志修改")
    print("=" * 50)
    
    base_url = "http://localhost:5007"
    
    # 测试数据
    test_data = {"id": "", "shopcode": "08"}
    
    try:
        # 检查GUI服务器
        try:
            health_response = requests.get(f"{base_url}/health", timeout=5)
            if health_response.status_code != 200:
                raise Exception("服务器未正常响应")
        except:
            print("[错误] GUI服务器未运行")
            print("[信息] 请运行: python gui_main.py")
            return False
        
        print(f"[信息] GUI服务器正在运行")
        print(f"[请求] {json.dumps(test_data, ensure_ascii=False)}")
        
        # 发送测试请求
        response = requests.post(
            f"{base_url}/dx/inter/getDoctorInfo",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"[响应] 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"[响应] 返回码: {result.get('code', 'N/A')}")
            print(f"[响应] 消息: {result.get('msg', 'N/A')}")
            print(f"[响应] 数据条数: {len(result.get('data', []))}")
            
            print(f"\n[期望日志格式]")
            print(f"请求: 18号接口: 收到查询医生信息请求 - 查询全部医生, 机构: 08")
            
            if result.get('code') == 0:
                data_count = len(result.get('data', []))
                if data_count > 0:
                    print(f"成功: 18号接口: 查询成功 - 查询全部医生, 机构: 08, 返回医生数: {data_count}")
                else:
                    print(f"完成: 18号接口: 查询完成但未找到医生信息 - 查询全部医生, 机构: 08")
            else:
                print(f"失败: 18号接口: 查询失败 - 查询全部医生, 机构: 08, 错误: {result.get('msg', '')}")
            
            print(f"\n[成功] 18号接口日志格式已更新，像16号接口一样显示详细信息")
            return True
        else:
            print(f"[错误] HTTP错误: {response.status_code}")
            print(f"[错误] 响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"[异常] {str(e)}")
        return False

if __name__ == "__main__":
    success = verify_18_interface_logs()
    
    print(f"\n" + "=" * 50)
    if success:
        print("[总结] 18号接口GUI日志修改验证成功")
        print("[信息] 新的日志格式包含:")
        print("  - 医生ID（或'查询全部医生'）")
        print("  - 机构编码（如果提供）")
        print("  - 查询状态（成功/失败/未找到）")
        print("  - 返回的医生数量")
    else:
        print("[总结] 18号接口GUI日志修改验证失败")
        print("[建议] 请检查GUI服务器是否正在运行")