#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证18号接口修复的完整测试
"""

import json
import requests
import time
import threading
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_gui_interface_18():
    """测试GUI中的18号接口"""
    print("[TEST] 测试GUI服务器上的18号接口")
    print("=" * 50)
    
    base_url = "http://localhost:5007"
    
    # 测试请求数据
    test_data = {
        "id": "",
        "shopcode": "08"
    }
    
    try:
        # 发送POST请求到GUI服务器的18号接口
        response = requests.post(
            f"{base_url}/dx/inter/getDoctorInfo",
            json=test_data,
            timeout=10
        )
        
        print(f"[REQUEST] URL: {base_url}/dx/inter/getDoctorInfo")
        print(f"[REQUEST] Data: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
        print(f"[RESPONSE] Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"[RESPONSE] 返回结果:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
            # 验证是否没有NoneType错误
            if result.get('code') == 0:
                data = result.get('data')
                if data:
                    print(f"\n[SUCCESS] 查询成功，返回 {len(data)} 条医生信息，没有NoneType错误")
                else:
                    print(f"\n[SUCCESS] 查询成功，没有返回数据，但没有NoneType错误")
            else:
                print(f"\n[INFO] 查询返回码: {result.get('code')}, 消息: {result.get('msg')}")
                
        else:
            print(f"[ERROR] HTTP错误: {response.status_code}")
            print(f"[ERROR] 响应内容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print(f"[ERROR] 无法连接到GUI服务器 {base_url}")
        print("[INFO] 请确保GUI服务器正在运行: python gui_main.py")
    except Exception as e:
        print(f"[ERROR] 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()

def check_gui_server_running():
    """检查GUI服务器是否运行"""
    try:
        response = requests.get("http://localhost:5007/health", timeout=5)
        return response.status_code == 200
    except:
        return False

if __name__ == "__main__":
    if check_gui_server_running():
        print("[INFO] GUI服务器正在运行")
        test_gui_interface_18()
    else:
        print("[WARNING] GUI服务器未运行")
        print("[INFO] 请运行以下命令启动GUI服务器:")
        print("python gui_main.py")
        print("\n[INFO] 正在测试接口文件本身的修复...")
        
        # 测试接口文件本身的修复
        from interface_18_getDoctorInfo import TianjianInterface18
        from config import Config
        
        api_config = Config.get_tianjian_api_config()
        interface = TianjianInterface18(api_config)
        
        # 测试数据
        test_data = {"id": "", "shopcode": "08"}
        result = interface.get_doctor_info(test_data)
        
        print(f"\n[INTERFACE TEST] 接口文件测试结果:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        if 'NoneType' not in result.get('msg', ''):
            print(f"\n[SUCCESS] 接口文件修复成功，没有NoneType错误")