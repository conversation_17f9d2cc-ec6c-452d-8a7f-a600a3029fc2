# 01和03接口卡号设置功能实现说明

## 功能概述

为01号接口（体检信息传输）和03号接口（体检科室结果）增加了卡号设置功能，可以指定测试某一个卡号的数据。

## 实现的功能

### 1. GUI界面增强

在`gui_main.py`中的`setup_core_interfaces`方法中：

- **为01和03接口添加了卡号输入框**
  - 输入框标签：`指定卡号:`
  - 占位符提示：`可选，指定测试某个卡号的数据`
  - 工具提示：`输入具体的卡号，只处理该卡号的数据；留空则按天数和数据量获取数据`

- **修改了按钮事件处理**
  - 测试和发送按钮都支持传递`card_no`参数
  - 如果卡号输入框为空，则使用原有的天数和数据量查询方式
  - 如果输入了卡号，则优先使用卡号查询

### 2. 接口脚本增强

#### 01号接口 (`interface_01_sendPeInfo.py`)

- **添加命令行参数支持**
  ```bash
  --card-no TEXT  指定卡号，只处理该卡号的数据
  ```

- **修改数据查询逻辑**
  - 当指定`client_code`时，SQL查询条件改为：
    ```sql
    WHERE (rm.cClientCode = '{client_code}' OR rm.cCardNo = '{client_code}')
    ```
  - 支持通过客户编码或卡号进行查询
  - 不再限制TOP数量，返回所有匹配的记录

- **缓存键优化**
  - 指定卡号时使用：`exam_records_client_{client_code}`
  - 按天数查询时使用：`exam_records_{days}_{limit}`

#### 03号接口 (`interface_03_deptInfo.py`)

- **添加命令行参数支持**
  ```bash
  --card-no TEXT  指定卡号，只处理该卡号的数据
  ```

- **修改调用逻辑**
  - 当指定卡号时，调用`sync_dept_results(client_codes=[card_no])`
  - 原有的`get_dept_results_data`方法已支持`client_codes`参数

### 3. 参数传递机制

在`gui_main.py`的`InterfaceWorker`类中：

- **添加了card_no参数处理**
  ```python
  if self.interface_params.get('card_no'):
      cmd.extend(["--card-no", str(self.interface_params['card_no'])])
  ```

## 使用方法

### 1. 命令行使用

```bash
# 01号接口 - 指定卡号
python interface_01_sendPeInfo.py --test-mode --card-no 085041129

# 03号接口 - 指定卡号  
python interface_03_deptInfo.py --test-mode --card-no 085041129

# 不指定卡号，使用原有方式
python interface_01_sendPeInfo.py --test-mode --limit 10 --days 180
python interface_03_deptInfo.py --test-mode --limit 10 --days 180
```

### 2. GUI界面使用

1. 启动GUI程序：`python gui_main.py`
2. 在"核心数据接口 (01-06)"标签页中
3. 找到01号或03号接口的面板
4. 在"指定卡号"输入框中输入要测试的卡号（如：085041129）
5. 点击"测试"或"发送"按钮

## 测试验证

### 测试用例1：01号接口指定卡号
```bash
python interface_01_sendPeInfo.py --test-mode --card-no 085041129
```
**结果**：成功找到卡号085041129对应的记录（李晶），返回1条记录

### 测试用例2：03号接口指定卡号
```bash
python interface_03_deptInfo.py --test-mode --card-no 085041129
```
**结果**：正确处理指定卡号，虽然该卡号无科室结果数据，但功能正常

### 测试用例3：不指定卡号
```bash
python interface_01_sendPeInfo.py --test-mode --limit 2 --days 180
```
**结果**：按原有逻辑查询最近180天的2条记录

## 技术特点

1. **向后兼容**：不影响原有功能，卡号为空时使用原有查询方式
2. **灵活查询**：支持通过客户编码或卡号进行查询
3. **缓存优化**：不同查询方式使用不同的缓存键
4. **用户友好**：GUI界面提供清晰的提示和工具提示
5. **参数验证**：自动处理空值和字符串清理

## 注意事项

1. 卡号输入框支持客户编码（cClientCode）和卡号（cCardNo）两种格式
2. 指定卡号时会忽略天数和数据量限制参数
3. 如果指定的卡号不存在，会返回空结果但不会报错
4. 建议在测试模式下先验证卡号是否存在对应数据
