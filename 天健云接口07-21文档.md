# 天健云数据同步系统 - 接口文档 (07-21号)

## 系统概述

本系统提供天健云平台与嘉仁体检中心之间的数据同步服务，支持21个标准接口，统一运行在5007端口。

## 接口总览

| 接口编号 | 接口名称 | 端点路径 | 请求方法 | 功能描述 |
|---------|----------|----------|----------|----------|
| 07 | 主检结束结论回传 | `/dx/inter/receiveConclusion` | POST | 接收天健云回传的总检信息 |
| 08 | 查询字典信息 | `/dx/inter/queryDict` | POST | 查询体检服务类型和体检类型字典 |
| 09 | 体检科室结果重传 | `/dx/inter/retransmitDeptInfo` | POST | 重新传输科室检查结果 |
| 10 | 批量获取体检单信息 | `/dx/inter/batchGetPeInfo` | POST | 批量查询体检单详细信息 |
| 11 | 查询项目字典信息 | `/dx/inter/getApplyItemDict` | POST | 查询申请项目字典数据 |
| 12 | 主检锁定解锁 | `/dx/inter/lockPeInfo` | POST | 体检单主检锁定和解锁操作 |
| 13 | 体检状态更新 | `/dx/inter/updatePeStatus` | POST | 更新体检流程节点状态 |
| 14 | 重要异常标注 | `/dx/inter/markAbnormal` | POST | 标注重要异常检查结果 |
| 15 | 分科退回 | `/dx/inter/returnDept` | POST | 分科检查结果退回操作 |
| 16 | 查询图片 | `/dx/inter/getImages` | POST | 获取体检相关图片数据 |
| 17 | 删除重要异常 | `/dx/inter/deleteAbnormal` | POST | 删除已标注的重要异常 |
| 18 | 查询医生信息 | `/dx/inter/getDoctorInfo` | POST | 查询医生基本信息 |
| 19 | 查询科室信息 | `/dx/inter/getDeptInfo` | POST | 查询科室基本信息 |
| 20 | 查询个人开单 | `/dx/inter/getPersonalOrders` | POST | 查询个人医嘱开单信息 |
| 21 | 查询异常通知 | `/dx/inter/getAbnormalNotice` | POST | 查询异常结果通知信息 |

## 通用说明

### 服务地址
- **基础URL**: `http://localhost:5007`
- **健康检查**: `GET /health`

### 通用响应格式
```json
{
    "code": 0,           // 响应码：0=成功，非0=失败
    "msg": "操作成功",    // 响应消息
    "data": [],          // 响应数据
    "reponseTime": 1640995200000  // 响应时间戳（部分接口）
}
```

### 错误码说明
- `0`: 操作成功
- `-1`: 通用错误
- `1001`: 请求数据不能为空
- `1002`: 参数错误
- `1003`: 查询/操作失败
- `1004`: 数据库连接失败
- `1999`: 系统内部错误

---

## 接口详细说明

### 07号接口 - 主检结束结论回传

**功能**: 接收天健云回传的总检结论信息，更新本地数据库

**请求路径**: `POST /dx/inter/receiveConclusion`

**请求参数**:
```json
{
    "peNo": "202312010001",           // 体检号（必填）
    "hospital": {                     // 医院信息
        "code": "001",               // 门店编码
        "name": "嘉仁体检中心"        // 门店名称
    },
    "mainCheckFinishTime": "2023-12-01 14:30:00",  // 主检完成时间
    "mainCheckFinishDoctor": {        // 主检医生信息
        "code": "DOC001",            // 医生编码
        "name": "张医生"              // 医生姓名
    },
    "currentNodeType": 4,             // 当前节点类型：4=总审完成
    "conclusionList": [               // 结论列表
        {
            "conclusionCode": "C001", // 结论编码
            "conclusionName": "健康", // 结论名称
            "parentCode": "MAIN",     // 父级编码
            "explain": "各项指标正常", // 解释说明
            "suggest": "继续保持",    // 建议
            "checkResult": "正常",    // 检查结果
            "level": 1                // 严重等级
        }
    ]
}
```

**响应示例**:
```json
{
    "success": true,
    "message": "总检信息更新成功",
    "code": 0,
    "data": {
        "peNo": "202312010001",
        "updated_records": 1,
        "conclusion_count": 1
    }
}
```

---

### 08号接口 - 查询字典信息

**功能**: 查询体检服务类型和体检类型字典数据

**请求路径**: `POST /dx/inter/queryDict`

**请求参数**:
```json
{
    "id": "",                        // 字典ID（可选）
    "type": "OPEVIP",               // 字典类型：OPEVIP=体检服务类型，OPBET=体检类型
    "hospitalCode": ""              // 医院编码（可选）
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "查询成功",
    "data": [
        {
            "id": "VIP001",
            "name": "VIP服务",
            "type": "OPEVIP"
        },
        {
            "id": "VIP002", 
            "name": "普通服务",
            "type": "OPEVIP"
        }
    ]
}
```

---

### 09号接口 - 体检科室结果重传

**功能**: 重新传输指定体检号的科室检查结果

**请求路径**: `POST /dx/inter/retransmitDeptInfo`

**请求参数**:
```json
{
    "peNoList": ["202312010001", "202312010002"],  // 体检号列表（必填）
    "deptId": "DEPT001"                           // 科室ID（可选，空表示所有科室）
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "查询成功",
    "data": [
        {
            "peNo": "202312010001",
            "dept": {
                "code": "DEPT001",
                "name": "内科"
            },
            "checkTime": "2023-12-01 10:30:00",
            "checkDoctor": {
                "code": "DOC001",
                "name": "李医生"
            },
            "summary": "心肺功能正常",
            "auditStatus": {
                "code": "isAudited",
                "name": "已审核"
            },
            "itemDesc": [
                {
                    "applyItem": {
                        "code": "ITEM001",
                        "name": "心电图"
                    },
                    "checkItem": {
                        "code": "ECG001",
                        "name": "12导联心电图"
                    },
                    "inspectResult": "窦性心律，正常",
                    "unit": "",
                    "reference": "正常",
                    "unNormalFlag": ""
                }
            ]
        }
    ]
}
```

---

### 10号接口 - 批量获取体检单信息

**功能**: 根据时间范围或体检号批量获取体检单详细信息

**请求路径**: `POST /dx/inter/batchGetPeInfo`

**请求参数**:
```json
{
    "start": "2023-12-01",           // 开始日期（可选）
    "end": "2023-12-31",             // 结束日期（可选）
    "peNo": "",                      // 体检号（可选）
    "hospitalCode": ""               // 医院编码（可选）
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "",
    "data": [
        {
            "peUserInfo": {
                "archiveNo": "202312010001",
                "name": "张三",
                "icCode": "310101199001011234",
                "sex": {
                    "code": "1",
                    "name": "男"
                },
                "birthday": "19900101",
                "peDate": "2023-12-01 08:30:00",
                "phone": "13800138000",
                "peStates": {
                    "code": "4",
                    "name": "主检初审完成"
                },
                "age": 33,
                "currentNodeType": "4"
            },
            "hospital": {
                "code": "001",
                "name": "嘉仁体检中心"
            }
        }
    ],
    "reponseTime": 1640995200000
}
```

---

### 11号接口 - 查询项目字典信息

**功能**: 查询申请项目字典数据，支持按项目ID和医院编码筛选

**请求路径**: `POST /dx/inter/getApplyItemDict`

**请求参数**:
```json
{
    "id": "",                        // 申请项目ID（可选）
    "hospitalCode": ""               // 医院编码（可选）
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "",
    "data": [
        {
            "id": "ITEM001",
            "name": "血常规",
            "deptCode": "LAB001",
            "deptName": "检验科",
            "price": "25.00",
            "unit": "项",
            "hospitalCode": "001"
        }
    ]
}
```

---

### 12号接口 - 主检锁定解锁

**功能**: 对体检单进行主检锁定或解锁操作

**请求路径**: `POST /dx/inter/lockPeInfo`

**请求参数**:
```json
{
    "peInfoList": [
        {
            "peNo": "202312010001",      // 体检号
            "lockStatus": "1",           // 锁定状态：1=锁定，0=解锁
            "lockDoctor": {              // 锁定医生
                "code": "DOC001",
                "name": "张医生"
            },
            "lockTime": "2023-12-01 14:00:00"  // 锁定时间
        }
    ]
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "操作成功",
    "data": {
        "successCount": 1,
        "failCount": 0
    }
}
```

---

### 13号接口 - 体检状态更新

**功能**: 更新体检流程节点状态

**请求路径**: `POST /dx/inter/updatePeStatus`

**请求参数**:
```json
{
    "peNo": "202312010001",          // 体检号
    "nodeType": "3",                 // 节点类型：1=登记，2=分科检查，3=总检，4=总审
    "doUser": {                      // 操作用户
        "code": "DOC001",
        "name": "张医生"
    },
    "operateTime": "2023-12-01 14:30:00"  // 操作时间
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "状态更新成功",
    "data": {
        "peNo": "202312010001",
        "oldStatus": "2",
        "newStatus": "3"
    }
}
```

---

### 14号接口 - 重要异常标注

**功能**: 对检查结果进行重要异常标注

**请求路径**: `POST /dx/inter/markAbnormal`

**请求参数**:
```json
{
    "abnormalList": [
        {
            "peNo": "202312010001",      // 体检号
            "deptCode": "LAB001",        // 科室编码
            "itemCode": "ITEM001",       // 项目编码
            "abnormalLevel": "3",        // 异常等级：1=轻度，2=中度，3=重度
            "abnormalDesc": "血压偏高",   // 异常描述
            "markDoctor": {              // 标注医生
                "code": "DOC001",
                "name": "张医生"
            },
            "markTime": "2023-12-01 15:00:00"  // 标注时间
        }
    ]
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "标注成功",
    "data": {
        "successCount": 1,
        "failCount": 0
    }
}
```

---

### 15号接口 - 分科退回

**功能**: 分科检查结果退回操作，支持多门店数据库

**请求路径**: `POST /dx/inter/returnDept`

**请求参数**:
```json
{
    "shopCode": "001",               // 门店编码
    "returnList": [                  // 退回列表（支持批量）
        {
            "peNo": "202312010001",      // 体检号
            "deptCode": "DEPT001",       // 科室编码  
            "returnReason": "检查不完整", // 退回原因
            "returnDoctor": {            // 退回医生
                "code": "DOC001",
                "name": "张医生"
            },
            "returnTime": "2023-12-01 16:00:00"  // 退回时间
        }
    ]
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "退回操作成功",
    "data": {
        "total": 1,
        "success": 1,
        "error": 0,
        "results": [
            {
                "code": 0,
                "msg": "退回成功",
                "data": {"peNo": "202312010001"}
            }
        ]
    }
}
```

---

### 16号接口 - 查询图片

**功能**: 获取体检相关的图片数据（B超、X光等）

**请求路径**: `POST /dx/inter/getImages`

**请求参数**:
```json
{
    "peNo": "202312010001",          // 体检号
    "cshopcode": "001",             // 门店编码
    "deptId": "DEPT001",            // 科室ID（可选）
    "applyItemId": ["ITEM001"]      // 申请项目ID列表（可选）
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "",
    "data": [
        {
            "imageId": "IMG001",
            "imageName": "胸部X光片",
            "imageType": "X-RAY",
            "imageUrl": "http://pacs.server.com/images/IMG001.jpg",
            "imageData": "base64编码的图片数据",
            "deptCode": "DEPT001",
            "itemCode": "ITEM001",
            "shootTime": "2023-12-01 10:30:00"
        }
    ]
}
```

---

### 17号接口 - 删除重要异常

**功能**: 删除已标注的重要异常信息

**请求路径**: `POST /dx/inter/deleteAbnormal`

**请求参数**:
```json
{
    "abnormalIdList": ["ABN001", "ABN002"],  // 异常ID列表
    "deleteReason": "误标注",                // 删除原因
    "deleteDoctor": {                        // 删除操作医生
        "code": "DOC001",
        "name": "张医生"
    }
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "删除成功",
    "data": {
        "deletedCount": 2,
        "failedIds": []
    }
}
```

---

### 18号接口 - 查询医生信息

**功能**: 查询医生基本信息，支持按医生ID和姓名筛选

**请求路径**: `POST /dx/inter/getDoctorInfo`

**请求参数**:
```json
{
    "id": "",                        // 医生ID（可选）
    "name": ""                       // 医生姓名（可选）
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "",
    "data": [
        {
            "doctorId": "DOC001",
            "doctorName": "张医生",
            "deptCode": "DEPT001",
            "deptName": "内科",
            "title": "主任医师",
            "phone": "***********",
            "email": "<EMAIL>",
            "status": "1"
        }
    ]
}
```

---

### 19号接口 - 查询科室信息

**功能**: 查询科室基本信息，支持按科室ID和名称筛选

**请求路径**: `POST /dx/inter/getDeptInfo`

**请求参数**:
```json
{
    "id": "",                        // 科室ID（可选）
    "name": ""                       // 科室名称（可选）
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "",
    "data": [
        {
            "deptId": "DEPT001",
            "deptName": "内科",
            "deptType": "临床科室",
            "location": "二楼",
            "phone": "021-12345678",
            "director": "李主任",
            "status": "1"
        }
    ]
}
```

---

### 20号接口 - 查询个人开单

**功能**: 查询个人医嘱开单信息

**请求路径**: `POST /dx/inter/getPersonalOrders`

**请求参数**:
```json
{
    "peNo": "202312010001",          // 体检号（可选）
    "doctorCode": "",                // 医生编码（可选）
    "startDate": "2023-12-01",       // 开始日期（可选）
    "endDate": "2023-12-31"          // 结束日期（可选）
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "",
    "data": [
        {
            "orderId": "ORD001",
            "peNo": "202312010001",
            "patientName": "张三",
            "orderType": "检查",
            "itemCode": "ITEM001",
            "itemName": "血常规",
            "orderDoctor": {
                "code": "DOC001",
                "name": "张医生"
            },
            "orderTime": "2023-12-01 09:30:00",
            "status": "已执行"
        }
    ]
}
```

---

### 21号接口 - 查询异常通知

**功能**: 查询异常结果通知信息

**请求路径**: `POST /dx/inter/getAbnormalNotice`

**请求参数**:
```json
{
    "peNo": "",                      // 体检号（可选）
    "noticeType": "",                // 通知类型（可选）
    "startDate": "2023-12-01",       // 开始日期（可选）
    "endDate": "2023-12-31"          // 结束日期（可选）
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "",
    "data": [
        {
            "noticeId": "NOT001",
            "peNo": "202312010001",
            "patientName": "张三",
            "noticeType": "危急值",
            "noticeContent": "血压严重偏高，建议立即就医",
            "abnormalLevel": "3",
            "noticeTime": "2023-12-01 14:30:00",
            "noticeDoctor": {
                "code": "DOC001",
                "name": "张医生"
            },
            "status": "已通知"
        }
    ]
}
```

---

## 部署和运行

### 系统要求
- Python 3.8+
- SQL Server 2008 R2+
- ODBC Driver 17 for SQL Server

### 启动服务
```bash
# 通过GUI启动（推荐）
python gui_main.py

# 或直接启动CLI
python health_sync/cli.py
```

### 健康检查
```bash
curl http://localhost:5007/health
```

### 日志查看
- GUI界面：系统日志标签页
- 文件日志：`logs/` 目录下

---

## 注意事项

1. **多门店支持**: 15号接口支持多门店数据库配置
2. **数据安全**: 所有接口支持MD5签名验证
3. **性能优化**: 内置连接池和缓存机制
4. **错误处理**: 完善的错误日志和异常处理机制
5. **并发处理**: 支持多线程并发请求处理

---

## 技术支持

- **版本**: v1.3.0
- **开发团队**: 福能AI对接项目组
- **技术栈**: Python + PySide6 + SQLAlchemy + Flask
- **数据库**: SQL Server 2008 R2+

如需技术支持，请联系开发团队或查看系统日志获取详细错误信息。