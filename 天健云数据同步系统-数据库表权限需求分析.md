# 天健云数据同步系统 - 数据库表权限需求分析

## 项目概述

天健云数据同步系统是一个多机构分布式体检数据管理系统，支持21个天健云接口，采用中心库+分部库的架构模式。

### 系统架构
- **中心库**：`examdb_center` (服务器: 81.70.17.88, 数据库: Examdb)
- **分部库**：各体检机构的独立数据库
- **用户权限**：采用基于角色的权限控制(RBAC)

---

## 一、中心库权限需求

### 1.1 中心库基本信息
```
服务器: 81.70.17.88
数据库: Examdb  
用户名: tj
密码: jiare<PERSON>jian
```

### 1.2 中心库核心表权限

#### 机构配置管理表
| 表名 | 权限需求 | 用途说明 |
|------|----------|----------|
| `T_Center_Organization_Config` | SELECT, INSERT, UPDATE | 机构基础配置信息管理 |
| `T_Organization_Interface_Config` | SELECT, INSERT, UPDATE | 机构接口配置管理 |
| `T_Organization_Sync_Log` | SELECT, INSERT, UPDATE | 数据同步日志记录 |

#### 权限配置脚本
```sql
-- 创建中心库专用角色
USE Examdb;
CREATE ROLE [TianjianCenter_Role];

-- 授予机构配置表权限
GRANT SELECT, INSERT, UPDATE ON T_Center_Organization_Config TO [TianjianCenter_Role];
GRANT SELECT, INSERT, UPDATE ON T_Organization_Interface_Config TO [TianjianCenter_Role];
GRANT SELECT, INSERT, UPDATE ON T_Organization_Sync_Log TO [TianjianCenter_Role];

-- 创建专用用户并分配角色
CREATE LOGIN [tianjian_center] WITH PASSWORD = 'TJ_Center_2025!';
CREATE USER [tianjian_center] FOR LOGIN [tianjian_center];
ALTER ROLE [TianjianCenter_Role] ADD MEMBER [tianjian_center];
```

---

## 二、分部库权限需求

### 2.1 分部库基本信息
```
服务器: 各机构独立服务器
数据库: examdb_center (或机构自定义名称)
用户名: znzj (或机构自定义)
密码: 2025znzj/888 (或机构自定义)
```

### 2.2 核心业务表权限需求

#### 2.2.1 客户与体检流程表 (需要 SELECT, INSERT, UPDATE 权限)

| 表名 | 权限 | 用途说明 |
|------|------|----------|
| `T_Client` | SELECT, INSERT, UPDATE | 客户基础信息管理 |
| `T_Register_Main` | SELECT, INSERT, UPDATE | 体检登记主表 |
| `T_Register_Detail` | SELECT, INSERT, UPDATE | 体检登记明细表 |
| `T_Check_result` | SELECT, INSERT, UPDATE | 检查结果数据 |
| `T_Check_result_Main` | SELECT, INSERT, UPDATE | 检查结果主表 |
| `T_Check_Result_Illness` | SELECT, INSERT, UPDATE | 检查结果疾病信息 |
| `T_Diagnosis` | SELECT, INSERT, UPDATE | 诊断信息 |
| `T_Diagnosis_Conclusion` | SELECT, INSERT, UPDATE | 诊断结论 |
| `T_AI_Ask_Response` | SELECT, INSERT, UPDATE | AI问答响应记录 |

#### 2.2.2 字典配置表 (需要 SELECT 权限)

| 表名 | 权限 | 用途说明 |
|------|------|----------|
| `Code_Sex` | SELECT | 性别字典 |
| `Code_Nation_Dict` | SELECT | 民族字典 |
| `Code_Area_Dict` | SELECT | 地区字典 |
| `Code_Occup_Dict` | SELECT | 职业字典 |
| `Code_Dept_dict` | SELECT | 科室字典 |
| `Code_Item_Main` | SELECT | 检查项目主表 |
| `Code_Item_Detail` | SELECT | 检查项目明细 |
| `code_Item_Price` | SELECT | 检查项目价格 |
| `Code_Suit_Master` | SELECT | 套餐主表 |
| `Code_Suit_Detail` | SELECT | 套餐明细 |
| `Code_Illness_Crite` | SELECT | 疾病诊断标准 |
| `AI_Medical_Conditions` | SELECT | AI医疗条件 |
| `MedicalSymptoms` | SELECT | 医疗症状信息 |

#### 2.2.3 操作员权限表 (需要 SELECT 权限)

| 表名 | 权限 | 用途说明 |
|------|------|----------|
| `Code_Operator_dict` | SELECT | 操作员基础信息 |
| `Code_Operator_Dept` | SELECT | 操作员部门关系 |
| `Code_Operator_Shop` | SELECT | 操作员门店关系 |
| `Code_Operator_Button` | SELECT | 操作员按钮权限 |
| `Code_Station_dict` | SELECT | 岗位字典 |
| `Code_Station_Right` | SELECT | 岗位权限 |

#### 2.2.4 收费系统表 (需要 SELECT, INSERT, UPDATE 权限)

| 表名 | 权限 | 用途说明 |
|------|------|----------|
| `T_Charge_Main` | SELECT, INSERT, UPDATE | 收费主表 |
| `T_Charge_Detail` | SELECT, INSERT, UPDATE | 收费明细 |
| `T_Charge_PayDetail` | SELECT, INSERT, UPDATE | 支付明细 |
| `T_Card` | SELECT, INSERT, UPDATE | 会员卡信息 |
| `T_Card_Detail` | SELECT, INSERT, UPDATE | 会员卡明细 |

### 2.3 分部库权限配置脚本

```sql
-- 创建分部库专用角色
CREATE ROLE [TianjianBranch_Role];

-- 核心业务表权限 (读写权限)
GRANT SELECT, INSERT, UPDATE ON T_Client TO [TianjianBranch_Role];
GRANT SELECT, INSERT, UPDATE ON T_Register_Main TO [TianjianBranch_Role];
GRANT SELECT, INSERT, UPDATE ON T_Register_Detail TO [TianjianBranch_Role];
GRANT SELECT, INSERT, UPDATE ON T_Check_result TO [TianjianBranch_Role];
GRANT SELECT, INSERT, UPDATE ON T_Check_result_Main TO [TianjianBranch_Role];
GRANT SELECT, INSERT, UPDATE ON T_Check_Result_Illness TO [TianjianBranch_Role];
GRANT SELECT, INSERT, UPDATE ON T_Diagnosis TO [TianjianBranch_Role];
GRANT SELECT, INSERT, UPDATE ON T_Diagnosis_Conclusion TO [TianjianBranch_Role];
GRANT SELECT, INSERT, UPDATE ON T_AI_Ask_Response TO [TianjianBranch_Role];

-- 字典表权限 (只读权限)
GRANT SELECT ON Code_Sex TO [TianjianBranch_Role];
GRANT SELECT ON Code_Nation_Dict TO [TianjianBranch_Role];
GRANT SELECT ON Code_Area_Dict TO [TianjianBranch_Role];
GRANT SELECT ON Code_Occup_Dict TO [TianjianBranch_Role];
GRANT SELECT ON Code_Dept_dict TO [TianjianBranch_Role];
GRANT SELECT ON Code_Item_Main TO [TianjianBranch_Role];
GRANT SELECT ON Code_Item_Detail TO [TianjianBranch_Role];
GRANT SELECT ON code_Item_Price TO [TianjianBranch_Role];
GRANT SELECT ON Code_Suit_Master TO [TianjianBranch_Role];
GRANT SELECT ON Code_Suit_Detail TO [TianjianBranch_Role];
GRANT SELECT ON Code_Illness_Crite TO [TianjianBranch_Role];
GRANT SELECT ON AI_Medical_Conditions TO [TianjianBranch_Role];
GRANT SELECT ON MedicalSymptoms TO [TianjianBranch_Role];

-- 操作员权限表 (只读权限)
GRANT SELECT ON Code_Operator_dict TO [TianjianBranch_Role];
GRANT SELECT ON Code_Operator_Dept TO [TianjianBranch_Role];
GRANT SELECT ON Code_Operator_Shop TO [TianjianBranch_Role];
GRANT SELECT ON Code_Operator_Button TO [TianjianBranch_Role];
GRANT SELECT ON Code_Station_dict TO [TianjianBranch_Role];
GRANT SELECT ON Code_Station_Right TO [TianjianBranch_Role];

-- 收费系统表权限 (读写权限)
GRANT SELECT, INSERT, UPDATE ON T_Charge_Main TO [TianjianBranch_Role];
GRANT SELECT, INSERT, UPDATE ON T_Charge_Detail TO [TianjianBranch_Role];
GRANT SELECT, INSERT, UPDATE ON T_Charge_PayDetail TO [TianjianBranch_Role];
GRANT SELECT, INSERT, UPDATE ON T_Card TO [TianjianBranch_Role];
GRANT SELECT, INSERT, UPDATE ON T_Card_Detail TO [TianjianBranch_Role];

-- 创建专用用户并分配角色
CREATE LOGIN [tianjian_branch] WITH PASSWORD = 'TJ_Branch_2025!';
CREATE USER [tianjian_branch] FOR LOGIN [tianjian_branch];
ALTER ROLE [TianjianBranch_Role] ADD MEMBER [tianjian_branch];
```

---

## 三、天健云接口权限映射

### 3.1 接口权限对应表

| 接口编号 | 接口名称 | 主要涉及表 | 权限需求 |
|----------|----------|------------|----------|
| 01 | sendPeInfo | T_Register_Main, T_Client | SELECT, INSERT, UPDATE |
| 02 | syncApplyItem | Code_Item_Main, Code_Item_Detail | SELECT |
| 03 | deptInfo | Code_Dept_dict, T_Check_result_Main | SELECT, INSERT, UPDATE |
| 04 | syncUser | Code_Operator_dict | SELECT |
| 05 | syncDept | Code_Dept_dict | SELECT |
| 06 | syncDict | Code_Sex, Code_Nation_Dict 等字典表 | SELECT |
| 07 | sendConclusion | T_Diagnosis, T_Diagnosis_Conclusion | SELECT, INSERT, UPDATE |
| 08 | getDict | 各类字典表 | SELECT |
| 09 | retransmitDeptInfo | T_Check_result_Main | SELECT, INSERT, UPDATE |
| 10 | batchGetPeInfo | T_Register_Main, T_Client | SELECT |
| 11 | getApplyItemDict | Code_Item_Main, code_Item_Price | SELECT |
| 12 | lockPeInfo | T_Register_Main | SELECT, UPDATE |
| 13 | updatePeStatus | T_Register_Main | SELECT, UPDATE |
| 14 | markAbnormal | T_Check_Result_Illness | SELECT, INSERT, UPDATE |
| 15 | returnDept | T_Check_result_Main | SELECT, UPDATE |
| 16 | getImages | 图像相关表 | SELECT |
| 17 | deleteAbnormal | T_Check_Result_Illness | SELECT, UPDATE |
| 18 | getDoctorInfo | Code_Operator_dict | SELECT |
| 19 | getDeptInfo | Code_Dept_dict | SELECT |
| 20 | getPersonalOrders | T_Register_Main, T_Register_Detail | SELECT |
| 21 | getAbnormalNotice | T_Check_Result_Illness | SELECT |

---

## 四、安全配置建议

### 4.1 权限最佳实践

1. **最小权限原则**
   - 只授予业务必需的最小权限
   - 字典表一般只需要SELECT权限
   - 核心业务表需要SELECT, INSERT, UPDATE权限
   - 不建议授予DELETE权限，使用软删除机制

2. **用户角色分离**
   - 中心库管理用户：专门管理机构配置
   - 分部库业务用户：处理体检业务数据
   - 只读用户：用于报表和查询

3. **密码策略**
   - 使用强密码策略
   - 定期更换密码
   - 避免使用默认密码

### 4.2 监控与审计

```sql
-- 创建权限审计视图
CREATE VIEW V_Permission_Audit AS
SELECT 
    dp.class_desc,
    dp.permission_name,
    dp.state_desc,
    pr.name AS principal_name,
    o.name AS object_name
FROM sys.database_permissions dp
LEFT JOIN sys.objects o ON dp.major_id = o.object_id
LEFT JOIN sys.database_principals pr ON dp.grantee_principal_id = pr.principal_id
WHERE pr.name IN ('tianjian_center', 'tianjian_branch');

-- 查询当前权限状态
SELECT * FROM V_Permission_Audit ORDER BY principal_name, object_name;
```

### 4.3 备份与恢复权限

```sql
-- 备份权限配置
SELECT 
    'GRANT ' + dp.permission_name + ' ON ' + ISNULL(o.name, 'DATABASE') + ' TO [' + pr.name + '];' AS GrantScript
FROM sys.database_permissions dp
LEFT JOIN sys.objects o ON dp.major_id = o.object_id
LEFT JOIN sys.database_principals pr ON dp.grantee_principal_id = pr.principal_id
WHERE pr.name IN ('tianjian_center', 'tianjian_branch')
AND dp.state = 'G';
```

---

## 五、部署检查清单

### 5.1 中心库部署检查
- [ ] 创建机构配置表
- [ ] 创建专用角色和用户
- [ ] 授予必要权限
- [ ] 测试连接和基本操作
- [ ] 插入默认机构配置数据

### 5.2 分部库部署检查
- [ ] 确认所有业务表存在
- [ ] 创建专用角色和用户
- [ ] 授予业务表权限
- [ ] 授予字典表查询权限
- [ ] 测试各接口的数据访问
- [ ] 验证数据同步功能

### 5.3 权限验证脚本
```sql
-- 验证用户权限
SELECT 
    HAS_PERMS_BY_NAME('T_Client', 'OBJECT', 'SELECT') AS CanSelect,
    HAS_PERMS_BY_NAME('T_Client', 'OBJECT', 'INSERT') AS CanInsert,
    HAS_PERMS_BY_NAME('T_Client', 'OBJECT', 'UPDATE') AS CanUpdate,
    HAS_PERMS_BY_NAME('T_Client', 'OBJECT', 'DELETE') AS CanDelete;
```

---

## 六、常见问题与解决方案

### 6.1 权限问题排查
1. **连接失败**：检查用户名密码、网络连接
2. **查询失败**：检查SELECT权限
3. **插入失败**：检查INSERT权限和字段约束
4. **更新失败**：检查UPDATE权限和WHERE条件

### 6.2 性能优化建议
1. 为常用查询字段创建索引
2. 定期更新统计信息
3. 监控慢查询并优化
4. 合理设置连接池大小

---

## 附录

### A. 相关配置文件
- `config.py`: 基础配置
- `multi_org_config.py`: 多机构配置管理
- `database_service.py`: 数据库访问服务

### B. 联系信息
- 项目维护：系统管理员
- 技术支持：开发团队
- 更新日期：2025年1月

---

*本文档基于天健云数据同步系统v1.0版本编写，如有疑问请联系技术支持团队。*