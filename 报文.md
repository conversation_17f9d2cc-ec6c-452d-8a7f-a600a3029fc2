请求头:
Content-Type: application/json
sign: 02666b739304dfa3569ef69fc2318d49
timestamp: 20250722001456
nonce: 4ec0b1cc-b493-419b-a3df-728a148952c0
mic-code: MIC1.001E
misc-id: MISC1.00001A
--- 请求体 ---
   {
  "peUserInfo": {
    "archiveNo": "**********",
    "name": "赵亚南",
    "icCode": "130429198912262645",
    "sex": {
      "code": "2",
      "name": "女"
    },
    "birthday": "19891226",
    "peno": "**********",
    "peDate": "2025-07-21 16:21:13",
    "phone": "15931046747",
    "ms": {
      "code": "unknown",
      "name": "未知"
    },
    "pregnantState": {
      "code": "unknown",
      "name": "未知"
    },
    "vipLevel": {
      "code": "NORMAL",
      "name": "普通"
    },
    "medicalType": {
      "code": "GROUP",
      "name": "团体体检"
    },
    "isGroup": true,
    "company": "金蛇金卡会员专享5年",
    "workDept": "",
    "teamNo": "08011109",
    "professional": "",
    "workAge": "",
    "peStates": {
      "code": "2",
      "name": "分科未完成"
    },
    "deptCount": 1,
    "age": 36,
    "deptFinishTime": "",
    "firstCheckFinishTime": "",
    "firstCheckFinishDoctor": {
      "code": "",
      "name": ""
    },
    "mainCheckFinishTime": "",
    "mainCheckFinishDoctor": {
      "code": "",
      "name": ""
    },
    "forbidGoCheck": false,
    "reportPrint": false,
    "reportGot": false,
    "replacementInspectionMark": false,
    "applyItemList": [
      "08_JB0003",
      "08_JB1757",
      "08_JB0174",
      "08_JB0104",
      "08_JB0686",
      "08_JB0942",
      "08_JB0002",
      "08_JB0010",
      "08_JB0083",
      "08_JB0311",
      "08_JB0176",
      "08_JB1931",
      "08_JB0072",
      "08_JB0004",
      "08_JB0938",
      "08_JB0048",
      "08_JB0069",
      "08_JB0347",
      "08_JB0101",
      "08_JB0944",
      "08_JB0354",
      "08_JB0084",
      "08_JB0089",
      "08_JB0013",
      "08_JB3516",
      "08_JB0355",
      "08_JB0005",
      "08_JB2761",
      "08_JB0036",
      "08_JB0388",
      "08_JB0488",
      "08_JB0085",
      "08_JB3228",
      "08_JB1923",
      "08_JB2490",
      "08_JB0688",
      "08_JB0934",
      "08_JB0007",
      "08_JB0006",
      "08_JB0086"
    ],
    "currentNodeType": 1,
    "pePackage": {
      "code": "08092340",
      "name": "女士（彩超七选四、核磁三选一）"
    }
},
"archiveInfo": {
"name": "赵亚南",
"icCode": "130429198912262645",
"sex": {
"code": "2",
"name": "女"
},
"birthday": "19891226",
"peNoList": [
"**********"
]
}
}