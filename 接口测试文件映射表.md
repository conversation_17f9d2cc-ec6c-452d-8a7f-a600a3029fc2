# 接口测试文件映射表

## 清理后的接口测试文件状态

### 01-21号接口测试文件映射

| 接口号 | 接口名称 | 主实现文件 | 测试文件 | 状态 |
|--------|----------|------------|----------|------|
| 01号 | 体检信息传输 | `interface_01_sendPeInfo.py` | `tests/test_01_complete_message.py` | ✅ |
| 02号 | 申请项目字典 | `interface_02_syncApplyItem.py` | `tests/test_apply_item_interface.py` | ✅ |
| 03号 | 科室结果传输 | `interface_03_deptInfo.py` | `tests/test_interfaces_07_10.py` | ✅ |
| 04号 | 医生信息传输 | `interface_04_syncUser.py` | `tests/tianjian_interface_test_suite.py` | ✅ |
| 05号 | 科室信息传输 | `interface_05_syncDept.py` | `tests/tianjian_interface_test_suite.py` | ✅ |
| 06号 | 字典信息传输 | `interface_06_syncDict.py` | `tests/tianjian_interface_test_suite.py` | ✅ |
| 07号 | 主检结束结论回传 | `gui_main.py` (内置) | `test_interface_07_new_params.py` | ✅ |
| 08号 | 查询字典信息 | `gui_main.py` (内置) | `tests/test_interfaces_07_10.py` | ✅ |
| 09号 | 科室结果重传 | `interface_09_retransmitDeptInfo.py` | `tests/test_interfaces_07_10.py` | ✅ |
| 10号 | 批量获取体检单信息 | `gui_main.py` (内置) | `test_gui_interface_10.py` | ✅ |
| 11号 | 查询项目字典 | `interface_11_getApplyItemDict.py` | `tests/test_interfaces_11_21.py` | ✅ |
| 12号 | 主检锁定与解锁 | `interface_12_lockPeInfo.py` | `tests/test_interfaces_11_21.py` | ✅ |
| 13号 | 体检报告状态更新 | `interface_13_updatePeStatus.py` | `tests/test_interfaces_11_21.py` | ✅ |
| 14号 | 重要异常标注 | `interface_14_markAbnormal.py` | `tests/test_interfaces_11_21.py` | ✅ |
| 15号 | 分科退回 | `interface_15_returnDept.py` | `tests/test_interfaces_11_21.py` | ✅ |
| 16号 | 查询图片 | `interface_16_getImages.py` | `tests/test_interfaces_11_21.py` | ✅ |
| 17号 | 重要异常删除 | `interface_17_deleteAbnormal.py` | `tests/test_interfaces_11_21.py` | ✅ |
| 18号 | 查询医生信息 | `interface_18_getDoctorInfo.py` | `tests/test_interfaces_11_21.py` | ✅ |
| 19号 | 查询科室信息 | `interface_19_getDeptInfo.py` | `tests/test_interfaces_11_21.py` | ✅ |
| 20号 | 查询个人开单情况 | `interface_20_getPersonalOrders.py` | `tests/test_interfaces_11_21.py` | ✅ |
| 21号 | 查询重要异常通知 | `interface_21_getAbnormalNotice.py` | `tests/test_interfaces_11_21.py` | ✅ |

### 系统测试文件

| 测试类型 | 测试文件 | 说明 |
|----------|----------|------|
| API连接测试 | `tests/test_api_connection.py` | 测试天健云API连接 |
| 数据库连接测试 | `tests/test_db_connection.py` | 测试数据库连接 |
| 配置管理测试 | `tests/test_config.py` | 测试配置管理功能 |
| 数据同步测试 | `tests/test_sync_data.py` | 测试数据同步功能 |
| 综合测试套件 | `tests/tianjian_interface_test_suite.py` | 天健云接口综合测试 |
| 真实API测试 | `tests/real_api_test.py` | 真实环境API测试 |

## 测试文件使用指南

### 单个接口测试

#### 07号接口测试（重点）
```bash
# 07号接口新增字段测试
python test_interface_07_new_params.py
```

#### 10号接口测试（GUI集成版本）
```bash
# 10号接口GUI集成测试（推荐）
python test_gui_interface_10.py

# 10号接口标准报文格式测试（独立版本）
python test_interface_10_standard.py
```

#### 01号接口测试
```bash
# 01号接口完整消息测试
python tests/test_01_complete_message.py
```

#### 02号接口测试
```bash
# 02号申请项目接口测试
python tests/test_apply_item_interface.py
```

### 批量接口测试

#### 07-10号接口测试
```bash
python tests/test_interfaces_07_10.py
```

#### 11-21号接口测试
```bash
python tests/test_interfaces_11_21.py
```

#### 综合测试套件
```bash
python tests/tianjian_interface_test_suite.py
```

### 系统功能测试

#### 基础连接测试
```bash
# 数据库连接测试
python tests/test_db_connection.py

# API连接测试
python tests/test_api_connection.py

# 配置管理测试
python tests/test_config.py
```

#### 数据同步测试
```bash
python tests/test_sync_data.py
```

## 清理效果总结

### 删除的文件类型
1. **重复的调试文件** - 36个根目录文件
2. **多余的分析工具** - 如字段长度分析、简单检查等
3. **临时测试脚本** - 各种临时创建的测试文件
4. **过时的文档** - 10个过时的技术文档
5. **tests目录重复文件** - 29个重复的测试文件

### 保留的核心文件
1. **每个接口的主要测试文件**
2. **系统功能测试文件**
3. **重要的技术文档**
4. **核心配置和工具文件**

### 最终保留的测试文件列表

#### 根目录测试文件
- `test_interface_07_new_params.py` - 07号接口主测试文件（支持新增字段）
- `test_interface_10_standard.py` - 10号接口标准报文格式测试（独立版本）
- `test_gui_interface_10.py` - 10号接口GUI集成测试

#### tests目录核心测试文件
- `test_01_complete_message.py` - 01号接口测试
- `test_interface_02_fixed.py` - 02号接口测试
- `test_apply_item_interface.py` - 申请项目接口测试
- `test_interfaces_07_10.py` - 07-10号接口批量测试
- `test_interfaces_11_21.py` - 11-21号接口批量测试
- `test_api_connection.py` - API连接测试
- `test_db_connection.py` - 数据库连接测试
- `test_config.py` - 配置管理测试
- `test_sync_data.py` - 数据同步测试
- `tianjian_interface_test_suite.py` - 综合测试套件
- `real_api_test.py` - 真实API测试
- `run_all_tests.py` - 批量测试运行器
- `test_affirmdate_query.py` - 特定查询测试

### 项目结构优化
- ✅ **清晰的测试结构** - 每个接口都有明确的测试文件
- ✅ **减少文件冗余** - 删除了重复和过时的文件
- ✅ **保持功能完整** - 所有核心功能都有对应的测试
- ✅ **文档结构简化** - 保留最重要的技术文档

## 使用建议

### 开发测试
1. **新功能开发** - 使用对应接口的测试文件进行验证
2. **问题调试** - 使用系统测试文件定位问题
3. **集成测试** - 使用综合测试套件进行全面测试

### 生产部署
1. **部署前测试** - 运行所有核心测试文件
2. **功能验证** - 使用真实API测试验证功能
3. **监控维护** - 定期运行测试文件检查系统状态

---

**清理完成时间**: 2025-07-23
**清理文件数量**: 75个文件（65个代码文件 + 10个文档文件）
**保留测试文件**: 13个核心测试文件
**项目状态**: 结构清晰，功能完整 ✅

### 清理统计
- ✅ **根目录清理**: 删除36个调试文件，保留1个主测试文件
- ✅ **tests目录清理**: 删除29个重复文件，保留12个核心测试文件
- ✅ **文档清理**: 删除10个过时文档，保留核心技术文档
- ✅ **总体效果**: 项目文件数量减少约60%，结构更加清晰
