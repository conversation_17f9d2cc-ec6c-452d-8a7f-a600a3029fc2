# 体检系统数据库表结构分析

## Overview（项目概述）
- **项目名称**: 体检系统数据库 (EXAMDB)
- **数据库**: examdb_center
- **描述**: 一个完整的医疗体检管理系统，包含客户管理、检查流程、结果诊断、套餐管理等功能模块

## Directory Structure（目录结构）
```
EXAMDB/
├── exmdb.sql           # 完整数据库结构文件
├── README.md           # 数据库表结构分析文档
└── test/               # 测试脚本目录
```

## Table of Contents（快速导航）
- [Key Table Relationships（核心业务表关联关系）](#核心业务表关联关系)
- [Table Categories（表分类详细说明）](#表分类详细说明)
- [Foreign Keys（外键约束关系）](#外键约束关系)
- [Business Modules（系统功能模块总结）](#系统功能模块总结)
- [Important Fields（重要字段说明）](#重要字段说明)
- [Billing Relationships（收费系统核心关联关系）](#收费系统核心关联关系)
- [Core Joins (Revised)（核心关联关系）](#四核心关联关系修正版)
- [Common Pitfalls（常见错误修正说明）](#十常见错误修正说明)
- [Extended Relationships（扩展关联关系）](#十一扩展关联关系ai、建议、套餐等)

## Key Table Relationships（核心业务表关联关系）

### 1. 客户与体检流程核心关系

```
T_Client (客户表)
    └── T_Register_Main (体检登记主表) [cClientCode]
        ├── T_Check_result (检查结果表) [cClientCode]
        ├── T_Check_result_Main (检查结果主表) [cClientCode]
        ├── T_Check_Result_Illness (检查结果疾病表) [cClientCode]
        ├── T_Diagnosis (诊断表) [cClientCode]
        ├── T_Diagnosis_Conclusion (诊断结论表) [cClientCode]
        └── T_AI_Ask_Response (AI问答响应表) [cClientCode] *外键约束
```

### 2. 检查项目和套餐关系

```
Code_Item_Main (检查项目主表)
    ├── Code_Item_Detail (检查项目明细表) [cMainCode]
    ├── Code_Suit_Detail (套餐明细表) [cMainCode]
    ├── code_Item_Price (价格表) [cMainCode]
    └── Code_Dept_Main (科室项目关联表) [cMainCode]

Code_Dept_Main (科室项目关联表)
    └── Code_Dept_dict (科室字典表) [cDeptCode]

T_Register_Detail (体检登记明细表)
    └── code_Item_Price (价格表) [cPriceCode]

Code_Suit_Master (套餐主表)
    └── Code_Suit_Detail (套餐明细表) [cSuitCode]
```

### 3. 部门科室关系

```
Code_Dept_Main (科室主表)
    ├── Code_Dept_room (科室房间表) [cDeptCode]
    ├── Code_Called_CheckArea (叫号检查区域表) [cDeptCode]
    └── T_Check_result (检查结果表) [cDeptCode]
```

### 4. 操作员权限关系

```
Code_Operator_dict (操作员字典表)
    ├── Code_Operator_Dept (操作员部门关系表) [cOperCode]
    ├── Code_Operator_Shop (操作员门店关系表) [cOperCode]
    └── Code_Operator_Button (操作员按钮权限表) [cOperCode]
```

## Table Categories（表分类详细说明）

### 系统基础表 (Code_开头)

#### 字典表类
- **Code_Sex**: 性别字典表
- **Code_Nation_Dict**: 民族字典表
- **Code_Area_Dict**: 地区字典表
- **Code_Occup_Dict**: 职业字典表
- **Code_Common_Langu**: 通用语言表

#### 检查项目管理
- **Code_Item_Main**: 检查项目主表 - 定义各种检查项目
- **Code_Item_Detail**: 检查项目明细表 - 具体检查指标
  - `cConsult`: 参考值范围（正常值）
  - `cUnit`: 检查单位
  - `cName`: 检查项目明细名称
- **Code_ItemDetail_Result**: 检查项目结果模板表
- **code_Item_Price**: 检查项目价格表

#### 套餐管理
- **Code_Suit_Master**: 套餐主表 - 体检套餐定义
- **Code_Suit_Detail**: 套餐明细表 - 套餐包含的检查项目

#### 部门科室管理
- **Code_Dept_Main**: 科室主表
- **Code_Dept_room**: 科室房间表
- **Code_Department_Dict**: 部门字典表

#### 疾病诊断相关
- **Code_Illness_Class**: 疾病分类表
- **Code_Illness_Crite**: 疾病诊断标准表
- **Code_Advice_Crite**: 建议标准表
- **Code_Advice_Illness**: 建议疾病关联表

### 业务数据表 (T_开头)

#### 客户管理模块
- **T_Client**: 客户基本信息表 (主键: cId)
- **T_Client_Contract**: 客户合同表
- **T_Client_EditLog**: 客户编辑日志表
- **T_Client_LinkPeople**: 客户联系人表
- **T_Client_FaithLink**: 客户回访记录表

#### 体检登记模块
- **T_Register_Main**: 体检登记主表 (主键: cClientCode)
  - 包含体检者基本信息、套餐信息、操作员信息等
- **T_Register_ItemPrint**: 体检项目打印表

#### 检查结果模块
- **T_Check_result**: 检查结果明细表 (主键: cClientCode + cDetailCode)
  - `cResult`: 检查结果值
  - `cResultDesc`: 结果描述
  - `cAbnor`: 是否异常（'1'=异常，'0'=正常）
  - `cDoctName`: 检查医生
  - `dOperDate`: 检查时间
- **T_Check_result_Main**: 检查结果主表 (主键: cClientCode + cDeptCode + cMainCode)
- **T_Check_Result_Illness**: 客户异常疾病汇总表
  - 汇总客户所有异常检查结果对应的疾病
- **T_Check_result_Option**: 检查结果选项表
- **T_Check_result_Param**: 检查结果参数表

#### 诊断模块
- **T_Diagnosis**: 诊断表 (主键: ID, 关联: cClientCode)
- **T_Diagnosis_Conclusion**: 诊断结论表 (主键: id, 关联: cClientCode)
- **T_Diag_result**: 总检结论表 (主键: cClientCode)
  - `cDiag`: 总检结论
  - `cDiagDesc`: 总检结论描述
  - `cDoctName`: 总检医生
  - `dOperDate`: 总检时间

#### AI诊断模块
- **AI_Medical_Conditions**: AI医疗条件表
- **AI_Diagnosis_Mapping**: AI诊断映射表
- **T_AI_Ask_Response**: AI问答响应表 (外键: cClientCode → T_Register_Main)

#### 验证模块
- **T_Check_Result_Validation**: 检查结果验证表 (主键: validation_id)
- **T_Check_Result_Validation_Issue**: 验证问题表 (外键: validation_id)

#### 卡券管理
- **T_Card**: 卡券主表
- **T_Card_Detail**: 卡券明细表
- **T_Card_Sale_Detail**: 卡券销售明细表

#### 收费管理
- **T_Charge_Main**: 收费主表
- **T_Charge_Detail**: 收费明细表
- **T_Charge_PayDetail**: 收费支付明细表

### 临时表和工具表

#### 清理数据表
- **cleaned_MedicalSymptoms**: 清理后的医疗症状表
- **MedicalSymptoms**: 医疗症状表

#### 临时表
- **mytmp系列**: 各种临时表用于数据处理
- **temp_开头**: 临时数据表

## Foreign Keys（外键约束关系）

### 已定义的外键约束
1. **T_AI_Ask_Response.cClientCode** → **T_Register_Main.cClientCode**
2. **T_Check_Result_Validation_Issue.validation_id** → **T_Check_Result_Validation.validation_id**

### 逻辑关联关系 (通过字段名推导)

#### 以cClientCode为关联的表
- T_Register_Main (主表)
- T_Check_result 
- T_Check_result_Main
- T_Check_Result_Illness
- T_Diagnosis
- T_Diagnosis_Conclusion
- T_Diag_result
- T_AI_Ask_Response

#### 以cCode为关联的表
- 各种Code_开头的字典表之间通过cCode字段关联

#### 以cMainCode为关联的表
- Code_Item_Main → Code_Item_Detail
- Code_Item_Main → T_Check_result
- Code_Item_Main → T_Check_result_Main

## Business Modules（系统功能模块总结）

### 1. 客户管理系统
- 客户基本信息维护
- 客户联系人管理
- 客户编辑历史追踪
- 客户合同管理

### 2. 体检流程管理
- 体检登记
- 检查项目管理
- 检查结果录入
- 科室分配

### 3. 诊断系统
- 医生诊断
- AI辅助诊断
- 诊断结论汇总

### 4. 套餐管理
- 体检套餐定义
- 套餐项目配置
- 价格管理

### 5. 质量控制
- 结果验证
- 数据校验
- 异常跟踪

### 6. 系统管理
- 用户权限管理
- 操作日志
- 系统配置

## Important Fields（重要字段说明）

### 体检状态字段 (cStatus)

在`T_Register_Main`表中，`cStatus`字段表示体检流程的状态：

| 状态码 | 状态名称 | 说明 |
|--------|----------|------|
| 0 | 已登记 | 完成体检登记，等待确认 |
| 1 | 已确认 | 确认体检信息，可以开始检查 |
| 2 | 已总检 | 完成所有检查项目，等待打印报告 |
| 3 | 已打印 | 已打印体检报告，流程完成 |

### 收费金额字段说明

在`T_Register_Main`和`T_Charge_Main`表中：
- **fTotal**: 应收金额（套餐原价）
- **fFactTotal**: 实际收费金额（扣除优惠后）

### 机构字段说明

系统中有多个机构相关字段，用途不同：

#### T_SysLinkShopInfo（连锁机构信息字典表）
- 存储所有连锁机构的基本信息
- **cShopCode**: 机构编码（主键）
- **cShopName**: 机构名称

#### T_Register_Main（体检登记主表）中的机构字段
- **cShopCode**: 信息登记机构 - 客户在哪个机构登记体检
- **cAffirmShopCode**: 确认到检机构 - 客户确认在哪个机构进行体检

#### T_Charge_Main（收费主表）中的机构字段
- **cShopCode**: 收费机构 - 在哪个机构收费

> **注意**：同一客户的登记机构、到检机构、收费机构可能不同，需要根据业务需求选择正确的机构字段进行统计。

## Billing Relationships（收费系统核心关联关系）

> 基于dashboard_api.php的业务逻辑提炼

### 1. 收费主流程关联

```
T_Charge_Main (收费主表) ←→ T_Register_Main (登记主表)
    关联字段: cClientCode (客户编号)
    
T_Charge_Main ←→ T_Charge_PayDetail (支付明细表)
    关联字段: cChargeNo (收费单号)
    
T_Charge_Main ←→ Code_Operator_dict (操作员表)
    关联字段: cSellCode (销售员编码)
    
T_Charge_Main ←→ T_SysLinkShopInfo (连锁机构信息字典表)
    关联字段: cShopCode (收费机构编码)
```

### 2. 客户分类逻辑

#### 个检客户识别
```sql
-- 个检条件：无合同编码 OR 合同类型为'散客体检'
WHERE (rm.cContractCode IS NULL OR rm.cContractCode = '')
   OR tc.cCheckType = '散客体检'
```

#### 团检客户识别
```sql
-- 团检条件：有合同编码 AND 合同类型不是'散客体检'
WHERE rm.cContractCode IS NOT NULL 
  AND rm.cContractCode != ''
  AND tc.cCheckType != '散客体检'
```

### 3. 体检类型分类逻辑

```sql
-- 体检类型优先级：
CASE
    WHEN rm.cContractCode IS NOT NULL AND rm.cContractCode != ''
    THEN ct.cType  -- 优先使用合同类型
    ELSE rm.cType  -- 无合同则使用登记类型
END
```

### 4. 客户来源分类逻辑

```sql
-- 来源编码优先级：
CASE
    WHEN rm.cContractCode IS NULL OR rm.cContractCode = ''
    THEN rm.cCustSourceCode  -- 个检使用登记表来源
    ELSE ct.cCustSourceCode  -- 团检使用合同表来源
END
```

### 5. 关键统计字段

#### 收入统计
- **主要字段**: `t_charge_main.fFactTotal` (实际收费金额)
- **时间过滤**: `dOperDate` (操作日期)
- **门店过滤**: `cShopCode NOT IN ('00', '18')` (排除特定机构)

#### 客户统计
- **状态过滤**: `cStatus > 0` (有效状态)
- **时间字段**: `dAffirmdate` (确认日期)
- **性别统计**: `cSex` ('1'=男, '2'=女)

### 6. 重要的数据排除规则

```sql
-- 门店过滤：排除机构编码00和18
WHERE cShopCode NOT IN ('00', '18')

-- 有效记录过滤
WHERE cStatus > 0 AND fFactTotal > 0
```

### 7. 关联表映射关系

| 业务需求 | 主表 | 关联表 | 关联字段 | 用途 |
|----------|------|--------|----------|------|
| 收费统计 | T_Charge_Main | T_Register_Main | cClientCode | 获取客户基本信息 |
| 支付方式 | T_Charge_Main | T_Charge_PayDetail | cChargeNo | 支付明细分析 |
| 销售员统计 | T_Charge_Main | Code_Operator_dict | cSellCode | 销售业绩统计 |
| 门店统计 | T_Charge_Main | T_SysLinkShopInfo | cShopCode | 分院收入分析 |
| 体检类型 | T_Register_Main | T_Contract | cContractCode | 业务类型分类 |
| 客户来源 | T_Register_Main/T_Contract | Code_Cust_Source_Dict | cCustSourceCode | 营销渠道分析 |

### 8. 核心业务SQL模板

#### 日收入统计
```sql
SELECT SUM(fFactTotal) as 日收入
FROM t_charge_main 
WHERE CONVERT(VARCHAR(10), dOperDate, 120) = '2024-01-01'
  AND cShopCode NOT IN ('00', '18')
```

#### 客户数统计
```sql
SELECT COUNT(*) as 客户数
FROM T_Register_Main rm
LEFT JOIN T_Contract tc ON rm.cContractCode = tc.cCode
WHERE CONVERT(VARCHAR(10), rm.dAffirmdate, 120) = '2024-01-01'
  AND rm.cStatus > 0
```

#### 支付方式统计
```sql
SELECT pd.cPayMode, SUM(pd.fPayMoney) as 金额
FROM T_Charge_Main cm
INNER JOIN T_Charge_PayDetail pd ON cm.cChargeNo = pd.cChargeNo
WHERE CONVERT(VARCHAR(10), cm.dOperDate, 120) = '2024-01-01'
GROUP BY pd.cPayMode
```

---

### 状态查询示例

```sql
-- 查询不同状态的体检人数
SELECT 
    CASE cStatus 
        WHEN '0' THEN '已登记'
        WHEN '1' THEN '已确认'
        WHEN '2' THEN '已总检'
        WHEN '3' THEN '已打印'
        ELSE '未知状态'
    END as 体检状态,
    COUNT(*) as 人数
FROM T_Register_Main 
WHERE CONVERT(date, dOperdate) = CONVERT(date, GETDATE())
GROUP BY cStatus
ORDER BY cStatus;

-- 查询今天体检流程进度
SELECT 
    COUNT(CASE WHEN cStatus = '0' THEN 1 END) as 已登记人数,
    COUNT(CASE WHEN cStatus = '1' THEN 1 END) as 已确认人数,
    COUNT(CASE WHEN cStatus = '2' THEN 1 END) as 已总检人数,
    COUNT(CASE WHEN cStatus = '3' THEN 1 END) as 已打印人数,
    COUNT(*) as 总人数
FROM T_Register_Main 
WHERE CONVERT(date, dOperdate) = CONVERT(date, GETDATE());
```

## 数据库设计特点

1. **模块化设计**: 通过表名前缀(Code_, T_)区分字典表和业务表
2. **完整的审计追踪**: 包含操作员、操作时间等审计字段
3. **灵活的配置**: 大量字典表支持系统配置
4. **AI集成**: 包含AI诊断相关表结构
5. **数据验证**: 专门的验证表确保数据质量

## 使用说明

测试脚本应存储在 `test/` 目录中，以保持项目结构的一致性。

## 常用查询示例

### 1. 查询今天体检人数

```sql
-- 根据登记时间查询今天体检人数
SELECT COUNT(*) as 今天体检人数
FROM T_Register_Main 
WHERE CONVERT(date, dOperdate) = CONVERT(date, GETDATE());

-- 查询不同状态的体检人数 (cStatus: 0=已登记, 1=已确认, 2=已总检, 3=已打印)
SELECT 
    CASE cStatus 
        WHEN '0' THEN '已登记'
        WHEN '1' THEN '已确认'
        WHEN '2' THEN '已总检'
        WHEN '3' THEN '已打印'
        ELSE '未知状态'
    END as 体检状态,
    COUNT(*) as 人数,
    CONVERT(decimal(5,2), COUNT(*) * 100.0 / SUM(COUNT(*)) OVER()) as 占比百分比
FROM T_Register_Main 
WHERE CONVERT(date, dOperdate) = CONVERT(date, GETDATE())
GROUP BY cStatus
ORDER BY cStatus;

-- 查询今天体检流程进度汇总
SELECT 
    COUNT(CASE WHEN cStatus = '0' THEN 1 END) as 已登记人数,
    COUNT(CASE WHEN cStatus = '1' THEN 1 END) as 已确认人数,
    COUNT(CASE WHEN cStatus = '2' THEN 1 END) as 已总检人数,
    COUNT(CASE WHEN cStatus = '3' THEN 1 END) as 已打印人数,
    COUNT(*) as 总登记人数
FROM T_Register_Main 
WHERE CONVERT(date, dOperdate) = CONVERT(date, GETDATE());
```

### 2. 查询体检完成情况

```sql
-- 查询今天已完成体检的人数（有检查结果的）
SELECT COUNT(DISTINCT r.cClientCode) as 今天完成体检人数
FROM T_Register_Main r
INNER JOIN T_Check_result_Main cr ON r.cClientCode = cr.cClientCode
WHERE CONVERT(date, r.dOperdate) = CONVERT(date, GETDATE())
  AND cr.dOperDate IS NOT NULL;
```

### 3. 查询各科室检查情况

```sql
-- 查询今天各科室检查人数
SELECT 
    d.cName as 科室名称,
    COUNT(DISTINCT cr.cClientCode) as 检查人数
FROM T_Check_result_Main cr
INNER JOIN Code_Dept_Main d ON cr.cDeptCode = d.cCode
WHERE CONVERT(date, cr.dOperDate) = CONVERT(date, GETDATE())
GROUP BY d.cCode, d.cName
ORDER BY 检查人数 DESC;
```

### 4. 查询套餐使用情况

```sql
-- 查询今天各套餐体检人数（仅团检，散客无套餐）
SELECT 
    ISNULL(us.cName, '无套餐') as 套餐名称,
    COUNT(*) as 使用人数,
    CONVERT(decimal(5,2), COUNT(*) * 100.0 / SUM(COUNT(*)) OVER()) as 占比百分比
FROM T_Register_Main r
LEFT JOIN T_UnitsSuit_Master us ON r.cSuitCode = us.cSuitCode  -- 修正：使用正确的套餐表
WHERE CONVERT(date, r.dOperdate) = CONVERT(date, GETDATE())
  AND r.cStatus IS NOT NULL  -- 有效登记记录
GROUP BY us.cSuitCode, us.cName
ORDER BY 使用人数 DESC;
```

### 5. 查询异常结果情况

```sql
-- 查询今天有异常结果的体检者
SELECT 
    r.cClientCode as 客户编号,
    r.cName as 姓名,
    COUNT(cr.cDetailCode) as 异常项目数
FROM T_Register_Main r
INNER JOIN T_Check_result cr ON r.cClientCode = cr.cClientCode
WHERE CONVERT(date, r.dOperdate) = CONVERT(date, GETDATE())
  AND cr.cAbnor = '1'  -- 异常标记
GROUP BY r.cClientCode, r.cName
HAVING COUNT(cr.cDetailCode) > 0
ORDER BY 异常项目数 DESC;
```

### 6. 查询今天收入统计

```sql
-- 方法1：从收费主表查询收入（正确方法）
SELECT 
    SUM(cm.fFactTotal) as 今天实际收入,
    SUM(cm.fTotal) as 今天应收金额,
    COUNT(*) as 收费笔数,
    AVG(cm.fFactTotal) as 平均收费
FROM T_Charge_Main cm
WHERE CONVERT(date, cm.dOperDate) = CONVERT(date, GETDATE())
  AND cm.fFactTotal IS NOT NULL
  AND cm.fFactTotal > 0;

-- 方法2：按支付方式分组统计
SELECT 
    cd.cName as 支付方式,
    cm.cPayMode as 支付方式编码,
    SUM(cm.fFactTotal) as 收入金额,
    COUNT(*) as 笔数,
    AVG(cm.fFactTotal) as 平均金额
FROM T_Charge_Main cm
LEFT JOIN Code_Comm_Dict cd ON (cm.cPayMode = cd.cCode AND cd.iNameCode = 8)
WHERE CONVERT(date, cm.dOperDate) = CONVERT(date, GETDATE())
  AND cm.fFactTotal > 0
GROUP BY cm.cPayMode, cd.cName
ORDER BY 收入金额 DESC;

-- 方法3：详细收入明细（关联体检人员信息）
SELECT 
    rm.cClientCode as 客户编号,
    rm.cName as 姓名,
    cm.fTotal as 应收金额,
    cm.fFactTotal as 实际收费,
    cm.dOperDate as 收费时间,
    cm.cOpername as 收费员,
    cd.cName as 支付方式
FROM T_Register_Main rm
INNER JOIN T_Charge_Main cm ON rm.cClientCode = cm.cClientCode
LEFT JOIN Code_Comm_Dict cd ON (cm.cPayMode = cd.cCode AND cd.iNameCode = 8)
WHERE CONVERT(date, cm.dOperDate) = CONVERT(date, GETDATE())
  AND cm.fFactTotal > 0
ORDER BY cm.dOperDate DESC;

-- 方法4：按时段统计收入
SELECT 
    DATEPART(HOUR, cm.dOperDate) as 小时,
    SUM(cm.fFactTotal) as 收入金额,
    COUNT(*) as 收费笔数,
    AVG(cm.fFactTotal) as 平均收费
FROM T_Charge_Main cm
WHERE CONVERT(date, cm.dOperDate) = CONVERT(date, GETDATE())
  AND cm.fFactTotal > 0
GROUP BY DATEPART(HOUR, cm.dOperDate)
ORDER BY 小时;
```

### 7. 查询个人体检项目（修正版）

```sql
-- 方法1：根据客户编号查询体检项目（推荐）
SELECT 
    r.cClientCode as 客户编号,
    r.cName as 姓名,
    r.cSex as 性别,
    r.dOperdate as 登记时间,
    -- 团检散客区分
    CASE 
        WHEN (r.cContractCode IS NULL OR r.cContractCode = '') THEN '散客体检'
        WHEN tc.cCheckType = '散客体检' THEN '散客体检'
        WHEN r.cContractCode IS NOT NULL AND r.cContractCode != '' AND tc.cCheckType != '散客体检' THEN '团检体检'
        ELSE '未知类型'
    END as 体检类型,
    im.cName as 检查项目,
    ip.cName as 价格项目名称,
    ip.fPrice as 项目价格,
    dd.cName as 科室名称,
    r.cSuitCode as 套餐编码,
    -- 套餐信息（仅团检有套餐）
    CASE 
        WHEN (r.cContractCode IS NULL OR r.cContractCode = '' OR tc.cCheckType = '散客体检') THEN '无套餐（散客）'
        ELSE ISNULL(us.cName, '未设置套餐')
    END as 套餐名称
FROM T_Register_Main r
INNER JOIN T_Register_Detail rd ON r.cClientCode = rd.cClientCode
INNER JOIN code_Item_Price ip ON rd.cPriceCode = ip.cCode
INNER JOIN Code_Item_Main im ON ip.cMainCode = im.cCode
LEFT JOIN Code_Dept_Main dm ON im.cCode = dm.cMainCode
LEFT JOIN Code_Dept_dict dd ON dm.cDeptCode = dd.cCode
LEFT JOIN T_Contract tc ON r.cContractCode = tc.cCode
LEFT JOIN T_UnitsSuit_Master us ON r.cSuitCode = us.cSuitCode  -- 修正：使用正确的套餐表
WHERE r.cClientCode = '0000000001'  -- 替换为具体的客户编号
ORDER BY dd.cName, im.cName, ip.cName;

```

**注意：完整的个人体检项目查询示例请参考 `test/personal_exam_queries.sql` 文件，其中包含了10种不同的查询方法，所有查询都使用了正确的表关联关系。** 
        WHEN COUNT(rd.cDetailCode) > 0 
        THEN CONVERT(decimal(5,2), COUNT(cr.cDetailCode) * 100.0 / COUNT(rd.cDetailCode))
        ELSE 0 
    END as 完成百分比
FROM T_Register_Main r
LEFT JOIN T_Register_Detail rd ON r.cClientCode = rd.cClientCode
LEFT JOIN T_Check_result cr ON (r.cClientCode = cr.cClientCode AND rd.cDetailCode = cr.cDetailCode)
WHERE r.cClientCode = '0000000001'  -- 替换为具体的客户编号
GROUP BY r.cClientCode, r.cName;
```

---

## Core Joins (Revised)（核心关联关系）

### 1. 个人体检项目查询的正确表关联
```sql
-- 正确的关联路径：
T_Register_Main → T_Register_Detail → code_Item_Price → Code_Item_Main
Code_Item_Main → Code_Dept_Main → Code_Dept_dict (科室信息)
Code_Item_Main → Code_Item_Detail (检查明细，用于T_Check_result)

-- 套餐名称查询（仅团检用户有套餐）：
T_Register_Main → T_UnitsSuit_Master (套餐信息)

-- 收费信息查询：
T_Register_Main → T_Charge_Main (收费信息)
T_Charge_Main → Code_Comm_Dict (支付方式字典)

-- 单位信息查询：
T_Register_Main → T_Contract → 单位信息
```

### 2. 关键字段关联
- `T_Register_Detail.cPriceCode = code_Item_Price.cCode`
- `code_Item_Price.cMainCode = Code_Item_Main.cCode`
- `Code_Item_Main.cCode = Code_Dept_Main.cMainCode`
- `Code_Dept_Main.cDeptCode = Code_Dept_dict.cCode`
- `T_Register_Main.cSuitCode = T_UnitsSuit_Master.cSuitCode` (套餐信息)
- `T_Register_Main.cClientCode = T_Charge_Main.cClientCode` (收费信息)
- `T_Charge_Main.cPayMode = Code_Comm_Dict.cCode AND Code_Comm_Dict.iNameCode = 8` (支付方式)
- `T_Register_Main.cContractCode = T_Contract.cCode` (合同关联)
- `T_Contract.cUnitsCode` (单位编码)

### 3. 团检与散客标识规则
根据`dashboard_api.php`的业务逻辑：

**散客体检：**
- `T_Register_Main.cContractCode` 为NULL或空字符串
- 或者 `T_Contract.cCheckType = '散客体检'`
- 散客没有套餐名称

**团检体检：**
- `T_Register_Main.cContractCode` 不为空
- 且 `T_Contract.cCheckType != '散客体检'`
- 团检用户通过 `T_Register_Main.cSuitCode = T_UnitsSuit_Master.cSuitCode` 关联套餐
- 套餐名称字段：`T_UnitsSuit_Master.cName`

## Table Categories（表分类详细说明）

### 系统基础表 (Code_开头)

#### 字典表类
- **Code_Sex**: 性别字典表
- **Code_Nation_Dict**: 民族字典表
- **Code_Area_Dict**: 地区字典表
- **Code_Occup_Dict**: 职业字典表
- **Code_Common_Langu**: 通用语言表

#### 检查项目管理
- **Code_Item_Main**: 检查项目主表 - 定义各种检查项目
- **Code_Item_Detail**: 检查项目明细表 - 具体检查指标
  - `cConsult`: 参考值范围（正常值）
  - `cUnit`: 检查单位
  - `cName`: 检查项目明细名称
- **Code_ItemDetail_Result**: 检查项目结果模板表
- **code_Item_Price**: 检查项目价格表

#### 套餐管理
- **Code_Suit_Master**: 套餐主表 - 体检套餐定义
- **Code_Suit_Detail**: 套餐明细表 - 套餐包含的检查项目

#### 部门科室管理
- **Code_Dept_Main**: 科室主表
- **Code_Dept_room**: 科室房间表
- **Code_Department_Dict**: 部门字典表

#### 疾病诊断相关
- **Code_Illness_Class**: 疾病分类表
- **Code_Illness_Crite**: 疾病诊断标准表
- **Code_Advice_Crite**: 建议标准表
- **Code_Advice_Illness**: 建议疾病关联表

### 业务数据表 (T_开头)

#### 客户管理模块
- **T_Client**: 客户基本信息表 (主键: cId)
- **T_Client_Contract**: 客户合同表
- **T_Client_EditLog**: 客户编辑日志表
- **T_Client_LinkPeople**: 客户联系人表
- **T_Client_FaithLink**: 客户回访记录表

#### 体检登记模块
- **T_Register_Main**: 体检登记主表 (主键: cClientCode)
  - 包含体检者基本信息、套餐信息、操作员信息等
- **T_Register_ItemPrint**: 体检项目打印表

#### 检查结果模块
- **T_Check_result**: 检查结果明细表 (主键: cClientCode + cDetailCode)
  - `cResult`: 检查结果值
  - `cResultDesc`: 结果描述
  - `cAbnor`: 是否异常（'1'=异常，'0'=正常）
  - `cDoctName`: 检查医生
  - `dOperDate`: 检查时间
- **T_Check_result_Main**: 检查结果主表 (主键: cClientCode + cDeptCode + cMainCode)
- **T_Check_Result_Illness**: 客户异常疾病汇总表
  - 汇总客户所有异常检查结果对应的疾病
- **T_Check_result_Option**: 检查结果选项表
- **T_Check_result_Param**: 检查结果参数表

#### 诊断模块
- **T_Diagnosis**: 诊断表 (主键: ID, 关联: cClientCode)
- **T_Diagnosis_Conclusion**: 诊断结论表 (主键: id, 关联: cClientCode)
- **T_Diag_result**: 总检结论表 (主键: cClientCode)
  - `cDiag`: 总检结论
  - `cDiagDesc`: 总检结论描述
  - `cDoctName`: 总检医生
  - `dOperDate`: 总检时间

#### AI诊断模块
- **AI_Medical_Conditions**: AI医疗条件表
- **AI_Diagnosis_Mapping**: AI诊断映射表
- **T_AI_Ask_Response**: AI问答响应表 (外键: cClientCode → T_Register_Main)

#### 验证模块
- **T_Check_Result_Validation**: 检查结果验证表 (主键: validation_id)
- **T_Check_Result_Validation_Issue**: 验证问题表 (外键: validation_id)

#### 卡券管理
- **T_Card**: 卡券主表
- **T_Card_Detail**: 卡券明细表
- **T_Card_Sale_Detail**: 卡券销售明细表

#### 收费管理
- **T_Charge_Main**: 收费主表
- **T_Charge_Detail**: 收费明细表
- **T_Charge_PayDetail**: 收费支付明细表

### 临时表和工具表

#### 清理数据表
- **cleaned_MedicalSymptoms**: 清理后的医疗症状表
- **MedicalSymptoms**: 医疗症状表

#### 临时表
- **mytmp系列**: 各种临时表用于数据处理
- **temp_开头**: 临时数据表

## Common Pitfalls（常见错误修正说明）

### 1. 套餐表名错误
- **错误**：使用 `Code_Suit_Master` 表查询套餐
- **正确**：使用 `T_UnitsSuit_Master` 表
- **原因**：团检套餐使用专门的T_UnitsSuit_Master表，不是通用套餐表

### 2. 收费信息位置错误
- **错误**：从 `T_Register_Main` 表中查询 `fTotal`、`fFactTotal` 等收费字段
- **正确**：从 `T_Charge_Main` 表中查询收费信息
- **关联**：`T_Register_Main.cClientCode = T_Charge_Main.cClientCode`

### 3. 单位字段名错误
- **错误**：使用 `T_Contract.cClientCode` 存储单位编号
- **正确**：使用 `T_Contract.cUnitsCode` 字段
- **说明**：T_Contract表中没有cClientCode字段

### 4. 支付方式查询错误
- **错误**：直接使用支付方式编码
- **正确**：通过字典表获取名称
- **关联**：`T_Charge_Main.cPayMode = Code_Comm_Dict.cCode AND Code_Comm_Dict.iNameCode = 8`

### 5. 团检散客区分错误
- **错误**：仅通过是否有合同判断
- **正确**：同时判断合同和体检类型
- **逻辑**：
  ```sql
  -- 散客
  WHERE (rm.cContractCode IS NULL OR rm.cContractCode = '' OR tc.cCheckType = '散客体检')
  -- 团检
  WHERE rm.cContractCode IS NOT NULL AND rm.cContractCode != '' AND tc.cCheckType != '散客体检'
  ```

### 6. 冗余查询条件
- **错误**：在INNER JOIN后仍添加IS NOT NULL条件
- **正确**：INNER JOIN本身保证关联字段不为空
- **优化**：去除冗余的NULL判断提高查询性能

### 7. GROUP BY语法错误
- **错误**：SELECT中有非聚合字段但GROUP BY中没有包含
- **正确**：GROUP BY包含所有非聚合函数字段
- **解决**：要么添加到GROUP BY，要么移除该字段，要么使用聚合函数

### 8. 检查结果相关字段错误
- **错误**：使用 `Code_Item_Detail.cNormalValue` 作为正常值字段
- **正确**：使用 `Code_Item_Detail.cConsult` 作为参考值字段
- **错误**：使用 `T_Check_result.cOperName` 作为检查医生
- **正确**：使用 `T_Check_result.cDoctName` 作为检查医生
- **说明**：Code_Item_Detail表中没有cNormalValue字段，参考值存储在cConsult字段中

### 9. 诊断结论表混淆错误
- **错误**：将 `T_Diagnosis_Conclusion` 当作最终总检结论表
- **正确**：`T_Diag_result` 才是总检结论表，包含最终体检总结论
- **区别**：
  - `T_Diagnosis_Conclusion`: 各科室诊断结论
  - `T_Diag_result`: 综合所有检查的最终总检结论
- **关联**：都通过 `cClientCode` 关联到体检人员
- **T_Diag_result 重要字段**：
  - `cDiag`: 总检结论（不是 cResultDesc）
  - `cDiagDesc`: 总检结论描述（不是 cAdvice）
  - `cDoctName`: 总检医生
  - `dOperDate`: 总检时间

### 10. 机构字段混淆错误
- **错误**：将所有机构字段都当作同一概念使用
- **正确**：不同表中的 cShopCode 含义不同
- **区别**：
  - `T_Register_Main.cShopCode`: 信息登记机构
  - `T_Register_Main.cAffirmShopCode`: 确认到检机构  
  - `T_Charge_Main.cShopCode`: 收费机构
- **统计注意**：根据业务需求选择正确的机构字段
  - 登记统计用 `T_Register_Main.cShopCode`
  - 收费统计用 `T_Charge_Main.cShopCode`
  - 到检统计用 `T_Register_Main.cAffirmShopCode`

这些修正确保了查询的准确性和性能，避免了常见的数据库关联错误。

## Extended Relationships（扩展关联关系）

以下关联关系在 `exmdb.sql` 中**已经存在但此前未在文档中列出**，现统一补充说明。

> 说明：
> 1. 标注为【物理外键】的关系在数据库脚本中已通过 `ALTER TABLE … FOREIGN KEY` 明确约束。
> 2. 标注为【逻辑关联】的关系仅通过字段值对应，业务上强关联但未设置外键。

| # | 主表 → 从表 | 关键字段 | 关系类型 |
|---|-------------|-----------|-----------|
| 1 | AI_Diagnosis_Mapping → AI_Medical_Conditions | mapped_illness_code = code | 【逻辑关联】AI 诊断映射到疾病明细 |
| 2 | Code_Advice_Illness → Code_Advice_Crite | cAdviceCode = cCode | 【逻辑关联】建议–建议标准 |
|   | Code_Advice_Illness → Code_Illness_Class | cIllnessCode = cCode | 【逻辑关联】建议–疾病分类 |
| 3 | Code_Illness_Crite_0709 / _pgy → Code_Illness_Class | cIllnessCode = cCode | 【逻辑关联】疾病诊断标准 |
| 4 | T_UnitsSuit_Master → T_UnitsSuit_Detail | cSuitCode = cSuitCode | 【逻辑关联】团检套餐主从 |
|   | T_UnitsSuit_Detail → Code_Item_Main | cMainCode = cCode | 【逻辑关联】套餐包含项目 |
| 5 | T_Units_Appoints → T_Contract | cContractCode = cCode | 【逻辑关联】单位体检预约–合同 |
|   | T_Units_Appoints_Item → T_Units_Appoints | cAppointNo = cAppointNo | 【逻辑关联】预约明细 |
| 6 | T_Charge_Main → T_Charge_Detail | cChargeNo = cChargeNo | 【逻辑关联】收费主从明细 |
|   | T_Charge_Main → T_Charge_PayDetail | cChargeNo = cChargeNo | 【逻辑关联】收费支付明细 |
| 7 | T_Card → T_Card_Sale_Detail | cCardNo = cCardNo | 【逻辑关联】卡券–销售明细 |
|   | T_Card_Sale_Detail → T_Card_SellReceive_PayDetail | cSaleNo = cSaleNo | 【逻辑关联】卡券销售收款 |
| 8 | Code_Called_CheckRoom → Code_Called_CheckRoom_Vs_ItemMain | cCode = cRoomCode | 【逻辑关联】叫号房间–项目 |
|   | Code_Item_Main → Code_Called_CheckRoom_Vs_ItemMain | cCode = cMainCode | 【逻辑关联】项目–叫号房间映射 |
| 9 | Code_Operator_dict → Code_Operator_Shop | cCode = cOperCode | 【逻辑关联】操作员–门店 |
|   | T_SysSeting → Code_Operator_Shop | cShopCode = cShopCode | 【逻辑关联】门店–操作员 |
|10 | Code_Comm_Dict → Code_Comm_DictName | iNameCode = iNameCode | 【逻辑关联】通用字典–扩展名称 |

上述补充信息可帮助开发者在进行业务查询、报表统计或数据同步时，正确理解各表之间的依赖关系，避免遗漏关键 Join 条件。